# Base Repository Implementation Guide

This guide provides detailed instructions for implementing a base repository class to eliminate redundant CRUD operations across repository implementations in the Lonors AI Platform backend codebase.

## Table of Contents

1. [Overview](#1-overview)
2. [Implementation Steps](#2-implementation-steps)
3. [Usage Examples](#3-usage-examples)
4. [Migration Guide](#4-migration-guide)
5. [Testing](#5-testing)
6. [Advanced Patterns](#6-advanced-patterns)

## 1. Overview

### Current Redundancy

Currently, similar CRUD operations appear across multiple repository implementations:

- `/backend/src/infrastructure/database/repositories/user_repository.py`
- `/backend/src/infrastructure/database/repositories/agent_repository.py`
- `/backend/src/infrastructure/database/repositories/protocol_repository.py`

### Solution

Implement a base repository class that provides:

- Common CRUD operations (create, read, update, delete)
- Pagination support
- Type safety with generics
- Consistent error handling
- Mapping between domain entities and database models

## 2. Implementation Steps

### Step 1: Create the Base Entity

First, ensure there's a base entity class in the domain layer:

```bash
mkdir -p backend/src/domain/entities
touch backend/src/domain/entities/base_entity.py
```

```python
# /backend/src/domain/entities/base_entity.py
from datetime import datetime
from typing import Optional
from uuid import UUID, uuid4


class BaseEntity:
    """Base class for all domain entities."""

    def __init__(
        self,
        id: Optional[UUID] = None,
        created_at: Optional[datetime] = None,
        updated_at: Optional[datetime] = None,
    ) -> None:
        self.id = id or uuid4()
        self.created_at = created_at or datetime.utcnow()
        self.updated_at = updated_at or self.created_at
```

### Step 2: Create the Base Repository Interface

Create a base repository interface in the domain layer:

```bash
mkdir -p backend/src/domain/repositories
touch backend/src/domain/repositories/base_repository.py
```

```python
# /backend/src/domain/repositories/base_repository.py
from abc import ABC, abstractmethod
from typing import Generic, List, Optional, TypeVar
from uuid import UUID

from src.domain.entities.base_entity import BaseEntity

T = TypeVar("T", bound=BaseEntity)


class BaseRepositoryInterface(Generic[T], ABC):
    """Base interface for all repositories."""

    @abstractmethod
    async def get_by_id(self, id: UUID) -> Optional[T]:
        """Get entity by ID."""
        pass

    @abstractmethod
    async def list(self, skip: int = 0, limit: int = 100) -> List[T]:
        """Get list of entities with pagination."""
        pass

    @abstractmethod
    async def create(self, entity: T) -> T:
        """Create new entity."""
        pass

    @abstractmethod
    async def update(self, entity: T) -> Optional[T]:
        """Update entity."""
        pass

    @abstractmethod
    async def delete(self, id: UUID) -> bool:
        """Delete entity by ID."""
        pass

    @abstractmethod
    async def count(self) -> int:
        """Count total entities."""
        pass
```

### Step 3: Create the Base Database Model

Create a base SQLAlchemy model:

```bash
mkdir -p backend/src/infrastructure/database/models
touch backend/src/infrastructure/database/models/base.py
```

```python
# /backend/src/infrastructure/database/models/base.py
import uuid
from datetime import datetime

from sqlalchemy import Column, DateTime, String
from sqlalchemy.dialects.postgresql import UUID
from sqlalchemy.ext.declarative import declarative_base

Base = declarative_base()


class BaseModel(Base):
    """Base model for all database models."""

    __abstract__ = True

    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    created_at = Column(DateTime, default=datetime.utcnow, nullable=False)
    updated_at = Column(
        DateTime, default=datetime.utcnow, onupdate=datetime.utcnow, nullable=False
    )
```

### Step 4: Implement the Base Repository

Create the base repository implementation:

```bash
mkdir -p backend/src/infrastructure/database/repositories
touch backend/src/infrastructure/database/repositories/base_repository.py
```

```python
# /backend/src/infrastructure/database/repositories/base_repository.py
from typing import Any, Dict, Generic, List, Optional, Type, TypeVar, cast
from uuid import UUID

from sqlalchemy import func, select
from sqlalchemy.ext.asyncio import AsyncSession

from src.domain.entities.base_entity import BaseEntity
from src.domain.repositories.base_repository import BaseRepositoryInterface
from src.infrastructure.database.models.base import BaseModel

T = TypeVar("T", bound=BaseEntity)
M = TypeVar("M", bound=BaseModel)


class BaseRepository(Generic[T, M], BaseRepositoryInterface[T]):
    """Base repository implementing common CRUD operations."""

    def __init__(self, session_factory: callable) -> None:
        """
        Initialize repository with session factory.

        Args:
            session_factory: Callable that returns an AsyncSession
        """
        self._session_factory = session_factory

    async def _get_session(self) -> AsyncSession:
        """Get database session."""
        return self._session_factory()

    @property
    def model(self) -> Type[M]:
        """Get the SQLAlchemy model class."""
        raise NotImplementedError("Subclasses must implement model property")

    async def get_by_id(self, id: UUID) -> Optional[T]:
        """
        Get entity by ID.

        Args:
            id: Entity ID

        Returns:
            Entity if found, None otherwise
        """
        async with await self._get_session() as session:
            result = await session.execute(
                select(self.model).where(self.model.id == id)
            )
            db_item = result.scalars().first()
            return self._to_entity(db_item) if db_item else None

    async def list(self, skip: int = 0, limit: int = 100) -> List[T]:
        """
        Get list of entities with pagination.

        Args:
            skip: Number of items to skip
            limit: Maximum number of items to return

        Returns:
            List of entities
        """
        async with await self._get_session() as session:
            result = await session.execute(
                select(self.model).offset(skip).limit(limit)
            )
            return [self._to_entity(item) for item in result.scalars().all()]

    async def create(self, entity: T) -> T:
        """
        Create new entity.

        Args:
            entity: Entity to create

        Returns:
            Created entity with ID and timestamps
        """
        db_item = self._to_model(entity)
        async with await self._get_session() as session:
            session.add(db_item)
            await session.commit()
            await session.refresh(db_item)
            return self._to_entity(db_item)

    async def update(self, entity: T) -> Optional[T]:
        """
        Update entity.

        Args:
            entity: Entity to update

        Returns:
            Updated entity if found, None otherwise
        """
        async with await self._get_session() as session:
            result = await session.execute(
                select(self.model).where(self.model.id == entity.id)
            )
            db_item = result.scalars().first()

            if db_item:
                # Update model with entity values
                for key, value in self._to_dict(entity).items():
                    if hasattr(db_item, key):
                        setattr(db_item, key, value)

                await session.commit()
                await session.refresh(db_item)
                return self._to_entity(db_item)

            return None

    async def delete(self, id: UUID) -> bool:
        """
        Delete entity by ID.

        Args:
            id: Entity ID

        Returns:
            True if entity was deleted, False otherwise
        """
        async with await self._get_session() as session:
            result = await session.execute(
                select(self.model).where(self.model.id == id)
            )
            db_item = result.scalars().first()

            if db_item:
                await session.delete(db_item)
                await session.commit()
                return True

            return False

    async def count(self) -> int:
        """
        Count total entities.

        Returns:
            Total number of entities
        """
        async with await self._get_session() as session:
            result = await session.execute(
                select(func.count()).select_from(self.model)
            )
            return cast(int, result.scalar_one())

    def _to_entity(self, model: M) -> T:
        """
        Convert database model to domain entity.

        Args:
            model: Database model

        Returns:
            Domain entity
        """
        raise NotImplementedError("Subclasses must implement _to_entity method")

    def _to_model(self, entity: T) -> M:
        """
        Convert domain entity to database model.

        Args:
            entity: Domain entity

        Returns:
            Database model
        """
        raise NotImplementedError("Subclasses must implement _to_model method")

    def _to_dict(self, entity: T) -> Dict[str, Any]:
        """
        Convert entity to dictionary.

        Args:
            entity: Domain entity

        Returns:
            Dictionary representation of entity
        """
        return {
            key: value
            for key, value in entity.__dict__.items()
            if not key.startswith("_")
        }
```

### Step 5: Create Repository Exception Classes

Create custom exceptions for repository operations:

```bash
mkdir -p backend/src/domain/exceptions
touch backend/src/domain/exceptions/__init__.py
```

```python
# /backend/src/domain/exceptions/__init__.py
class DomainException(Exception):
    """Base exception for all domain exceptions."""
    pass


class EntityNotFoundError(DomainException):
    """Raised when an entity is not found."""
    pass


class EntityAlreadyExistsError(DomainException):
    """Raised when an entity already exists."""
    pass


class RepositoryError(DomainException):
    """Base exception for all repository errors."""
    pass
```

## 3. Usage Examples

### Implementing a User Repository

```python
# /backend/src/domain/entities/user.py
from datetime import datetime
from typing import Optional
from uuid import UUID

from src.domain.entities.base_entity import BaseEntity


class User(BaseEntity):
    """User entity."""

    def __init__(
        self,
        email: str,
        first_name: str,
        last_name: str,
        is_active: bool = True,
        id: Optional[UUID] = None,
        created_at: Optional[datetime] = None,
        updated_at: Optional[datetime] = None,
    ) -> None:
        super().__init__(id, created_at, updated_at)
        self.email = email
        self.first_name = first_name
        self.last_name = last_name
        self.is_active = is_active
```

```python
# /backend/src/domain/repositories/user_repository.py
from typing import List, Optional
from uuid import UUID

from src.domain.entities.user import User
from src.domain.repositories.base_repository import BaseRepositoryInterface


class UserRepositoryInterface(BaseRepositoryInterface[User]):
    """Interface for user repository."""

    async def get_by_email(self, email: str) -> Optional[User]:
        """Get user by email."""
        pass

    async def find_by_name(self, name: str) -> List[User]:
        """Find users by name."""
        pass
```

```python
# /backend/src/infrastructure/database/models/user.py
from sqlalchemy import Boolean, Column, String

from src.infrastructure.database.models.base import BaseModel


class UserModel(BaseModel):
    """User database model."""

    __tablename__ = "users"

    email = Column(String, unique=True, index=True, nullable=False)
    first_name = Column(String, nullable=False)
    last_name = Column(String, nullable=False)
    is_active = Column(Boolean, default=True, nullable=False)
    hashed_password = Column(String, nullable=False)
```

```python
# /backend/src/infrastructure/database/repositories/user_repository.py
from typing import List, Optional
from uuid import UUID

from sqlalchemy import or_, select

from src.domain.entities.user import User
from src.domain.repositories.user_repository import UserRepositoryInterface
from src.infrastructure.database.models.user import UserModel
from src.infrastructure.database.repositories.base_repository import BaseRepository


class UserRepository(BaseRepository[User, UserModel], UserRepositoryInterface):
    """User repository implementation."""

    @property
    def model(self):
        """Get the SQLAlchemy model class."""
        return UserModel

    async def get_by_email(self, email: str) -> Optional[User]:
        """Get user by email."""
        async with await self._get_session() as session:
            result = await session.execute(
                select(self.model).where(self.model.email == email)
            )
            db_user = result.scalars().first()
            return self._to_entity(db_user) if db_user else None

    async def find_by_name(self, name: str) -> List[User]:
        """Find users by name."""
        async with await self._get_session() as session:
            result = await session.execute(
                select(self.model).where(
                    or_(
                        self.model.first_name.ilike(f"%{name}%"),
                        self.model.last_name.ilike(f"%{name}%"),
                    )
                )
            )
            return [self._to_entity(item) for item in result.scalars().all()]

    def _to_entity(self, model: UserModel) -> User:
        """Convert database model to domain entity."""
        return User(
            id=model.id,
            email=model.email,
            first_name=model.first_name,
            last_name=model.last_name,
            is_active=model.is_active,
            created_at=model.created_at,
            updated_at=model.updated_at,
        )

    def _to_model(self, entity: User) -> UserModel:
        """Convert domain entity to database model."""
        return UserModel(
            id=entity.id,
            email=entity.email,
            first_name=entity.first_name,
            last_name=entity.last_name,
            is_active=entity.is_active,
            created_at=entity.created_at,
            updated_at=entity.updated_at,
            # Note: hashed_password would be set separately
            hashed_password="",  # This is a placeholder
        )
```

### Using the Repository in a Service

```python
# /backend/src/application/use_cases/user_service.py
from typing import List, Optional
from uuid import UUID

from src.domain.entities.user import User
from src.domain.exceptions import EntityNotFoundError
from src.domain.repositories.user_repository import UserRepositoryInterface


class UserService:
    """User service implementing user-related use cases."""

    def __init__(self, user_repository: UserRepositoryInterface) -> None:
        self._user_repository = user_repository

    async def get_user_by_id(self, user_id: UUID) -> User:
        """
        Get user by ID.

        Args:
            user_id: User ID

        Returns:
            User entity

        Raises:
            EntityNotFoundError: If user is not found
        """
        user = await self._user_repository.get_by_id(user_id)
        if not user:
            raise EntityNotFoundError(f"User with ID {user_id} not found")
        return user

    async def create_user(self, user: User) -> User:
        """
        Create new user.

        Args:
            user: User entity

        Returns:
            Created user entity
        """
        # Check if user with same email already exists
        existing_user = await self._user_repository.get_by_email(user.email)
        if existing_user:
            raise EntityAlreadyExistsError(f"User with email {user.email} already exists")

        return await self._user_repository.create(user)

    async def update_user(self, user: User) -> User:
        """
        Update user.

        Args:
            user: User entity

        Returns:
            Updated user entity

        Raises:
            EntityNotFoundError: If user is not found
        """
        updated_user = await self._user_repository.update(user)
        if not updated_user:
            raise EntityNotFoundError(f"User with ID {user.id} not found")
        return updated_user

    async def delete_user(self, user_id: UUID) -> None:
        """
        Delete user by ID.

        Args:
            user_id: User ID

        Raises:
            EntityNotFoundError: If user is not found
        """
        deleted = await self._user_repository.delete(user_id)
        if not deleted:
            raise EntityNotFoundError(f"User with ID {user_id} not found")

    async def list_users(self, skip: int = 0, limit: int = 100) -> List[User]:
        """
        List users with pagination.

        Args:
            skip: Number of users to skip
            limit: Maximum number of users to return

        Returns:
            List of users
        """
        return await self._user_repository.list(skip, limit)

    async def search_users_by_name(self, name: str) -> List[User]:
        """
        Search users by name.

        Args:
            name: Name to search for

        Returns:
            List of matching users
        """
        return await self._user_repository.find_by_name(name)
```

## 4. Migration Guide

Follow these steps to migrate existing repositories to use the base repository:

### Step 1: Identify Repositories to Migrate

Identify all repository implementations that perform similar CRUD operations.

### Step 2: Update Domain Entities

Ensure all domain entities inherit from `BaseEntity`:

```python
# Before
class User:
    def __init__(self, id, email, first_name, last_name, is_active=True):
        self.id = id
        self.email = email
        self.first_name = first_name
        self.last_name = last_name
        self.is_active = is_active

# After
from src.domain.entities.base_entity import BaseEntity

class User(BaseEntity):
    def __init__(
        self,
        email: str,
        first_name: str,
        last_name: str,
        is_active: bool = True,
        id: Optional[UUID] = None,
        created_at: Optional[datetime] = None,
        updated_at: Optional[datetime] = None,
    ) -> None:
        super().__init__(id, created_at, updated_at)
        self.email = email
        self.first_name = first_name
        self.last_name = last_name
        self.is_active = is_active
```

### Step 3: Update Repository Interfaces

Ensure all repository interfaces inherit from `BaseRepositoryInterface`:

```python
# Before
class UserRepositoryInterface(ABC):
    @abstractmethod
    async def get_by_id(self, id: UUID) -> Optional[User]:
        pass

    @abstractmethod
    async def list(self, skip: int = 0, limit: int = 100) -> List[User]:
        pass

    # ... other methods

# After
from src.domain.repositories.base_repository import BaseRepositoryInterface

class UserRepositoryInterface(BaseRepositoryInterface[User]):
    # Only add methods specific to User
    async def get_by_email(self, email: str) -> Optional[User]:
        pass
```

### Step 4: Update Repository Implementations

Refactor repository implementations to inherit from `BaseRepository`:

```python
# Before
class UserRepository(UserRepositoryInterface):
    def __init__(self, session_factory: callable) -> None:
        self._session_factory = session_factory

    async def get_by_id(self, id: UUID) -> Optional[User]:
        async with self._session_factory() as session:
            result = await session.execute(
                select(UserModel).where(UserModel.id == id)
            )
            db_user = result.scalars().first()
            return self._to_entity(db_user) if db_user else None

    # ... other methods

# After
from src.infrastructure.database.repositories.base_repository import BaseRepository

class UserRepository(BaseRepository[User, UserModel], UserRepositoryInterface):
    @property
    def model(self):
        return UserModel

    # Only implement methods specific to User and the mapping methods
    async def get_by_email(self, email: str) -> Optional[User]:
        # Implementation

    def _to_entity(self, model: UserModel) -> User:
        # Implementation

    def _to_model(self, entity: User) -> UserModel:
        # Implementation
```

### Step 5: Update Service Layer

Update service layer to handle repository exceptions:

```python
# Before
async def get_user_by_id(self, user_id: UUID) -> Optional[User]:
    return await self._user_repository.get_by_id(user_id)

# After
async def get_user_by_id(self, user_id: UUID) -> User:
    user = await self._user_repository.get_by_id(user_id)
    if not user:
        raise EntityNotFoundError(f"User with ID {user_id} not found")
    return user
```

### Step 6: Test Thoroughly

Test each migrated repository to ensure:
- CRUD operations work correctly
- Custom methods work correctly
- Error handling functions as expected
- Edge cases are handled properly

## 5. Testing

### Unit Testing the Base Repository

Create unit tests for the base repository:

```python
# /backend/tests/infrastructure/database/repositories/test_base_repository.py
import pytest
from uuid import UUID, uuid4
from datetime import datetime

from src.domain.entities.base_entity import BaseEntity
from src.infrastructure.database.models.base import BaseModel
from src.infrastructure.database.repositories.base_repository import BaseRepository


# Create test entity and model classes
class TestEntity(BaseEntity):
    def __init__(
        self,
        name: str,
        description: str = "",
        id: UUID = None,
        created_at: datetime = None,
        updated_at: datetime = None,
    ):
        super().__init__(id, created_at, updated_at)
        self.name = name
        self.description = description


class TestModel(BaseModel):
    __tablename__ = "test_items"

    name = Column(String, nullable=False)
    description = Column(String, nullable=True)


# Create test repository
class TestRepository(BaseRepository[TestEntity, TestModel]):
    @property
    def model(self):
        return TestModel

    def _to_entity(self, model: TestModel) -> TestEntity:
        return TestEntity(
            id=model.id,
            name=model.name,
            description=model.description,
            created_at=model.created_at,
            updated_at=model.updated_at,
        )

    def _to_model(self, entity: TestEntity) -> TestModel:
        return TestModel(
            id=entity.id,
            name=entity.name,
            description=entity.description,
            created_at=entity.created_at,
            updated_at=entity.updated_at,
        )


@pytest.fixture
async def test_repository(db_session):
    async def get_session():
        return db_session

    return TestRepository(get_session)


@pytest.mark.asyncio
async def test_create(test_repository):
    # Create test entity
    entity = TestEntity(name="Test Item", description="Test Description")

    # Create in repository
    created = await test_repository.create(entity)

    # Check that entity was created with ID
    assert created.id is not None
    assert created.name == "Test Item"
    assert created.description == "Test Description"
    assert created.created_at is not None
    assert created.updated_at is not None


@pytest.mark.asyncio
async def test_get_by_id(test_repository):
    # Create test entity
    entity = TestEntity(name="Test Item", description="Test Description")
    created = await test_repository.create(entity)

    # Get by ID
    retrieved = await test_repository.get_by_id(created.id)

    # Check that entity was retrieved
    assert retrieved is not None
    assert retrieved.id == created.id
    assert retrieved.name == created.name
    assert retrieved.description == created.description


@pytest.mark.asyncio
async def test_list(test_repository):
    # Create multiple test entities
    for i in range(5):
        entity = TestEntity(name=f"Test Item {i}", description=f"Test Description {i}")
        await test_repository.create(entity)

    # List entities
    entities = await test_repository.list(skip=0, limit=10)

    # Check that entities were retrieved
    assert len(entities) == 5


@pytest.mark.asyncio
async def test_update(test_repository):
    # Create test entity
    entity = TestEntity(name="Test Item", description="Test Description")
    created = await test_repository.create(entity)

    # Update entity
    created.name = "Updated Name"
    updated = await test_repository.update(created)

    # Check that entity was updated
    assert updated is not None
    assert updated.id == created.id
    assert updated.name == "Updated Name"
    assert updated.description == created.description


@pytest.mark.asyncio
async def test_delete(test_repository):
    # Create test entity
    entity = TestEntity(name="Test Item", description="Test Description")
    created = await test_repository.create(entity)

    # Delete entity
    deleted = await test_repository.delete(created.id)

    # Check that entity was deleted
    assert deleted is True

    # Try to get deleted entity
    retrieved = await test_repository.get_by_id(created.id)
    assert retrieved is None


@pytest.mark.asyncio
async def test_count(test_repository):
    # Create multiple test entities
    for i in range(3):
        entity = TestEntity(name=f"Test Item {i}", description=f"Test Description {i}")
        await test_repository.create(entity)

    # Count entities
    count = await test_repository.count()

    # Check that count is correct
    assert count == 3
```

### Integration Testing

Create integration tests for repositories:

```python
# /backend/tests/integration/test_user_repository.py
import pytest
from uuid import uuid4

from src.domain.entities.user import User
from src.infrastructure.database.repositories.user_repository import UserRepository


@pytest.fixture
async def user_repository(db_session):
    async def get_session():
        return db_session

    return UserRepository(get_session)


@pytest.mark.asyncio
async def test_create_and_get_user(user_repository):
    # Create test user
    user = User(
        email="<EMAIL>",
        first_name="Test",
        last_name="User",
        is_active=True,
    )

    # Create in repository
    created = await user_repository.create(user)

    # Get by ID
    retrieved = await user_repository.get_by_id(created.id)

    # Check that user was retrieved
    assert retrieved is not None
    assert retrieved.id == created.id
    assert retrieved.email == created.email
    assert retrieved.first_name == created.first_name
    assert retrieved.last_name == created.last_name
    assert retrieved.is_active == created.is_active


@pytest.mark.asyncio
async def test_get_by_email(user_repository):
    # Create test user
    user = User(
        email="<EMAIL>",
        first_name="Test",
        last_name="User",
        is_active=True,
    )
    await user_repository.create(user)

    # Get by email
    retrieved = await user_repository.get_by_email("<EMAIL>")

    # Check that user was retrieved
    assert retrieved is not None
    assert retrieved.email == "<EMAIL>"

    # Try to get non-existent user
    non_existent = await user_repository.get_by_email("<EMAIL>")
    assert non_existent is None


@pytest.mark.asyncio
async def test_find_by_name(user_repository):
    # Create test users
    users = [
        User(email="<EMAIL>", first_name="John", last_name="Doe"),
        User(email="<EMAIL>", first_name="Jane", last_name="Doe"),
        User(email="<EMAIL>", first_name="Bob", last_name="Smith"),
    ]

    for user in users:
        await user_repository.create(user)

    # Find by name
    doe_users = await user_repository.find_by_name("Doe")

    # Check that users were found
    assert len(doe_users) == 2
    assert any(user.first_name == "John" for user in doe_users)
    assert any(user.first_name == "Jane" for user in doe_users)

    # Find by first name
    john_users = await user_repository.find_by_name("John")
    assert len(john_users) == 1
    assert john_users[0].first_name == "John"
```

## 6. Advanced Patterns

### Filtering and Sorting

Enhance the base repository with filtering and sorting capabilities:

```python
# /backend/src/infrastructure/database/repositories/base_repository.py
from typing import Any, Dict, List, Optional, Tuple, Type, TypeVar, Union, cast
from uuid import UUID

from sqlalchemy import asc, desc, func, select
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.sql.expression import BinaryExpression, Select

from src.domain.entities.base_entity import BaseEntity
from src.domain.repositories.base_repository import BaseRepositoryInterface
from src.infrastructure.database.models.base import BaseModel

# ... existing code ...

class BaseRepository(Generic[T, M], BaseRepositoryInterface[T]):
    # ... existing methods ...

    async def find(
        self,
        filters: Optional[List[BinaryExpression]] = None,
        order_by: Optional[List[Union[str, Tuple[str, str]]]] = None,
        skip: int = 0,
        limit: int = 100,
    ) -> List[T]:
        """
        Find entities with filtering, sorting, and pagination.

        Args:
            filters: List of SQLAlchemy filter expressions
            order_by: List of field names or (field, direction) tuples for sorting
            skip: Number of items to skip
            limit: Maximum number of items to return

        Returns:
            List of matching entities
        """
        query = select(self.model)

        # Apply filters
        if filters:
            for filter_expr in filters:
                query = query.where(filter_expr)

        # Apply sorting
        if order_by:
            for order_item in order_by:
                if isinstance(order_item, tuple):
                    field, direction = order_item
                    column = getattr(self.model, field)
                    query = query.order_by(
                        desc(column) if direction.lower() == "desc" else asc(column)
                    )
                else:
                    column = getattr(self.model, order_item)
                    query = query.order_by(column)

        # Apply pagination
        query = query.offset(skip).limit(limit)

        async with await self._get_session() as session:
            result = await session.execute(query)
            return [self._to_entity(item) for item in result.scalars().all()]
```

### Transactions

Add transaction support to the base repository:

```python
# /backend/src/infrastructure/database/repositories/base_repository.py
from contextlib import asynccontextmanager
from typing import AsyncGenerator, TypeVar

from sqlalchemy.ext.asyncio import AsyncSession

# ... existing code ...

class BaseRepository(Generic[T, M], BaseRepositoryInterface[T]):
    # ... existing methods ...

    @asynccontextmanager
    async def transaction(self) -> AsyncGenerator[AsyncSession, None]:
        """
        Create a transaction context.

        Usage:
            async with repository.transaction() as session:
                # Perform multiple operations in a single transaction
                await session.execute(...)
                await session.execute(...)
        """
        async with await self._get_session() as session:
            async with session.begin():
                yield session
```

### Soft Delete

Implement soft delete functionality:

```python
# /backend/src/infrastructure/database/models/base.py
import uuid
from datetime import datetime

from sqlalchemy import Boolean, Column, DateTime, String
from sqlalchemy.dialects.postgresql import UUID
from sqlalchemy.ext.declarative import declarative_base

Base = declarative_base()


class BaseModel(Base):
    """Base model for all database models."""

    __abstract__ = True

    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    created_at = Column(DateTime, default=datetime.utcnow, nullable=False)
    updated_at = Column(
        DateTime, default=datetime.utcnow, onupdate=datetime.utcnow, nullable=False
    )
    is_deleted = Column(Boolean, default=False, nullable=False)
    deleted_at = Column(DateTime, nullable=True)
```

```python
# /backend/src/infrastructure/database/repositories/base_repository.py
from datetime import datetime
from typing import List, Optional
from uuid import UUID

from sqlalchemy import select
from sqlalchemy.ext.asyncio import AsyncSession

# ... existing code ...

class BaseRepository(Generic[T, M], BaseRepositoryInterface[T]):
    # ... existing methods ...

    async def get_by_id(self, id: UUID) -> Optional[T]:
        """Get entity by ID."""
        async with await self._get_session() as session:
            result = await session.execute(
                select(self.model).where(
                    self.model.id == id,
                    self.model.is_deleted == False,  # Only return non-deleted items
                )
            )
            db_item = result.scalars().first()
            return self._to_entity(db_item) if db_item else None

    async def list(self, skip: int = 0, limit: int = 100) -> List[T]:
        """Get list of entities with pagination."""
        async with await self._get_session() as session:
            result = await session.execute(
                select(self.model)
                .where(self.model.is_deleted == False)  # Only return non-deleted items
                .offset(skip)
                .limit(limit)
            )
            return [self._to_entity(item) for item in result.scalars().all()]

    async def soft_delete(self, id: UUID) -> bool:
        """
        Soft delete entity by ID.

        Args:
            id: Entity ID

        Returns:
            True if entity was soft deleted, False otherwise
        """
        async with await self._get_session() as session:
            result = await session.execute(
                select(self.model).where(
                    self.model.id == id,
                    self.model.is_deleted == False,
                )
            )
            db_item = result.scalars().first()

            if db_item:
                db_item.is_deleted = True
                db_item.deleted_at = datetime.utcnow()
                await session.commit()
                return True

            return False

    async def restore(self, id: UUID) -> bool:
        """
        Restore soft-deleted entity by ID.

        Args:
            id: Entity ID

        Returns:
            True if entity was restored, False otherwise
        """
        async with await self._get_session() as session:
            result = await session.execute(
                select(self.model).where(
                    self.model.id == id,
                    self.model.is_deleted == True,
                )
            )
            db_item = result.scalars().first()

            if db_item:
                db_item.is_deleted = False
                db_item.deleted_at = None
                await session.commit()
                return True

            return False
```

---

By implementing this base repository class, you'll eliminate redundant CRUD operations across repository implementations in the Lonors AI Platform backend codebase, improving maintainability, consistency, and type safety.

*Last Updated: 2024-12-30*# Base Repository Implementation Guide

This guide provides detailed instructions for implementing a base repository class to eliminate redundant CRUD operations across repository implementations in the Lonors AI Platform backend codebase.

## Table of Contents

1. [Overview](#1-overview)
2. [Implementation Steps](#2-implementation-steps)
3. [Usage Examples](#3-usage-examples)
4. [Migration Guide](#4-migration-guide)
5. [Testing](#5-testing)
6. [Advanced Patterns](#6-advanced-patterns)

## 1. Overview

### Current Redundancy

Currently, similar CRUD operations appear across multiple repository implementations:

- `/backend/src/infrastructure/database/repositories/user_repository.py`
- `/backend/src/infrastructure/database/repositories/agent_repository.py`
- `/backend/src/infrastructure/database/repositories/protocol_repository.py`

### Solution

Implement a base repository class that provides:

- Common CRUD operations (create, read, update, delete)
- Pagination support
- Type safety with generics
- Consistent error handling
- Mapping between domain entities and database models

## 2. Implementation Steps

### Step 1: Create the Base Entity

First, ensure there's a base entity class in the domain layer:

```bash
mkdir -p backend/src/domain/entities
touch backend/src/domain/entities/base_entity.py
```

```python
# /backend/src/domain/entities/base_entity.py
from datetime import datetime
from typing import Optional
from uuid import UUID, uuid4


class BaseEntity:
    """Base class for all domain entities."""

    def __init__(
        self,
        id: Optional[UUID] = None,
        created_at: Optional[datetime] = None,
        updated_at: Optional[datetime] = None,
    ) -> None:
        self.id = id or uuid4()
        self.created_at = created_at or datetime.utcnow()
        self.updated_at = updated_at or self.created_at
```

### Step 2: Create the Base Repository Interface

Create a base repository interface in the domain layer:

```bash
mkdir -p backend/src/domain/repositories
touch backend/src/domain/repositories/base_repository.py
```

```python
# /backend/src/domain/repositories/base_repository.py
from abc import ABC, abstractmethod
from typing import Generic, List, Optional, TypeVar
from uuid import UUID

from src.domain.entities.base_entity import BaseEntity

T = TypeVar("T", bound=BaseEntity)


class BaseRepositoryInterface(Generic[T], ABC):
    """Base interface for all repositories."""

    @abstractmethod
    async def get_by_id(self, id: UUID) -> Optional[T]:
        """Get entity by ID."""
        pass

    @abstractmethod
    async def list(self, skip: int = 0, limit: int = 100) -> List[T]:
        """Get list of entities with pagination."""
        pass

    @abstractmethod
    async def create(self, entity: T) -> T:
        """Create new entity."""
        pass

    @abstractmethod
    async def update(self, entity: T) -> Optional[T]:
        """Update entity."""
        pass

    @abstractmethod
    async def delete(self, id: UUID) -> bool:
        """Delete entity by ID."""
        pass

    @abstractmethod
    async def count(self) -> int:
        """Count total entities."""
        pass
```

### Step 3: Create the Base Database Model

Create a base SQLAlchemy model:

```bash
mkdir -p backend/src/infrastructure/database/models
touch backend/src/infrastructure/database/models/base.py
```

```python
# /backend/src/infrastructure/database/models/base.py
import uuid
from datetime import datetime

from sqlalchemy import Column, DateTime, String
from sqlalchemy.dialects.postgresql import UUID
from sqlalchemy.ext.declarative import declarative_base

Base = declarative_base()


class BaseModel(Base):
    """Base model for all database models."""

    __abstract__ = True

    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    created_at = Column(DateTime, default=datetime.utcnow, nullable=False)
    updated_at = Column(
        DateTime, default=datetime.utcnow, onupdate=datetime.utcnow, nullable=False
    )
```

### Step 4: Implement the Base Repository

Create the base repository implementation:

```bash
mkdir -p backend/src/infrastructure/database/repositories
touch backend/src/infrastructure/database/repositories/base_repository.py
```

```python
# /backend/src/infrastructure/database/repositories/base_repository.py
from typing import Any, Dict, Generic, List, Optional, Type, TypeVar, cast
from uuid import UUID

from sqlalchemy import func, select
from sqlalchemy.ext.asyncio import AsyncSession

from src.domain.entities.base_entity import BaseEntity
from src.domain.repositories.base_repository import BaseRepositoryInterface
from src.infrastructure.database.models.base import BaseModel

T = TypeVar("T", bound=BaseEntity)
M = TypeVar("M", bound=BaseModel)


class BaseRepository(Generic[T, M], BaseRepositoryInterface[T]):
    """Base repository implementing common CRUD operations."""

    def __init__(self, session_factory: callable) -> None:
        """
        Initialize repository with session factory.

        Args:
            session_factory: Callable that returns an AsyncSession
        """
        self._session_factory = session_factory

    async def _get_session(self) -> AsyncSession:
        """Get database session."""
        return self._session_factory()

    @property
    def model(self) -> Type[M]:
        """Get the SQLAlchemy model class."""
        raise NotImplementedError("Subclasses must implement model property")

    async def get_by_id(self, id: UUID) -> Optional[T]:
        """
        Get entity by ID.

        Args:
            id: Entity ID

        Returns:
            Entity if found, None otherwise
        """
        async with await self._get_session() as session:
            result = await session.execute(
                select(self.model).where(self.model.id == id)
            )
            db_item = result.scalars().first()
            return self._to_entity(db_item) if db_item else None

    async def list(self, skip: int = 0, limit: int = 100) -> List[T]:
        """
        Get list of entities with pagination.

        Args:
            skip: Number of items to skip
            limit: Maximum number of items to return

        Returns:
            List of entities
        """
        async with await self._get_session() as session:
            result = await session.execute(
                select(self.model).offset(skip).limit(limit)
            )
            return [self._to_entity(item) for item in result.scalars().all()]

    async def create(self, entity: T) -> T:
        """
        Create new entity.

        Args:
            entity: Entity to create

        Returns:
            Created entity with ID and timestamps
        """
        db_item = self._to_model(entity)
        async with await self._get_session() as session:
            session.add(db_item)
            await session.commit()
            await session.refresh(db_item)
            return self._to_entity(db_item)

    async def update(self, entity: T) -> Optional[T]:
        """
        Update entity.

        Args:
            entity: Entity to update

        Returns:
            Updated entity if found, None otherwise
        """
        async with await self._get_session() as session:
            result = await session.execute(
                select(self.model).where(self.model.id == entity.id)
            )
            db_item = result.scalars().first()

            if db_item:
                # Update model with entity values
                for key, value in self._to_dict(entity).items():
                    if hasattr(db_item, key):
                        setattr(db_item, key, value)

                await session.commit()
                await session.refresh(db_item)
                return self._to_entity(db_item)

            return None

    async def delete(self, id: UUID) -> bool:
        """
        Delete entity by ID.

        Args:
            id: Entity ID

        Returns:
            True if entity was deleted, False otherwise
        """
        async with await self._get_session() as session:
            result = await session.execute(
                select(self.model).where(self.model.id == id)
            )
            db_item = result.scalars().first()

            if db_item:
                await session.delete(db_item)
                await session.commit()
                return True

            return False

    async def count(self) -> int:
        """
        Count total entities.

        Returns:
            Total number of entities
        """
        async with await self._get_session() as session:
            result = await session.execute(
                select(func.count()).select_from(self.model)
            )
            return cast(int, result.scalar_one())

    def _to_entity(self, model: M) -> T:
        """
        Convert database model to domain entity.

        Args:
            model: Database model

        Returns:
            Domain entity
        """
        raise NotImplementedError("Subclasses must implement _to_entity method")

    def _to_model(self, entity: T) -> M:
        """
        Convert domain entity to database model.

        Args:
            entity: Domain entity

        Returns:
            Database model
        """
        raise NotImplementedError("Subclasses must implement _to_model method")

    def _to_dict(self, entity: T) -> Dict[str, Any]:
        """
        Convert entity to dictionary.

        Args:
            entity: Domain entity

        Returns:
            Dictionary representation of entity
        """
        return {
            key: value
            for key, value in entity.__dict__.items()
            if not key.startswith("_")
        }
```

### Step 5: Create Repository Exception Classes

Create custom exceptions for repository operations:

```bash
mkdir -p backend/src/domain/exceptions
touch backend/src/domain/exceptions/__init__.py
```

```python
# /backend/src/domain/exceptions/__init__.py
class DomainException(Exception):
    """Base exception for all domain exceptions."""
    pass


class EntityNotFoundError(DomainException):
    """Raised when an entity is not found."""
    pass


class EntityAlreadyExistsError(DomainException):
    """Raised when an entity already exists."""
    pass


class RepositoryError(DomainException):
    """Base exception for all repository errors."""
    pass
```

## 3. Usage Examples

### Implementing a User Repository

```python
# /backend/src/domain/entities/user.py
from datetime import datetime
from typing import Optional
from uuid import UUID

from src.domain.entities.base_entity import BaseEntity


class User(BaseEntity):
    """User entity."""

    def __init__(
        self,
        email: str,
        first_name: str,
        last_name: str,
        is_active: bool = True,
        id: Optional[UUID] = None,
        created_at: Optional[datetime] = None,
        updated_at: Optional[datetime] = None,
    ) -> None:
        super().__init__(id, created_at, updated_at)
        self.email = email
        self.first_name = first_name
        self.last_name = last_name
        self.is_active = is_active
```

```python
# /backend/src/domain/repositories/user_repository.py
from typing import List, Optional
from uuid import UUID

from src.domain.entities.user import User
from src.domain.repositories.base_repository import BaseRepositoryInterface


class UserRepositoryInterface(BaseRepositoryInterface[User]):
    """Interface for user repository."""

    async def get_by_email(self, email: str) -> Optional[User]:
        """Get user by email."""
        pass

    async def find_by_name(self, name: str) -> List[User]:
        """Find users by name."""
        pass
```

```python
# /backend/src/infrastructure/database/models/user.py
from sqlalchemy import Boolean, Column, String

from src.infrastructure.database.models.base import BaseModel


class UserModel(BaseModel):
    """User database model."""

    __tablename__ = "users"

    email = Column(String, unique=True, index=True, nullable=False)
    first_name = Column(String, nullable=False)
    last_name = Column(String, nullable=False)
    is_active = Column(Boolean, default=True, nullable=False)
    hashed_password = Column(String, nullable=False)
```

```python
# /backend/src/infrastructure/database/repositories/user_repository.py
from typing import List, Optional
from uuid import UUID

from sqlalchemy import or_, select

from src.domain.entities.user import User
from src.domain.repositories.user_repository import UserRepositoryInterface
from src.infrastructure.database.models.user import UserModel
from src.infrastructure.database.repositories.base_repository import BaseRepository


class UserRepository(BaseRepository[User, UserModel], UserRepositoryInterface):
    """User repository implementation."""

    @property
    def model(self):
        """Get the SQLAlchemy model class."""
        return UserModel

    async def get_by_email(self, email: str) -> Optional[User]:
        """Get user by email."""
        async with await self._get_session() as session:
            result = await session.execute(
                select(self.model).where(self.model.email == email)
            )
            db_user = result.scalars().first()
            return self._to_entity(db_user) if db_user else None

    async def find_by_name(self, name: str) -> List[User]:
        """Find users by name."""
        async with await self._get_session() as session:
            result = await session.execute(
                select(self.model).where(
                    or_(
                        self.model.first_name.ilike(f"%{name}%"),
                        self.model.last_name.ilike(f"%{name}%"),
                    )
                )
            )
            return [self._to_entity(item) for item in result.scalars().all()]

    def _to_entity(self, model: UserModel) -> User:
        """Convert database model to domain entity."""
        return User(
            id=model.id,
            email=model.email,
            first_name=model.first_name,
            last_name=model.last_name,
            is_active=model.is_active,
            created_at=model.created_at,
            updated_at=model.updated_at,
        )

    def _to_model(self, entity: User) -> UserModel:
        """Convert domain entity to database model."""
        return UserModel(
            id=entity.id,
            email=entity.email,
            first_name=entity.first_name,
            last_name=entity.last_name,
            is_active=entity.is_active,
            created_at=entity.created_at,
            updated_at=entity.updated_at,
            # Note: hashed_password would be set separately
            hashed_password="",  # This is a placeholder
        )
```

### Using the Repository in a Service

```python
# /backend/src/application/use_cases/user_service.py
from typing import List, Optional
from uuid import UUID

from src.domain.entities.user import User
from src.domain.exceptions import EntityNotFoundError
from src.domain.repositories.user_repository import UserRepositoryInterface


class UserService:
    """User service implementing user-related use cases."""

    def __init__(self, user_repository: UserRepositoryInterface) -> None:
        self._user_repository = user_repository

    async def get_user_by_id(self, user_id: UUID) -> User:
        """
        Get user by ID.

        Args:
            user_id: User ID

        Returns:
            User entity

        Raises:
            EntityNotFoundError: If user is not found
        """
        user = await self._user_repository.get_by_id(user_id)
        if not user:
            raise EntityNotFoundError(f"User with ID {user_id} not found")
        return user

    async def create_user(self, user: User) -> User:
        """
        Create new user.

        Args:
            user: User entity

        Returns:
            Created user entity
        """
        # Check if user with same email already exists
        existing_user = await self._user_repository.get_by_email(user.email)
        if existing_user:
            raise EntityAlreadyExistsError(f"User with email {user.email} already exists")

        return await self._user_repository.create(user)

    async def update_user(self, user: User) -> User:
        """
        Update user.

        Args:
            user: User entity

        Returns:
            Updated user entity

        Raises:
            EntityNotFoundError: If user is not found
        """
        updated_user = await self._user_repository.update(user)
        if not updated_user:
            raise EntityNotFoundError(f"User with ID {user.id} not found")
        return updated_user

    async def delete_user(self, user_id: UUID) -> None:
        """
        Delete user by ID.

        Args:
            user_id: User ID

        Raises:
            EntityNotFoundError: If user is not found
        """
        deleted = await self._user_repository.delete(user_id)
        if not deleted:
            raise EntityNotFoundError(f"User with ID {user_id} not found")

    async def list_users(self, skip: int = 0, limit: int = 100) -> List[User]:
        """
        List users with pagination.

        Args:
            skip: Number of users to skip
            limit: Maximum number of users to return

        Returns:
            List of users
        """
        return await self._user_repository.list(skip, limit)

    async def search_users_by_name(self, name: str) -> List[User]:
        """
        Search users by name.

        Args:
            name: Name to search for

        Returns:
            List of matching users
        """
        return await self._user_repository.find_by_name(name)
```

## 4. Migration Guide

Follow these steps to migrate existing repositories to use the base repository:

### Step 1: Identify Repositories to Migrate

Identify all repository implementations that perform similar CRUD operations.

### Step 2: Update Domain Entities

Ensure all domain entities inherit from `BaseEntity`:

```python
# Before
class User:
    def __init__(self, id, email, first_name, last_name, is_active=True):
        self.id = id
        self.email = email
        self.first_name = first_name
        self.last_name = last_name
        self.is_active = is_active

# After
from src.domain.entities.base_entity import BaseEntity

class User(BaseEntity):
    def __init__(
        self,
        email: str,
        first_name: str,
        last_name: str,
        is_active: bool = True,
        id: Optional[UUID] = None,
        created_at: Optional[datetime] = None,
        updated_at: Optional[datetime] = None,
    ) -> None:
        super().__init__(id, created_at, updated_at)
        self.email = email
        self.first_name = first_name
        self.last_name = last_name
        self.is_active = is_active
```

### Step 3: Update Repository Interfaces

Ensure all repository interfaces inherit from `BaseRepositoryInterface`:

```python
# Before
class UserRepositoryInterface(ABC):
    @abstractmethod
    async def get_by_id(self, id: UUID) -> Optional[User]:
        pass

    @abstractmethod
    async def list(self, skip: int = 0, limit: int = 100) -> List[User]:
        pass

    # ... other methods

# After
from src.domain.repositories.base_repository import BaseRepositoryInterface

class UserRepositoryInterface(BaseRepositoryInterface[User]):
    # Only add methods specific to User
    async def get_by_email(self, email: str) -> Optional[User]:
        pass
```

### Step 4: Update Repository Implementations

Refactor repository implementations to inherit from `BaseRepository`:

```python
# Before
class UserRepository(UserRepositoryInterface):
    def __init__(self, session_factory: callable) -> None:
        self._session_factory = session_factory

    async def get_by_id(self, id: UUID) -> Optional[User]:
        async with self._session_factory() as session:
            result = await session.execute(
                select(UserModel).where(UserModel.id == id)
            )
            db_user = result.scalars().first()
            return self._to_entity(db_user) if db_user else None

    # ... other methods

# After
from src.infrastructure.database.repositories.base_repository import BaseRepository

class UserRepository(BaseRepository[User, UserModel], UserRepositoryInterface):
    @property
    def model(self):
        return UserModel

    # Only implement methods specific to User and the mapping methods
    async def get_by_email(self, email: str) -> Optional[User]:
        # Implementation

    def _to_entity(self, model: UserModel) -> User:
        # Implementation

    def _to_model(self, entity: User) -> UserModel:
        # Implementation
```

### Step 5: Update Service Layer

Update service layer to handle repository exceptions:

```python
# Before
async def get_user_by_id(self, user_id: UUID) -> Optional[User]:
    return await self._user_repository.get_by_id(user_id)

# After
async def get_user_by_id(self, user_id: UUID) -> User:
    user = await self._user_repository.get_by_id(user_id)
    if not user:
        raise EntityNotFoundError(f"User with ID {user_id} not found")
    return user
```

### Step 6: Test Thoroughly

Test each migrated repository to ensure:
- CRUD operations work correctly
- Custom methods work correctly
- Error handling functions as expected
- Edge cases are handled properly

## 5. Testing

### Unit Testing the Base Repository

Create unit tests for the base repository:

```python
# /backend/tests/infrastructure/database/repositories/test_base_repository.py
import pytest
from uuid import UUID, uuid4
from datetime import datetime

from src.domain.entities.base_entity import BaseEntity
from src.infrastructure.database.models.base import BaseModel
from src.infrastructure.database.repositories.base_repository import BaseRepository


# Create test entity and model classes
class TestEntity(BaseEntity):
    def __init__(
        self,
        name: str,
        description: str = "",
        id: UUID = None,
        created_at: datetime = None,
        updated_at: datetime = None,
    ):
        super().__init__(id, created_at, updated_at)
        self.name = name
        self.description = description


class TestModel(BaseModel):
    __tablename__ = "test_items"

    name = Column(String, nullable=False)
    description = Column(String, nullable=True)


# Create test repository
class TestRepository(BaseRepository[TestEntity, TestModel]):
    @property
    def model(self):
        return TestModel

    def _to_entity(self, model: TestModel) -> TestEntity:
        return TestEntity(
            id=model.id,
            name=model.name,
            description=model.description,
            created_at=model.created_at,
            updated_at=model.updated_at,
        )

    def _to_model(self, entity: TestEntity) -> TestModel:
        return TestModel(
            id=entity.id,
            name=entity.name,
            description=entity.description,
            created_at=entity.created_at,
            updated_at=entity.updated_at,
        )


@pytest.fixture
async def test_repository(db_session):
    async def get_session():
        return db_session

    return TestRepository(get_session)


@pytest.mark.asyncio
async def test_create(test_repository):
    # Create test entity
    entity = TestEntity(name="Test Item", description="Test Description")

    # Create in repository
    created = await test_repository.create(entity)

    # Check that entity was created with ID
    assert created.id is not None
    assert created.name == "Test Item"
    assert created.description == "Test Description"
    assert created.created_at is not None
    assert created.updated_at is not None


@pytest.mark.asyncio
async def test_get_by_id(test_repository):
    # Create test entity
    entity = TestEntity(name="Test Item", description="Test Description")
    created = await test_repository.create(entity)

    # Get by ID
    retrieved = await test_repository.get_by_id(created.id)

    # Check that entity was retrieved
    assert retrieved is not None
    assert retrieved.id == created.id
    assert retrieved.name == created.name
    assert retrieved.description == created.description


@pytest.mark.asyncio
async def test_list(test_repository):
    # Create multiple test entities
    for i in range(5):
        entity = TestEntity(name=f"Test Item {i}", description=f"Test Description {i}")
        await test_repository.create(entity)

    # List entities
    entities = await test_repository.list(skip=0, limit=10)

    # Check that entities were retrieved
    assert len(entities) == 5


@pytest.mark.asyncio
async def test_update(test_repository):
    # Create test entity
    entity = TestEntity(name="Test Item", description="Test Description")
    created = await test_repository.create(entity)

    # Update entity
    created.name = "Updated Name"
    updated = await test_repository.update(created)

    # Check that entity was updated
    assert updated is not None
    assert updated.id == created.id
    assert updated.name == "Updated Name"
    assert updated.description == created.description


@pytest.mark.asyncio
async def test_delete(test_repository):
    # Create test entity
    entity = TestEntity(name="Test Item", description="Test Description")
    created = await test_repository.create(entity)

    # Delete entity
    deleted = await test_repository.delete(created.id)

    # Check that entity was deleted
    assert deleted is True

    # Try to get deleted entity
    retrieved = await test_repository.get_by_id(created.id)
    assert retrieved is None


@pytest.mark.asyncio
async def test_count(test_repository):
    # Create multiple test entities
    for i in range(3):
        entity = TestEntity(name=f"Test Item {i}", description=f"Test Description {i}")
        await test_repository.create(entity)

    # Count entities
    count = await test_repository.count()

    # Check that count is correct
    assert count == 3
```

### Integration Testing

Create integration tests for repositories:

```python
# /backend/tests/integration/test_user_repository.py
import pytest
from uuid import uuid4

from src.domain.entities.user import User
from src.infrastructure.database.repositories.user_repository import UserRepository


@pytest.fixture
async def user_repository(db_session):
    async def get_session():
        return db_session

    return UserRepository(get_session)


@pytest.mark.asyncio
async def test_create_and_get_user(user_repository):
    # Create test user
    user = User(
        email="<EMAIL>",
        first_name="Test",
        last_name="User",
        is_active=True,
    )

    # Create in repository
    created = await user_repository.create(user)

    # Get by ID
    retrieved = await user_repository.get_by_id(created.id)

    # Check that user was retrieved
    assert retrieved is not None
    assert retrieved.id == created.id
    assert retrieved.email == created.email
    assert retrieved.first_name == created.first_name
    assert retrieved.last_name == created.last_name
    assert retrieved.is_active == created.is_active


@pytest.mark.asyncio
async def test_get_by_email(user_repository):
    # Create test user
    user = User(
        email="<EMAIL>",
        first_name="Test",
        last_name="User",
        is_active=True,
    )
    await user_repository.create(user)

    # Get by email
    retrieved = await user_repository.get_by_email("<EMAIL>")

    # Check that user was retrieved
    assert retrieved is not None
    assert retrieved.email == "<EMAIL>"

    # Try to get non-existent user
    non_existent = await user_repository.get_by_email("<EMAIL>")
    assert non_existent is None


@pytest.mark.asyncio
async def test_find_by_name(user_repository):
    # Create test users
    users = [
        User(email="<EMAIL>", first_name="John", last_name="Doe"),
        User(email="<EMAIL>", first_name="Jane", last_name="Doe"),
        User(email="<EMAIL>", first_name="Bob", last_name="Smith"),
    ]

    for user in users:
        await user_repository.create(user)

    # Find by name
    doe_users = await user_repository.find_by_name("Doe")

    # Check that users were found
    assert len(doe_users) == 2
    assert any(user.first_name == "John" for user in doe_users)
    assert any(user.first_name == "Jane" for user in doe_users)

    # Find by first name
    john_users = await user_repository.find_by_name("John")
    assert len(john_users) == 1
    assert john_users[0].first_name == "John"
```

## 6. Advanced Patterns

### Filtering and Sorting

Enhance the base repository with filtering and sorting capabilities:

```python
# /backend/src/infrastructure/database/repositories/base_repository.py
from typing import Any, Dict, List, Optional, Tuple, Type, TypeVar, Union, cast
from uuid import UUID

from sqlalchemy import asc, desc, func, select
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.sql.expression import BinaryExpression, Select

from src.domain.entities.base_entity import BaseEntity
from src.domain.repositories.base_repository import BaseRepositoryInterface
from src.infrastructure.database.models.base import BaseModel

# ... existing code ...

class BaseRepository(Generic[T, M], BaseRepositoryInterface[T]):
    # ... existing methods ...

    async def find(
        self,
        filters: Optional[List[BinaryExpression]] = None,
        order_by: Optional[List[Union[str, Tuple[str, str]]]] = None,
        skip: int = 0,
        limit: int = 100,
    ) -> List[T]:
        """
        Find entities with filtering, sorting, and pagination.

        Args:
            filters: List of SQLAlchemy filter expressions
            order_by: List of field names or (field, direction) tuples for sorting
            skip: Number of items to skip
            limit: Maximum number of items to return

        Returns:
            List of matching entities
        """
        query = select(self.model)

        # Apply filters
        if filters:
            for filter_expr in filters:
                query = query.where(filter_expr)

        # Apply sorting
        if order_by:
            for order_item in order_by:
                if isinstance(order_item, tuple):
                    field, direction = order_item
                    column = getattr(self.model, field)
                    query = query.order_by(
                        desc(column) if direction.lower() == "desc" else asc(column)
                    )
                else:
                    column = getattr(self.model, order_item)
                    query = query.order_by(column)

        # Apply pagination
        query = query.offset(skip).limit(limit)

        async with await self._get_session() as session:
            result = await session.execute(query)
            return [self._to_entity(item) for item in result.scalars().all()]
```

### Transactions

Add transaction support to the base repository:

```python
# /backend/src/infrastructure/database/repositories/base_repository.py
from contextlib import asynccontextmanager
from typing import AsyncGenerator, TypeVar

from sqlalchemy.ext.asyncio import AsyncSession

# ... existing code ...

class BaseRepository(Generic[T, M], BaseRepositoryInterface[T]):
    # ... existing methods ...

    @asynccontextmanager
    async def transaction(self) -> AsyncGenerator[AsyncSession, None]:
        """
        Create a transaction context.

        Usage:
            async with repository.transaction() as session:
                # Perform multiple operations in a single transaction
                await session.execute(...)
                await session.execute(...)
        """
        async with await self._get_session() as session:
            async with session.begin():
                yield session
```

### Soft Delete

Implement soft delete functionality:

```python
# /backend/src/infrastructure/database/models/base.py
import uuid
from datetime import datetime

from sqlalchemy import Boolean, Column, DateTime, String
from sqlalchemy.dialects.postgresql import UUID
from sqlalchemy.ext.declarative import declarative_base

Base = declarative_base()


class BaseModel(Base):
    """Base model for all database models."""

    __abstract__ = True

    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    created_at = Column(DateTime, default=datetime.utcnow, nullable=False)
    updated_at = Column(
        DateTime, default=datetime.utcnow, onupdate=datetime.utcnow, nullable=False
    )
    is_deleted = Column(Boolean, default=False, nullable=False)
    deleted_at = Column(DateTime, nullable=True)
```

```python
# /backend/src/infrastructure/database/repositories/base_repository.py
from datetime import datetime
from typing import List, Optional
from uuid import UUID

from sqlalchemy import select
from sqlalchemy.ext.asyncio import AsyncSession

# ... existing code ...

class BaseRepository(Generic[T, M], BaseRepositoryInterface[T]):
    # ... existing methods ...

    async def get_by_id(self, id: UUID) -> Optional[T]:
        """Get entity by ID."""
        async with await self._get_session() as session:
            result = await session.execute(
                select(self.model).where(
                    self.model.id == id,
                    self.model.is_deleted == False,  # Only return non-deleted items
                )
            )
            db_item = result.scalars().first()
            return self._to_entity(db_item) if db_item else None

    async def list(self, skip: int = 0, limit: int = 100) -> List[T]:
        """Get list of entities with pagination."""
        async with await self._get_session() as session:
            result = await session.execute(
                select(self.model)
                .where(self.model.is_deleted == False)  # Only return non-deleted items
                .offset(skip)
                .limit(limit)
            )
            return [self._to_entity(item) for item in result.scalars().all()]

    async def soft_delete(self, id: UUID) -> bool:
        """
        Soft delete entity by ID.

        Args:
            id: Entity ID

        Returns:
            True if entity was soft deleted, False otherwise
        """
        async with await self._get_session() as session:
            result = await session.execute(
                select(self.model).where(
                    self.model.id == id,
                    self.model.is_deleted == False,
                )
            )
            db_item = result.scalars().first()

            if db_item:
                db_item.is_deleted = True
                db_item.deleted_at = datetime.utcnow()
                await session.commit()
                return True

            return False

    async def restore(self, id: UUID) -> bool:
        """
        Restore soft-deleted entity by ID.

        Args:
            id: Entity ID

        Returns:
            True if entity was restored, False otherwise
        """
        async with await self._get_session() as session:
            result = await session.execute(
                select(self.model).where(
                    self.model.id == id,
                    self.model.is_deleted == True,
                )
            )
            db_item = result.scalars().first()

            if db_item:
                db_item.is_deleted = False
                db_item.deleted_at = None
                await session.commit()
                return True

            return False
```

---

By implementing this base repository class, you'll eliminate redundant CRUD operations across repository implementations in the Lonors AI Platform backend codebase, improving maintainability, consistency, and type safety.

*Last Updated: 2024-12-30*
