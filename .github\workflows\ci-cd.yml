name: 🚀 Lonors CI/CD Pipeline

on:
  push:
    branches: [ develop, main ]
  pull_request:
    branches: [ develop, main ]
  workflow_dispatch:
    inputs:
      environment:
        description: 'Deployment environment'
        required: true
        default: 'staging'
        type: choice
        options:
        - staging
        - production
      skip_tests:
        description: 'Skip test execution'
        required: false
        default: false
        type: boolean

env:
  DOCKER_BUILDKIT: 1
  COMPOSE_DOCKER_CLI_BUILD: 1
  REGISTRY: ghcr.io
  IMAGE_NAME: ${{ github.repository }}

jobs:
  # ============================================================================
  # SETUP AND VALIDATION
  # ============================================================================
  setup:
    name: 🔧 Setup and Validation
    runs-on: ubuntu-latest
    outputs:
      should-deploy: ${{ steps.deployment-check.outputs.should-deploy }}
      environment: ${{ steps.deployment-check.outputs.environment }}
      frontend-changed: ${{ steps.changes.outputs.frontend }}
      backend-changed: ${{ steps.changes.outputs.backend }}
      docker-changed: ${{ steps.changes.outputs.docker }}
    steps:
      - name: 📥 Checkout repository
        uses: actions/checkout@v4
        with:
          fetch-depth: 0

      - name: 🔍 Detect changes
        uses: dorny/paths-filter@v2
        id: changes
        with:
          filters: |
            frontend:
              - 'frontend/**'
              - 'package.json'
              - 'pnpm-lock.yaml'
            backend:
              - 'backend/**'
              - 'pyproject.toml'
              - 'uv.lock'
            docker:
              - 'docker-compose*.yml'
              - '**/Dockerfile*'
              - '.dockerignore'

      - name: 🎯 Determine deployment strategy
        id: deployment-check
        run: |
          if [[ "${{ github.event_name }}" == "workflow_dispatch" ]]; then
            echo "should-deploy=true" >> $GITHUB_OUTPUT
            echo "environment=${{ github.event.inputs.environment }}" >> $GITHUB_OUTPUT
          elif [[ "${{ github.ref }}" == "refs/heads/main" ]]; then
            echo "should-deploy=true" >> $GITHUB_OUTPUT
            echo "environment=production" >> $GITHUB_OUTPUT
          elif [[ "${{ github.ref }}" == "refs/heads/develop" ]]; then
            echo "should-deploy=true" >> $GITHUB_OUTPUT
            echo "environment=staging" >> $GITHUB_OUTPUT
          else
            echo "should-deploy=false" >> $GITHUB_OUTPUT
            echo "environment=none" >> $GITHUB_OUTPUT
          fi

  # ============================================================================
  # DOCKER BUILD AND CACHE
  # ============================================================================
  docker-build:
    name: 🐳 Docker Build & Cache
    runs-on: ubuntu-latest
    needs: setup
    if: needs.setup.outputs.frontend-changed == 'true' || needs.setup.outputs.backend-changed == 'true' || needs.setup.outputs.docker-changed == 'true'
    strategy:
      matrix:
        service: [frontend, backend]
        platform: [linux/amd64, linux/arm64]
    steps:
      - name: 📥 Checkout repository
        uses: actions/checkout@v4

      - name: 🔧 Set up Docker Buildx
        uses: docker/setup-buildx-action@v3
        with:
          platforms: linux/amd64,linux/arm64

      - name: 🔐 Log in to Container Registry
        uses: docker/login-action@v3
        with:
          registry: ${{ env.REGISTRY }}
          username: ${{ github.actor }}
          password: ${{ secrets.GITHUB_TOKEN }}

      - name: 📝 Extract metadata
        id: meta
        uses: docker/metadata-action@v5
        with:
          images: ${{ env.REGISTRY }}/${{ env.IMAGE_NAME }}/${{ matrix.service }}
          tags: |
            type=ref,event=branch
            type=ref,event=pr
            type=sha,prefix={{branch}}-
            type=raw,value=latest,enable={{is_default_branch}}

      - name: 🏗️ Build and push Docker image
        uses: docker/build-push-action@v5
        with:
          context: ./${{ matrix.service }}
          platforms: ${{ matrix.platform }}
          push: true
          tags: ${{ steps.meta.outputs.tags }}
          labels: ${{ steps.meta.outputs.labels }}
          cache-from: type=gha
          cache-to: type=gha,mode=max
          target: development

  # ============================================================================
  # SECURITY AUDIT
  # ============================================================================
  security-audit:
    name: 🛡️ Security Audit
    runs-on: ubuntu-latest
    needs: [setup, docker-build]
    if: always() && (needs.setup.outputs.docker-changed == 'true' || github.event_name == 'pull_request')
    steps:
      - name: 📥 Checkout repository
        uses: actions/checkout@v4

      - name: 🔧 Set up Docker Buildx
        uses: docker/setup-buildx-action@v3

      - name: 🔐 Log in to Container Registry
        uses: docker/login-action@v3
        with:
          registry: ${{ env.REGISTRY }}
          username: ${{ github.actor }}
          password: ${{ secrets.GITHUB_TOKEN }}

      - name: 🛡️ Run Docker security scan
        uses: docker/scout-action@v1
        with:
          command: cves
          image: ${{ env.REGISTRY }}/${{ env.IMAGE_NAME }}/frontend:${{ github.sha }}
          only-severities: critical,high
          exit-code: true

      - name: 🔍 Audit Docker Compose configuration
        run: |
          # Install docker-compose
          sudo curl -L "https://github.com/docker/compose/releases/download/v2.23.0/docker-compose-$(uname -s)-$(uname -m)" -o /usr/local/bin/docker-compose
          sudo chmod +x /usr/local/bin/docker-compose

          # Validate compose files
          docker-compose -f docker-compose.yml config --quiet
          docker-compose -f docker-compose.prod.yml config --quiet

          echo "✅ Docker Compose files are valid"

      - name: 🔒 Security best practices check
        run: |
          # Check for security issues in Docker configurations
          ISSUES=0

          # Check for privileged containers
          if grep -r "privileged.*true" docker-compose*.yml; then
            echo "❌ Found privileged containers"
            ISSUES=$((ISSUES + 1))
          fi

          # Check for host network mode
          if grep -r "network_mode.*host" docker-compose*.yml; then
            echo "❌ Found host network mode usage"
            ISSUES=$((ISSUES + 1))
          fi

          # Check for root user in Dockerfiles
          if grep -r "USER root" */Dockerfile*; then
            echo "❌ Found root user usage"
            ISSUES=$((ISSUES + 1))
          fi

          # Check for latest tags
          if grep -r ":latest" docker-compose*.yml; then
            echo "⚠️ Found latest tags (consider using specific versions)"
          fi

          if [ $ISSUES -gt 0 ]; then
            echo "❌ Security audit failed with $ISSUES critical issues"
            exit 1
          else
            echo "✅ Security audit passed"
          fi

      - name: 📊 Generate security report
        if: always()
        run: |
          cat > security-report.md << 'EOF'
          # 🛡️ Security Audit Report

          **Repository:** ${{ github.repository }}
          **Branch:** ${{ github.ref_name }}
          **Commit:** ${{ github.sha }}
          **Date:** $(date -u +"%Y-%m-%d %H:%M:%S UTC")

          ## Docker Security Scan Results

          - ✅ Container vulnerability scan completed
          - ✅ Docker Compose configuration validated
          - ✅ Security best practices verified

          ## Recommendations

          - Keep base images updated regularly
          - Use specific version tags instead of 'latest'
          - Implement regular security scanning in CI/CD
          - Monitor for new vulnerabilities

          EOF

      - name: 📝 Upload security report
        uses: actions/upload-artifact@v3
        if: always()
        with:
          name: security-report
          path: security-report.md
          retention-days: 30

  # ============================================================================
  # COMPREHENSIVE TESTING
  # ============================================================================
  test-frontend:
    name: 🎨 Frontend Tests
    runs-on: ubuntu-latest
    needs: [setup, docker-build]
    if: needs.setup.outputs.frontend-changed == 'true' || github.event.inputs.skip_tests != 'true'
    steps:
      - name: 📥 Checkout repository
        uses: actions/checkout@v4

      - name: 🔧 Set up Docker Buildx
        uses: docker/setup-buildx-action@v3

      - name: 🔐 Log in to Container Registry
        uses: docker/login-action@v3
        with:
          registry: ${{ env.REGISTRY }}
          username: ${{ github.actor }}
          password: ${{ secrets.GITHUB_TOKEN }}

      - name: 🚀 Start test environment
        run: |
          # Create test environment file
          cp .env.development .env.test

          # Start services for testing
          docker-compose -f docker-compose.yml --env-file .env.test up -d frontend

          # Wait for services to be ready
          timeout 300 bash -c 'until docker-compose -f docker-compose.yml exec -T frontend curl -f http://localhost:5500/health || docker-compose -f docker-compose.yml exec -T frontend curl -f http://localhost:5500; do sleep 5; done'

      - name: 🧪 Run frontend tests
        run: |
          # Run unit tests with coverage
          docker-compose -f docker-compose.yml exec -T frontend pnpm test:run --coverage

          # Run type checking
          docker-compose -f docker-compose.yml exec -T frontend pnpm type-check

          # Run linting
          docker-compose -f docker-compose.yml exec -T frontend pnpm lint

      - name: 📊 Check test coverage
        run: |
          # Extract coverage percentage
          COVERAGE=$(docker-compose -f docker-compose.yml exec -T frontend pnpm test:coverage --reporter=json | jq -r '.total.lines.pct')

          echo "Test coverage: $COVERAGE%"

          # Enforce 90% coverage threshold
          if (( $(echo "$COVERAGE < 90" | bc -l) )); then
            echo "❌ Test coverage ($COVERAGE%) is below required threshold (90%)"
            exit 1
          else
            echo "✅ Test coverage ($COVERAGE%) meets requirements"
          fi

      - name: 🎯 Bundle size check
        run: |
          # Build production bundle
          docker-compose -f docker-compose.yml exec -T frontend pnpm build

          # Check bundle size
          BUNDLE_SIZE=$(docker-compose -f docker-compose.yml exec -T frontend du -sh dist | cut -f1)
          echo "Bundle size: $BUNDLE_SIZE"

          # Convert to bytes for comparison (assuming MB)
          SIZE_BYTES=$(docker-compose -f docker-compose.yml exec -T frontend du -sb dist | cut -f1)
          MAX_SIZE=$((1024 * 1024))  # 1MB in bytes

          if [ "$SIZE_BYTES" -gt "$MAX_SIZE" ]; then
            echo "❌ Bundle size ($BUNDLE_SIZE) exceeds 1MB limit"
            exit 1
          else
            echo "✅ Bundle size ($BUNDLE_SIZE) is within limits"
          fi

      - name: ♿ WCAG 2.1 AA Compliance Check
        run: |
          # Install axe-core CLI
          docker-compose -f docker-compose.yml exec -T frontend npm install -g @axe-core/cli

          # Run accessibility tests
          docker-compose -f docker-compose.yml exec -T frontend axe http://localhost:5500 --exit

          echo "✅ WCAG 2.1 AA compliance verified"

      - name: 📊 Generate frontend test report
        if: always()
        run: |
          # Extract test results
          docker-compose -f docker-compose.yml exec -T frontend pnpm test:run --reporter=json > frontend-test-results.json

          # Create test report
          cat > frontend-test-report.md << 'EOF'
          # 🎨 Frontend Test Report

          **Repository:** ${{ github.repository }}
          **Branch:** ${{ github.ref_name }}
          **Commit:** ${{ github.sha }}

          ## Test Results
          - ✅ Unit tests passed
          - ✅ Type checking passed
          - ✅ Linting passed
          - ✅ Coverage threshold met (>90%)
          - ✅ Bundle size within limits (<1MB)
          - ✅ WCAG 2.1 AA compliance verified

          EOF

      - name: 🧹 Cleanup
        if: always()
        run: |
          docker-compose -f docker-compose.yml down -v

      - name: 📝 Upload test artifacts
        uses: actions/upload-artifact@v3
        if: always()
        with:
          name: frontend-test-results
          path: |
            frontend-test-results.json
            frontend-test-report.md
          retention-days: 30

  test-backend:
    name: 🔧 Backend Tests
    runs-on: ubuntu-latest
    needs: [setup, docker-build]
    if: needs.setup.outputs.backend-changed == 'true' || github.event.inputs.skip_tests != 'true'
    steps:
      - name: 📥 Checkout repository
        uses: actions/checkout@v4

      - name: 🔧 Set up Docker Buildx
        uses: docker/setup-buildx-action@v3

      - name: 🔐 Log in to Container Registry
        uses: docker/login-action@v3
        with:
          registry: ${{ env.REGISTRY }}
          username: ${{ github.actor }}
          password: ${{ secrets.GITHUB_TOKEN }}

      - name: 🚀 Start test environment
        run: |
          # Create test environment file
          cp .env.development .env.test

          # Start services for testing
          docker-compose -f docker-compose.yml --env-file .env.test up -d postgres dragonflydb backend

          # Wait for services to be ready
          timeout 300 bash -c 'until docker-compose -f docker-compose.yml exec -T backend curl -f http://localhost:3001/health; do sleep 5; done'

      - name: 🧪 Run backend tests
        run: |
          # Run tests with coverage
          docker-compose -f docker-compose.yml exec -T backend uv run pytest --cov=src --cov-report=json --cov-report=term-missing --cov-fail-under=90

          # Run type checking
          docker-compose -f docker-compose.yml exec -T backend uv run mypy src/

          # Run linting
          docker-compose -f docker-compose.yml exec -T backend uv run ruff check src/

      - name: 📊 Check test coverage
        run: |
          # Extract coverage from pytest output
          COVERAGE=$(docker-compose -f docker-compose.yml exec -T backend uv run pytest --cov=src --cov-report=json | jq -r '.totals.percent_covered')

          echo "Test coverage: $COVERAGE%"

          if (( $(echo "$COVERAGE < 90" | bc -l) )); then
            echo "❌ Test coverage ($COVERAGE%) is below required threshold (90%)"
            exit 1
          else
            echo "✅ Test coverage ($COVERAGE%) meets requirements"
          fi

      - name: 🔍 Database migration tests
        run: |
          # Test database migrations
          docker-compose -f docker-compose.yml exec -T backend uv run alembic upgrade head
          docker-compose -f docker-compose.yml exec -T backend uv run alembic downgrade -1
          docker-compose -f docker-compose.yml exec -T backend uv run alembic upgrade head

          echo "✅ Database migrations tested successfully"

      - name: 📊 Generate backend test report
        if: always()
        run: |
          # Create test report
          cat > backend-test-report.md << 'EOF'
          # 🔧 Backend Test Report

          **Repository:** ${{ github.repository }}
          **Branch:** ${{ github.ref_name }}
          **Commit:** ${{ github.sha }}

          ## Test Results
          - ✅ Unit tests passed
          - ✅ Integration tests passed
          - ✅ Type checking passed
          - ✅ Linting passed
          - ✅ Coverage threshold met (>90%)
          - ✅ Database migrations tested

          EOF

      - name: 🧹 Cleanup
        if: always()
        run: |
          docker-compose -f docker-compose.yml down -v

      - name: 📝 Upload test artifacts
        uses: actions/upload-artifact@v3
        if: always()
        with:
          name: backend-test-results
          path: backend-test-report.md
          retention-days: 30

  # ============================================================================
  # PERFORMANCE BENCHMARKING
  # ============================================================================
  performance-benchmark:
    name: 📊 Performance Benchmark
    runs-on: ubuntu-latest
    needs: [setup, docker-build]
    if: github.event_name == 'pull_request' || github.ref == 'refs/heads/develop'
    steps:
      - name: 📥 Checkout repository
        uses: actions/checkout@v4

      - name: 🔧 Set up Docker Buildx
        uses: docker/setup-buildx-action@v3

      - name: 🔐 Log in to Container Registry
        uses: docker/login-action@v3
        with:
          registry: ${{ env.REGISTRY }}
          username: ${{ github.actor }}
          password: ${{ secrets.GITHUB_TOKEN }}

      - name: 📊 Run performance benchmarks
        run: |
          # Function to measure startup time
          measure_startup() {
            local compose_file=$1
            local environment=$2

            echo "🚀 Measuring startup time for $environment environment..."

            # Stop any running containers
            docker-compose -f $compose_file down -v

            local start_time=$(date +%s.%N)
            docker-compose -f $compose_file up -d

            # Wait for all services to be healthy
            timeout 300 bash -c '
              while true; do
                if docker-compose -f '$compose_file' exec -T frontend curl -f http://localhost:5500 >/dev/null 2>&1 && \
                   docker-compose -f '$compose_file' exec -T backend curl -f http://localhost:3001/health >/dev/null 2>&1; then
                  break
                fi
                sleep 2
              done
            '

            local end_time=$(date +%s.%N)
            local duration=$(echo "$end_time - $start_time" | bc)

            echo "Startup time for $environment: ${duration}s"
            echo "${environment}_startup_time=${duration}" >> $GITHUB_ENV

            docker-compose -f $compose_file down -v
          }

          # Measure development environment
          measure_startup "docker-compose.yml" "development"

          # Measure production environment
          measure_startup "docker-compose.prod.yml" "production"

      - name: 🏗️ Build time benchmark
        run: |
          echo "🔨 Measuring build times..."

          # Clean build cache
          docker builder prune -f

          # Measure frontend build time
          start_time=$(date +%s.%N)
          docker-compose -f docker-compose.yml build frontend
          end_time=$(date +%s.%N)
          frontend_build_time=$(echo "$end_time - $start_time" | bc)
          echo "Frontend build time: ${frontend_build_time}s"
          echo "frontend_build_time=${frontend_build_time}" >> $GITHUB_ENV

          # Measure backend build time
          start_time=$(date +%s.%N)
          docker-compose -f docker-compose.yml build backend
          end_time=$(date +%s.%N)
          backend_build_time=$(echo "$end_time - $start_time" | bc)
          echo "Backend build time: ${backend_build_time}s"
          echo "backend_build_time=${backend_build_time}" >> $GITHUB_ENV

      - name: 📈 Resource usage benchmark
        run: |
          echo "📊 Measuring resource usage..."

          # Start services
          docker-compose -f docker-compose.yml up -d

          # Wait for services to stabilize
          sleep 30

          # Collect resource metrics
          docker stats --no-stream --format "table {{.Container}}\t{{.CPUPerc}}\t{{.MemUsage}}" > resource-usage.txt

          # Calculate total resource usage
          total_cpu=$(docker stats --no-stream --format "{{.CPUPerc}}" | grep -E "lonors-.*-dev" | sed 's/%//' | awk '{sum += $1} END {print sum}')
          total_memory=$(docker stats --no-stream --format "{{.MemUsage}}" | grep -E "lonors-.*-dev" | cut -d'/' -f1 | sed 's/MiB//' | awk '{sum += $1} END {print sum}')

          echo "Total CPU usage: ${total_cpu}%"
          echo "Total memory usage: ${total_memory}MiB"
          echo "total_cpu=${total_cpu}" >> $GITHUB_ENV
          echo "total_memory=${total_memory}" >> $GITHUB_ENV

          docker-compose -f docker-compose.yml down -v

      - name: 🚀 Response time benchmark
        run: |
          echo "⚡ Measuring response times..."

          # Start services
          docker-compose -f docker-compose.yml up -d

          # Wait for services to be ready
          timeout 300 bash -c '
            until docker-compose -f docker-compose.yml exec -T frontend curl -f http://localhost:5500 >/dev/null 2>&1 && \
                  docker-compose -f docker-compose.yml exec -T backend curl -f http://localhost:3001/health >/dev/null 2>&1; do
              sleep 5
            done
          '

          # Measure frontend response time
          frontend_response=$(docker-compose -f docker-compose.yml exec -T frontend curl -o /dev/null -s -w '%{time_total}' http://localhost:5500)
          echo "Frontend response time: ${frontend_response}s"
          echo "frontend_response_time=${frontend_response}" >> $GITHUB_ENV

          # Measure backend response time
          backend_response=$(docker-compose -f docker-compose.yml exec -T backend curl -o /dev/null -s -w '%{time_total}' http://localhost:3001/health)
          echo "Backend response time: ${backend_response}s"
          echo "backend_response_time=${backend_response}" >> $GITHUB_ENV

          docker-compose -f docker-compose.yml down -v

      - name: 📊 Generate performance report
        run: |
          cat > performance-report.md << EOF
          # 📊 Performance Benchmark Report

          **Repository:** ${{ github.repository }}
          **Branch:** ${{ github.ref_name }}
          **Commit:** ${{ github.sha }}
          **Date:** $(date -u +"%Y-%m-%d %H:%M:%S UTC")

          ## Startup Times
          - **Development Environment:** ${development_startup_time}s
          - **Production Environment:** ${production_startup_time}s

          ## Build Times
          - **Frontend Build:** ${frontend_build_time}s
          - **Backend Build:** ${backend_build_time}s

          ## Resource Usage
          - **Total CPU Usage:** ${total_cpu}%
          - **Total Memory Usage:** ${total_memory}MiB

          ## Response Times
          - **Frontend Response:** ${frontend_response_time}s
          - **Backend Response:** ${backend_response_time}s

          ## Performance Targets
          | Metric | Target | Actual | Status |
          |--------|--------|--------|---------|
          | Frontend Build | <120s | ${frontend_build_time}s | $([ $(echo "${frontend_build_time} < 120" | bc) -eq 1 ] && echo "✅ Pass" || echo "❌ Fail") |
          | Backend Build | <60s | ${backend_build_time}s | $([ $(echo "${backend_build_time} < 60" | bc) -eq 1 ] && echo "✅ Pass" || echo "❌ Fail") |
          | Memory Usage | <4GB | ${total_memory}MiB | $([ $(echo "${total_memory} < 4096" | bc) -eq 1 ] && echo "✅ Pass" || echo "❌ Fail") |
          | Frontend Response | <1s | ${frontend_response_time}s | $([ $(echo "${frontend_response_time} < 1" | bc) -eq 1 ] && echo "✅ Pass" || echo "❌ Fail") |
          | Backend Response | <0.2s | ${backend_response_time}s | $([ $(echo "${backend_response_time} < 0.2" | bc) -eq 1 ] && echo "✅ Pass" || echo "❌ Fail") |

          EOF

      - name: 📝 Upload performance report
        uses: actions/upload-artifact@v3
        with:
          name: performance-report
          path: performance-report.md
          retention-days: 30

      - name: 💬 Comment performance results on PR
        if: github.event_name == 'pull_request'
        uses: actions/github-script@v6
        with:
          script: |
            const fs = require('fs');
            const report = fs.readFileSync('performance-report.md', 'utf8');

            github.rest.issues.createComment({
              issue_number: context.issue.number,
              owner: context.repo.owner,
              repo: context.repo.repo,
              body: report
            });

  # ============================================================================
  # DEPLOYMENT PIPELINE
  # ============================================================================
  deploy-staging:
    name: 🚀 Deploy to Staging
    runs-on: ubuntu-latest
    needs: [setup, security-audit, test-frontend, test-backend, performance-benchmark]
    if: needs.setup.outputs.should-deploy == 'true' && needs.setup.outputs.environment == 'staging'
    environment:
      name: staging
      url: https://staging.lonors.com
    steps:
      - name: 📥 Checkout repository
        uses: actions/checkout@v4

      - name: 🔧 Set up Docker Buildx
        uses: docker/setup-buildx-action@v3

      - name: 🔐 Log in to Container Registry
        uses: docker/login-action@v3
        with:
          registry: ${{ env.REGISTRY }}
          username: ${{ github.actor }}
          password: ${{ secrets.GITHUB_TOKEN }}

      - name: 🏗️ Build production images
        run: |
          # Build multi-platform production images
          docker buildx build \
            --platform linux/amd64,linux/arm64 \
            --target production \
            --tag ${{ env.REGISTRY }}/${{ env.IMAGE_NAME }}/frontend:staging \
            --push \
            ./frontend

          docker buildx build \
            --platform linux/amd64,linux/arm64 \
            --target production \
            --tag ${{ env.REGISTRY }}/${{ env.IMAGE_NAME }}/backend:staging \
            --push \
            ./backend

      - name: 🔒 Setup staging secrets
        run: |
          # Create secrets directory
          mkdir -p secrets

          # Generate staging secrets (in production, these would come from secure storage)
          echo "${{ secrets.STAGING_POSTGRES_PASSWORD }}" > secrets/postgres_password.txt
          echo "${{ secrets.STAGING_REDIS_PASSWORD }}" > secrets/redis_password.txt
          echo "${{ secrets.STAGING_JWT_SECRET }}" > secrets/jwt_secret.txt

          # Set proper permissions
          chmod 600 secrets/*.txt

      - name: 🚀 Deploy to staging
        run: |
          # Create staging environment file
          cat > .env.staging << EOF
          COMPOSE_PROJECT_NAME=lonors-staging
          POSTGRES_DB=lonors_staging_db
          POSTGRES_USER=lonors_staging_user
          REDIS_HOST=dragonflydb
          REDIS_PORT=6379
          ENVIRONMENT=staging
          DEBUG=false
          LOG_LEVEL=info
          JWT_ALGORITHM=HS256
          JWT_EXPIRES_IN=24h
          BACKEND_HOST=0.0.0.0
          BACKEND_PORT=3001
          BACKEND_WORKERS=2
          CORS_ORIGINS=https://staging.lonors.com
          VITE_API_URL=https://api-staging.lonors.com
          VITE_APP_NAME=Lonors Staging
          VITE_ENVIRONMENT=staging
          EOF

          # Deploy using production compose file
          docker-compose -f docker-compose.prod.yml --env-file .env.staging up -d

      - name: 🏥 Health check deployment
        run: |
          echo "🔍 Waiting for services to be healthy..."

          # Wait for backend health check
          timeout 300 bash -c '
            until curl -f http://localhost:3001/health >/dev/null 2>&1; do
              echo "Waiting for backend..."
              sleep 10
            done
          '

          # Wait for frontend to be accessible
          timeout 300 bash -c '
            until curl -f http://localhost:5500 >/dev/null 2>&1; do
              echo "Waiting for frontend..."
              sleep 10
            done
          '

          echo "✅ All services are healthy"

      - name: 🧪 Run smoke tests
        run: |
          # Basic smoke tests for staging deployment
          echo "🧪 Running smoke tests..."

          # Test backend API endpoints
          curl -f http://localhost:3001/health || exit 1
          curl -f http://localhost:3001/api/v1/health || exit 1

          # Test frontend accessibility
          curl -f http://localhost:5500 || exit 1

          # Test database connectivity
          docker-compose -f docker-compose.prod.yml exec -T backend python -c "
          import asyncio
          import asyncpg
          async def test_db():
              conn = await asyncpg.connect('postgresql://lonors_staging_user:${{ secrets.STAGING_POSTGRES_PASSWORD }}@postgres:5432/lonors_staging_db')
              await conn.execute('SELECT 1')
              await conn.close()
              print('✅ Database connection successful')
          asyncio.run(test_db())
          "

          echo "✅ Smoke tests passed"

      - name: 📊 Post-deployment validation
        run: |
          # Collect deployment metrics
          echo "📊 Collecting deployment metrics..."

          # Check container status
          docker-compose -f docker-compose.prod.yml ps

          # Check resource usage
          docker stats --no-stream --format "table {{.Container}}\t{{.CPUPerc}}\t{{.MemUsage}}"

          # Check logs for errors
          docker-compose -f docker-compose.prod.yml logs --tail=50

          echo "✅ Deployment validation completed"

      - name: 🔔 Notify deployment status
        if: always()
        uses: actions/github-script@v6
        with:
          script: |
            const status = '${{ job.status }}' === 'success' ? '✅ Success' : '❌ Failed';
            const message = `
            ## 🚀 Staging Deployment ${status}

            **Environment:** Staging
            **Branch:** ${{ github.ref_name }}
            **Commit:** ${{ github.sha }}
            **URL:** https://staging.lonors.com

            ${status === '✅ Success' ?
              '- ✅ Health checks passed\n- ✅ Smoke tests completed\n- ✅ Services are running' :
              '- ❌ Deployment failed\n- 🔍 Check logs for details'
            }
            `;

            github.rest.repos.createCommitComment({
              owner: context.repo.owner,
              repo: context.repo.repo,
              commit_sha: context.sha,
              body: message
            });

  deploy-production:
    name: 🏭 Deploy to Production
    runs-on: ubuntu-latest
    needs: [setup, security-audit, test-frontend, test-backend, performance-benchmark]
    if: needs.setup.outputs.should-deploy == 'true' && needs.setup.outputs.environment == 'production'
    environment:
      name: production
      url: https://lonors.com
    steps:
      - name: 📥 Checkout repository
        uses: actions/checkout@v4

      - name: 🔧 Set up Docker Buildx
        uses: docker/setup-buildx-action@v3

      - name: 🔐 Log in to Container Registry
        uses: docker/login-action@v3
        with:
          registry: ${{ env.REGISTRY }}
          username: ${{ github.actor }}
          password: ${{ secrets.GITHUB_TOKEN }}

      - name: 🏗️ Build production images
        run: |
          # Build multi-platform production images
          docker buildx build \
            --platform linux/amd64,linux/arm64 \
            --target production \
            --tag ${{ env.REGISTRY }}/${{ env.IMAGE_NAME }}/frontend:latest \
            --tag ${{ env.REGISTRY }}/${{ env.IMAGE_NAME }}/frontend:${{ github.sha }} \
            --push \
            ./frontend

          docker buildx build \
            --platform linux/amd64,linux/arm64 \
            --target production \
            --tag ${{ env.REGISTRY }}/${{ env.IMAGE_NAME }}/backend:latest \
            --tag ${{ env.REGISTRY }}/${{ env.IMAGE_NAME }}/backend:${{ github.sha }} \
            --push \
            ./backend

      - name: 🔒 Setup production secrets
        run: |
          # Create secrets directory
          mkdir -p secrets

          # Setup production secrets from GitHub secrets
          echo "${{ secrets.PROD_POSTGRES_PASSWORD }}" > secrets/postgres_password.txt
          echo "${{ secrets.PROD_REDIS_PASSWORD }}" > secrets/redis_password.txt
          echo "${{ secrets.PROD_JWT_SECRET }}" > secrets/jwt_secret.txt
          echo "${{ secrets.PROD_SSL_CERT }}" > secrets/ssl_cert.pem
          echo "${{ secrets.PROD_SSL_KEY }}" > secrets/ssl_key.pem

          # Set proper permissions
          chmod 600 secrets/*

      - name: 🚀 Deploy to production
        run: |
          # Create production environment file
          cat > .env.production << EOF
          COMPOSE_PROJECT_NAME=lonors-prod
          POSTGRES_DB=lonors_prod_db
          POSTGRES_USER=lonors_prod_user
          REDIS_HOST=dragonflydb
          REDIS_PORT=6379
          ENVIRONMENT=production
          DEBUG=false
          LOG_LEVEL=info
          JWT_ALGORITHM=HS256
          JWT_EXPIRES_IN=24h
          BACKEND_HOST=0.0.0.0
          BACKEND_PORT=3001
          BACKEND_WORKERS=4
          CORS_ORIGINS=https://lonors.com,https://www.lonors.com
          VITE_API_URL=https://api.lonors.com
          VITE_APP_NAME=Lonors
          VITE_ENVIRONMENT=production
          EOF

          # Deploy using production compose file
          docker-compose -f docker-compose.prod.yml --env-file .env.production up -d

      - name: 🏥 Health check deployment
        run: |
          echo "🔍 Waiting for services to be healthy..."

          # Wait for backend health check
          timeout 600 bash -c '
            until curl -f http://localhost:3001/health >/dev/null 2>&1; do
              echo "Waiting for backend..."
              sleep 15
            done
          '

          # Wait for frontend to be accessible
          timeout 600 bash -c '
            until curl -f http://localhost:5500 >/dev/null 2>&1; do
              echo "Waiting for frontend..."
              sleep 15
            done
          '

          echo "✅ All services are healthy"

      - name: 🧪 Run production smoke tests
        run: |
          # Comprehensive smoke tests for production
          echo "🧪 Running production smoke tests..."

          # Test all critical endpoints
          curl -f http://localhost:3001/health || exit 1
          curl -f http://localhost:3001/api/v1/health || exit 1
          curl -f http://localhost:5500 || exit 1

          # Test database connectivity and migrations
          docker-compose -f docker-compose.prod.yml exec -T backend uv run alembic current

          # Test cache connectivity
          docker-compose -f docker-compose.prod.yml exec -T dragonflydb redis-cli ping

          echo "✅ Production smoke tests passed"

      - name: 📊 Production metrics collection
        run: |
          echo "📊 Collecting production metrics..."

          # Performance metrics
          docker stats --no-stream --format "table {{.Container}}\t{{.CPUPerc}}\t{{.MemUsage}}\t{{.NetIO}}"

          # Service status
          docker-compose -f docker-compose.prod.yml ps

          # Log health
          docker-compose -f docker-compose.prod.yml logs --tail=20 backend
          docker-compose -f docker-compose.prod.yml logs --tail=20 frontend

          echo "✅ Metrics collection completed"

      - name: 🔔 Notify production deployment
        if: always()
        uses: actions/github-script@v6
        with:
          script: |
            const status = '${{ job.status }}' === 'success' ? '✅ Success' : '❌ Failed';
            const message = `
            ## 🏭 Production Deployment ${status}

            **Environment:** Production
            **Branch:** ${{ github.ref_name }}
            **Commit:** ${{ github.sha }}
            **URL:** https://lonors.com

            ${status === '✅ Success' ?
              '- ✅ Health checks passed\n- ✅ Smoke tests completed\n- ✅ All services operational\n- ✅ SSL/TLS configured' :
              '- ❌ Production deployment failed\n- 🚨 Immediate attention required'
            }
            `;

            github.rest.repos.createCommitComment({
              owner: context.repo.owner,
              repo: context.repo.repo,
              commit_sha: context.sha,
              body: message
            });
