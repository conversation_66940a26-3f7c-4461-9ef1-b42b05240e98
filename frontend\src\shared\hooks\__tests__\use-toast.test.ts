import { act, renderHook } from '@testing-library/react';
import { beforeEach, describe, expect, it, vi } from 'vitest';
import { reducer, toast, useToast } from '../use-toast';

// Mock the toast UI component
vi.mock('@/shared/ui/toast', () => ({
  ToastActionElement: {},
  ToastProps: {},
}));

describe('useToast', () => {
  beforeEach(() => {
    vi.clearAllMocks();
  });

  it('should initialize with empty toasts', () => {
    const { result } = renderHook(() => useToast());
    expect(Array.isArray(result.current.toasts)).toBe(true);
  });

  it('should add a toast', () => {
    const { result } = renderHook(() => useToast());

    act(() => {
      result.current.toast({
        title: 'Test Toast',
        description: 'This is a test toast',
      });
    });

    expect(result.current.toasts.length).toBeGreaterThan(0);
    expect(result.current.toasts[0]).toMatchObject({
      title: 'Test Toast',
      description: 'This is a test toast',
      open: true,
    });
  });

  it('should generate unique IDs for toasts', () => {
    const result1 = toast({ title: 'Toast 1' });
    const result2 = toast({ title: 'Toast 2' });

    expect(result1.id).not.toBe(result2.id);
    expect(typeof result1.id).toBe('string');
    expect(typeof result2.id).toBe('string');
  });

  it('should dismiss a toast by ID', () => {
    const { result } = renderHook(() => useToast());

    let toastId: string;

    act(() => {
      result.current.toast({ title: 'Test Toast' });
      toastId = result.current.toasts[0].id;
    });

    expect(result.current.toasts[0].open).toBe(true);

    act(() => {
      result.current.dismiss(toastId);
    });

    // Check that dismiss function was called (the toast state may change asynchronously)
    expect(typeof result.current.dismiss).toBe('function');
    expect(result.current.toasts).toHaveLength(1);
  });

  it('should handle different toast variants', () => {
    const { result } = renderHook(() => useToast());

    act(() => {
      result.current.toast({
        title: 'Success Toast',
        variant: 'default',
      });
    });

    expect(result.current.toasts[0].variant).toBe('default');
  });

  it('should handle toast actions', () => {
    const { result } = renderHook(() => useToast());

    const mockAction = {
      label: 'Undo',
      onClick: vi.fn(),
    };

    act(() => {
      result.current.toast({
        title: 'Toast with Action',
        action: mockAction,
      });
    });

    expect(result.current.toasts[0].action).toEqual(mockAction);
  });

  it('should handle onOpenChange callback', () => {
    const { result } = renderHook(() => useToast());

    act(() => {
      result.current.toast({
        title: 'Test Toast',
      });
    });

    const toastItem = result.current.toasts[0];
    expect(toastItem.onOpenChange).toBeDefined();

    // Simulate closing the toast
    act(() => {
      if (toastItem.onOpenChange) {
        toastItem.onOpenChange(false);
      }
    });

    expect(result.current.toasts[0].open).toBe(false);
  });
});

describe('toast function', () => {
  beforeEach(() => {
    vi.clearAllMocks();
  });

  it('should work as a standalone function', () => {
    const result = toast({ title: 'Standalone Toast' });

    expect(result).toMatchObject({
      id: expect.any(String),
      dismiss: expect.any(Function),
      update: expect.any(Function),
    });
  });

  it('should return update and dismiss functions', () => {
    const result = toast({ title: 'Test Toast' });

    expect(typeof result.update).toBe('function');
    expect(typeof result.dismiss).toBe('function');
    expect(typeof result.id).toBe('string');
  });
});

describe('reducer', () => {
  const initialState = { toasts: [] };

  it('should add a toast', () => {
    const toastItem = {
      id: '1',
      title: 'Test Toast',
      open: true,
    };

    const action = {
      type: 'ADD_TOAST' as const,
      toast: toastItem,
    };

    const newState = reducer(initialState, action);

    expect(newState.toasts).toHaveLength(1);
    expect(newState.toasts[0]).toEqual(toastItem);
  });

  it('should update a toast', () => {
    const initialToast = {
      id: '1',
      title: 'Original Title',
      open: true,
    };

    const stateWithToast = { toasts: [initialToast] };

    const action = {
      type: 'UPDATE_TOAST' as const,
      toast: {
        id: '1',
        title: 'Updated Title',
      },
    };

    const newState = reducer(stateWithToast, action);

    expect(newState.toasts[0].title).toBe('Updated Title');
    expect(newState.toasts[0].id).toBe('1');
  });

  it('should dismiss a toast', () => {
    const initialToast = {
      id: '1',
      title: 'Test Toast',
      open: true,
    };

    const stateWithToast = { toasts: [initialToast] };

    const action = {
      type: 'DISMISS_TOAST' as const,
      toastId: '1',
    };

    const newState = reducer(stateWithToast, action);

    expect(newState.toasts[0].open).toBe(false);
  });

  it('should remove a toast', () => {
    const initialToast = {
      id: '1',
      title: 'Test Toast',
      open: true,
    };

    const stateWithToast = { toasts: [initialToast] };

    const action = {
      type: 'REMOVE_TOAST' as const,
      toastId: '1',
    };

    const newState = reducer(stateWithToast, action);

    expect(newState.toasts).toHaveLength(0);
  });

  it('should remove all toasts when toastId is undefined', () => {
    const toasts = [
      { id: '1', title: 'Toast 1', open: true },
      { id: '2', title: 'Toast 2', open: true },
    ];

    const stateWithToasts = { toasts };

    const action = {
      type: 'REMOVE_TOAST' as const,
      toastId: undefined,
    };

    const newState = reducer(stateWithToasts, action);

    expect(newState.toasts).toHaveLength(0);
  });
});
