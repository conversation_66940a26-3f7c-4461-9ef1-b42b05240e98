"""
Model Context Protocol (MCP) endpoints.

This module provides MCP API endpoints for model interactions
and context management.
"""

import uuid
from typing import List

from fastapi import APIRouter, Depends, HTTPException, Query, status

from src.application.use_cases.mcp_service import MCPService
from src.domain.entities.mcp import (
    MCPContextCreate,
    MCPContextResponse,
    MCPContextUpdate,
    MCPModelResponse,
    MCPRequest,
    MCPResponse,
)
from src.infrastructure.logging.setup import get_logger
from src.presentation.dependencies.auth import get_current_user

logger = get_logger(__name__)

router = APIRouter()


def get_mcp_service() -> MCPService:
    """Get MCP service dependency."""
    return MCPService()


@router.get("/models", response_model=List[MCPModelResponse])
async def list_models(
    current_user: dict = Depends(get_current_user),
    mcp_service: MCPService = Depends(get_mcp_service)
) -> List[MCPModelResponse]:
    """
    List available models.
    
    Args:
        current_user: Current authenticated user
        mcp_service: MCP service dependency
        
    Returns:
        List[MCPModelResponse]: List of available models
    """
    try:
        models = await mcp_service.list_models()
        
        logger.debug(f"Listed {len(models)} models for user: {current_user['user_id']}")
        return models
        
    except Exception as e:
        logger.error(f"List models error: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to list models"
        )


@router.get("/models/{model_id}", response_model=MCPModelResponse)
async def get_model(
    model_id: str,
    current_user: dict = Depends(get_current_user),
    mcp_service: MCPService = Depends(get_mcp_service)
) -> MCPModelResponse:
    """
    Get model by ID.
    
    Args:
        model_id: Model identifier
        current_user: Current authenticated user
        mcp_service: MCP service dependency
        
    Returns:
        MCPModelResponse: Model information
        
    Raises:
        HTTPException: If model not found
    """
    try:
        model = await mcp_service.get_model(model_id)
        
        if not model:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=f"Model not found: {model_id}"
            )
        
        logger.debug(f"Retrieved model: {model_id} for user: {current_user['user_id']}")
        return model
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Get model error: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to retrieve model"
        )


@router.post("/contexts", response_model=MCPContextResponse, status_code=status.HTTP_201_CREATED)
async def create_context(
    context_data: MCPContextCreate,
    current_user: dict = Depends(get_current_user),
    mcp_service: MCPService = Depends(get_mcp_service)
) -> MCPContextResponse:
    """
    Create new MCP context.
    
    Args:
        context_data: Context creation data
        current_user: Current authenticated user
        mcp_service: MCP service dependency
        
    Returns:
        MCPContextResponse: Created context information
        
    Raises:
        HTTPException: If creation fails
    """
    try:
        user_id = uuid.UUID(current_user["user_id"])
        context = await mcp_service.create_context(user_id, context_data)
        
        logger.info(f"Created MCP context: {context.id} for user: {user_id}")
        return context
        
    except ValueError as e:
        logger.warning(f"Context creation failed: {e}")
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=str(e)
        )
    except Exception as e:
        logger.error(f"Context creation error: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to create context"
        )


@router.get("/contexts", response_model=List[MCPContextResponse])
async def list_contexts(
    skip: int = Query(0, ge=0, description="Number of contexts to skip"),
    limit: int = Query(100, ge=1, le=1000, description="Maximum number of contexts to return"),
    current_user: dict = Depends(get_current_user),
    mcp_service: MCPService = Depends(get_mcp_service)
) -> List[MCPContextResponse]:
    """
    List user's MCP contexts.
    
    Args:
        skip: Number of contexts to skip
        limit: Maximum number of contexts to return
        current_user: Current authenticated user
        mcp_service: MCP service dependency
        
    Returns:
        List[MCPContextResponse]: List of user's contexts
    """
    try:
        user_id = uuid.UUID(current_user["user_id"])
        contexts = await mcp_service.list_user_contexts(user_id, skip, limit)
        
        logger.debug(f"Listed {len(contexts)} contexts for user: {user_id}")
        return contexts
        
    except Exception as e:
        logger.error(f"List contexts error: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to list contexts"
        )


@router.get("/contexts/{context_id}", response_model=MCPContextResponse)
async def get_context(
    context_id: uuid.UUID,
    current_user: dict = Depends(get_current_user),
    mcp_service: MCPService = Depends(get_mcp_service)
) -> MCPContextResponse:
    """
    Get MCP context by ID.
    
    Args:
        context_id: Context ID
        current_user: Current authenticated user
        mcp_service: MCP service dependency
        
    Returns:
        MCPContextResponse: Context information
        
    Raises:
        HTTPException: If context not found
    """
    try:
        user_id = uuid.UUID(current_user["user_id"])
        context = await mcp_service.get_context(context_id, user_id)
        
        if not context:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Context not found"
            )
        
        logger.debug(f"Retrieved context: {context_id} for user: {user_id}")
        return context
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Get context error: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to retrieve context"
        )


@router.put("/contexts/{context_id}", response_model=MCPContextResponse)
async def update_context(
    context_id: uuid.UUID,
    update_data: MCPContextUpdate,
    current_user: dict = Depends(get_current_user),
    mcp_service: MCPService = Depends(get_mcp_service)
) -> MCPContextResponse:
    """
    Update MCP context.
    
    Args:
        context_id: Context ID
        update_data: Update data
        current_user: Current authenticated user
        mcp_service: MCP service dependency
        
    Returns:
        MCPContextResponse: Updated context information
        
    Raises:
        HTTPException: If update fails
    """
    try:
        user_id = uuid.UUID(current_user["user_id"])
        context = await mcp_service.update_context(context_id, user_id, update_data)
        
        if not context:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Context not found"
            )
        
        logger.info(f"Updated context: {context_id} for user: {user_id}")
        return context
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Update context error: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to update context"
        )


@router.delete("/contexts/{context_id}")
async def delete_context(
    context_id: uuid.UUID,
    current_user: dict = Depends(get_current_user),
    mcp_service: MCPService = Depends(get_mcp_service)
) -> dict:
    """
    Delete MCP context.
    
    Args:
        context_id: Context ID
        current_user: Current authenticated user
        mcp_service: MCP service dependency
        
    Returns:
        dict: Success message
        
    Raises:
        HTTPException: If deletion fails
    """
    try:
        user_id = uuid.UUID(current_user["user_id"])
        deleted = await mcp_service.delete_context(context_id, user_id)
        
        if not deleted:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Context not found"
            )
        
        logger.info(f"Deleted context: {context_id} for user: {user_id}")
        return {"message": "Context deleted successfully"}
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Delete context error: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to delete context"
        )


@router.post("/generate", response_model=MCPResponse)
async def generate_response(
    request: MCPRequest,
    current_user: dict = Depends(get_current_user),
    mcp_service: MCPService = Depends(get_mcp_service)
) -> MCPResponse:
    """
    Generate response using MCP.
    
    Args:
        request: MCP request data
        current_user: Current authenticated user
        mcp_service: MCP service dependency
        
    Returns:
        MCPResponse: Generated response
        
    Raises:
        HTTPException: If generation fails
    """
    try:
        user_id = uuid.UUID(current_user["user_id"])
        response = await mcp_service.process_request(user_id, request)
        
        logger.info(f"Generated MCP response for user: {user_id}, model: {request.model_id}")
        return response
        
    except ValueError as e:
        logger.warning(f"MCP generation failed: {e}")
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=str(e)
        )
    except Exception as e:
        logger.error(f"MCP generation error: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to generate response"
        )
