"""
Domain exceptions for the <PERSON>nors application.

This module contains all domain-specific exceptions following Clean Architecture
principles. These exceptions represent business rule violations and domain errors.
"""

from typing import Any, Dict, List, Optional


class DomainError(Exception):
    """Base exception for all domain errors."""

    def __init__(
        self,
        message: str,
        details: Optional[Dict[str, Any]] = None,
        error_code: Optional[str] = None,
    ) -> None:
        """
        Initialize domain error.

        Args:
            message: Error message
            details: Additional error details
            error_code: Specific error code
        """
        super().__init__(message)
        self.message = message
        self.details = details or {}
        self.error_code = error_code or self.__class__.__name__.upper()

    def to_dict(self) -> Dict[str, Any]:
        """Convert error to dictionary representation."""
        return {
            "type": self.__class__.__name__,
            "message": self.message,
            "details": self.details,
            "error_code": self.error_code,
        }


class DomainValidationError(DomainError):
    """Exception raised when domain validation fails."""

    def __init__(
        self,
        message: str,
        field: Optional[str] = None,
        value: Optional[Any] = None,
        violations: Optional[List[Dict[str, str]]] = None,
        **kwargs: Any,
    ) -> None:
        """
        Initialize validation error.

        Args:
            message: Error message
            field: Field that failed validation
            value: Value that failed validation
            violations: List of validation violations
            **kwargs: Additional arguments for parent class
        """
        super().__init__(message, error_code="VALIDATION_ERROR", **kwargs)
        self.field = field
        self.value = value
        self.violations = violations or []


class EntityNotFoundError(DomainError):
    """Exception raised when an entity is not found."""

    def __init__(
        self,
        entity_type: str,
        entity_id: Optional[str] = None,
        criteria: Optional[Dict[str, Any]] = None,
        **kwargs: Any,
    ) -> None:
        """
        Initialize entity not found error.

        Args:
            entity_type: Type of entity that was not found
            entity_id: ID of entity that was not found
            criteria: Search criteria used
            **kwargs: Additional arguments for parent class
        """
        if entity_id:
            message = f"{entity_type} with id '{entity_id}' not found"
        else:
            message = f"{entity_type} not found"

        super().__init__(message, error_code="ENTITY_NOT_FOUND", **kwargs)
        self.entity_type = entity_type
        self.entity_id = entity_id
        self.criteria = criteria


class DuplicateEntityError(DomainError):
    """Exception raised when attempting to create a duplicate entity."""

    def __init__(
        self,
        entity_type: str,
        field: Optional[str] = None,
        value: Optional[Any] = None,
        conflicts: Optional[List[Dict[str, Any]]] = None,
        **kwargs: Any,
    ) -> None:
        """
        Initialize duplicate entity error.

        Args:
            entity_type: Type of entity that is duplicated
            field: Field that has duplicate value
            value: Duplicate value
            conflicts: List of conflicting fields
            **kwargs: Additional arguments for parent class
        """
        if field and value:
            message = f"{entity_type} with {field} '{value}' already exists"
        else:
            message = f"{entity_type} already exists"

        super().__init__(message, error_code="DUPLICATE_ENTITY", **kwargs)
        self.entity_type = entity_type
        self.field = field
        self.value = value
        self.conflicts = conflicts


class BusinessRuleViolationError(DomainError):
    """Exception raised when a business rule is violated."""

    def __init__(
        self,
        rule_name: str,
        message: str,
        context: Optional[Dict[str, Any]] = None,
        **kwargs: Any,
    ) -> None:
        """
        Initialize business rule violation error.

        Args:
            rule_name: Name of the violated business rule
            message: Error message
            context: Additional context about the violation
            **kwargs: Additional arguments for parent class
        """
        super().__init__(message, error_code="BUSINESS_RULE_VIOLATION", **kwargs)
        self.rule_name = rule_name
        self.context = context


class UnauthorizedOperationError(DomainError):
    """Exception raised when an unauthorized operation is attempted."""

    def __init__(
        self,
        operation: str,
        user_id: str,
        resource_type: Optional[str] = None,
        resource_id: Optional[str] = None,
        **kwargs: Any,
    ) -> None:
        """
        Initialize unauthorized operation error.

        Args:
            operation: Operation that was attempted
            user_id: ID of user attempting the operation
            resource_type: Type of resource being accessed
            resource_id: ID of resource being accessed
            **kwargs: Additional arguments for parent class
        """
        message = f"User '{user_id}' is not authorized to perform operation '{operation}'"
        super().__init__(message, error_code="UNAUTHORIZED_OPERATION", **kwargs)
        self.operation = operation
        self.user_id = user_id
        self.resource_type = resource_type
        self.resource_id = resource_id


class InvalidOperationError(DomainError):
    """Exception raised when an invalid operation is attempted."""

    def __init__(
        self,
        operation: str,
        reason: str,
        current_state: Optional[Dict[str, Any]] = None,
        **kwargs: Any,
    ) -> None:
        """
        Initialize invalid operation error.

        Args:
            operation: Operation that was attempted
            reason: Reason why the operation is invalid
            current_state: Current state that prevents the operation
            **kwargs: Additional arguments for parent class
        """
        message = f"Invalid operation '{operation}': {reason}"
        super().__init__(message, error_code="INVALID_OPERATION", **kwargs)
        self.operation = operation
        self.reason = reason
        self.current_state = current_state
