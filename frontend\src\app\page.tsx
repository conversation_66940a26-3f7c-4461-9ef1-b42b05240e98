'use client';

import { API_ENDPOINTS, apiClient } from '@/shared/lib/api';
import { Badge } from '@/shared/ui/badge';
import { <PERSON><PERSON> } from '@/shared/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/shared/ui/card';
import { ThemeToggle } from '@/shared/ui/theme-toggle';
import {
    AlertCircle,
    Bot,
    Brain,
    CheckCircle,
    Loader2,
    Workflow,
    Zap
} from 'lucide-react';
import { useEffect, useState } from 'react';

interface HealthStatus {
  status: string;
  version: string;
  environment: string;
  timestamp: string;
}

export default function HomePage() {
  const [healthStatus, setHealthStatus] = useState<HealthStatus | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    checkBackendHealth();
  }, []);

  const checkBackendHealth = async () => {
    try {
      setIsLoading(true);
      setError(null);
      const response = await apiClient.get<HealthStatus>(API_ENDPOINTS.HEALTH);
      setHealthStatus(response.data);
    } catch (err) {
      setError('Failed to connect to backend API');
      console.error('Health check failed:', err);
    } finally {
      setIsLoading(false);
    }
  };

  const features = [
    {
      icon: Bot,
      title: 'AI Agent Orchestration',
      description: 'Create and manage intelligent agents with CopilotKit and AG2/LangGraph integration.',
      status: 'coming-soon',
    },
    {
      icon: Workflow,
      title: 'Visual Flow Builder',
      description: 'Drag-and-drop interface for building complex agent workflows without code.',
      status: 'available',
      href: '/workflow-builder',
    },
    {
      icon: Brain,
      title: 'Knowledge Graph',
      description: 'Interactive visualization and management of knowledge relationships with Graphiti.',
      status: 'coming-soon',
    },
    {
      icon: Zap,
      title: 'Local Model Management',
      description: 'Download, serve, and manage AI models locally with Ollama and HuggingFace integration.',
      status: 'coming-soon',
    },
  ];

  return (
    <div className="flex flex-col min-h-screen">
      {/* Header */}
      <header className="border-b bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60">
        <div className="container flex h-16 items-center justify-between">
          <div className="flex items-center space-x-2">
            <Bot className="h-8 w-8 text-primary" />
            <h1 className="text-2xl font-bold lonors-gradient-text">Lonors</h1>
          </div>

          {/* Backend Status and Theme Toggle */}
          <div className="flex items-center space-x-4">
            <ThemeToggle />
            <div className="flex items-center space-x-2">
            {isLoading ? (
              <div className="flex items-center space-x-2">
                <Loader2 className="h-4 w-4 animate-spin" />
                <span className="text-sm text-muted-foreground">Checking backend...</span>
              </div>
            ) : error ? (
              <div className="flex items-center space-x-2">
                <AlertCircle className="h-4 w-4 text-destructive" />
                <Badge variant="destructive">Backend Offline</Badge>
              </div>
            ) : healthStatus ? (
              <div className="flex items-center space-x-2">
                <CheckCircle className="h-4 w-4 text-green-500" />
                <Badge variant="secondary">Backend Online</Badge>
              </div>
            ) : null}
            </div>
          </div>
        </div>
      </header>

      {/* Main Content */}
      <main className="flex-1">
        {/* Hero Section */}
        <section className="py-20 px-4">
          <div className="container mx-auto text-center">
            <h2 className="text-4xl md:text-6xl font-bold mb-6">
              AI Agent Platform for{' '}
              <span className="lonors-gradient-text">Everyone</span>
            </h2>
            <p className="text-xl text-muted-foreground mb-8 max-w-3xl mx-auto">
              Build, deploy, and manage AI agents with our intuitive no-code platform.
              Create complex workflows, visualize knowledge graphs, and orchestrate
              intelligent automation without writing a single line of code.
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <Link href="/workflow-builder">
                <Button size="lg" className="lonors-gradient">
                  Try Workflow Builder
                  <Workflow className="ml-2 h-4 w-4" />
                </Button>
              </Link>
              <Button size="lg" variant="outline">
                View Documentation
              </Button>
            </div>

            {/* Theme Test Card */}
            <div className="mt-8 max-w-md mx-auto">
              <Card className="p-6 border-2 border-dashed border-primary/20">
                <div className="text-center space-y-3">
                  <p className="text-lg font-semibold text-foreground">
                    🎨 Theme Test Card - Hot Reload Working! ✅ (Updated)
                  </p>
                  <div className="p-3 rounded-lg bg-muted">
                    <p className="text-sm text-muted-foreground">
                      Background: <span className="font-mono">bg-muted</span>
                    </p>
                  </div>
                  <div className="p-3 rounded-lg bg-card border">
                    <p className="text-sm text-card-foreground">
                      Card: <span className="font-mono">bg-card</span>
                    </p>
                  </div>
                  <p className="text-xs text-muted-foreground">
                    Click the theme toggle in the header to see colors change
                  </p>
                  <div className="flex gap-2 mt-3">
                    <Button
                      onClick={async () => {
                        try {
                          const response = await fetch('http://localhost:3003/health');
                          const data = await response.json();
                          alert(`Backend Status: ${data.status} - Service: ${data.service}`);
                        } catch (error) {
                          alert(`Backend Error: ${error}`);
                        }
                      }}
                      size="sm"
                    >
                      Test API
                    </Button>
                    <Button
                      onClick={() => {
                        try {
                          const ws = new WebSocket('ws://localhost:3003/ws/mcp');
                          ws.onopen = () => alert('WebSocket MCP Connected!');
                          ws.onmessage = (event) => {
                            const data = JSON.parse(event.data);
                            alert(`WebSocket Message: ${data.type} - ${data.protocol}`);
                            ws.close();
                          };
                          ws.onerror = (error) => alert(`WebSocket Error: ${error}`);
                        } catch (error) {
                          alert(`WebSocket Error: ${error}`);
                        }
                      }}
                      size="sm"
                      variant="outline"
                    >
                      Test WebSocket
                    </Button>
                  </div>
                </div>
              </Card>
            </div>
          </div>
        </section>

        {/* Features Section */}
        <section className="py-20 px-4 bg-muted/50">
          <div className="container mx-auto">
            <div className="text-center mb-12">
              <h3 className="text-3xl font-bold mb-4">Platform Features</h3>
              <p className="text-lg text-muted-foreground max-w-2xl mx-auto">
                Everything you need to build and deploy AI agents at scale
              </p>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
              {features.map((feature, index) => {
                const CardComponent = (
                  <Card key={index} className="relative overflow-hidden group hover:shadow-lg transition-shadow">
                    <CardHeader>
                      <div className="flex items-center justify-between mb-2">
                        <feature.icon className="h-8 w-8 text-primary" />
                        <Badge variant="secondary" className="text-xs">
                          {feature.status === 'coming-soon' ? 'Coming Soon' : 'Available'}
                        </Badge>
                      </div>
                      <CardTitle className="text-lg">{feature.title}</CardTitle>
                    </CardHeader>
                    <CardContent>
                      <CardDescription>{feature.description}</CardDescription>
                      {feature.status === 'available' && (
                        <Button size="sm" className="mt-3">
                          Try Now <ArrowRight className="ml-1 h-3 w-3" />
                        </Button>
                      )}
                    </CardContent>

                    {/* Hover effect */}
                    <div className="absolute inset-0 bg-gradient-to-r from-primary/5 to-primary/10 opacity-0 group-hover:opacity-100 transition-opacity" />
                  </Card>
                );

                return feature.href ? (
                  <Link key={index} href={feature.href}>
                    {CardComponent}
                  </Link>
                ) : CardComponent;
              })}
            </div>
          </div>
        </section>

        {/* Status Section */}
        <section className="py-20 px-4">
          <div className="container mx-auto">
            <div className="text-center mb-12">
              <h3 className="text-3xl font-bold mb-4">System Status</h3>
              <p className="text-lg text-muted-foreground">
                Real-time status of platform components
              </p>
            </div>

            <div className="max-w-2xl mx-auto">
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center justify-between">
                    Backend API
                    {isLoading ? (
                      <Loader2 className="h-5 w-5 animate-spin" />
                    ) : error ? (
                      <Badge variant="destructive">Offline</Badge>
                    ) : (
                      <Badge className="bg-green-500">Online</Badge>
                    )}
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  {error ? (
                    <div className="text-destructive">
                      <p>{error}</p>
                      <Button
                        variant="outline"
                        size="sm"
                        className="mt-2"
                        onClick={checkBackendHealth}
                      >
                        Retry Connection
                      </Button>
                    </div>
                  ) : healthStatus ? (
                    <div className="space-y-2">
                      <div className="flex justify-between">
                        <span className="text-muted-foreground">Version:</span>
                        <span>{healthStatus.version}</span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-muted-foreground">Environment:</span>
                        <span className="capitalize">{healthStatus.environment}</span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-muted-foreground">Last Check:</span>
                        <span>{new Date(healthStatus.timestamp).toLocaleTimeString()}</span>
                      </div>
                    </div>
                  ) : (
                    <p className="text-muted-foreground">Checking backend status...</p>
                  )}
                </CardContent>
              </Card>
            </div>
          </div>
        </section>
      </main>

      {/* Footer */}
      <footer className="border-t py-8 px-4">
        <div className="container mx-auto text-center text-muted-foreground">
          <p>&copy; 2024 Lonors AI Agent Platform. Built with Next.js, TypeScript, and Tailwind CSS.</p>
        </div>
      </footer>
    </div>
  );
}
