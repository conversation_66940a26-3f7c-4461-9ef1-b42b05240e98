import { cn } from '@/shared/lib/utils';
import '@/shared/styles/globals.css';
import { ErrorBoundary } from '@/shared/ui/error-boundary';
import type { Metadata } from 'next';
import { Inter, JetBrains_Mono } from 'next/font/google';
import { Providers } from './providers';

// Font configurations
const fontSans = Inter({
  subsets: ['latin'],
  variable: '--font-sans',
  display: 'swap',
});

const fontMono = JetBrains_Mono({
  subsets: ['latin'],
  variable: '--font-mono',
  display: 'swap',
});

// Metadata configuration
export const metadata: Metadata = {
  title: {
    default: 'Lonors - AI Agent Platform',
    template: '%s | Lonors',
  },
  description: 'Lonors AI Agent Platform - Build, deploy, and manage AI agents with drag-and-drop workflows, knowledge graphs, and local model management.',
  keywords: [
    'AI',
    'agents',
    'automation',
    'workflow',
    'knowledge graph',
    'machine learning',
    'no-code',
    'platform',
  ],
  authors: [
    {
      name: 'simyropandos',
      url: 'https://github.com/simyropandos',
    },
  ],
  creator: 'simy<PERSON>and<PERSON>',
  publisher: '<PERSON>nor<PERSON>',
  formatDetection: {
    email: false,
    address: false,
    telephone: false,
  },
  metadataBase: new URL(process.env.NEXT_PUBLIC_APP_URL || 'http://localhost:5500'),
  openGraph: {
    type: 'website',
    locale: 'en_US',
    url: process.env.NEXT_PUBLIC_APP_URL || 'http://localhost:5500',
    title: 'Lonors - AI Agent Platform',
    description: 'Build, deploy, and manage AI agents with drag-and-drop workflows, knowledge graphs, and local model management.',
    siteName: 'Lonors',
    images: [
      {
        url: '/og-image.png',
        width: 1200,
        height: 630,
        alt: 'Lonors AI Agent Platform',
      },
    ],
  },
  twitter: {
    card: 'summary_large_image',
    title: 'Lonors - AI Agent Platform',
    description: 'Build, deploy, and manage AI agents with drag-and-drop workflows, knowledge graphs, and local model management.',
    images: ['/og-image.png'],
    creator: '@lonors',
  },
  robots: {
    index: true,
    follow: true,
    googleBot: {
      index: true,
      follow: true,
      'max-video-preview': -1,
      'max-image-preview': 'large',
      'max-snippet': -1,
    },
  },
  icons: {
    icon: '/favicon.ico',
    shortcut: '/favicon-16x16.png',
    apple: '/apple-touch-icon.png',
  },
  manifest: '/site.webmanifest',
};

// Viewport configuration
export const viewport = {
  width: 'device-width',
  initialScale: 1,
  maximumScale: 5,
  userScalable: true,
  themeColor: [
    { media: '(prefers-color-scheme: light)', color: '#ffffff' },
    { media: '(prefers-color-scheme: dark)', color: '#0a0a0a' },
  ],
};

interface RootLayoutProps {
  children: React.ReactNode;
}

export default function RootLayout({ children }: RootLayoutProps) {
  return (
    <html lang="en" suppressHydrationWarning>
      <head>
        {/* Preload critical resources */}
        <link rel="preconnect" href="https://fonts.googleapis.com" />
        <link rel="preconnect" href="https://fonts.gstatic.com" crossOrigin="anonymous" />

        {/* Security headers */}
        <meta httpEquiv="X-Content-Type-Options" content="nosniff" />
        <meta httpEquiv="X-Frame-Options" content="DENY" />
        <meta httpEquiv="X-XSS-Protection" content="1; mode=block" />
        <meta name="referrer" content="strict-origin-when-cross-origin" />

        {/* Performance hints */}
        <link rel="dns-prefetch" href={process.env.NEXT_PUBLIC_API_URL || 'http://localhost:3001'} />
      </head>
      <body
        className={cn(
          'min-h-screen bg-background font-sans antialiased',
          fontSans.variable,
          fontMono.variable
        )}
        suppressHydrationWarning
      >
        {/* Skip to main content for accessibility */}
        <a
          href="#main-content"
          className="sr-only focus:not-sr-only focus:absolute focus:top-4 focus:left-4 z-50 bg-primary text-primary-foreground px-4 py-2 rounded-md focus-ring"
        >
          Skip to main content
        </a>

        {/* Main application content */}
        <ErrorBoundary>
          <Providers>
            <div id="main-content" className="relative flex min-h-screen flex-col">
              {children}
            </div>
          </Providers>
        </ErrorBoundary>

        {/* Portal root for modals and overlays */}
        <div id="portal-root" />

        {/* Development tools */}
        {process.env.NODE_ENV === 'development' && (
          <div className="fixed bottom-4 right-4 z-50 no-print">
            <div className="bg-muted text-muted-foreground px-2 py-1 rounded text-xs font-mono">
              {process.env.NODE_ENV}
            </div>
          </div>
        )}
      </body>
    </html>
  );
}
