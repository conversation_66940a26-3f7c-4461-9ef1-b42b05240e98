import { useState, useEffect } from 'react';
import { apiClient, API_ENDPOINTS } from '@/shared/lib/api';
import { AgentModel, createAgent } from './model';
import { Agent } from '@/shared/types';

export function useAgent(id: string) {
  const [agent, setAgent] = useState<AgentModel | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const fetchAgent = async () => {
      try {
        setLoading(true);
        setError(null);
        const response = await apiClient.get<Agent>(API_ENDPOINTS.AGENTS.BY_ID(id));
        setAgent(createAgent(response.data));
      } catch (err) {
        setError(err instanceof Error ? err.message : 'Failed to fetch agent');
      } finally {
        setLoading(false);
      }
    };

    if (id) {
      fetchAgent();
    }
  }, [id]);

  return { agent, loading, error };
}

export function useAgents() {
  const [agents, setAgents] = useState<AgentModel[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const fetchAgents = async () => {
      try {
        setLoading(true);
        setError(null);
        const response = await apiClient.get<Agent[]>(API_ENDPOINTS.AGENTS.BASE);
        setAgents(response.data.map(createAgent));
      } catch (err) {
        setError(err instanceof Error ? err.message : 'Failed to fetch agents');
      } finally {
        setLoading(false);
      }
    };

    fetchAgents();
  }, []);

  const refetch = async () => {
    try {
      setError(null);
      const response = await apiClient.get<Agent[]>(API_ENDPOINTS.AGENTS.BASE);
      setAgents(response.data.map(createAgent));
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to fetch agents');
    }
  };

  return { agents, loading, error, refetch };
}
