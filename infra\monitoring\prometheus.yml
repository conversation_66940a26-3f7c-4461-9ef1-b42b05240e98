# Prometheus configuration for Lonors Docker environment
global:
  scrape_interval: 15s
  evaluation_interval: 15s
  external_labels:
    monitor: 'lonors-monitor'
    environment: 'development'

# Alertmanager configuration
alerting:
  alertmanagers:
    - static_configs:
        - targets:
          # - alertmanager:9093

# Load rules once and periodically evaluate them according to the global 'evaluation_interval'.
rule_files:
  # - "first_rules.yml"
  # - "second_rules.yml"

# A scrape configuration containing exactly one endpoint to scrape:
scrape_configs:
  # Prometheus itself
  - job_name: 'prometheus'
    static_configs:
      - targets: ['localhost:9090']

  # Lonors Backend API
  - job_name: 'lonors-backend'
    static_configs:
      - targets: ['backend:3001']
    metrics_path: '/metrics'
    scrape_interval: 30s
    scrape_timeout: 10s

  # Lonors Frontend (if metrics endpoint is available)
  - job_name: 'lonors-frontend'
    static_configs:
      - targets: ['frontend:5500']
    metrics_path: '/metrics'
    scrape_interval: 30s
    scrape_timeout: 10s

  # PostgreSQL Exporter (if enabled)
  - job_name: 'postgres'
    static_configs:
      - targets: ['postgres:5432']
    scrape_interval: 30s
    scrape_timeout: 10s

  # DragonflyDB/Redis Exporter (if enabled)
  - job_name: 'dragonfly'
    static_configs:
      - targets: ['dragonflydb:6379']
    scrape_interval: 30s
    scrape_timeout: 10s

  # Docker daemon metrics (if enabled)
  - job_name: 'docker'
    static_configs:
      - targets: ['host.docker.internal:9323']
    scrape_interval: 30s
    scrape_timeout: 10s

  # Node Exporter (if enabled)
  - job_name: 'node'
    static_configs:
      - targets: ['node-exporter:9100']
    scrape_interval: 30s
    scrape_timeout: 10s

  # cAdvisor for container metrics (if enabled)
  - job_name: 'cadvisor'
    static_configs:
      - targets: ['cadvisor:8080']
    scrape_interval: 30s
    scrape_timeout: 10s
