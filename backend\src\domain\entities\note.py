"""
Note Entity Module

This module defines the Note entity and related value objects for the domain layer.
"""

from datetime import datetime
from enum import Enum
from uuid import UUID, uuid4

from pydantic import BaseModel, Field


class NoteFormat(str, Enum):
    """Format of the note content."""

    MARKDOWN = "markdown"
    RICHTEXT = "richtext"


class NoteContent(BaseModel):
    """Value object representing the content of a note."""

    content: str
    format: NoteFormat = NoteFormat.MARKDOWN
    version: int = 1


class Note(BaseModel):
    """Note entity representing a user's note in the system."""

    id: UUID = Field(default_factory=uuid4)
    title: str
    content: NoteContent
    folder_id: UUID | None = None
    tags: list[str] = Field(default_factory=list)
    is_archived: bool = False
    is_starred: bool = False
    created_at: datetime = Field(default_factory=datetime.now)
    updated_at: datetime = Field(default_factory=datetime.now)
    created_by: UUID
    last_edited_by: UUID

    def archive(self) -> None:
        """Archive the note."""
        self.is_archived = True
        self.updated_at = datetime.now()

    def unarchive(self) -> None:
        """Unarchive the note."""
        self.is_archived = False
        self.updated_at = datetime.now()

    def star(self) -> None:
        """Star the note."""
        self.is_starred = True
        self.updated_at = datetime.now()

    def unstar(self) -> None:
        """Unstar the note."""
        self.is_starred = False
        self.updated_at = datetime.now()

    def update_content(self, content: str, format: NoteFormat | None = None) -> None:
        """
        Update the note content.

        Args:
            content: The new content
            format: The format of the content (if changing)
        """
        new_format = format if format is not None else self.content.format
        self.content = NoteContent(
            content=content,
            format=new_format,
            version=self.content.version + 1,
        )
        self.updated_at = datetime.now()

    def update_title(self, title: str) -> None:
        """
        Update the note title.

        Args:
            title: The new title
        """
        self.title = title
        self.updated_at = datetime.now()

    def update_folder(self, folder_id: UUID | None) -> None:
        """
        Move the note to a different folder.

        Args:
            folder_id: The ID of the new folder, or None for root
        """
        self.folder_id = folder_id
        self.updated_at = datetime.now()

    def update_tags(self, tags: list[str]) -> None:
        """
        Update the note tags.

        Args:
            tags: The new list of tags
        """
        self.tags = tags
        self.updated_at = datetime.now()

    def update_editor(self, editor_id: UUID) -> None:
        """
        Update the last editor of the note.

        Args:
            editor_id: The ID of the user who edited the note
        """
        self.last_edited_by = editor_id
        self.updated_at = datetime.now()
