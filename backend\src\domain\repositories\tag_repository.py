"""
Tag Repository Interface

This module defines the interface for the tag repository.
"""

from abc import ABC, abstractmethod
from uuid import UUID

from src.domain.entities.tag import Tag


class TagRepository(ABC):
    """Interface for tag repository operations."""

    @abstractmethod
    async def get_by_id(self, tag_id: UUID) -> Tag | None:
        """
        Get a tag by its ID.

        Args:
            tag_id: The ID of the tag to retrieve

        Returns:
            The tag if found, None otherwise
        """
        pass

    @abstractmethod
    async def get_by_name(self, user_id: UUID, name: str) -> Tag | None:
        """
        Get a tag by its name for a specific user.

        Args:
            user_id: The ID of the user
            name: The name of the tag

        Returns:
            The tag if found, None otherwise
        """
        pass

    @abstractmethod
    async def get_all_by_user(self, user_id: UUID) -> list[Tag]:
        """
        Get all tags for a user.

        Args:
            user_id: The ID of the user

        Returns:
            List of tags for the user
        """
        pass

    @abstractmethod
    async def create(self, tag: Tag) -> Tag:
        """
        Create a new tag.

        Args:
            tag: The tag to create

        Returns:
            The created tag with any generated fields
        """
        pass

    @abstractmethod
    async def update(self, tag: Tag) -> Tag:
        """
        Update an existing tag.

        Args:
            tag: The tag to update

        Returns:
            The updated tag
        """
        pass

    @abstractmethod
    async def delete(self, tag_id: UUID) -> bool:
        """
        Delete a tag by its ID.

        Args:
            tag_id: The ID of the tag to delete

        Returns:
            True if the tag was deleted, False otherwise
        """
        pass
