'use client'; // Mark as a Client Component

import { useAnime, UseAnimeConfig } from '@/shared/lib/hooks/useAnime'; // Adjust path as necessary
import { cn } from '@/shared/lib/utils';
import React, { useMemo } from 'react';

interface AnimatedBoxProps extends React.HTMLAttributes<HTMLDivElement> {
  animationConfig?: UseAnimeConfig;
  animationPreset?: keyof typeof import('@/shared/lib/animations').ANIMATION_PRESETS; // For preset names
  animationDeps?: React.DependencyList;
}

const AnimatedBox: React.FC<AnimatedBoxProps> = ({
  children,
  className,
  animationConfig,
  animationPreset,
  animationDeps = [], // Default to empty array for deps
  ...props
}) => {
  // Memoize the config to prevent re-running the effect unnecessarily
  // if the config object is redefined on each render.
  const memoizedAnimationConfig = useMemo(() => {
    if (animationPreset) {
      return { preset: animationPreset, ...animationConfig };
    }
    return animationConfig || { opacity: [0, 1], translateY: [20, 0] }; // Default animation if none provided
  }, [animationConfig, animationPreset]);

  const boxRef = useAnime<HTMLDivElement>(memoizedAnimationConfig, animationDeps);

  return (
    <div
      ref={boxRef}
      className={cn('opacity-0', className)} // Start with opacity-0, animation will make it visible
      {...props}
    >
      {children}
    </div>
  );
};

AnimatedBox.displayName = 'AnimatedBox';

export { AnimatedBox };

// Example Usage (can be in another component or a storybook story):
/*
import { AnimatedBox } from './animated-box';
import { Button } from './button'; // Assuming Button is in the same directory or path is adjusted

const MyPageComponent = () => {
  const [isVisible, setIsVisible] = React.useState(true);

  return (
    <div>
      <Button onClick={() => setIsVisible(!isVisible)}>
        {isVisible ? 'Hide' : 'Show'} Box
      </Button>

      {isVisible && (
        <AnimatedBox
          className="w-32 h-32 bg-primary rounded-md shadow-lg flex items-center justify-center text-primary-foreground mt-4"
          animationPreset="fadeIn" // Use a preset
        >
          Fade In
        </AnimatedBox>
      )}

      <AnimatedBox
        className="w-32 h-32 bg-secondary rounded-md shadow-lg flex items-center justify-center text-secondary-foreground mt-4"
        animationConfig={{ // Custom config
          translateX: [-100, 0],
          opacity: [0, 1],
          duration: 800,
          easing: 'easeOutElastic(1, .8)',
        }}
      >
        Slide In
      </AnimatedBox>
    </div>
  );
};
*/
