import { afterEach, beforeEach, describe, expect, it, vi } from 'vitest';

// Mock anime.js using factory function
vi.mock('animejs', () => ({
  default: vi.fn(),
}));

import {
    animateCardHover,
    animateError,
    animateSuccess,
    ANIMATION_PRESETS,
    createAnimation
} from '../animations';

describe('Animations', () => {
  let mockAnime: any;

  beforeEach(async () => {
    // Get the mocked anime function
    const animeModule = await import('animejs');
    mockAnime = animeModule.default;

    mockAnime.mockClear();
    mockAnime.mockReturnValue({
      play: vi.fn(),
      pause: vi.fn(),
      restart: vi.fn(),
      finished: Promise.resolve(),
    });
  });

  afterEach(() => {
    vi.clearAllMocks();
  });

  describe('createAnimation', () => {
    it('calls anime with provided config and defaults', () => {
      const config = {
        targets: '.test-element',
        opacity: [0, 1],
        duration: 300,
      };

      createAnimation(config);

      expect(mockAnime).toHaveBeenCalledWith({
        delay: 0,
        duration: 300,
        easing: 'easeOutExpo',
        targets: '.test-element',
        opacity: [0, 1],
      });
    });

    it('returns anime instance', () => {
      const mockInstance = { play: vi.fn() };
      mockAnime.mockReturnValue(mockInstance);

      const result = createAnimation({ targets: '.test' });

      expect(result).toBe(mockInstance);
    });

    it('merges defaults with provided config', () => {
      createAnimation({});

      expect(mockAnime).toHaveBeenCalledWith({
        delay: 0,
        duration: 600,
        easing: 'easeOutExpo',
      });
    });
  });

  describe('animateSuccess', () => {
    it('creates success feedback animation', () => {
      const targets = '.test-element';

      animateSuccess(targets);

      expect(mockAnime).toHaveBeenCalledWith({
        delay: 0,
        duration: 400,
        easing: 'easeOutBack',
        targets,
        scale: [1, 1.1, 1],
      });
    });
  });

  describe('animateError', () => {
    it('creates error feedback animation', () => {
      const targets = '.test-element';

      animateError(targets);

      expect(mockAnime).toHaveBeenCalledWith({
        delay: 0,
        duration: 500,
        easing: 'easeInOutQuad',
        targets,
        translateX: [0, -10, 10, -10, 10, 0],
      });
    });
  });

  describe('animateCardHover', () => {
    it('creates hover animation when hovering', () => {
      const card = document.createElement('div');

      animateCardHover(card, true);

      expect(mockAnime).toHaveBeenCalledWith({
        delay: 0,
        duration: 200,
        easing: 'easeOutQuad',
        targets: card,
        scale: 1.02,
        translateY: -2,
      });
    });

    it('creates unhover animation when not hovering', () => {
      const card = document.createElement('div');

      animateCardHover(card, false);

      expect(mockAnime).toHaveBeenCalledWith({
        delay: 0,
        duration: 200,
        easing: 'easeOutQuad',
        targets: card,
        scale: 1,
        translateY: 0,
      });
    });
  });

  describe('ANIMATION_PRESETS', () => {
    it('contains all expected presets', () => {
      expect(ANIMATION_PRESETS).toHaveProperty('fadeIn');
      expect(ANIMATION_PRESETS).toHaveProperty('fadeOut');
      expect(ANIMATION_PRESETS).toHaveProperty('slideInUp');
      expect(ANIMATION_PRESETS).toHaveProperty('slideInDown');
      expect(ANIMATION_PRESETS).toHaveProperty('slideInLeft');
      expect(ANIMATION_PRESETS).toHaveProperty('slideInRight');
      expect(ANIMATION_PRESETS).toHaveProperty('scaleIn');
      expect(ANIMATION_PRESETS).toHaveProperty('scaleOut');
      expect(ANIMATION_PRESETS).toHaveProperty('bounce');
      expect(ANIMATION_PRESETS).toHaveProperty('pulse');
      expect(ANIMATION_PRESETS).toHaveProperty('shake');
      expect(ANIMATION_PRESETS).toHaveProperty('rotate');
    });

    it('has valid animation configurations', () => {
      Object.values(ANIMATION_PRESETS).forEach(preset => {
        expect(preset).toHaveProperty('duration');
        expect(preset).toHaveProperty('easing');
        expect(typeof preset.duration).toBe('number');
        expect(typeof preset.easing).toBe('string');
      });
    });
  });

});
