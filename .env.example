# Environment Configuration
ENVIRONMENT=development

# Database Configuration (PostgreSQL with async support)
DATABASE_URL=postgresql+asyncpg://lonors_user:lonors_password@localhost:5432/lonors_db
DB_HOST=localhost
DB_PORT=5432
DB_NAME=lonors_db
DB_USER=lonors_user
DB_PASSWORD=lonors_password

# Redis Configuration
REDIS_URL=redis://localhost:6379
REDIS_HOST=localhost
REDIS_PORT=6379

# JWT Configuration
JWT_SECRET=your-super-secret-jwt-key-change-in-production
JWT_ALGORITHM=HS256
JWT_EXPIRES_IN=7d
JWT_REFRESH_EXPIRES_IN=30d

# Backend API Configuration (Python FastAPI)
PORT=3001
HOST=0.0.0.0
API_URL=http://localhost:3001
API_PREFIX=/api/v1

# Frontend Configuration (React + Vite)
VITE_API_URL=http://localhost:3001
VITE_APP_NAME=Lonors
VITE_APP_VERSION=1.0.0
VITE_ENVIRONMENT=development
FRONTEND_PORT=5500

# Email Configuration (Optional)
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_USER=<EMAIL>
SMTP_PASS=your-app-password
SMTP_TLS=true

# File Upload Configuration
MAX_FILE_SIZE=10485760
UPLOAD_PATH=./uploads
ALLOWED_FILE_TYPES=jpg,jpeg,png,gif,pdf,doc,docx

# Security Configuration
CORS_ORIGINS=http://localhost:5500,http://127.0.0.1:5500
CORS_ALLOW_CREDENTIALS=true
RATE_LIMIT_WINDOW_MS=900000
RATE_LIMIT_MAX_REQUESTS=100
BCRYPT_ROUNDS=12

# Logging Configuration
LOG_LEVEL=info
LOG_FILE=./logs/app.log
LOG_FORMAT=json

# Protocol Configuration
# Model Context Protocol (MCP)
MCP_ENABLED=true
MCP_ENDPOINT=/api/v1/mcp
MCP_MAX_CONTEXT_LENGTH=4096

# AG-UI Protocol
AG_UI_ENABLED=true
AG_UI_ENDPOINT=/api/v1/ag-ui
AG_UI_WEBSOCKET_PATH=/ws/ag-ui

# A2A Protocol
A2A_ENABLED=true
A2A_ENDPOINT=/api/v1/a2a
A2A_AUTH_TOKEN=your-a2a-auth-token-change-in-production

# Development Configuration
DEBUG=true
RELOAD=true
WORKERS=1

# Production Configuration (override in production)
# DEBUG=false
# RELOAD=false
# WORKERS=4
# LOG_LEVEL=warning
