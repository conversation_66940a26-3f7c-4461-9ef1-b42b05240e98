# Knowledge Graph Architecture

## Overview

The Knowledge Graph system automatically extracts entities, concepts, and relationships from the user's notes and documents, creating a structured representation of their knowledge. This enables powerful navigation, discovery, and inference capabilities beyond what's possible with traditional search.

## Core Components

1. **Entity Extraction**
   - Identifies named entities (people, places, organizations)
   - Extracts concepts and topics
   - Recognizes dates and temporal references
   - Detects custom entity types

2. **Relationship Extraction**
   - Identifies connections between entities
   - Extracts semantic relationships
   - Detects hierarchical structures
   - Captures temporal and causal relationships

3. **Graph Storage**
   - Efficiently stores entities and relationships
   - Supports complex graph queries
   - Enables traversal and path finding
   - Maintains metadata and provenance

4. **Graph Enrichment**
   - Merges duplicate entities
   - Infers missing relationships
   - Enhances with external knowledge
   - Validates and corrects inconsistencies

5. **Visualization & Exploration**
   - Interactive graph visualization
   - Filtering and focusing mechanisms
   - Path exploration and discovery
   - Customizable views and layouts

6. **Query & Inference**
   - Natural language queries against the graph
   - Path-based reasoning
   - Pattern matching and discovery
   - Knowledge gap identification

## Technical Architecture

### Entity Extraction

```typescript
interface Entity {
  id: string;
  type: string;
  name: string;
  aliases?: string[];
  sourceIds: string[];
  confidence: number;
  metadata: Record<string, any>;
  extractedAt: Date;
}

class EntityExtractor {
  constructor(
    private llmService: LLMService,
    private customEntityTypes: string[] = []
  ) {}

  async extractEntities(document: Document): Promise<Entity[]> {
    // 1. Prepare document chunks
    const chunks = this.prepareChunks(document);

    // 2. Extract entities from each chunk
    const chunkEntities = await Promise.all(
      chunks.map(chunk => this.extractEntitiesFromChunk(chunk))
    );

    // 3. Merge and deduplicate entities
    const mergedEntities = this.mergeEntities(chunkEntities.flat());

    return mergedEntities;
  }

  private prepareChunks(document: Document): DocumentChunk[] {
    // Split document into manageable chunks
  }

  private async extractEntitiesFromChunk(chunk: DocumentChunk): Promise<Entity[]> {
    // Use LLM to extract entities
    const prompt = this.constructEntityExtractionPrompt(chunk);
    const response = await this.llmService.generateText(prompt);

    // Parse LLM response into structured entities
    return this.parseEntityExtractionResponse(response, chunk);
  }

  private constructEntityExtractionPrompt(chunk: DocumentChunk): string {
    // Create prompt for entity extraction
  }

  private parseEntityExtractionResponse(
    response: string,
    chunk: DocumentChunk
  ): Entity[] {
    // Parse LLM response into structured entities
  }

  private mergeEntities(entities: Entity[]): Entity[] {
    // Deduplicate and merge entities
  }
}
```

### Relationship Extraction

```typescript
interface Relationship {
  id: string;
  sourceEntityId: string;
  targetEntityId: string;
  type: string;
  label?: string;
  direction: 'outgoing' | 'incoming' | 'bidirectional';
  sourceIds: string[];
  confidence: number;
  metadata: Record<string, any>;
  extractedAt: Date;
}

class RelationshipExtractor {
  constructor(private llmService: LLMService) {}

  async extractRelationships(
    document: Document,
    entities: Entity[]
  ): Promise<Relationship[]> {
    // 1. Prepare document chunks
    const chunks = this.prepareChunks(document);

    // 2. Extract relationships from each chunk
    const chunkRelationships = await Promise.all(
      chunks.map(chunk => this.extractRelationshipsFromChunk(chunk, entities))
    );

    // 3. Merge and deduplicate relationships
    const mergedRelationships = this.mergeRelationships(chunkRelationships.flat());

    return mergedRelationships;
  }

  private prepareChunks(document: Document): DocumentChunk[] {
    // Split document into manageable chunks
  }

  private async extractRelationshipsFromChunk(
    chunk: DocumentChunk,
    entities: Entity[]
  ): Promise<Relationship[]> {
    // Filter entities that appear in this chunk
    const chunkEntities = this.filterEntitiesInChunk(chunk, entities);

    if (chunkEntities.length < 2) {
      return []; // Need at least 2 entities for a relationship
    }

    // Use LLM to extract relationships
    const prompt = this.constructRelationshipExtractionPrompt(chunk, chunkEntities);
    const response = await this.llmService.generateText(prompt);

    // Parse LLM response into structured relationships
    return this.parseRelationshipExtractionResponse(response, chunkEntities, chunk);
  }

  private filterEntitiesInChunk(
    chunk: DocumentChunk,
    entities: Entity[]
  ): Entity[] {
    // Find entities that appear in this chunk
  }

  private constructRelationshipExtractionPrompt(
    chunk: DocumentChunk,
    entities: Entity[]
  ): string {
    // Create prompt for relationship extraction
  }

  private parseRelationshipExtractionResponse(
    response: string,
    entities: Entity[],
    chunk: DocumentChunk
  ): Relationship[] {
    // Parse LLM response into structured relationships
  }

  private mergeRelationships(relationships: Relationship[]): Relationship[] {
    // Deduplicate and merge relationships
  }
}
```

### Graph Storage

```typescript
interface GraphNode {
  id: string;
  labels: string[];
  properties: Record<string, any>;
}

interface GraphRelationship {
  id: string;
  startNodeId: string;
  endNodeId: string;
  type: string;
  properties: Record<string, any>;
}

interface GraphQuery {
  query: string;
  parameters?: Record<string, any>;
}

class Neo4jGraphStore {
  constructor(private driver: Driver) {}

  async addEntity(entity: Entity): Promise<string> {
    const session = this.driver.session();
    try {
      const result = await session.run(
        `
        MERGE (e:Entity {id: $id})
        SET e += $properties
        SET e:$type
        RETURN e.id as id
        `,
        {
          id: entity.id,
          type: entity.type,
          properties: {
            name: entity.name,
            aliases: entity.aliases || [],
            sourceIds: entity.sourceIds,
            confidence: entity.confidence,
            metadata: entity.metadata,
            extractedAt: entity.extractedAt.toISOString()
          }
        }
      );

      return result.records[0].get('id');
    } finally {
      await session.close();
    }
  }

  async addRelationship(relationship: Relationship): Promise<string> {
    const session = this.driver.session();
    try {
      const result = await session.run(
        `
        MATCH (source:Entity {id: $sourceId})
        MATCH (target:Entity {id: $targetId})
        MERGE (source)-[r:${relationship.type} {id: $id}]->(target)
        SET r += $properties
        RETURN r.id as id
        `,
        {
          id: relationship.id,
          sourceId: relationship.sourceEntityId,
          targetId: relationship.targetEntityId,
          properties: {
            label: relationship.label,
            sourceIds: relationship.sourceIds,
            confidence: relationship.confidence,
            metadata: relationship.metadata,
            extractedAt: relationship.extractedAt.toISOString()
          }
        }
      );

      return result.records[0].get('id');
    } finally {
      await session.close();
    }
  }

  async getEntity(id: string): Promise<Entity | null> {
    const session = this.driver.session();
    try {
      const result = await session.run(
        `
        MATCH (e:Entity {id: $id})
        RETURN e
        `,
        { id }
      );

      if (result.records.length === 0) {
        return null;
      }

      const node = result.records[0].get('e');
      return this.nodeToEntity(node);
    } finally {
      await session.close();
    }
  }

  async getRelationship(id: string): Promise<Relationship | null> {
    const session = this.driver.session();
    try {
      const result = await session.run(
        `
        MATCH ()-[r {id: $id}]->()
        RETURN r, startNode(r) as source, endNode(r) as target
        `,
        { id }
      );

      if (result.records.length === 0) {
        return null;
      }

      const record = result.records[0];
      return this.recordToRelationship(record);
    } finally {
      await session.close();
    }
  }

  async findPath(
    sourceId: string,
    targetId: string,
    maxDepth: number = 3
  ): Promise<(Entity | Relationship)[]> {
    const session = this.driver.session();
    try {
      const result = await session.run(
        `
        MATCH path = shortestPath((source:Entity {id: $sourceId})-[*1..$maxDepth]-(target:Entity {id: $targetId}))
        RETURN path
        `,
        { sourceId, targetId, maxDepth }
      );

      if (result.records.length === 0) {
        return [];
      }

      const path = result.records[0].get('path');
      return this.pathToEntitiesAndRelationships(path);
    } finally {
      await session.close();
    }
  }

  async executeQuery(query: GraphQuery): Promise<any[]> {
    const session = this.driver.session();
    try {
      const result = await session.run(query.query, query.parameters);
      return result.records.map(record => {
        const obj: Record<string, any> = {};
        for (const key of record.keys) {
          obj[key] = this.neo4jValueToJs(record.get(key));
        }
        return obj;
      });
    } finally {
      await session.close();
    }
  }

  private nodeToEntity(node: any): Entity {
    // Convert Neo4j node to Entity
  }

  private recordToRelationship(record: any): Relationship {
    // Convert Neo4j record to Relationship
  }

  private pathToEntitiesAndRelationships(path: any): (Entity | Relationship)[] {
    // Convert Neo4j path to array of entities and relationships
  }

  private neo4jValueToJs(value: any): any {
    // Convert Neo4j values to JavaScript values
  }
}
```

### Graph Enrichment

```typescript
class GraphEnricher {
  constructor(
    private graphStore: Neo4jGraphStore,
    private llmService: LLMService
  ) {}

  async mergeEntities(
    entityIds: string[],
    primaryEntityId?: string
  ): Promise<string> {
    // 1. Get all entities
    const entities = await Promise.all(
      entityIds.map(id => this.graphStore.getEntity(id))
    );

    // 2. Determine primary entity if not specified
    const primaryId = primaryEntityId || this.determinePrimaryEntity(entities);
    const primaryEntity = entities.find(e => e.id === primaryId);

    if (!primaryEntity) {
      throw new Error('Primary entity not found');
    }

    // 3. Merge properties
    const mergedEntity = this.mergeEntityProperties(primaryEntity, entities);

    // 4. Update primary entity
    await this.graphStore.updateEntity(primaryId, mergedEntity);

    // 5. Redirect relationships
    await this.redirectRelationships(entityIds, primaryId);

    // 6. Delete merged entities
    await Promise.all(
      entityIds
        .filter(id => id !== primaryId)
        .map(id => this.graphStore.deleteEntity(id))
    );

    return primaryId;
  }

  async inferRelationships(entityId: string): Promise<Relationship[]> {
    // 1. Get entity and its connections
    const entity = await this.graphStore.getEntity(entityId);
    const connections = await this.graphStore.getEntityConnections(entityId);

    // 2. Find potential new relationships
    const potentialRelationships = this.findPotentialRelationships(connections);

    // 3. Use LLM to validate and label relationships
    const validatedRelationships = await this.validateRelationships(
      entity,
      potentialRelationships
    );

    // 4. Add new relationships to graph
    await Promise.all(
      validatedRelationships.map(rel => this.graphStore.addRelationship(rel))
    );

    return validatedRelationships;
  }

  async enrichWithExternalKnowledge(
    entityId: string,
    sources: string[] = ['wikipedia', 'wikidata']
  ): Promise<Entity> {
    // 1. Get entity
    const entity = await this.graphStore.getEntity(entityId);

    // 2. Fetch external knowledge
    const externalData = await this.fetchExternalData(entity, sources);

    // 3. Enrich entity with external data
    const enrichedEntity = this.enrichEntityWithExternalData(entity, externalData);

    // 4. Update entity in graph
    await this.graphStore.updateEntity(entityId, enrichedEntity);

    return enrichedEntity;
  }

  private determinePrimaryEntity(entities: Entity[]): string {
    // Determine which entity should be the primary one
  }

  private mergeEntityProperties(
    primary: Entity,
    entities: Entity[]
  ): Entity {
    // Merge properties from all entities into the primary one
  }

  private async redirectRelationships(
    entityIds: string[],
    primaryId: string
  ): Promise<void> {
    // Redirect relationships from merged entities to primary entity
  }

  private findPotentialRelationships(
    connections: { entity: Entity; relationship: Relationship }[]
  ): { source: Entity; target: Entity }[] {
    // Find potential new relationships between connected entities
  }

  private async validateRelationships(
    entity: Entity,
    potentialRelationships: { source: Entity; target: Entity }[]
  ): Promise<Relationship[]> {
    // Use LLM to validate and label potential relationships
  }

  private async fetchExternalData(
    entity: Entity,
    sources: string[]
  ): Promise<Record<string, any>> {
    // Fetch data from external sources
  }

  private enrichEntityWithExternalData(
    entity: Entity,
    externalData: Record<string, any>
  ): Entity {
    // Enrich entity with external data
  }
}
```

### Visualization & Exploration

```typescript
interface GraphVisualizationNode {
  id: string;
  label: string;
  type: string;
  size?: number;
  color?: string;
  image?: string;
  properties?: Record<string, any>;
}

interface GraphVisualizationEdge {
  id: string;
  source: string;
  target: string;
  label: string;
  type: string;
  width?: number;
  color?: string;
  properties?: Record<string, any>;
}

interface GraphVisualizationData {
  nodes: GraphVisualizationNode[];
  edges: GraphVisualizationEdge[];
}

class GraphVisualizer {
  constructor(private graphStore: Neo4jGraphStore) {}

  async getNeighborhood(
    entityId: string,
    depth: number = 1,
    limit: number = 50,
    filters?: {
      nodeTypes?: string[];
      edgeTypes?: string[];
      properties?: Record<string, any>;
    }
  ): Promise<GraphVisualizationData> {
    // 1. Build query based on parameters
    const query = this.buildNeighborhoodQuery(entityId, depth, limit, filters);

    // 2. Execute query
    const result = await this.graphStore.executeQuery(query);

    // 3. Transform result to visualization format
    return this.transformToVisualizationData(result);
  }

  async getPath(
    sourceId: string,
    targetId: string,
    maxDepth: number = 3
  ): Promise<GraphVisualizationData> {
    // 1. Find path between entities
    const path = await this.graphStore.findPath(sourceId, targetId, maxDepth);

    // 2. Transform path to visualization format
    return this.pathToVisualizationData(path);
  }

  async getCustomView(
    query: string,
    parameters: Record<string, any> = {}
  ): Promise<GraphVisualizationData> {
    // 1. Execute custom query
    const result = await this.graphStore.executeQuery({
      query,
      parameters
    });

    // 2. Transform result to visualization format
    return this.transformToVisualizationData(result);
  }

  private buildNeighborhoodQuery(
    entityId: string,
    depth: number,
    limit: number,
    filters?: {
      nodeTypes?: string[];
      edgeTypes?: string[];
      properties?: Record<string, any>;
    }
  ): GraphQuery {
    // Build Cypher query for neighborhood
  }

  private transformToVisualizationData(result: any[]): GraphVisualizationData {
    // Transform query result to visualization format
  }

  private pathToVisualizationData(
    path: (Entity | Relationship)[]
  ): GraphVisualizationData {
    // Transform path to visualization format
  }
}
```

### Query & Inference

```typescript
interface GraphQueryResult {
  entities: Entity[];
  relationships: Relationship[];
  paths: {
    entities: Entity[];
    relationships: Relationship[];
  }[];
  metadata: Record<string, any>;
}

class GraphQueryEngine {
  constructor(
    private graphStore: Neo4jGraphStore,
    private llmService: LLMService
  ) {}

  async queryWithNaturalLanguage(
    query: string,
    context?: {
      userId?: string;
      focusEntityIds?: string[];
      recentEntityIds?: string[];
    }
  ): Promise<GraphQueryResult> {
    // 1. Convert natural language to Cypher query
    const cypherQuery = await this.naturalLanguageToCypher(query, context);

    // 2. Execute Cypher query
    const result = await this.graphStore.executeQuery(cypherQuery);

    // 3. Transform result to structured format
    return this.transformQueryResult(result, query);
  }

  async findConnections(
    entityIds: string[],
    maxDepth: number = 3,
    relationshipTypes?: string[]
  ): Promise<GraphQueryResult> {
    if (entityIds.length < 2) {
      throw new Error('Need at least 2 entities to find connections');
    }

    // 1. Build query to find connections
    const query = this.buildConnectionsQuery(entityIds, maxDepth, relationshipTypes);

    // 2. Execute query
    const result = await this.graphStore.executeQuery(query);

    // 3. Transform result to structured format
    return this.transformQueryResult(result, 'find connections');
  }

  async identifyKnowledgeGaps(
    entityId: string,
    depth: number = 2
  ): Promise<{
    missingEntities: { type: string; name: string; confidence: number }[];
    missingRelationships: {
      sourceId: string;
      targetId: string;
      type: string;
      confidence: number;
    }[];
  }> {
    // 1. Get entity neighborhood
    const neighborhood = await this.graphStore.getNeighborhood(entityId, depth);

    // 2. Use LLM to identify potential gaps
    const gaps = await this.identifyGapsWithLLM(neighborhood);

    return gaps;
  }

  private async naturalLanguageToCypher(
    query: string,
    context?: {
      userId?: string;
      focusEntityIds?: string[];
      recentEntityIds?: string[];
    }
  ): Promise<GraphQuery> {
    // Use LLM to convert natural language to Cypher query
  }

  private buildConnectionsQuery(
    entityIds: string[],
    maxDepth: number,
    relationshipTypes?: string[]
  ): GraphQuery {
    // Build Cypher query to find connections between entities
  }

  private transformQueryResult(
    result: any[],
    originalQuery: string
  ): GraphQueryResult {
    // Transform query result to structured format
  }

  private async identifyGapsWithLLM(
    neighborhood: any
  ): Promise<{
    missingEntities: { type: string; name: string; confidence: number }[];
    missingRelationships: {
      sourceId: string;
      targetId: string;
      type: string;
      confidence: number;
    }[];
  }> {
    // Use LLM to identify potential knowledge gaps
  }
}
```

## Prompt Templates

### Entity Extraction Prompt

```
You are an AI assistant that specializes in identifying entities in text.

TEXT:
{text}

Please identify all entities in the text above. An entity can be a person, organization, location, concept, product, event, or other important noun phrase.

For each entity, provide:
1. The entity name as it appears in the text
2. The entity type (person, organization, location, concept, product, event, etc.)
3. Any aliases or alternative names mentioned in the text
4. A brief description based on the context

Format your response as a JSON array:
[
  {
    "name": "entity name",
    "type": "entity type",
    "aliases": ["alias1", "alias2"],
    "description": "brief description from context"
  },
  ...
]
```

### Relationship Extraction Prompt

```
You are an AI assistant that specializes in identifying relationships between entities in text.

TEXT:
{text}

ENTITIES FOUND IN THIS TEXT:
{entities_json}

Please identify all relationships between these entities in the text. A relationship describes how two entities are connected.

For each relationship, provide:
1. The source entity (from the list above)
2. The target entity (from the list above)
3. The relationship type (e.g., works_for, located_in, part_of, created_by, etc.)
4. A brief description of the relationship based on the context
5. The direction of the relationship (outgoing from source to target, incoming from target to source, or bidirectional)

Format your response as a JSON array:
[
  {
    "source": "source entity name",
    "target": "target entity name",
    "type": "relationship type",
    "description": "brief description from context",
    "direction": "outgoing" | "incoming" | "bidirectional"
  },
  ...
]

Only include relationships that are explicitly mentioned or strongly implied in the text.
```

### Knowledge Gap Identification Prompt

```
You are an AI assistant that specializes in knowledge graphs and identifying missing information.

KNOWLEDGE GRAPH EXCERPT:
{graph_json}

Based on the knowledge graph excerpt above, please identify:

1. Potential missing entities that would likely be connected to the existing entities
2. Potential missing relationships between existing entities

For missing entities, provide:
- The entity name
- The entity type
- The confidence level (0.0-1.0) that this entity is actually missing
- Which existing entities it would likely connect to

For missing relationships, provide:
- The source entity name
- The target entity name
- The relationship type
- The confidence level (0.0-1.0) that this relationship is actually missing

Format your response as a JSON object:
{
  "missingEntities": [
    {
      "name": "entity name",
      "type": "entity type",
      "confidence": 0.8,
      "connectedTo": ["existing entity 1", "existing entity 2"]
    },
    ...
  ],
  "missingRelationships": [
    {
      "source": "source entity name",
      "target": "target entity name",
      "type": "relationship type",
      "confidence": 0.9
    },
    ...
  ]
}

Focus on the most likely and important missing information rather than speculating too broadly.
```

## Implementation Phases

### Phase 1: Basic Knowledge Graph

- Implement entity extraction with LLM
- Set up Neo4j for graph storage
- Create basic entity and relationship models
- Build simple graph visualization
- Implement manual entity and relationship creation

### Phase 2: Automated Extraction

- Enhance entity extraction with custom types
- Implement relationship extraction
- Create batch processing for documents
- Build entity merging and deduplication
- Implement basic graph queries

### Phase 3: Advanced Features

- Add external knowledge enrichment
- Implement relationship inference
- Create natural language query interface
- Build knowledge gap identification
- Enhance visualization with filtering and layouts

### Phase 4: Integration & Optimization

- Integrate with note-taking application
- Implement real-time graph updates
- Add user feedback mechanisms
- Optimize extraction performance
- Implement advanced graph algorithms

## Integration with Note-Taking App

The Knowledge Graph system will be integrated with the note-taking application to provide:

1. **Concept Map**
   - Visual representation of notes and concepts
   - Interactive exploration of connections
   - Identification of central concepts and clusters

2. **Smart Navigation**
   - Suggestion of related notes based on graph
   - Path finding between concepts
   - Discovery of indirect connections

3. **Knowledge Gaps**
   - Identification of missing information
   - Suggestion of topics to explore
   - Highlighting of incomplete areas

4. **Contextual Understanding**
   - Enhanced search with graph context
   - Personalized content recommendations
   - Improved AI responses with graph knowledge

## Next Steps

1. Set up Neo4j database for graph storage
2. Implement basic entity extraction with LLM
3. Create graph storage service
4. Build simple visualization component
5. Implement relationship extraction
6. Integrate with note-taking application
