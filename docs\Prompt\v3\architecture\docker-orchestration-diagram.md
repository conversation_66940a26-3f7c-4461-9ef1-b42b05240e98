# Docker Container Orchestration Diagram

This diagram illustrates the Docker container orchestration for the Lonors AI Platform in both development and production environments.

```mermaid
graph TD
    subgraph "Development Environment"
        direction TB

        DevUser([Developer]) --> |docker-compose up| DevOrchestration[Docker Compose]

        DevOrchestration --> DevFrontend[Frontend Container]
        DevOrchestration --> DevBackend[Backend Container]
        DevOrchestration --> DevPostgres[PostgreSQL Container]
        DevOrchestration --> DevRedis[Redis Container]

        DevFrontend --> |Volume Mount| FrontendCode[Frontend Source Code]
        DevBackend --> |Volume Mount| BackendCode[Backend Source Code]
        DevPostgres --> |Volume Mount| DevPGData[PostgreSQL Data Volume]
        DevRedis --> |Volume Mount| DevRedisData[Redis Data Volume]

        DevFrontend --> |Hot Reload| DevUser
        DevBackend --> |Hot Reload| DevUser

        DevFrontend --> |"http://localhost:5500"| DevUser
        DevBackend --> |"http://localhost:3001"| DevFrontend
        DevBackend --> |SQL Queries| DevPostgres
        DevBackend --> |Cache Operations| DevRedis
    end

    subgraph "Production Environment"
        direction TB

        ProdDeploy([Deployment]) --> |docker-compose up| ProdOrchestration[Docker Compose]

        ProdOrchestration --> ProdNginx[Nginx Container]
        ProdOrchestration --> ProdFrontend[Frontend Container]
        ProdOrchestration --> ProdBackend[Backend Container]
        ProdOrchestration --> ProdPostgres[PostgreSQL Container]
        ProdOrchestration --> ProdRedis[Redis Container]

        ProdNginx --> |Reverse Proxy| ProdFrontend
        ProdNginx --> |API Proxy| ProdBackend

        ProdFrontend --> |Static Assets| ProdNginx
        ProdBackend --> |API Responses| ProdNginx

        ProdBackend --> |SQL Queries| ProdPostgres
        ProdBackend --> |Cache Operations| ProdRedis

        ProdPostgres --> |Persistent Storage| ProdPGData[PostgreSQL Data Volume]
        ProdRedis --> |Persistent Storage| ProdRedisData[Redis Data Volume]
        ProdNginx --> |Logs| NginxLogs[Nginx Logs Volume]

        User([End User]) --> |"https://lonors.com"| ProdNginx
    end

    subgraph "Container Images"
        direction TB

        FrontendBase[Node Alpine Base] --> FrontendBuild[Build Stage]
        FrontendBuild --> FrontendProd[Production Stage]

        BackendBase[Python Alpine Base] --> BackendBuild[Build Stage]
        BackendBuild --> BackendProd[Production Stage]

        FrontendProd --> DevFrontend
        FrontendProd --> ProdFrontend

        BackendProd --> DevBackend
        BackendProd --> ProdBackend
    end

    subgraph "Network Configuration"
        direction TB

        DevNetwork[Development Network]
        ProdNetwork[Production Network]

        DevFrontend --> DevNetwork
        DevBackend --> DevNetwork
        DevPostgres --> DevNetwork
        DevRedis --> DevNetwork

        ProdNginx --> ProdNetwork
        ProdFrontend --> ProdNetwork
        ProdBackend --> ProdNetwork
        ProdPostgres --> ProdNetwork
        ProdRedis --> ProdNetwork
    end

    subgraph "Resource Limits"
        direction TB

        ProdBackend --> BackendLimits["CPU: 2 cores\nMemory: 2GB"]
        ProdFrontend --> FrontendLimits["CPU: 1 core\nMemory: 512MB"]
        ProdPostgres --> PostgresLimits["CPU: 2 cores\nMemory: 4GB"]
        ProdRedis --> RedisLimits["CPU: 1 core\nMemory: 1GB"]
        ProdNginx --> NginxLimits["CPU: 1 core\nMemory: 256MB"]
    end
```

## Docker Container Architecture

### Development Environment

#### Containers
- **Frontend Container**: React development server with hot reload
- **Backend Container**: FastAPI development server with hot reload
- **PostgreSQL Container**: Database for development
- **Redis Container**: Cache and session store for development

#### Volumes
- **Frontend Source Code**: Mounted from host for live editing
- **Backend Source Code**: Mounted from host for live editing
- **PostgreSQL Data**: Persistent storage for development database
- **Redis Data**: Persistent storage for development cache

#### Networking
- All containers on shared bridge network
- Frontend accessible at http://localhost:5500
- Backend accessible at http://localhost:3001

### Production Environment

#### Containers
- **Nginx Container**: Reverse proxy and static file server
- **Frontend Container**: Optimized static assets
- **Backend Container**: Production-ready API server
- **PostgreSQL Container**: Production database
- **Redis Container**: Production cache and session store

#### Volumes
- **PostgreSQL Data**: Persistent storage for production database
- **Redis Data**: Persistent storage for production cache
- **Nginx Logs**: Persistent storage for access and error logs

#### Networking
- All containers on isolated bridge network
- Only Nginx exposed to public network
- Internal services communicate over private network

### Container Images

#### Frontend Image
- **Base**: Node Alpine for minimal size
- **Build Stage**: Compiles TypeScript and bundles assets
- **Production Stage**: Nginx serving static files

#### Backend Image
- **Base**: Python Alpine for minimal size
- **Build Stage**: Installs dependencies and compiles code
- **Production Stage**: Optimized Python environment

### Resource Management

#### Resource Limits
- **Backend**: 2 CPU cores, 2GB memory
- **Frontend**: 1 CPU core, 512MB memory
- **PostgreSQL**: 2 CPU cores, 4GB memory
- **Redis**: 1 CPU core, 1GB memory
- **Nginx**: 1 CPU core, 256MB memory

#### Resource Reservations
- **Backend**: 0.5 CPU cores, 512MB memory
- **Frontend**: 0.25 CPU cores, 128MB memory
- **PostgreSQL**: 0.5 CPU cores, 1GB memory
- **Redis**: 0.25 CPU cores, 256MB memory
- **Nginx**: 0.1 CPU cores, 64MB memory

## Container Optimization Strategies

1. **Multi-stage Builds**: Separate build and runtime environments
2. **Alpine Base Images**: Minimal footprint for production
3. **Layer Caching**: Optimize Dockerfile for cache utilization
4. **Resource Limits**: Prevent container resource contention
5. **Health Checks**: Ensure container availability and proper functioning
6. **Restart Policies**: Automatically recover from failures
7. **Volume Management**: Proper data persistence strategy
