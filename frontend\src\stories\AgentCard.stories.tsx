import type { <PERSON><PERSON>, StoryObj } from '@storybook/react';
import { action } from '@storybook/addon-actions';
import { AgentCard } from '@/entities/agent/ui/agent-card';
import { createAgent } from '@/entities/agent/model';
import { AgentStatus, AgentType } from '@/shared/types';

const meta: Meta<typeof AgentCard> = {
  title: 'Entities/Agent/AgentCard',
  component: AgentCard,
  parameters: {
    layout: 'centered',
    docs: {
      description: {
        component: 'A comprehensive agent card component that displays agent information, status, metrics, and provides action controls. Designed for the Lonors AI Agent Platform with accessibility and animation support.',
      },
    },
  },
  tags: ['autodocs'],
  argTypes: {
    variant: {
      control: 'select',
      options: ['default', 'compact', 'detailed'],
      description: 'Visual variant of the card',
    },
    showMetrics: {
      control: 'boolean',
      description: 'Whether to show performance metrics',
    },
    animate: {
      control: 'boolean',
      description: 'Whether to enable animations',
    },
    isSelected: {
      control: 'boolean',
      description: 'Whether the card is selected',
    },
  },
};

export default meta;
type Story = StoryObj<typeof meta>;

// Base agent data
const baseAgent = createAgent({
  id: '1',
  name: 'Customer Support Agent',
  description: 'An intelligent agent that handles customer inquiries and provides support across multiple channels.',
  agent_type: AgentType.CHAT,
  status: AgentStatus.IDLE,
  capabilities: [
    { name: 'natural-language', description: 'Natural language processing', parameters: {}, required: true },
    { name: 'sentiment-analysis', description: 'Sentiment analysis', parameters: {}, required: false },
    { name: 'knowledge-base', description: 'Knowledge base access', parameters: {}, required: true },
  ],
  tags: ['customer-service', 'support', 'chat', 'nlp'],
  metrics: {
    total_executions: 156,
    successful_executions: 142,
    failed_executions: 14,
    average_execution_time: 2.3,
    total_tokens_used: 45000,
    total_cost: 12.45,
  },
});

// Story actions
const actions = {
  onExecute: action('onExecute'),
  onPause: action('onPause'),
  onStop: action('onStop'),
  onEdit: action('onEdit'),
  onSelect: action('onSelect'),
};

export const Default: Story = {
  args: {
    agent: baseAgent,
    ...actions,
  },
};

export const Running: Story = {
  args: {
    agent: createAgent({
      ...baseAgent,
      status: AgentStatus.RUNNING,
      current_task_id: 'task-123',
    }),
    ...actions,
  },
};

export const Failed: Story = {
  args: {
    agent: createAgent({
      ...baseAgent,
      status: AgentStatus.FAILED,
      metrics: {
        ...baseAgent.metrics,
        failed_executions: 25,
        successful_executions: 131,
      },
    }),
    ...actions,
  },
};

export const Completed: Story = {
  args: {
    agent: createAgent({
      ...baseAgent,
      status: AgentStatus.COMPLETED,
    }),
    ...actions,
  },
};

export const WorkflowAgent: Story = {
  args: {
    agent: createAgent({
      ...baseAgent,
      name: 'Data Processing Workflow',
      description: 'Automated workflow for processing and analyzing large datasets with multiple transformation steps.',
      agent_type: AgentType.WORKFLOW,
      capabilities: [
        { name: 'data-ingestion', description: 'Data ingestion', parameters: {}, required: true },
        { name: 'transformation', description: 'Data transformation', parameters: {}, required: true },
        { name: 'validation', description: 'Data validation', parameters: {}, required: false },
        { name: 'export', description: 'Data export', parameters: {}, required: true },
      ],
      tags: ['workflow', 'data-processing', 'automation'],
    }),
    ...actions,
  },
};

export const ReasoningAgent: Story = {
  args: {
    agent: createAgent({
      ...baseAgent,
      name: 'Strategic Planning Agent',
      description: 'Advanced reasoning agent that analyzes complex business scenarios and provides strategic recommendations.',
      agent_type: AgentType.REASONING,
      capabilities: [
        { name: 'logical-reasoning', description: 'Logical reasoning', parameters: {}, required: true },
        { name: 'scenario-analysis', description: 'Scenario analysis', parameters: {}, required: true },
        { name: 'decision-trees', description: 'Decision tree analysis', parameters: {}, required: false },
      ],
      tags: ['reasoning', 'strategy', 'analysis'],
      metrics: {
        ...baseAgent.metrics,
        average_execution_time: 45.7,
        total_cost: 28.90,
      },
    }),
    ...actions,
  },
};

export const CompactVariant: Story = {
  args: {
    agent: baseAgent,
    variant: 'compact',
    ...actions,
  },
};

export const DetailedVariant: Story = {
  args: {
    agent: baseAgent,
    variant: 'detailed',
    ...actions,
  },
};

export const WithoutMetrics: Story = {
  args: {
    agent: baseAgent,
    showMetrics: false,
    ...actions,
  },
};

export const Selected: Story = {
  args: {
    agent: baseAgent,
    isSelected: true,
    ...actions,
  },
};

export const NoAnimations: Story = {
  args: {
    agent: baseAgent,
    animate: false,
    ...actions,
  },
};

export const NewAgent: Story = {
  args: {
    agent: createAgent({
      id: '2',
      name: 'New Agent',
      description: 'A newly created agent with minimal execution history.',
      agent_type: AgentType.TOOL,
      status: AgentStatus.IDLE,
      capabilities: [
        { name: 'basic-tools', description: 'Basic tool access', parameters: {}, required: true },
      ],
      tags: ['new', 'tool'],
      metrics: {
        total_executions: 0,
        successful_executions: 0,
        failed_executions: 0,
        average_execution_time: 0,
        total_tokens_used: 0,
        total_cost: 0,
      },
    }),
    ...actions,
  },
};

// Grid layout story to show multiple cards
export const GridLayout: Story = {
  render: () => (
    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 p-6 max-w-6xl">
      <AgentCard
        agent={createAgent({ ...baseAgent, status: AgentStatus.IDLE })}
        {...actions}
      />
      <AgentCard
        agent={createAgent({ 
          ...baseAgent, 
          id: '2',
          name: 'Running Agent',
          status: AgentStatus.RUNNING,
          agent_type: AgentType.WORKFLOW,
        })}
        {...actions}
      />
      <AgentCard
        agent={createAgent({ 
          ...baseAgent, 
          id: '3',
          name: 'Failed Agent',
          status: AgentStatus.FAILED,
          agent_type: AgentType.REASONING,
        })}
        {...actions}
      />
      <AgentCard
        agent={createAgent({ 
          ...baseAgent, 
          id: '4',
          name: 'Completed Agent',
          status: AgentStatus.COMPLETED,
          agent_type: AgentType.MULTIMODAL,
        })}
        {...actions}
      />
      <AgentCard
        agent={createAgent({ 
          ...baseAgent, 
          id: '5',
          name: 'Tool Agent',
          agent_type: AgentType.TOOL,
          variant: 'compact' as any,
        })}
        {...actions}
      />
      <AgentCard
        agent={createAgent({ 
          ...baseAgent, 
          id: '6',
          name: 'Custom Agent',
          agent_type: AgentType.CUSTOM,
          isSelected: true as any,
        })}
        {...actions}
      />
    </div>
  ),
  parameters: {
    layout: 'fullscreen',
  },
};
