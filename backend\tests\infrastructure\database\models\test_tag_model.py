"""
Unit tests for TagModel.

This module contains comprehensive tests for the TagModel
database model including domain entity conversion.
"""

import uuid
from datetime import datetime

import pytest

from src.domain.entities.tag import Tag
from src.infrastructure.database.models.tag import TagModel


class TestTagModel:
    """Test cases for TagModel."""

    def test_tag_model_creation(self):
        """Test creating a TagModel instance."""
        tag_id = uuid.uuid4()
        user_id = uuid.uuid4()
        now = datetime.now()

        tag_model = TagModel(
            id=tag_id,
            name="test-tag",
            color="#FF0000",
            user_id=user_id,
            created_at=now,
            updated_at=now,
        )

        assert tag_model.id == tag_id
        assert tag_model.name == "test-tag"
        assert tag_model.color == "#FF0000"
        assert tag_model.user_id == user_id
        assert tag_model.created_at == now
        assert tag_model.updated_at == now

    def test_tag_model_defaults(self):
        """Test TagModel default values."""
        user_id = uuid.uuid4()

        tag_model = TagModel(
            name="test-tag",
            user_id=user_id,
        )

        assert tag_model.color is None

    def test_to_domain_entity(self):
        """Test converting TagModel to domain entity."""
        tag_id = uuid.uuid4()
        user_id = uuid.uuid4()
        now = datetime.now()

        tag_model = TagModel(
            id=tag_id,
            name="test-tag",
            color="#00FF00",
            user_id=user_id,
            created_at=now,
            updated_at=now,
        )

        domain_entity = tag_model.to_domain_entity()

        assert isinstance(domain_entity, Tag)
        assert domain_entity.id == tag_id
        assert domain_entity.name == "test-tag"
        assert domain_entity.color == "#00FF00"
        assert domain_entity.user_id == user_id
        assert domain_entity.created_at == now
        assert domain_entity.updated_at == now

    def test_from_domain_entity(self):
        """Test creating TagModel from domain entity."""
        tag_id = uuid.uuid4()
        user_id = uuid.uuid4()
        now = datetime.now()

        domain_entity = Tag(
            id=tag_id,
            name="test-tag",
            color="#0000FF",
            user_id=user_id,
            created_at=now,
            updated_at=now,
        )

        tag_model = TagModel.from_domain_entity(domain_entity)

        assert tag_model.id == tag_id
        assert tag_model.name == "test-tag"
        assert tag_model.color == "#0000FF"
        assert tag_model.user_id == user_id
        assert tag_model.created_at == now
        assert tag_model.updated_at == now

    def test_round_trip_conversion(self):
        """Test converting from domain entity to model and back."""
        tag_id = uuid.uuid4()
        user_id = uuid.uuid4()
        now = datetime.now()

        original_entity = Tag(
            id=tag_id,
            name="test-tag",
            color="#FFFF00",
            user_id=user_id,
            created_at=now,
            updated_at=now,
        )

        # Convert to model and back
        tag_model = TagModel.from_domain_entity(original_entity)
        converted_entity = tag_model.to_domain_entity()

        # Verify all fields match
        assert converted_entity.id == original_entity.id
        assert converted_entity.name == original_entity.name
        assert converted_entity.color == original_entity.color
        assert converted_entity.user_id == original_entity.user_id
        assert converted_entity.created_at == original_entity.created_at
        assert converted_entity.updated_at == original_entity.updated_at

    def test_tag_model_repr(self):
        """Test TagModel string representation."""
        tag_model = TagModel(
            name="test-tag",
            user_id=uuid.uuid4(),
        )

        repr_str = repr(tag_model)
        assert "TagModel" in repr_str
        assert "test-tag" in repr_str

    def test_none_color_handling(self):
        """Test handling of None color."""
        user_id = uuid.uuid4()

        tag_model = TagModel(
            name="test-tag",
            user_id=user_id,
            color=None,
        )

        domain_entity = tag_model.to_domain_entity()
        assert domain_entity.color is None

    def test_tag_with_color(self):
        """Test tag with color."""
        user_id = uuid.uuid4()

        tag_model = TagModel(
            name="important",
            color="#FF0000",
            user_id=user_id,
        )

        domain_entity = tag_model.to_domain_entity()
        assert domain_entity.color == "#FF0000"

    def test_tag_name_validation(self):
        """Test tag name handling."""
        user_id = uuid.uuid4()

        # Test with various tag names
        tag_names = ["simple", "with-dash", "with_underscore", "123numeric"]
        
        for name in tag_names:
            tag_model = TagModel(
                name=name,
                user_id=user_id,
            )
            
            domain_entity = tag_model.to_domain_entity()
            assert domain_entity.name == name

    def test_user_specific_tags(self):
        """Test that tags are user-specific."""
        user1_id = uuid.uuid4()
        user2_id = uuid.uuid4()

        tag1 = TagModel(
            name="work",
            user_id=user1_id,
            color="#FF0000",
        )

        tag2 = TagModel(
            name="work",
            user_id=user2_id,
            color="#00FF00",
        )

        # Same tag name but different users and colors
        entity1 = tag1.to_domain_entity()
        entity2 = tag2.to_domain_entity()

        assert entity1.name == entity2.name
        assert entity1.user_id != entity2.user_id
        assert entity1.color != entity2.color

    def test_tag_update_scenario(self):
        """Test tag update scenario with updated_at."""
        user_id = uuid.uuid4()
        created_time = datetime.now()
        updated_time = datetime.now()

        tag_model = TagModel(
            name="original-name",
            color="#FF0000",
            user_id=user_id,
            created_at=created_time,
            updated_at=updated_time,
        )

        domain_entity = tag_model.to_domain_entity()
        
        # Simulate update
        domain_entity.update_name("updated-name")
        domain_entity.update_color("#00FF00")

        # Convert back to model
        updated_model = TagModel.from_domain_entity(domain_entity)

        assert updated_model.name == "updated-name"
        assert updated_model.color == "#00FF00"
        assert updated_model.created_at == created_time
        assert updated_model.updated_at > updated_time
