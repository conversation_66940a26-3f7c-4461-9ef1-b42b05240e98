{"compilerOptions": {"lib": ["dom", "dom.iterable", "es6"], "allowJs": true, "skipLibCheck": true, "strict": true, "noImplicitAny": true, "strictNullChecks": true, "strictFunctionTypes": true, "strictBindCallApply": true, "strictPropertyInitialization": true, "noImplicitReturns": true, "noFallthroughCasesInSwitch": true, "noImplicitOverride": true, "noEmit": true, "esModuleInterop": true, "module": "esnext", "moduleResolution": "bundler", "resolveJsonModule": true, "isolatedModules": true, "jsx": "preserve", "incremental": true, "plugins": [{"name": "next"}], "baseUrl": ".", "paths": {"@/*": ["./src/*"], "@/components/*": ["./src/components/*"], "@/lib/*": ["./src/lib/*"], "@/hooks/*": ["./src/hooks/*"], "@/types/*": ["./src/types/*"], "@/styles/*": ["./src/styles/*"], "@/app/*": ["./src/app/*"], "@/shared/*": ["./src/shared/*"], "@/entities/*": ["./src/entities/*"], "@/features/*": ["./src/features/*"], "@/widgets/*": ["./src/widgets/*"], "@/pages/*": ["./src/pages/*"]}, "target": "ES2017", "forceConsistentCasingInFileNames": true, "noUncheckedIndexedAccess": true, "exactOptionalPropertyTypes": true}, "include": ["next-env.d.ts", "**/*.ts", "**/*.tsx", ".next/types/**/*.ts"], "exclude": ["node_modules", ".next", "out", "dist", "build"]}