# Docker Secrets Configuration

This directory contains Docker secrets for production deployment. These files should contain sensitive information and must be properly secured.

## Required Secret Files

### Database Secrets
- `postgres_password.txt` - PostgreSQL database password
- `redis_password.txt` - DragonflyDB/Redis password

### Application Secrets
- `jwt_secret.txt` - JWT signing secret key
- `database_url.txt` - Complete database connection URL
- `redis_url.txt` - Complete Redis connection URL

### SSL/TLS Certificates
- `ssl_cert.pem` - SSL certificate file
- `ssl_key.pem` - SSL private key file

## Security Guidelines

1. **Never commit these files to version control**
2. **Use strong, randomly generated passwords**
3. **Rotate secrets regularly**
4. **Limit file permissions to 600 (owner read/write only)**
5. **Use a secrets management system in production**

## Creating Secret Files

### Generate Strong Passwords
```bash
# Generate a 32-character random password
openssl rand -base64 32

# Generate a JWT secret (64 characters)
openssl rand -hex 64
```

### Example Commands
```bash
# Create postgres password
echo "your_secure_postgres_password_here" > postgres_password.txt

# Create Redis password
echo "your_secure_redis_password_here" > redis_password.txt

# Create JWT secret
echo "your_jwt_secret_key_here" > jwt_secret.txt

# Set proper permissions
chmod 600 *.txt *.pem
```

## Production Deployment

For production deployment, consider using:
- **Docker Swarm secrets**
- **Kubernetes secrets**
- **HashiCorp Vault**
- **AWS Secrets Manager**
- **Azure Key Vault**
- **Google Secret Manager**

## Environment-Specific Secrets

### Development
Use the `.env.development` file for development secrets (less sensitive).

### Production
Always use Docker secrets or external secret management systems for production.

## Secret Rotation

1. Generate new secrets
2. Update secret files
3. Restart services with zero-downtime deployment
4. Verify all services are working
5. Remove old secrets

## Monitoring

Monitor access to secrets and set up alerts for:
- Unauthorized access attempts
- Secret rotation events
- Failed authentication due to invalid secrets

## Backup and Recovery

- **DO NOT** backup secrets in plain text
- Use encrypted backups only
- Test secret recovery procedures
- Document secret recovery process
