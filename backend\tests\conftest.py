"""
Comprehensive test configuration and fixtures for Lonors backend TDD.

This module provides all necessary fixtures and configuration for testing
following Clean Architecture patterns with proper isolation and mocking.
"""

import asyncio
import os
import tempfile
from typing import Any, AsyncGenerator, Dict
from unittest.mock import AsyncMock, MagicMock
from uuid import uuid4

import pytest
import pytest_asyncio
from fastapi.testclient import Test<PERSON>lient
from sqlalchemy.ext.asyncio import AsyncSession, async_sessionmaker, create_async_engine
from sqlalchemy.pool import StaticPool

# Test database URL - using SQLite for simplicity in tests
TEST_DATABASE_URL = "sqlite+aiosqlite:///:memory:"


@pytest.fixture(scope="session")
def event_loop():
    """Create an instance of the default event loop for the test session."""
    loop = asyncio.get_event_loop_policy().new_event_loop()
    yield loop
    loop.close()


@pytest.fixture
async def db_engine():
    """Create a test database engine with proper configuration."""
    engine = create_async_engine(
        TEST_DATABASE_URL,
        echo=False,
        poolclass=StaticPool,
        connect_args={"check_same_thread": False},
    )
    yield engine
    await engine.dispose()


@pytest.fixture
async def db_session(db_engine) -> AsyncGenerator[AsyncSession, None]:
    """Create a test database session with transaction rollback."""
    async_session = async_sessionmaker(
        db_engine, class_=AsyncSession, expire_on_commit=False
    )

    async with async_session() as session:
        # Start a transaction
        transaction = await session.begin()
        try:
            yield session
        finally:
            # Rollback transaction to ensure test isolation
            await transaction.rollback()


@pytest.fixture
def mock_redis():
    """Create a comprehensive mock Redis client."""
    mock_redis = AsyncMock()
    mock_redis.ping.return_value = True
    mock_redis.get.return_value = None
    mock_redis.set.return_value = True
    mock_redis.delete.return_value = 1
    mock_redis.exists.return_value = False
    mock_redis.expire.return_value = True
    mock_redis.ttl.return_value = -1
    mock_redis.keys.return_value = []
    mock_redis.flushdb.return_value = True
    return mock_redis


@pytest.fixture
def mock_cache_service():
    """Create a comprehensive mock cache service."""
    mock_cache = AsyncMock()
    mock_cache.get.return_value = None
    mock_cache.set.return_value = True
    mock_cache.delete.return_value = True
    mock_cache.exists.return_value = False
    mock_cache.clear.return_value = True
    mock_cache.get_many.return_value = {}
    mock_cache.set_many.return_value = True
    return mock_cache


@pytest.fixture
def mock_settings():
    """Create mock settings for testing."""
    mock_settings = MagicMock()
    mock_settings.database_url = TEST_DATABASE_URL
    mock_settings.redis_url = "redis://localhost:6379/1"
    mock_settings.secret_key = "test-secret-key"
    mock_settings.algorithm = "HS256"
    mock_settings.access_token_expire_minutes = 30
    mock_settings.environment = "test"
    mock_settings.debug = True
    mock_settings.log_level = "DEBUG"
    mock_settings.cors_origins = ["http://localhost:3000"]
    return mock_settings


@pytest.fixture
def sample_user_data() -> Dict[str, Any]:
    """Provide sample user data for testing."""
    return {
        "id": str(uuid4()),
        "email": "<EMAIL>",
        "username": "testuser",
        "full_name": "Test User",
        "is_active": True,
        "is_verified": False,
        "created_at": "2024-01-01T00:00:00Z",
        "updated_at": "2024-01-01T00:00:00Z",
    }


@pytest.fixture
def sample_note_data() -> Dict[str, Any]:
    """Provide sample note data for testing."""
    return {
        "id": str(uuid4()),
        "title": "Test Note",
        "content": "This is a test note content",
        "user_id": str(uuid4()),
        "folder_id": None,
        "is_archived": False,
        "created_at": "2024-01-01T00:00:00Z",
        "updated_at": "2024-01-01T00:00:00Z",
    }


@pytest.fixture
def sample_folder_data() -> Dict[str, Any]:
    """Provide sample folder data for testing."""
    return {
        "id": str(uuid4()),
        "name": "Test Folder",
        "description": "Test folder description",
        "user_id": str(uuid4()),
        "parent_id": None,
        "created_at": "2024-01-01T00:00:00Z",
        "updated_at": "2024-01-01T00:00:00Z",
    }


@pytest.fixture
def sample_tag_data() -> Dict[str, Any]:
    """Provide sample tag data for testing."""
    return {
        "id": str(uuid4()),
        "name": "test-tag",
        "color": "#FF0000",
        "user_id": str(uuid4()),
        "created_at": "2024-01-01T00:00:00Z",
        "updated_at": "2024-01-01T00:00:00Z",
    }


@pytest.fixture
def test_client():
    """Create a test client for the FastAPI application."""
    from src.main import create_application
    
    app = create_application()
    return TestClient(app)


@pytest.fixture
def mock_user_repository():
    """Create a mock user repository."""
    mock_repo = AsyncMock()
    mock_repo.create.return_value = None
    mock_repo.get_by_id.return_value = None
    mock_repo.get_by_email.return_value = None
    mock_repo.get_by_username.return_value = None
    mock_repo.update.return_value = None
    mock_repo.delete.return_value = None
    mock_repo.list.return_value = []
    return mock_repo


@pytest.fixture
def mock_note_repository():
    """Create a mock note repository."""
    mock_repo = AsyncMock()
    mock_repo.create.return_value = None
    mock_repo.get_by_id.return_value = None
    mock_repo.get_by_user_id.return_value = []
    mock_repo.update.return_value = None
    mock_repo.delete.return_value = None
    mock_repo.search.return_value = []
    return mock_repo


@pytest.fixture
def mock_folder_repository():
    """Create a mock folder repository."""
    mock_repo = AsyncMock()
    mock_repo.create.return_value = None
    mock_repo.get_by_id.return_value = None
    mock_repo.get_by_user_id.return_value = []
    mock_repo.update.return_value = None
    mock_repo.delete.return_value = None
    return mock_repo


@pytest.fixture
def mock_tag_repository():
    """Create a mock tag repository."""
    mock_repo = AsyncMock()
    mock_repo.create.return_value = None
    mock_repo.get_by_id.return_value = None
    mock_repo.get_by_user_id.return_value = []
    mock_repo.update.return_value = None
    mock_repo.delete.return_value = None
    return mock_repo


@pytest.fixture
def mock_jwt_service():
    """Create a mock JWT service."""
    mock_jwt = MagicMock()
    mock_jwt.create_access_token.return_value = "test-token"
    mock_jwt.decode_token.return_value = {"sub": "<EMAIL>"}
    mock_jwt.verify_token.return_value = True
    return mock_jwt


@pytest.fixture
def mock_password_service():
    """Create a mock password service."""
    mock_password = MagicMock()
    mock_password.hash_password.return_value = "hashed-password"
    mock_password.verify_password.return_value = True
    return mock_password


@pytest.fixture
def mock_container():
    """Create a mock dependency injection container."""
    mock_container = MagicMock()
    return mock_container


# Test markers for different test categories
pytestmark = [
    pytest.mark.asyncio,
]
