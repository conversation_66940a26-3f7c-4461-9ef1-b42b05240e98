# GitHub Repository Settings for Lonors Project
# This file configures repository settings for optimal CI/CD workflow

repository:
  # Repository settings
  name: lonors
  description: "Lonors AI Agent Platform - Comprehensive Docker CI/CD with GitHub Actions"
  homepage: https://lonors.com
  topics:
    - ai-platform
    - docker
    - github-actions
    - ci-cd
    - react
    - fastapi
    - typescript
    - python
  
  # Repository features
  private: false
  has_issues: true
  has_projects: true
  has_wiki: true
  has_downloads: true
  
  # Repository settings
  default_branch: main
  allow_squash_merge: true
  allow_merge_commit: false
  allow_rebase_merge: true
  delete_branch_on_merge: true
  
  # Security settings
  enable_automated_security_fixes: true
  enable_vulnerability_alerts: true

# Branch protection rules
branches:
  - name: main
    protection:
      required_status_checks:
        strict: true
        contexts:
          - "🔧 Setup and Validation"
          - "🛡️ Security Audit"
          - "🎨 Frontend Tests"
          - "🔧 Backend Tests"
          - "📊 Performance Benchmark"
      enforce_admins: false
      required_pull_request_reviews:
        required_approving_review_count: 1
        dismiss_stale_reviews: true
        require_code_owner_reviews: true
        require_last_push_approval: false
      restrictions: null
      allow_force_pushes: false
      allow_deletions: false

  - name: develop
    protection:
      required_status_checks:
        strict: true
        contexts:
          - "🔧 Setup and Validation"
          - "🛡️ Security Audit"
          - "🎨 Frontend Tests"
          - "🔧 Backend Tests"
      enforce_admins: false
      required_pull_request_reviews:
        required_approving_review_count: 1
        dismiss_stale_reviews: true
        require_code_owner_reviews: false
        require_last_push_approval: false
      restrictions: null
      allow_force_pushes: false
      allow_deletions: false

# Environment settings
environments:
  - name: staging
    protection_rules:
      - type: required_reviewers
        reviewers:
          - simyropandos
      - type: wait_timer
        wait_timer: 0
    deployment_branch_policy:
      protected_branches: false
      custom_branch_policies: true
      custom_branches:
        - develop
        - main

  - name: production
    protection_rules:
      - type: required_reviewers
        reviewers:
          - simyropandos
      - type: wait_timer
        wait_timer: 300  # 5 minute wait
    deployment_branch_policy:
      protected_branches: true
      custom_branch_policies: false

# Labels for issue and PR management
labels:
  # Priority labels
  - name: "priority: critical"
    color: "d73a4a"
    description: "Critical priority - immediate attention required"
  
  - name: "priority: high"
    color: "ff6b6b"
    description: "High priority - should be addressed soon"
  
  - name: "priority: medium"
    color: "ffa726"
    description: "Medium priority - normal timeline"
  
  - name: "priority: low"
    color: "4caf50"
    description: "Low priority - can be addressed later"

  # Type labels
  - name: "type: bug"
    color: "d73a4a"
    description: "Something isn't working"
  
  - name: "type: feature"
    color: "0052cc"
    description: "New feature or request"
  
  - name: "type: enhancement"
    color: "a2eeef"
    description: "Enhancement to existing functionality"
  
  - name: "type: documentation"
    color: "0075ca"
    description: "Improvements or additions to documentation"

  # Component labels
  - name: "component: frontend"
    color: "61dafb"
    description: "Frontend React/TypeScript code"
  
  - name: "component: backend"
    color: "3776ab"
    description: "Backend Python/FastAPI code"
  
  - name: "component: docker"
    color: "2496ed"
    description: "Docker configuration and containers"
  
  - name: "component: ci-cd"
    color: "28a745"
    description: "CI/CD workflows and automation"

  # Status labels
  - name: "status: needs-review"
    color: "fbca04"
    description: "Needs code review"
  
  - name: "status: needs-testing"
    color: "ff9500"
    description: "Needs testing"
  
  - name: "status: blocked"
    color: "d73a4a"
    description: "Blocked by external dependency"
  
  - name: "status: in-progress"
    color: "0052cc"
    description: "Currently being worked on"

  # Special labels
  - name: "security"
    color: "d73a4a"
    description: "Security-related issue"
  
  - name: "performance"
    color: "ff6b6b"
    description: "Performance-related issue"
  
  - name: "accessibility"
    color: "7057ff"
    description: "Accessibility-related issue"
  
  - name: "monitoring"
    color: "0e8a16"
    description: "Monitoring and observability"
  
  - name: "automated"
    color: "6f42c1"
    description: "Created by automation"
  
  - name: "needs-attention"
    color: "ff6b6b"
    description: "Requires immediate attention"

# Issue templates configuration
issue_templates:
  - name: Bug Report
    about: Create a report to help us improve
    title: "[BUG] "
    labels:
      - "type: bug"
      - "status: needs-review"
    body: |
      ## Bug Description
      A clear and concise description of what the bug is.
      
      ## Steps to Reproduce
      1. Go to '...'
      2. Click on '....'
      3. Scroll down to '....'
      4. See error
      
      ## Expected Behavior
      A clear and concise description of what you expected to happen.
      
      ## Actual Behavior
      A clear and concise description of what actually happened.
      
      ## Environment
      - OS: [e.g. Windows 11, macOS 12, Ubuntu 20.04]
      - Browser: [e.g. Chrome 96, Firefox 95, Safari 15]
      - Docker Version: [e.g. 20.10.12]
      - Node Version: [e.g. 18.12.0]
      - Python Version: [e.g. 3.11.0]
      
      ## Additional Context
      Add any other context about the problem here.

  - name: Feature Request
    about: Suggest an idea for this project
    title: "[FEATURE] "
    labels:
      - "type: feature"
      - "status: needs-review"
    body: |
      ## Feature Description
      A clear and concise description of what you want to happen.
      
      ## Problem Statement
      Is your feature request related to a problem? Please describe.
      
      ## Proposed Solution
      Describe the solution you'd like.
      
      ## Alternatives Considered
      Describe any alternative solutions or features you've considered.
      
      ## Additional Context
      Add any other context or screenshots about the feature request here.

# Pull request template
pull_request_template: |
  ## Description
  Brief description of the changes in this PR.
  
  ## Type of Change
  - [ ] Bug fix (non-breaking change which fixes an issue)
  - [ ] New feature (non-breaking change which adds functionality)
  - [ ] Breaking change (fix or feature that would cause existing functionality to not work as expected)
  - [ ] Documentation update
  - [ ] Performance improvement
  - [ ] Code refactoring
  
  ## Testing
  - [ ] Unit tests pass
  - [ ] Integration tests pass
  - [ ] E2E tests pass
  - [ ] Manual testing completed
  - [ ] Performance testing completed (if applicable)
  
  ## Quality Checklist
  - [ ] Code follows project style guidelines
  - [ ] Self-review of code completed
  - [ ] Code is properly commented
  - [ ] Documentation updated (if applicable)
  - [ ] No new warnings or errors introduced
  - [ ] Test coverage maintained (>90%)
  - [ ] Bundle size within limits (<1MB)
  - [ ] WCAG 2.1 AA compliance maintained
  
  ## Security Checklist
  - [ ] No sensitive information exposed
  - [ ] Security best practices followed
  - [ ] Dependencies updated and secure
  - [ ] No new security vulnerabilities introduced
  
  ## Deployment
  - [ ] Changes are backward compatible
  - [ ] Database migrations included (if applicable)
  - [ ] Environment variables documented (if applicable)
  - [ ] Deployment instructions updated (if applicable)
  
  ## Screenshots (if applicable)
  Add screenshots to help explain your changes.
  
  ## Additional Notes
  Any additional information that reviewers should know.
