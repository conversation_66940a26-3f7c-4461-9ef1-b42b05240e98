# Lonors Architecture Documentation

This directory contains comprehensive architecture documentation for the Lonors AI Platform. These diagrams and documents provide a detailed overview of the system design, component relationships, and technical implementation.

## Architecture Diagrams

### [System Architecture Diagram](system-architecture-diagram.md)

A high-level overview of the Lonors platform architecture, showing the relationships between frontend, backend, and database components.

![System Architecture](system-architecture-diagram.md)

### [Data Flow Diagram](data-flow-diagram.md)

Illustrates how data moves through the system during various user interactions and processes.

![Data Flow](data-flow-diagram.md)

### [Component Dependency Graph](component-dependency-graph.md)

Shows the dependencies between components in the Lonors AI Platform, following the Feature Slice Design (FSD) architecture.

![Component Dependencies](component-dependency-graph.md)

### [CI/CD Pipeline Diagram](cicd-pipeline-diagram.md)

Visualizes the Continuous Integration and Continuous Deployment pipeline for the Lonors AI Platform.

![CI/CD Pipeline](cicd-pipeline-diagram.md)

### [Docker Orchestration Diagram](docker-orchestration-diagram.md)

Illustrates the Docker container orchestration for the Lonors AI Platform in both development and production environments.

![Docker Orchestration](docker-orchestration-diagram.md)

### [Protocol Integration Diagram](protocol-integration-diagram.md)

Shows the integration of the three main protocols in the Lonors AI Platform: Model Context Protocol (MCP), AG-UI Protocol, and A2A Protocol.

![Protocol Integration](protocol-integration-diagram.md)

### [Database Schema](database-schema.md)

Provides a comprehensive overview of the database schema for the Lonors AI Platform.

![Database Schema](database-schema.md)

## Architecture Principles

The Lonors platform follows these key architectural principles:

1. **Clean Architecture** (Backend)
   - Clear separation of concerns
   - Domain-driven design
   - Dependency inversion
   - Use case centered design

2. **Feature Slice Design** (Frontend)
   - Business domain-oriented structure
   - Public API boundaries between slices
   - Shared kernel for common functionality
   - Unidirectional dependencies

3. **Protocol-First Design**
   - Standardized communication protocols
   - Clear interface definitions
   - Version compatibility
   - Extensibility

4. **Scalable Infrastructure**
   - Containerized deployment
   - Stateless services
   - Horizontal scaling
   - Resilient design

## Technology Stack

### Frontend
- React 18+ with TypeScript
- Vite for building and development
- ShadCN UI with Tailwind CSS
- Anime.js for animations
- Zustand and TanStack Query for state management

### Backend
- Python 3.11+ with FastAPI
- SQLAlchemy ORM with async support
- Alembic for database migrations
- Pydantic for data validation
- Redis for caching and sessions

### Database
- PostgreSQL 15
- Redis 7

### Infrastructure
- Docker and Docker Compose
- GitHub Actions for CI/CD
- Nginx for reverse proxy and load balancing

## Further Reading

- [Comprehensive Product Requirements Document](../product/comprehensive-prd.md)
- [API Documentation](../API_DOCUMENTATION.md)
- [Protocols Documentation](../PROTOCOLS.md)
