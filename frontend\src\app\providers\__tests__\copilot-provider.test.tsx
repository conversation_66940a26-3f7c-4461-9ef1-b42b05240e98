/**
 * @jest-environment jsdom
 */

import { render, screen } from '@testing-library/react';
import { beforeEach, describe, expect, it, vi } from 'vitest';
import { CopilotProvider } from '../copilot-provider';

// Mock CopilotKit components
vi.mock('@copilotkit/react-core', () => ({
  CopilotKit: ({ children, runtimeUrl, publicApiKey, showDevConsole, ...props }: any) => (
    <div
      data-testid="copilot-kit"
      data-runtime-url={runtimeUrl}
      data-public-api-key={publicApiKey}
      data-show-dev-console={showDevConsole?.toString()}
      {...props}
    >
      {children}
    </div>
  ),
  useCopilotAction: vi.fn(),
  useCopilotReadable: vi.fn(),
}));

vi.mock('@copilotkit/react-ui', () => ({
  CopilotSidebar: ({ children, defaultOpen, clickOutsideToClose, className, ...props }: any) => (
    <div
      data-testid="copilot-sidebar"
      data-default-open={defaultOpen?.toString()}
      data-click-outside-to-close={clickOutsideToClose?.toString()}
      data-class-name={className}
      {...props}
    >
      {children}
    </div>
  ),
}));

describe('CopilotProvider', () => {
  beforeEach(() => {
    vi.clearAllMocks();
  });

  it('renders children correctly', () => {
    render(
      <CopilotProvider>
        <div data-testid="test-child">Test Content</div>
      </CopilotProvider>
    );

    expect(screen.getByTestId('test-child')).toBeInTheDocument();
    expect(screen.getByText('Test Content')).toBeInTheDocument();
  });

  it('renders CopilotKit wrapper', () => {
    render(
      <CopilotProvider>
        <div>Test Content</div>
      </CopilotProvider>
    );

    expect(screen.getByTestId('copilot-kit')).toBeInTheDocument();
  });

  it('renders CopilotSidebar', () => {
    render(
      <CopilotProvider>
        <div>Test Content</div>
      </CopilotProvider>
    );

    expect(screen.getByTestId('copilot-sidebar')).toBeInTheDocument();
  });

  it('configures CopilotKit with correct props', () => {
    render(
      <CopilotProvider>
        <div>Test Content</div>
      </CopilotProvider>
    );

    const copilotKit = screen.getByTestId('copilot-kit');
    expect(copilotKit).toHaveAttribute('data-runtime-url', '/api/copilotkit');
  });

  it('configures CopilotSidebar with correct props', () => {
    render(
      <CopilotProvider>
        <div>Test Content</div>
      </CopilotProvider>
    );

    const sidebar = screen.getByTestId('copilot-sidebar');
    expect(sidebar).toHaveAttribute('data-default-open', 'false');
    expect(sidebar).toHaveAttribute('data-click-outside-to-close', 'true');
    expect(sidebar).toHaveAttribute('data-class-name', 'copilot-sidebar');
  });

  it('handles environment variables correctly', () => {
    const originalEnv = process.env.NODE_ENV;
    (process.env as any).NODE_ENV = 'development';

    render(
      <CopilotProvider>
        <div>Test Content</div>
      </CopilotProvider>
    );

    const copilotKit = screen.getByTestId('copilot-kit');
    expect(copilotKit).toHaveAttribute('data-show-dev-console', 'true');

    (process.env as any).NODE_ENV = originalEnv;
  });

  it('exports hooks correctly', async () => {
    const { useCopilotAction, useCopilotReadable } = await import('../copilot-provider');

    expect(useCopilotAction).toBeDefined();
    expect(useCopilotReadable).toBeDefined();
  });

  it('applies accessibility attributes', () => {
    render(
      <CopilotProvider>
        <div>Test Content</div>
      </CopilotProvider>
    );

    const sidebar = screen.getByTestId('copilot-sidebar');
    // Check that the sidebar has proper ARIA attributes (mocked)
    expect(sidebar).toBeInTheDocument();
  });

  it('handles multiple children correctly', () => {
    render(
      <CopilotProvider>
        <div data-testid="child-1">Child 1</div>
        <div data-testid="child-2">Child 2</div>
        <span data-testid="child-3">Child 3</span>
      </CopilotProvider>
    );

    expect(screen.getByTestId('child-1')).toBeInTheDocument();
    expect(screen.getByTestId('child-2')).toBeInTheDocument();
    expect(screen.getByTestId('child-3')).toBeInTheDocument();
  });

  it('maintains proper component hierarchy', () => {
    render(
      <CopilotProvider>
        <div data-testid="test-content">Test</div>
      </CopilotProvider>
    );

    const copilotKit = screen.getByTestId('copilot-kit');
    const sidebar = screen.getByTestId('copilot-sidebar');
    const content = screen.getByTestId('test-content');

    // Verify hierarchy: CopilotKit > CopilotSidebar > children
    expect(copilotKit).toContainElement(sidebar);
    expect(sidebar).toContainElement(content);
  });
});
