"""
Tag Entity Module

This module defines the Tag entity for the domain layer.
"""

from datetime import datetime
from uuid import UUID, uuid4

from pydantic import BaseModel, Field


class Tag(BaseModel):
    """Tag entity representing a label for notes."""

    id: UUID = Field(default_factory=uuid4)
    name: str
    color: str | None = None
    user_id: UUID
    created_at: datetime = Field(default_factory=datetime.now)
    updated_at: datetime = Field(default_factory=datetime.now)

    def update_name(self, name: str) -> None:
        """
        Update the tag name.

        Args:
            name: The new name
        """
        self.name = name
        self.updated_at = datetime.now()

    def update_color(self, color: str | None) -> None:
        """
        Update the tag color.

        Args:
            color: The new color
        """
        self.color = color
        self.updated_at = datetime.now()
