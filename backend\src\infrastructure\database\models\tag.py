"""
Tag database model.

This module defines the SQLAlchemy model for tags
with proper database constraints and relationships.
"""

import uuid
from typing import TYPE_CHECKING

from sqlalchemy import Foreign<PERSON>ey, String, UniqueConstraint
from sqlalchemy.dialects.postgresql import UUID
from sqlalchemy.orm import Mapped, mapped_column, relationship

from src.infrastructure.database.models.base import Base

if TYPE_CHECKING:
    from src.infrastructure.database.models.user import UserModel


class TagModel(Base):
    """
    Tag SQLAlchemy model.
    
    Represents the tags table in the database with all
    necessary constraints and relationships.
    """
    
    # Tag identification
    name: Mapped[str] = mapped_column(
        String(255),
        nullable=False,
        index=True,
        doc="Tag name"
    )
    
    color: Mapped[str | None] = mapped_column(
        String(50),
        nullable=True,
        doc="Tag color (hex code)"
    )
    
    # Relationships
    user_id: Mapped[uuid.UUID] = mapped_column(
        UUID(as_uuid=True),
        ForeignKey("users.id", ondelete="CASCADE"),
        nullable=False,
        index=True,
        doc="Owner user ID"
    )
    
    # Relationships
    user: Mapped["UserModel"] = relationship(
        "UserModel",
        back_populates="tags"
    )
    
    # Table constraints
    __table_args__ = (
        UniqueConstraint('name', 'user_id', name='uq_tag_name_user'),
    )
    
    def __repr__(self) -> str:
        """String representation of the tag."""
        return f"<TagModel(id={self.id}, name={self.name})>"
    
    def to_domain_entity(self) -> "Tag":
        """
        Convert database model to domain entity.
        
        Returns:
            Tag: Domain entity
        """
        from src.domain.entities.tag import Tag
        
        return Tag(
            id=self.id,
            name=self.name,
            color=self.color,
            user_id=self.user_id,
            created_at=self.created_at,
            updated_at=self.updated_at,
        )
    
    @classmethod
    def from_domain_entity(cls, tag: "Tag") -> "TagModel":
        """
        Create database model from domain entity.
        
        Args:
            tag: Domain entity
            
        Returns:
            TagModel: Database model
        """
        return cls(
            id=tag.id,
            name=tag.name,
            color=tag.color,
            user_id=tag.user_id,
            created_at=tag.created_at,
            updated_at=tag.updated_at,
        )
