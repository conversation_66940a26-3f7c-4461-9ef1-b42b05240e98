# Lonors AI Platform - Visual Architecture Diagrams

## System Architecture Overview

### High-Level System Architecture

```mermaid
graph TB
    subgraph "Client Layer"
        A[Web Browser]
        B[Mobile App]
        C[Desktop App]
    end
    
    subgraph "Load Balancer"
        D[Nginx]
    end
    
    subgraph "Frontend Services"
        E[React App<br/>Port 5500]
        F[Static Assets]
        G[Service Worker]
    end
    
    subgraph "Backend Services"
        H[FastAPI<br/>Port 3001]
        I[WebSocket Server]
        J[Protocol Handlers]
    end
    
    subgraph "AI Services"
        K[CopilotKit Agents]
        L[AG2/LangGraph]
        M[Local Models]
        N[Graphiti Knowledge Graph]
    end
    
    subgraph "Data Layer"
        O[PostgreSQL<br/>Port 5432]
        P[Redis Cache<br/>Port 6379]
        Q[File Storage]
    end
    
    subgraph "Infrastructure"
        R[Docker Containers]
        S[Monitoring Stack]
        T[CI/CD Pipeline]
    end
    
    A --> D
    B --> D
    C --> D
    D --> E
    E --> H
    E --> I
    H --> J
    J --> K
    J --> L
    K --> M
    L --> N
    H --> O
    H --> P
    I --> P
    R --> S
    T --> R
```

### Feature Slice Design Architecture

```mermaid
graph TB
    subgraph "App Layer"
        A1[App.tsx]
        A2[Router]
        A3[Providers]
        A4[Global Config]
    end
    
    subgraph "Pages Layer"
        P1[Dashboard]
        P2[Agent Management]
        P3[Flow Builder]
        P4[Knowledge Graph]
        P5[Settings]
    end
    
    subgraph "Widgets Layer"
        W1[Header]
        W2[Sidebar]
        W3[Stats Cards]
        W4[Activity Feed]
        W5[Quick Actions]
    end
    
    subgraph "Features Layer"
        F1[Authentication]
        F2[Agent Management]
        F3[Protocol Integration]
        F4[Flow Builder]
        F5[Knowledge Graph]
        F6[Model Management]
    end
    
    subgraph "Entities Layer"
        E1[User]
        E2[Agent]
        E3[Protocol]
        E4[Session]
        E5[Workflow]
    end
    
    subgraph "Shared Layer"
        S1[UI Components]
        S2[API Client]
        S3[Utils]
        S4[Types]
        S5[Hooks]
        S6[Store]
    end
    
    A1 --> P1
    A1 --> P2
    P1 --> W1
    P1 --> W3
    P2 --> W1
    P2 --> W2
    W1 --> F1
    W3 --> F2
    F1 --> E1
    F2 --> E2
    F3 --> E3
    E1 --> S1
    E2 --> S2
    F1 --> S3
    F2 --> S4
```

### Clean Architecture Backend

```mermaid
graph TB
    subgraph "Presentation Layer"
        PR1[FastAPI Routes]
        PR2[Middleware]
        PR3[Dependencies]
        PR4[WebSocket Handlers]
    end
    
    subgraph "Application Layer"
        AP1[User Service]
        AP2[Agent Service]
        AP3[MCP Service]
        AP4[A2A Service]
        AP5[AG-UI Service]
    end
    
    subgraph "Domain Layer"
        DO1[User Entity]
        DO2[Agent Entity]
        DO3[Context Entity]
        DO4[Message Entity]
        DO5[Repository Interfaces]
        DO6[Domain Services]
    end
    
    subgraph "Infrastructure Layer"
        IN1[Database Models]
        IN2[Repository Implementations]
        IN3[Cache Service]
        IN4[Security Service]
        IN5[External APIs]
        IN6[Configuration]
    end
    
    PR1 --> AP1
    PR2 --> AP2
    PR3 --> AP3
    PR4 --> AP4
    AP1 --> DO1
    AP2 --> DO2
    AP3 --> DO3
    AP4 --> DO4
    AP5 --> DO5
    DO5 --> IN1
    DO6 --> IN2
    IN2 --> IN3
    IN3 --> IN4
    IN4 --> IN5
    IN5 --> IN6
```

### Protocol Integration Flow

```mermaid
sequenceDiagram
    participant U as User
    participant F as Frontend
    participant B as Backend
    participant M as MCP Service
    participant A as A2A Service
    participant G as AG-UI Service
    participant AI as AI Model
    participant KG as Knowledge Graph
    
    U->>F: Request AI Interaction
    F->>B: WebSocket Connection
    B->>M: Initialize MCP Context
    M->>AI: Send Prompt
    AI-->>M: Generate Response
    M->>KG: Update Knowledge
    M-->>B: Context Response
    B->>G: Generate UI Components
    G-->>B: UI Description
    B->>A: Trigger Agent Actions
    A-->>B: Action Results
    B-->>F: Combined Response
    F-->>U: Updated Interface
```

### Data Flow Architecture

```mermaid
graph LR
    subgraph "Input Layer"
        I1[User Input]
        I2[API Requests]
        I3[WebSocket Messages]
        I4[File Uploads]
    end
    
    subgraph "Validation Layer"
        V1[Frontend Validation]
        V2[Backend Validation]
        V3[Schema Validation]
        V4[Security Checks]
    end
    
    subgraph "Processing Layer"
        P1[Business Logic]
        P2[Protocol Handlers]
        P3[AI Processing]
        P4[Data Transformation]
    end
    
    subgraph "Storage Layer"
        S1[Database Write]
        S2[Cache Update]
        S3[File Storage]
        S4[Session Storage]
    end
    
    subgraph "Output Layer"
        O1[API Response]
        O2[WebSocket Broadcast]
        O3[UI Updates]
        O4[Notifications]
    end
    
    I1 --> V1
    I2 --> V2
    I3 --> V3
    I4 --> V4
    V1 --> P1
    V2 --> P2
    V3 --> P3
    V4 --> P4
    P1 --> S1
    P2 --> S2
    P3 --> S3
    P4 --> S4
    S1 --> O1
    S2 --> O2
    S3 --> O3
    S4 --> O4
```

### CI/CD Pipeline Visualization

```mermaid
graph LR
    subgraph "Source Control"
        SC1[Git Push]
        SC2[Pull Request]
        SC3[Branch Protection]
    end
    
    subgraph "Build Stage"
        B1[Checkout Code]
        B2[Install Dependencies]
        B3[Type Check]
        B4[Lint & Format]
    end
    
    subgraph "Test Stage"
        T1[Unit Tests]
        T2[Integration Tests]
        T3[E2E Tests]
        T4[Coverage Report]
    end
    
    subgraph "Security Stage"
        S1[Dependency Scan]
        S2[SAST Analysis]
        S3[Container Scan]
        S4[License Check]
    end
    
    subgraph "Build & Package"
        BP1[Docker Build]
        BP2[Image Optimization]
        BP3[Registry Push]
        BP4[Artifact Storage]
    end
    
    subgraph "Deploy Stage"
        D1[Staging Deploy]
        D2[Smoke Tests]
        D3[Production Deploy]
        D4[Health Checks]
    end
    
    SC1 --> B1
    SC2 --> B2
    B2 --> B3
    B3 --> B4
    B4 --> T1
    T1 --> T2
    T2 --> T3
    T3 --> T4
    T4 --> S1
    S1 --> S2
    S2 --> S3
    S3 --> S4
    S4 --> BP1
    BP1 --> BP2
    BP2 --> BP3
    BP3 --> BP4
    BP4 --> D1
    D1 --> D2
    D2 --> D3
    D3 --> D4
```

### Database Schema Relationships

```mermaid
erDiagram
    USERS {
        uuid id PK
        string email UK
        string username UK
        string full_name
        string hashed_password
        enum role
        enum status
        boolean is_verified
        datetime last_login
        datetime created_at
        datetime updated_at
    }
    
    SESSIONS {
        uuid id PK
        uuid user_id FK
        string refresh_token
        datetime expires_at
        string user_agent
        string ip_address
        boolean is_active
        datetime created_at
        datetime updated_at
    }
    
    AGENTS {
        uuid id PK
        uuid user_id FK
        string name
        string description
        enum type
        jsonb configuration
        enum status
        datetime created_at
        datetime updated_at
    }
    
    CONTEXTS {
        uuid id PK
        uuid user_id FK
        uuid agent_id FK
        string title
        string description
        enum type
        string model_id
        int max_length
        int current_length
        datetime expires_at
        boolean is_expired
        boolean is_full
        datetime created_at
        datetime updated_at
    }
    
    MESSAGES {
        uuid id PK
        uuid context_id FK
        enum role
        text content
        jsonb metadata
        datetime created_at
    }
    
    UI_LAYOUTS {
        uuid id PK
        uuid user_id FK
        string name
        string description
        jsonb components
        enum status
        datetime created_at
        datetime updated_at
    }
    
    UI_COMPONENTS {
        uuid id PK
        uuid layout_id FK
        string name
        enum type
        jsonb properties
        jsonb events
        int order
        uuid parent_id FK
        datetime created_at
        datetime updated_at
    }
    
    WORKFLOWS {
        uuid id PK
        uuid user_id FK
        string name
        string description
        jsonb definition
        enum status
        datetime created_at
        datetime updated_at
    }
    
    WORKFLOW_EXECUTIONS {
        uuid id PK
        uuid workflow_id FK
        uuid user_id FK
        enum status
        jsonb input_data
        jsonb output_data
        jsonb error_data
        datetime started_at
        datetime completed_at
        datetime created_at
    }
    
    USERS ||--o{ SESSIONS : has
    USERS ||--o{ AGENTS : owns
    USERS ||--o{ CONTEXTS : creates
    USERS ||--o{ UI_LAYOUTS : designs
    USERS ||--o{ WORKFLOWS : creates
    AGENTS ||--o{ CONTEXTS : uses
    CONTEXTS ||--o{ MESSAGES : contains
    UI_LAYOUTS ||--o{ UI_COMPONENTS : contains
    UI_COMPONENTS ||--o{ UI_COMPONENTS : parent_child
    WORKFLOWS ||--o{ WORKFLOW_EXECUTIONS : executes
```

---

*Generated: 2024-12-30 | Mermaid Diagrams for Lonors AI Platform Architecture*
