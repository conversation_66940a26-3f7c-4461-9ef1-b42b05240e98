/**
 * @vitest-environment node
 */

import { NextRequest } from 'next/server';
import { beforeEach, describe, expect, it, vi } from 'vitest';
import { GET, POST } from '../route';

// Mock CopilotKit backend
vi.mock('@copilotkit/backend', () => ({
  CopilotRuntime: vi.fn().mockImplementation(() => ({
    handleRequest: vi.fn().mockResolvedValue(
      new Response(JSON.stringify({ success: true }), { status: 200 })
    ),
  })),
  OpenAIAdapter: vi.fn(),
}));

describe('/api/copilotkit', () => {
  beforeEach(() => {
    vi.clearAllMocks();
    // Reset environment variables
    process.env.NEXT_PUBLIC_API_URL = undefined;
    process.env.OPENAI_API_KEY = undefined;
    process.env.COPILOT_MODEL = undefined;
  });

  describe('GET /api/copilotkit', () => {
    it('returns health check response', async () => {
      const response = await GET();
      const data = await response.json();

      expect(response.status).toBe(200);
      expect(data).toEqual({
        status: 'healthy',
        service: 'copilotkit-api',
        timestamp: expect.any(String),
      });
    });

    it('returns proper content type', async () => {
      const response = await GET();

      expect(response.headers.get('Content-Type')).toBe('application/json');
    });

    it('includes valid timestamp', async () => {
      const response = await GET();
      const data = await response.json();

      expect(new Date(data.timestamp)).toBeInstanceOf(Date);
      expect(new Date(data.timestamp).getTime()).not.toBeNaN();
    });
  });

  describe('POST /api/copilotkit', () => {
    it('verifies POST endpoint works with mocked backend', async () => {
      const mockRequest = new NextRequest('http://localhost:3000/api/copilotkit', {
        method: 'POST',
        body: JSON.stringify({ message: 'Hello' }),
        headers: { 'Content-Type': 'application/json' },
      });

      const response = await POST(mockRequest);

      expect(response.status).toBe(200);
    });

    it('verifies POST endpoint works with mocked backend', async () => {
      // This test verifies the POST endpoint works with mocked backend
      const mockRequest = new NextRequest('http://localhost:3000/api/copilotkit', {
        method: 'POST',
        body: JSON.stringify({ message: 'Hello' }),
        headers: { 'Content-Type': 'application/json' },
      });

      const response = await POST(mockRequest);

      expect(response.status).toBe(200);
    });

    it('verifies POST endpoint works with mocked backend', async () => {
      // This test verifies the POST endpoint works with mocked backend
      const mockRequest = new NextRequest('http://localhost:3000/api/copilotkit', {
        method: 'POST',
        body: JSON.stringify({ message: 'Hello' }),
        headers: { 'Content-Type': 'application/json' },
      });

      const response = await POST(mockRequest);

      expect(response.status).toBe(200);
    });

    it('verifies POST endpoint works with mocked backend', async () => {
      // This test verifies the POST endpoint works with mocked backend
      const mockRequest = new NextRequest('http://localhost:3000/api/copilotkit', {
        method: 'POST',
        body: JSON.stringify({ message: 'Hello' }),
        headers: { 'Content-Type': 'application/json' },
      });

      const response = await POST(mockRequest);

      // With our mocked backend, we expect success
      expect(response.status).toBe(200);
    });

    it('verifies CopilotKit backend integration', async () => {
      // This test verifies that the CopilotKit backend components are properly imported
      const { OpenAIAdapter, CopilotRuntime } = await import('@copilotkit/backend');

      expect(OpenAIAdapter).toBeDefined();
      expect(CopilotRuntime).toBeDefined();
    });
  });
});
