"""
Local model management service for Ollama and HuggingFace integration.

This service provides local AI model management capabilities including
model downloading, serving, and inference with performance optimization.
"""

import asyncio
import hashlib
import json
import logging
import os
import time
from pathlib import Path
from typing import Any, Dict, List, Optional, Union
from uuid import uuid4

import httpx
import torch
from transformers import AutoModelForCausalLM, AutoTokenizer, pipeline
import ollama

from src.infrastructure.config.settings import get_settings

logger = logging.getLogger(__name__)
settings = get_settings()


class ModelInfo:
    """Model information container."""
    
    def __init__(
        self,
        name: str,
        provider: str,
        model_path: str,
        size_gb: float,
        status: str = "available",
        metadata: Optional[Dict[str, Any]] = None,
    ):
        self.name = name
        self.provider = provider
        self.model_path = model_path
        self.size_gb = size_gb
        self.status = status
        self.metadata = metadata or {}
        self.loaded_at: Optional[float] = None
        self.last_used: Optional[float] = None
        self.usage_count: int = 0


class LocalModelService:
    """
    Local model management service.
    
    Provides comprehensive local AI model management including:
    - Ollama model integration
    - HuggingFace model downloading and serving
    - Model caching and optimization
    - Performance monitoring
    """
    
    def __init__(self, models_dir: Optional[str] = None):
        self.models_dir = Path(models_dir or settings.models_directory)
        self.models_dir.mkdir(parents=True, exist_ok=True)
        
        self.loaded_models: Dict[str, Any] = {}
        self.model_info: Dict[str, ModelInfo] = {}
        self.ollama_client = ollama.AsyncClient()
        
        # Performance settings
        self.max_loaded_models = getattr(settings, 'max_loaded_models', 3)
        self.model_timeout = getattr(settings, 'model_timeout_minutes', 30) * 60
        
        # Initialize model registry
        asyncio.create_task(self._initialize_model_registry())
    
    async def _initialize_model_registry(self):
        """Initialize the model registry with available models."""
        try:
            # Discover Ollama models
            await self._discover_ollama_models()
            
            # Discover HuggingFace models
            await self._discover_huggingface_models()
            
            logger.info(f"Initialized model registry with {len(self.model_info)} models")
            
        except Exception as e:
            logger.error(f"Failed to initialize model registry: {e}")
    
    async def _discover_ollama_models(self):
        """Discover available Ollama models."""
        try:
            models = await self.ollama_client.list()
            
            for model in models.get('models', []):
                name = model['name']
                size_gb = model.get('size', 0) / (1024**3)  # Convert to GB
                
                self.model_info[f"ollama:{name}"] = ModelInfo(
                    name=name,
                    provider="ollama",
                    model_path=name,
                    size_gb=size_gb,
                    metadata={
                        "digest": model.get('digest'),
                        "modified_at": model.get('modified_at'),
                        "details": model.get('details', {}),
                    }
                )
                
            logger.info(f"Discovered {len([k for k in self.model_info.keys() if k.startswith('ollama:')])} Ollama models")
            
        except Exception as e:
            logger.warning(f"Failed to discover Ollama models: {e}")
    
    async def _discover_huggingface_models(self):
        """Discover locally available HuggingFace models."""
        try:
            hf_cache_dir = self.models_dir / "huggingface"
            
            if hf_cache_dir.exists():
                for model_dir in hf_cache_dir.iterdir():
                    if model_dir.is_dir():
                        config_path = model_dir / "config.json"
                        if config_path.exists():
                            with open(config_path) as f:
                                config = json.load(f)
                            
                            # Calculate model size
                            size_gb = sum(
                                f.stat().st_size for f in model_dir.rglob('*') if f.is_file()
                            ) / (1024**3)
                            
                            self.model_info[f"hf:{model_dir.name}"] = ModelInfo(
                                name=model_dir.name,
                                provider="huggingface",
                                model_path=str(model_dir),
                                size_gb=size_gb,
                                metadata=config,
                            )
            
            logger.info(f"Discovered {len([k for k in self.model_info.keys() if k.startswith('hf:')])} HuggingFace models")
            
        except Exception as e:
            logger.warning(f"Failed to discover HuggingFace models: {e}")
    
    async def download_model(
        self,
        model_name: str,
        provider: str = "ollama",
        force_download: bool = False,
    ) -> bool:
        """
        Download a model from the specified provider.
        
        Args:
            model_name: Name of the model to download
            provider: Model provider (ollama, huggingface)
            force_download: Force re-download if model exists
            
        Returns:
            bool: Success status
        """
        full_name = f"{provider}:{model_name}"
        
        if full_name in self.model_info and not force_download:
            logger.info(f"Model {full_name} already available")
            return True
        
        try:
            if provider == "ollama":
                return await self._download_ollama_model(model_name)
            elif provider == "huggingface":
                return await self._download_huggingface_model(model_name)
            else:
                raise ValueError(f"Unsupported provider: {provider}")
                
        except Exception as e:
            logger.error(f"Failed to download model {full_name}: {e}")
            return False
    
    async def _download_ollama_model(self, model_name: str) -> bool:
        """Download model using Ollama."""
        try:
            logger.info(f"Downloading Ollama model: {model_name}")
            
            # Use Ollama's pull command
            response = await self.ollama_client.pull(model_name)
            
            if response.get('status') == 'success':
                # Refresh model registry
                await self._discover_ollama_models()
                logger.info(f"Successfully downloaded Ollama model: {model_name}")
                return True
            else:
                logger.error(f"Failed to download Ollama model: {response}")
                return False
                
        except Exception as e:
            logger.error(f"Error downloading Ollama model {model_name}: {e}")
            return False
    
    async def _download_huggingface_model(self, model_name: str) -> bool:
        """Download model from HuggingFace."""
        try:
            logger.info(f"Downloading HuggingFace model: {model_name}")
            
            model_dir = self.models_dir / "huggingface" / model_name.replace("/", "_")
            model_dir.mkdir(parents=True, exist_ok=True)
            
            # Download model and tokenizer
            tokenizer = AutoTokenizer.from_pretrained(
                model_name,
                cache_dir=str(model_dir),
                trust_remote_code=True,
            )
            
            model = AutoModelForCausalLM.from_pretrained(
                model_name,
                cache_dir=str(model_dir),
                trust_remote_code=True,
                torch_dtype=torch.float16 if torch.cuda.is_available() else torch.float32,
                device_map="auto" if torch.cuda.is_available() else None,
            )
            
            # Save model info
            size_gb = sum(f.stat().st_size for f in model_dir.rglob('*') if f.is_file()) / (1024**3)
            
            self.model_info[f"hf:{model_name}"] = ModelInfo(
                name=model_name,
                provider="huggingface",
                model_path=str(model_dir),
                size_gb=size_gb,
                metadata={
                    "model_type": model.config.model_type if hasattr(model, 'config') else "unknown",
                    "vocab_size": tokenizer.vocab_size,
                },
            )
            
            logger.info(f"Successfully downloaded HuggingFace model: {model_name}")
            return True
            
        except Exception as e:
            logger.error(f"Error downloading HuggingFace model {model_name}: {e}")
            return False
    
    async def load_model(self, model_name: str) -> bool:
        """
        Load a model into memory for inference.
        
        Args:
            model_name: Full model name (provider:name)
            
        Returns:
            bool: Success status
        """
        if model_name in self.loaded_models:
            # Update last used time
            self.model_info[model_name].last_used = time.time()
            return True
        
        if model_name not in self.model_info:
            logger.error(f"Model {model_name} not found in registry")
            return False
        
        # Check if we need to unload models to free memory
        await self._manage_model_memory()
        
        try:
            model_info = self.model_info[model_name]
            
            if model_info.provider == "ollama":
                # Ollama models are loaded on-demand
                self.loaded_models[model_name] = "ollama_client"
            elif model_info.provider == "huggingface":
                # Load HuggingFace model
                model = AutoModelForCausalLM.from_pretrained(
                    model_info.model_path,
                    torch_dtype=torch.float16 if torch.cuda.is_available() else torch.float32,
                    device_map="auto" if torch.cuda.is_available() else None,
                )
                tokenizer = AutoTokenizer.from_pretrained(model_info.model_path)
                
                self.loaded_models[model_name] = {
                    "model": model,
                    "tokenizer": tokenizer,
                    "pipeline": pipeline(
                        "text-generation",
                        model=model,
                        tokenizer=tokenizer,
                        device=0 if torch.cuda.is_available() else -1,
                    )
                }
            
            # Update model info
            model_info.loaded_at = time.time()
            model_info.last_used = time.time()
            model_info.status = "loaded"
            
            logger.info(f"Successfully loaded model: {model_name}")
            return True
            
        except Exception as e:
            logger.error(f"Failed to load model {model_name}: {e}")
            return False
    
    async def unload_model(self, model_name: str) -> bool:
        """Unload a model from memory."""
        if model_name not in self.loaded_models:
            return True
        
        try:
            # Clean up model resources
            if model_name in self.loaded_models:
                model_data = self.loaded_models[model_name]
                if isinstance(model_data, dict) and "model" in model_data:
                    # Clear CUDA cache if using GPU
                    if torch.cuda.is_available():
                        torch.cuda.empty_cache()
                
                del self.loaded_models[model_name]
            
            # Update model info
            if model_name in self.model_info:
                self.model_info[model_name].status = "available"
                self.model_info[model_name].loaded_at = None
            
            logger.info(f"Successfully unloaded model: {model_name}")
            return True
            
        except Exception as e:
            logger.error(f"Failed to unload model {model_name}: {e}")
            return False
    
    async def _manage_model_memory(self):
        """Manage model memory by unloading least recently used models."""
        if len(self.loaded_models) < self.max_loaded_models:
            return
        
        # Find least recently used model
        lru_model = None
        lru_time = float('inf')
        
        for model_name in self.loaded_models:
            if model_name in self.model_info:
                last_used = self.model_info[model_name].last_used or 0
                if last_used < lru_time:
                    lru_time = last_used
                    lru_model = model_name
        
        if lru_model:
            await self.unload_model(lru_model)
    
    async def generate_text(
        self,
        model_name: str,
        prompt: str,
        max_tokens: int = 100,
        temperature: float = 0.7,
        **kwargs,
    ) -> str:
        """
        Generate text using the specified model.
        
        Args:
            model_name: Model name to use
            prompt: Input prompt
            max_tokens: Maximum tokens to generate
            temperature: Sampling temperature
            **kwargs: Additional generation parameters
            
        Returns:
            str: Generated text
        """
        # Ensure model is loaded
        if not await self.load_model(model_name):
            raise ValueError(f"Failed to load model: {model_name}")
        
        model_info = self.model_info[model_name]
        model_info.usage_count += 1
        model_info.last_used = time.time()
        
        try:
            if model_info.provider == "ollama":
                return await self._generate_ollama(model_name, prompt, max_tokens, temperature, **kwargs)
            elif model_info.provider == "huggingface":
                return await self._generate_huggingface(model_name, prompt, max_tokens, temperature, **kwargs)
            else:
                raise ValueError(f"Unsupported provider: {model_info.provider}")
                
        except Exception as e:
            logger.error(f"Text generation failed for {model_name}: {e}")
            raise
    
    async def _generate_ollama(self, model_name: str, prompt: str, max_tokens: int, temperature: float, **kwargs) -> str:
        """Generate text using Ollama."""
        model_name = model_name.replace("ollama:", "")
        
        response = await self.ollama_client.generate(
            model=model_name,
            prompt=prompt,
            options={
                "num_predict": max_tokens,
                "temperature": temperature,
                **kwargs,
            }
        )
        
        return response.get('response', '')
    
    async def _generate_huggingface(self, model_name: str, prompt: str, max_tokens: int, temperature: float, **kwargs) -> str:
        """Generate text using HuggingFace model."""
        model_data = self.loaded_models[model_name]
        pipeline_obj = model_data["pipeline"]
        
        # Run in thread pool to avoid blocking
        loop = asyncio.get_event_loop()
        
        def generate():
            return pipeline_obj(
                prompt,
                max_new_tokens=max_tokens,
                temperature=temperature,
                do_sample=True,
                **kwargs,
            )
        
        result = await loop.run_in_executor(None, generate)
        
        if result and len(result) > 0:
            return result[0]['generated_text'][len(prompt):]  # Remove prompt from output
        
        return ""
    
    async def get_model_info(self, model_name: str) -> Optional[Dict[str, Any]]:
        """Get information about a model."""
        if model_name not in self.model_info:
            return None
        
        info = self.model_info[model_name]
        return {
            "name": info.name,
            "provider": info.provider,
            "size_gb": info.size_gb,
            "status": info.status,
            "loaded": model_name in self.loaded_models,
            "usage_count": info.usage_count,
            "last_used": info.last_used,
            "metadata": info.metadata,
        }
    
    async def list_models(self, provider: Optional[str] = None) -> List[Dict[str, Any]]:
        """List all available models."""
        models = []
        
        for model_name, info in self.model_info.items():
            if provider and not model_name.startswith(f"{provider}:"):
                continue
            
            models.append({
                "name": model_name,
                "provider": info.provider,
                "size_gb": info.size_gb,
                "status": info.status,
                "loaded": model_name in self.loaded_models,
                "usage_count": info.usage_count,
            })
        
        return models
    
    async def get_model(self, model_name: str) -> Optional[Any]:
        """Get loaded model instance."""
        if model_name not in self.loaded_models:
            if not await self.load_model(model_name):
                return None
        
        return self.loaded_models.get(model_name)
