"""
Tag Service Module

This module implements the application service for tags.
"""

from uuid import UUID

from src.application.dto.tag_dto import CreateTagDTO, TagDTO, UpdateTagDTO
from src.domain.entities.tag import Tag
from src.domain.repositories.tag_repository import TagRepository


class TagService:
    """Service for managing tags."""

    def __init__(self, tag_repository: TagRepository):
        """
        Initialize the tag service.

        Args:
            tag_repository: Repository for tag operations
        """
        self._tag_repository = tag_repository

    async def get_tag(self, tag_id: UUID, user_id: UUID) -> TagDTO | None:
        """
        Get a tag by ID.

        Args:
            tag_id: ID of the tag to retrieve
            user_id: ID of the user making the request

        Returns:
            The tag if found and owned by the user, None otherwise
        """
        tag = await self._tag_repository.get_by_id(tag_id)

        if tag is None or tag.user_id != user_id:
            return None

        return self._to_tag_dto(tag)

    async def get_tags(self, user_id: UUID) -> list[TagDTO]:
        """
        Get all tags for a user.

        Args:
            user_id: ID of the user

        Returns:
            List of tags for the user
        """
        tags = await self._tag_repository.get_all_by_user(user_id)
        return [self._to_tag_dto(tag) for tag in tags]

    async def create_tag(self, user_id: UUID, create_dto: CreateTagDTO) -> TagDTO:
        """
        Create a new tag.

        Args:
            user_id: ID of the user creating the tag
            create_dto: Data for the new tag

        Returns:
            The created tag
        """
        # Check if tag with same name already exists
        existing_tag = await self._tag_repository.get_by_name(user_id, create_dto.name)
        if existing_tag:
            return self._to_tag_dto(existing_tag)

        tag = Tag(
            name=create_dto.name,
            color=create_dto.color,
            user_id=user_id,
        )

        created_tag = await self._tag_repository.create(tag)
        return self._to_tag_dto(created_tag)

    async def update_tag(
        self, tag_id: UUID, user_id: UUID, update_dto: UpdateTagDTO
    ) -> TagDTO | None:
        """
        Update an existing tag.

        Args:
            tag_id: ID of the tag to update
            user_id: ID of the user making the update
            update_dto: Data to update

        Returns:
            The updated tag if found and owned by the user, None otherwise
        """
        tag = await self._tag_repository.get_by_id(tag_id)

        if tag is None or tag.user_id != user_id:
            return None

        # Update fields if provided
        if update_dto.name is not None:
            tag.update_name(update_dto.name)

        if update_dto.color is not None:
            tag.update_color(update_dto.color)

        updated_tag = await self._tag_repository.update(tag)
        return self._to_tag_dto(updated_tag)

    async def delete_tag(self, tag_id: UUID, user_id: UUID) -> bool:
        """
        Delete a tag.

        Args:
            tag_id: ID of the tag to delete
            user_id: ID of the user making the request

        Returns:
            True if the tag was deleted, False otherwise
        """
        tag = await self._tag_repository.get_by_id(tag_id)

        if tag is None or tag.user_id != user_id:
            return False

        return await self._tag_repository.delete(tag_id)

    def _to_tag_dto(self, tag: Tag) -> TagDTO:
        """
        Convert a domain Tag entity to a TagDTO.

        Args:
            tag: The domain entity

        Returns:
            The DTO representation
        """
        return TagDTO(
            id=tag.id,
            name=tag.name,
            color=tag.color,
            user_id=tag.user_id,
            created_at=tag.created_at,
        )
