"""
TDD Tests for User Domain Entity - RED PHASE

This module contains comprehensive tests for the User domain entity
following TDD methodology. These tests will initially fail and drive
the implementation of the User entity.
"""

from datetime import datetime
from uuid import uuid4

import pytest

# These imports will fail initially - this is the RED phase
try:
    from src.domain.entities.user import User, UserStatus
    from src.domain.exceptions import DomainValidationError
    from src.domain.value_objects.email import Email
    from src.domain.value_objects.username import Username
except ImportError:
    # Expected during RED phase
    pass


class TestUserEntity:
    """Test suite for User domain entity."""

    @pytest.mark.unit
    def test_user_creation_with_valid_data(self, sample_user_data):
        """Test creating a user with valid data."""
        # RED PHASE: This test will fail because User entity doesn't exist yet
        user = User(
            id=sample_user_data["id"],
            email=Email(sample_user_data["email"]),
            username=Username(sample_user_data["username"]),
            full_name=sample_user_data["full_name"],
            hashed_password="hashed_password_123",
            is_active=sample_user_data["is_active"],
            is_verified=sample_user_data["is_verified"],
        )

        assert user.id == sample_user_data["id"]
        assert user.email.value == sample_user_data["email"]
        assert user.username.value == sample_user_data["username"]
        assert user.full_name == sample_user_data["full_name"]
        assert user.is_active == sample_user_data["is_active"]
        assert user.is_verified == sample_user_data["is_verified"]
        assert user.status == UserStatus.ACTIVE
        assert isinstance(user.created_at, datetime)
        assert isinstance(user.updated_at, datetime)

    @pytest.mark.unit
    def test_user_creation_with_invalid_email(self):
        """Test user creation fails with invalid email."""
        # RED PHASE: This test will fail because validation doesn't exist yet
        with pytest.raises(DomainValidationError, match="Invalid email format"):
            User(
                id=str(uuid4()),
                email=Email("invalid-email"),
                username=Username("testuser"),
                full_name="Test User",
                hashed_password="hashed_password_123",
            )

    @pytest.mark.unit
    def test_user_creation_with_invalid_username(self):
        """Test user creation fails with invalid username."""
        # RED PHASE: This test will fail because validation doesn't exist yet
        with pytest.raises(DomainValidationError, match="Invalid username"):
            User(
                id=str(uuid4()),
                email=Email("<EMAIL>"),
                username=Username("ab"),  # Too short
                full_name="Test User",
                hashed_password="hashed_password_123",
            )

    @pytest.mark.unit
    def test_user_creation_with_empty_full_name(self):
        """Test user creation fails with empty full name."""
        # RED PHASE: This test will fail because validation doesn't exist yet
        with pytest.raises(DomainValidationError, match="Full name cannot be empty"):
            User(
                id=str(uuid4()),
                email=Email("<EMAIL>"),
                username=Username("testuser"),
                full_name="",  # Empty full name
                hashed_password="hashed_password_123",
            )

    @pytest.mark.unit
    def test_user_activate(self, sample_user_data):
        """Test user activation."""
        # RED PHASE: This test will fail because methods don't exist yet
        user = User(
            id=sample_user_data["id"],
            email=Email(sample_user_data["email"]),
            username=Username(sample_user_data["username"]),
            full_name=sample_user_data["full_name"],
            hashed_password="hashed_password_123",
            is_active=False,
        )

        user.activate()

        assert user.is_active is True
        assert user.status == UserStatus.ACTIVE

    @pytest.mark.unit
    def test_user_deactivate(self, sample_user_data):
        """Test user deactivation."""
        # RED PHASE: This test will fail because methods don't exist yet
        user = User(
            id=sample_user_data["id"],
            email=Email(sample_user_data["email"]),
            username=Username(sample_user_data["username"]),
            full_name=sample_user_data["full_name"],
            hashed_password="hashed_password_123",
            is_active=True,
        )

        user.deactivate()

        assert user.is_active is False
        assert user.status == UserStatus.INACTIVE

    @pytest.mark.unit
    def test_user_verify_email(self, sample_user_data):
        """Test email verification."""
        # RED PHASE: This test will fail because methods don't exist yet
        user = User(
            id=sample_user_data["id"],
            email=Email(sample_user_data["email"]),
            username=Username(sample_user_data["username"]),
            full_name=sample_user_data["full_name"],
            hashed_password="hashed_password_123",
            is_verified=False,
        )

        user.verify_email()

        assert user.is_verified is True

    @pytest.mark.unit
    def test_user_update_profile(self, sample_user_data):
        """Test updating user profile."""
        # RED PHASE: This test will fail because methods don't exist yet
        user = User(
            id=sample_user_data["id"],
            email=Email(sample_user_data["email"]),
            username=Username(sample_user_data["username"]),
            full_name=sample_user_data["full_name"],
            hashed_password="hashed_password_123",
        )

        new_full_name = "Updated Full Name"
        user.update_profile(full_name=new_full_name)

        assert user.full_name == new_full_name
        assert user.updated_at > user.created_at

    @pytest.mark.unit
    def test_user_change_password(self, sample_user_data):
        """Test changing user password."""
        # RED PHASE: This test will fail because methods don't exist yet
        user = User(
            id=sample_user_data["id"],
            email=Email(sample_user_data["email"]),
            username=Username(sample_user_data["username"]),
            full_name=sample_user_data["full_name"],
            hashed_password="old_hashed_password",
        )

        new_hashed_password = "new_hashed_password"
        user.change_password(new_hashed_password)

        assert user.hashed_password == new_hashed_password
        assert user.updated_at > user.created_at

    @pytest.mark.unit
    def test_user_equality(self, sample_user_data):
        """Test user equality comparison."""
        # RED PHASE: This test will fail because equality methods don't exist yet
        user1 = User(
            id=sample_user_data["id"],
            email=Email(sample_user_data["email"]),
            username=Username(sample_user_data["username"]),
            full_name=sample_user_data["full_name"],
            hashed_password="hashed_password_123",
        )

        user2 = User(
            id=sample_user_data["id"],  # Same ID
            email=Email("<EMAIL>"),
            username=Username("different"),
            full_name="Different Name",
            hashed_password="different_password",
        )

        user3 = User(
            id=str(uuid4()),  # Different ID
            email=Email(sample_user_data["email"]),
            username=Username(sample_user_data["username"]),
            full_name=sample_user_data["full_name"],
            hashed_password="hashed_password_123",
        )

        assert user1 == user2  # Same ID
        assert user1 != user3  # Different ID

    @pytest.mark.unit
    def test_user_string_representation(self, sample_user_data):
        """Test user string representation."""
        # RED PHASE: This test will fail because __str__ method doesn't exist yet
        user = User(
            id=sample_user_data["id"],
            email=Email(sample_user_data["email"]),
            username=Username(sample_user_data["username"]),
            full_name=sample_user_data["full_name"],
            hashed_password="hashed_password_123",
        )

        expected_str = f"User(id={user.id}, username={user.username.value}, email={user.email.value})"
        assert str(user) == expected_str

    @pytest.mark.unit
    def test_user_to_dict(self, sample_user_data):
        """Test user serialization to dictionary."""
        # RED PHASE: This test will fail because to_dict method doesn't exist yet
        user = User(
            id=sample_user_data["id"],
            email=Email(sample_user_data["email"]),
            username=Username(sample_user_data["username"]),
            full_name=sample_user_data["full_name"],
            hashed_password="hashed_password_123",
        )

        user_dict = user.to_dict()

        assert user_dict["id"] == user.id
        assert user_dict["email"] == user.email.value
        assert user_dict["username"] == user.username.value
        assert user_dict["full_name"] == user.full_name
        assert user_dict["is_active"] == user.is_active
        assert user_dict["is_verified"] == user.is_verified
        assert "hashed_password" not in user_dict  # Should not expose password

    @pytest.mark.unit
    def test_user_domain_events(self, sample_user_data):
        """Test user domain events are raised correctly."""
        # RED PHASE: This test will fail because domain events don't exist yet
        user = User(
            id=sample_user_data["id"],
            email=Email(sample_user_data["email"]),
            username=Username(sample_user_data["username"]),
            full_name=sample_user_data["full_name"],
            hashed_password="hashed_password_123",
            is_active=False,  # Start with inactive user
            is_verified=False,  # Start with unverified user
        )

        # Clear any existing events
        user.clear_events()

        # Perform actions that should raise events
        user.verify_email()
        user.activate()

        events = user.get_events()
        assert len(events) == 2
        assert any(
            event.__class__.__name__ == "UserEmailVerifiedEvent" for event in events
        )
        assert any(event.__class__.__name__ == "UserActivatedEvent" for event in events)
