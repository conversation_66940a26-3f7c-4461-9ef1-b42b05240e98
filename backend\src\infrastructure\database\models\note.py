"""
Note database model.

This module defines the SQLAlchemy model for notes
with proper database constraints and relationships.
"""

import uuid
from typing import TYPE_CHECKING

from sqlalchemy import <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>ey, String, Text
from sqlalchemy.dialects.postgresql import UUID
from sqlalchemy.orm import Mapped, mapped_column, relationship

from src.domain.entities.note import NoteFormat
from src.infrastructure.database.models.base import Base

if TYPE_CHECKING:
    from src.domain.entities.note import Note
    from src.infrastructure.database.models.folder import FolderModel
    from src.infrastructure.database.models.user import UserModel


class NoteModel(Base):
    """
    Note SQLAlchemy model.

    Represents the notes table in the database with all
    necessary constraints and relationships.
    """

    # Note identification
    title: Mapped[str] = mapped_column(
        String(255), nullable=False, index=True, doc="Note title"
    )

    # Content fields
    content: Mapped[str] = mapped_column(Text, nullable=False, doc="Note content")

    content_format: Mapped[NoteFormat] = mapped_column(
        Enum(NoteFormat),
        default=NoteFormat.MARKDOWN,
        nullable=False,
        doc="Content format (markdown or richtext)",
    )

    content_version: Mapped[int] = mapped_column(
        default=1, nullable=False, doc="Content version number"
    )

    # Relationships
    folder_id: Mapped[uuid.UUID | None] = mapped_column(
        UUID(as_uuid=True),
        ForeignKey("folders.id", ondelete="SET NULL"),
        nullable=True,
        index=True,
        doc="Parent folder ID",
    )

    created_by: Mapped[uuid.UUID] = mapped_column(
        UUID(as_uuid=True),
        ForeignKey("users.id", ondelete="CASCADE"),
        nullable=False,
        index=True,
        doc="User who created the note",
    )

    last_edited_by: Mapped[uuid.UUID] = mapped_column(
        UUID(as_uuid=True),
        ForeignKey("users.id", ondelete="CASCADE"),
        nullable=False,
        index=True,
        doc="User who last edited the note",
    )

    # Status fields
    is_archived: Mapped[bool] = mapped_column(
        Boolean,
        default=False,
        nullable=False,
        index=True,
        doc="Whether note is archived",
    )

    is_starred: Mapped[bool] = mapped_column(
        Boolean,
        default=False,
        nullable=False,
        index=True,
        doc="Whether note is starred",
    )

    # Metadata
    tags: Mapped[list[str]] = mapped_column(
        JSON, default=list, nullable=False, doc="Note tags as JSON array"
    )

    # Relationships
    creator: Mapped["UserModel"] = relationship(
        "UserModel", foreign_keys=[created_by], back_populates="created_notes"
    )

    editor: Mapped["UserModel"] = relationship(
        "UserModel", foreign_keys=[last_edited_by], back_populates="edited_notes"
    )

    folder: Mapped["FolderModel"] = relationship("FolderModel", back_populates="notes")

    def __repr__(self) -> str:
        """String representation of the note."""
        return f"<NoteModel(id={self.id}, title={self.title[:30]}...)>"

    def to_domain_entity(self) -> "Note":
        """
        Convert database model to domain entity.

        Returns:
            Note: Domain entity
        """
        from src.domain.entities.note import Note, NoteContent

        return Note(
            id=self.id,
            title=self.title,
            content=NoteContent(
                content=self.content,
                format=self.content_format,
                version=self.content_version,
            ),
            folder_id=self.folder_id,
            tags=self.tags or [],
            is_archived=self.is_archived,
            is_starred=self.is_starred,
            created_at=self.created_at,
            updated_at=self.updated_at,
            created_by=self.created_by,
            last_edited_by=self.last_edited_by,
        )

    @classmethod
    def from_domain_entity(cls, note: "Note") -> "NoteModel":
        """
        Create database model from domain entity.

        Args:
            note: Domain entity

        Returns:
            NoteModel: Database model
        """
        return cls(
            id=note.id,
            title=note.title,
            content=note.content.content,
            content_format=note.content.format,
            content_version=note.content.version,
            folder_id=note.folder_id,
            tags=note.tags,
            is_archived=note.is_archived,
            is_starred=note.is_starred,
            created_at=note.created_at,
            updated_at=note.updated_at,
            created_by=note.created_by,
            last_edited_by=note.last_edited_by,
        )
