# API Client Standardization Guide

This guide provides detailed instructions for implementing a centralized API client factory to eliminate redundant API client initialization across the Lonors AI Platform frontend codebase.

## Table of Contents

1. [Overview](#1-overview)
2. [Implementation Steps](#2-implementation-steps)
3. [Usage Examples](#3-usage-examples)
4. [Migration Guide](#4-migration-guide)
5. [Testing](#5-testing)
6. [Advanced Configuration](#6-advanced-configuration)

## 1. Overview

### Current Redundancy

Currently, similar API client initialization appears across multiple files:

- `/frontend/src/shared/api/auth.ts`
- `/frontend/src/shared/api/agent.ts`
- `/frontend/src/features/agent-management/api/index.ts`
- `/frontend/src/features/knowledge-graph/api/index.ts`

### Solution

Implement a centralized API client factory that provides:

- Standardized configuration
- Authentication token handling
- Error handling
- Request/response interceptors
- Customization options for specific API endpoints

## 2. Implementation Steps

### Step 1: Create the API Client Factory

Create the API client factory in the shared API directory:

```bash
mkdir -p frontend/src/shared/api/core
touch frontend/src/shared/api/core/client.ts
```

### Step 2: Implement the API Client Factory

Implement the `createApiClient` function with the following functionality:

```typescript
// /frontend/src/shared/api/core/client.ts
import axios, { AxiosInstance, AxiosRequestConfig, AxiosResponse, AxiosError } from 'axios';

// Types for API client configuration
export interface ApiClientConfig extends AxiosRequestConfig {
  /**
   * Whether to include authentication token in requests
   * @default true
   */
  withAuth?: boolean;

  /**
   * Custom error handler
   */
  errorHandler?: (error: AxiosError) => Promise<never>;
}

/**
 * Default API client configuration
 */
const defaultConfig: ApiClientConfig = {
  baseURL: import.meta.env.VITE_API_URL || '/api',
  timeout: 10000,
  headers: {
    'Content-Type': 'application/json',
  },
  withAuth: true,
};

/**
 * Default error handler
 */
const defaultErrorHandler = (error: AxiosError): Promise<never> => {
  // Log error for debugging
  console.error('API Error:', error);

  // Handle specific error status codes
  if (error.response) {
    const status = error.response.status;

    // Handle unauthorized
    if (status === 401) {
      // Dispatch custom event for unauthorized
      window.dispatchEvent(new CustomEvent('auth:unauthorized'));
    }

    // Handle forbidden
    if (status === 403) {
      // Dispatch custom event for forbidden
      window.dispatchEvent(new CustomEvent('auth:forbidden'));
    }

    // Handle server error
    if (status >= 500) {
      // Dispatch custom event for server error
      window.dispatchEvent(new CustomEvent('api:server-error'));
    }
  }

  // Handle network errors
  if (error.code === 'ECONNABORTED' || error.message.includes('Network Error')) {
    window.dispatchEvent(new CustomEvent('api:network-error'));
  }

  return Promise.reject(error);
};

/**
 * Create an API client with custom configuration
 */
export const createApiClient = (config: ApiClientConfig = {}): AxiosInstance => {
  // Merge default config with custom config
  const mergedConfig: ApiClientConfig = {
    ...defaultConfig,
    ...config,
    headers: {
      ...defaultConfig.headers,
      ...config.headers,
    },
  };

  // Extract non-axios options
  const { withAuth = true, errorHandler, ...axiosConfig } = mergedConfig;

  // Create axios instance
  const instance = axios.create(axiosConfig);

  // Request interceptor for auth token
  instance.interceptors.request.use(
    (config) => {
      // Add auth token if withAuth is true
      if (withAuth) {
        const token = localStorage.getItem('auth_token');
        if (token) {
          config.headers = config.headers || {};
          config.headers.Authorization = `Bearer ${token}`;
        }
      }

      return config;
    },
    (error) => Promise.reject(error)
  );

  // Response interceptor for error handling
  instance.interceptors.response.use(
    (response: AxiosResponse) => response,
    (error: AxiosError) => {
      // Use custom error handler if provided, otherwise use default
      return (errorHandler || defaultErrorHandler)(error);
    }
  );

  return instance;
};

/**
 * Default API client instance
 */
export const apiClient = createApiClient();
```

### Step 3: Create API Response Types

Create standardized API response types:

```typescript
// /frontend/src/shared/api/core/types.ts
export interface ApiResponse<T> {
  data: T;
  message?: string;
  status: number;
}

export interface PaginatedApiResponse<T> {
  data: T[];
  total: number;
  page: number;
  pageSize: number;
  totalPages: number;
}

export interface ApiError {
  message: string;
  code?: string;
  details?: Record<string, string[]>;
}
```

### Step 4: Create API Utilities

Create utility functions for common API operations:

```typescript
// /frontend/src/shared/api/core/utils.ts
import { AxiosError, AxiosResponse } from 'axios';
import { ApiError } from './types';

/**
 * Extract error message from API error
 */
export const getErrorMessage = (error: unknown): string => {
  if (error instanceof AxiosError) {
    // Try to get error from response data
    const responseData = error.response?.data;

    if (responseData) {
      if (typeof responseData === 'string') {
        return responseData;
      }

      if (responseData.message) {
        return responseData.message;
      }

      if (responseData.error) {
        return responseData.error;
      }
    }

    // Fallback to error message
    return error.message;
  }

  // Handle non-axios errors
  if (error instanceof Error) {
    return error.message;
  }

  return 'An unknown error occurred';
};

/**
 * Parse API error response
 */
export const parseApiError = (error: unknown): ApiError => {
  const message = getErrorMessage(error);

  if (error instanceof AxiosError && error.response?.data) {
    const data = error.response.data;

    return {
      message,
      code: data.code || error.code,
      details: data.details || data.errors || undefined,
    };
  }

  return { message };
};

/**
 * Extract data from API response
 */
export const extractData = <T>(response: AxiosResponse): T => {
  // Handle different response formats
  if (response.data.data !== undefined) {
    return response.data.data;
  }

  return response.data;
};
```

### Step 5: Create Barrel Exports

Create an index file to export all API core functionality:

```typescript
// /frontend/src/shared/api/core/index.ts
export * from './client';
export * from './types';
export * from './utils';
```

## 3. Usage Examples

### Basic Usage

```typescript
// /frontend/src/features/authentication/api/auth-api.ts
import { apiClient, extractData, parseApiError } from '@/shared/api/core';

export interface LoginRequest {
  email: string;
  password: string;
}

export interface LoginResponse {
  token: string;
  user: {
    id: string;
    email: string;
    name: string;
  };
}

export const loginUser = async (data: LoginRequest): Promise<LoginResponse> => {
  try {
    const response = await apiClient.post('/auth/login', data);
    return extractData<LoginResponse>(response);
  } catch (error) {
    throw parseApiError(error);
  }
};
```

### Custom API Client

```typescript
// /frontend/src/features/agent-management/api/agent-api.ts
import { createApiClient, extractData, parseApiError } from '@/shared/api/core';

// Create a custom API client for agent endpoints
const agentApiClient = createApiClient({
  baseURL: `${import.meta.env.VITE_API_URL}/agents`,
  timeout: 20000, // Longer timeout for agent operations
});

export interface Agent {
  id: string;
  name: string;
  description: string;
  status: 'active' | 'inactive';
}

export const getAgents = async (): Promise<Agent[]> => {
  try {
    const response = await agentApiClient.get('/');
    return extractData<Agent[]>(response);
  } catch (error) {
    throw parseApiError(error);
  }
};

export const getAgentById = async (id: string): Promise<Agent> => {
  try {
    const response = await agentApiClient.get(`/${id}`);
    return extractData<Agent>(response);
  } catch (error) {
    throw parseApiError(error);
  }
};
```

### API Client with Custom Error Handling

```typescript
// /frontend/src/features/knowledge-graph/api/graph-api.ts
import { createApiClient, extractData } from '@/shared/api/core';
import { toast } from '@/shared/ui/toast';

// Create API client with custom error handling
const graphApiClient = createApiClient({
  baseURL: `${import.meta.env.VITE_API_URL}/knowledge-graph`,
  errorHandler: (error) => {
    // Show toast notification for errors
    toast({
      title: 'Knowledge Graph Error',
      description: error.response?.data?.message || error.message,
      variant: 'destructive',
    });

    // Rethrow the error
    return Promise.reject(error);
  },
});

export const getGraphData = async (graphId: string) => {
  const response = await graphApiClient.get(`/${graphId}`);
  return extractData(response);
};
```

## 4. Migration Guide

Follow these steps to migrate existing API clients to use the centralized factory:

### Step 1: Identify API Clients to Migrate

Identify all files that initialize axios instances or make direct API calls.

### Step 2: Create Feature-Specific API Modules

For each feature, create a dedicated API module:

```typescript
// /frontend/src/features/[feature-name]/api/index.ts
import { apiClient, extractData, parseApiError } from '@/shared/api/core';

// Define request/response types
export interface SomeRequest {
  // ...
}

export interface SomeResponse {
  // ...
}

// Implement API functions
export const someApiFunction = async (data: SomeRequest): Promise<SomeResponse> => {
  try {
    const response = await apiClient.post('/some-endpoint', data);
    return extractData<SomeResponse>(response);
  } catch (error) {
    throw parseApiError(error);
  }
};
```

### Step 3: Update Imports

Update all imports to use the new API modules:

```typescript
// Before
import axios from 'axios';
// ...
const response = await axios.post('/api/some-endpoint', data);

// After
import { someApiFunction } from '@/features/some-feature/api';
// ...
const result = await someApiFunction(data);
```

### Step 4: Test Thoroughly

Test each migrated API client to ensure:
- Authentication works correctly
- Error handling functions as expected
- Response data is correctly extracted
- Edge cases are handled properly

## 5. Testing

### Unit Testing the API Client

Create unit tests for the API client factory:

```typescript
// /frontend/src/shared/api/core/__tests__/client.test.ts
import { createApiClient } from '../client';
import axios from 'axios';

// Mock axios
jest.mock('axios', () => ({
  create: jest.fn(() => ({
    interceptors: {
      request: { use: jest.fn() },
      response: { use: jest.fn() },
    },
  })),
}));

describe('API Client', () => {
  beforeEach(() => {
    jest.clearAllMocks();
    localStorage.clear();
  });

  it('should create an axios instance with default config', () => {
    createApiClient();

    expect(axios.create).toHaveBeenCalledWith(expect.objectContaining({
      baseURL: expect.any(String),
      timeout: 10000,
      headers: expect.objectContaining({
        'Content-Type': 'application/json',
      }),
    }));
  });

  it('should merge custom config with defaults', () => {
    const customConfig = {
      baseURL: 'https://custom-api.example.com',
      timeout: 5000,
      headers: {
        'X-Custom-Header': 'custom-value',
      },
    };

    createApiClient(customConfig);

    expect(axios.create).toHaveBeenCalledWith(expect.objectContaining({
      baseURL: 'https://custom-api.example.com',
      timeout: 5000,
      headers: expect.objectContaining({
        'Content-Type': 'application/json',
        'X-Custom-Header': 'custom-value',
      }),
    }));
  });

  it('should add auth token to requests when withAuth is true', () => {
    // Set auth token in localStorage
    localStorage.setItem('auth_token', 'test-token');

    const instance = createApiClient();

    // Get the request interceptor function
    const requestInterceptor = instance.interceptors.request.use.mock.calls[0][0];

    // Create a mock request config
    const config = { headers: {} };

    // Call the interceptor
    const result = requestInterceptor(config);

    // Check that the token was added
    expect(result.headers.Authorization).toBe('Bearer test-token');
  });

  it('should not add auth token when withAuth is false', () => {
    // Set auth token in localStorage
    localStorage.setItem('auth_token', 'test-token');

    const instance = createApiClient({ withAuth: false });

    // Get the request interceptor function
    const requestInterceptor = instance.interceptors.request.use.mock.calls[0][0];

    // Create a mock request config
    const config = { headers: {} };

    // Call the interceptor
    const result = requestInterceptor(config);

    // Check that the token was not added
    expect(result.headers.Authorization).toBeUndefined();
  });
});
```

### Testing API Functions

Create tests for API functions:

```typescript
// /frontend/src/features/authentication/api/__tests__/auth-api.test.ts
import { loginUser } from '../auth-api';
import { apiClient } from '@/shared/api/core';

// Mock the API client
jest.mock('@/shared/api/core', () => ({
  apiClient: {
    post: jest.fn(),
  },
  extractData: jest.fn((response) => response.data),
  parseApiError: jest.fn((error) => ({ message: error.message })),
}));

describe('Auth API', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  it('should call login endpoint with correct data', async () => {
    // Mock successful response
    (apiClient.post as jest.Mock).mockResolvedValue({
      data: {
        token: 'test-token',
        user: {
          id: '123',
          email: '<EMAIL>',
          name: 'Test User',
        },
      },
    });

    const loginData = {
      email: '<EMAIL>',
      password: 'password123',
    };

    const result = await loginUser(loginData);

    // Check that the API was called correctly
    expect(apiClient.post).toHaveBeenCalledWith('/auth/login', loginData);

    // Check the result
    expect(result).toEqual({
      token: 'test-token',
      user: {
        id: '123',
        email: '<EMAIL>',
        name: 'Test User',
      },
    });
  });

  it('should handle login errors', async () => {
    // Mock error response
    const error = new Error('Invalid credentials');
    (apiClient.post as jest.Mock).mockRejectedValue(error);

    const loginData = {
      email: '<EMAIL>',
      password: 'wrong-password',
    };

    // Expect the function to throw
    await expect(loginUser(loginData)).rejects.toEqual({
      message: 'Invalid credentials',
    });
  });
});
```

## 6. Advanced Configuration

### Request Cancellation

Implement request cancellation for API calls:

```typescript
// /frontend/src/shared/api/core/cancellation.ts
import { CancelToken } from 'axios';

/**
 * Create a cancellable request
 */
export const createCancellableRequest = () => {
  const source = CancelToken.source();

  return {
    cancelToken: source.token,
    cancel: (message?: string) => source.cancel(message),
  };
};

// Usage example
export const searchWithCancellation = async (query: string) => {
  const { cancelToken, cancel } = createCancellableRequest();

  try {
    const response = await apiClient.get('/search', {
      params: { q: query },
      cancelToken,
    });

    return extractData(response);
  } catch (error) {
    if (axios.isCancel(error)) {
      // Request was cancelled
      console.log('Request cancelled:', error.message);
      return null;
    }

    throw parseApiError(error);
  }

  return { cancelRequest: cancel };
};
```

### Request Retries

Implement automatic retries for failed requests:

```typescript
// /frontend/src/shared/api/core/retry.ts
import { AxiosError, AxiosRequestConfig } from 'axios';
import { apiClient } from './client';

interface RetryConfig {
  maxRetries?: number;
  retryDelay?: number;
  retryStatusCodes?: number[];
}

const defaultRetryConfig: RetryConfig = {
  maxRetries: 3,
  retryDelay: 1000,
  retryStatusCodes: [408, 429, 500, 502, 503, 504],
};

/**
 * Make a request with automatic retries
 */
export const requestWithRetry = async <T>(
  config: AxiosRequestConfig,
  retryConfig: RetryConfig = {}
): Promise<T> => {
  const { maxRetries, retryDelay, retryStatusCodes } = {
    ...defaultRetryConfig,
    ...retryConfig,
  };

  let retries = 0;

  const executeRequest = async (): Promise<T> => {
    try {
      const response = await apiClient(config);
      return extractData<T>(response);
    } catch (error) {
      if (
        error instanceof AxiosError &&
        error.response &&
        retryStatusCodes.includes(error.response.status) &&
        retries < maxRetries
      ) {
        retries++;

        // Wait before retrying
        await new Promise((resolve) => setTimeout(resolve, retryDelay));

        // Retry the request
        return executeRequest();
      }

      throw parseApiError(error);
    }
  };

  return executeRequest();
};

// Usage example
export const fetchDataWithRetry = async (id: string) => {
  return requestWithRetry<SomeData>(
    { url: `/data/${id}`, method: 'GET' },
    { maxRetries: 5 }
  );
};
```

### Request Caching

Implement request caching for improved performance:

```typescript
// /frontend/src/shared/api/core/cache.ts
import { AxiosRequestConfig } from 'axios';
import { apiClient } from './client';

interface CacheConfig {
  ttl?: number; // Time to live in milliseconds
  cacheKey?: string; // Custom cache key
}

const defaultCacheConfig: CacheConfig = {
  ttl: 5 * 60 * 1000, // 5 minutes
};

// Simple in-memory cache
const cache: Record<string, { data: any; timestamp: number }> = {};

/**
 * Generate cache key from request config
 */
const generateCacheKey = (config: AxiosRequestConfig): string => {
  const { url, method, params, data } = config;
  return `${method || 'GET'}:${url}:${JSON.stringify(params)}:${JSON.stringify(data)}`;
};

/**
 * Make a request with caching
 */
export const requestWithCache = async <T>(
  config: AxiosRequestConfig,
  cacheConfig: CacheConfig = {}
): Promise<T> => {
  const { ttl } = { ...defaultCacheConfig, ...cacheConfig };
  const cacheKey = cacheConfig.cacheKey || generateCacheKey(config);

  // Check if we have a valid cached response
  const cachedItem = cache[cacheKey];
  if (cachedItem) {
    const now = Date.now();
    if (now - cachedItem.timestamp < ttl) {
      return cachedItem.data;
    }
  }

  // Make the request
  const response = await apiClient(config);
  const data = extractData<T>(response);

  // Cache the response
  cache[cacheKey] = {
    data,
    timestamp: Date.now(),
  };

  return data;
};

// Usage example
export const fetchUserWithCache = async (userId: string) => {
  return requestWithCache<User>(
    { url: `/users/${userId}`, method: 'GET' },
    { ttl: 10 * 60 * 1000 } // 10 minutes
  );
};

/**
 * Clear the entire cache or a specific key
 */
export const clearCache = (cacheKey?: string) => {
  if (cacheKey) {
    delete cache[cacheKey];
  } else {
    Object.keys(cache).forEach((key) => delete cache[key]);
  }
};
```

---

By implementing this centralized API client factory, you'll eliminate redundant API client initialization across the Lonors AI Platform frontend codebase, improving maintainability, consistency, and error handling.

*Last Updated: 2024-12-30*# API Client Standardization Guide

This guide provides detailed instructions for implementing a centralized API client factory to eliminate redundant API client initialization across the Lonors AI Platform frontend codebase.

## Table of Contents

1. [Overview](#1-overview)
2. [Implementation Steps](#2-implementation-steps)
3. [Usage Examples](#3-usage-examples)
4. [Migration Guide](#4-migration-guide)
5. [Testing](#5-testing)
6. [Advanced Configuration](#6-advanced-configuration)

## 1. Overview

### Current Redundancy

Currently, similar API client initialization appears across multiple files:

- `/frontend/src/shared/api/auth.ts`
- `/frontend/src/shared/api/agent.ts`
- `/frontend/src/features/agent-management/api/index.ts`
- `/frontend/src/features/knowledge-graph/api/index.ts`

### Solution

Implement a centralized API client factory that provides:

- Standardized configuration
- Authentication token handling
- Error handling
- Request/response interceptors
- Customization options for specific API endpoints

## 2. Implementation Steps

### Step 1: Create the API Client Factory

Create the API client factory in the shared API directory:

```bash
mkdir -p frontend/src/shared/api/core
touch frontend/src/shared/api/core/client.ts
```

### Step 2: Implement the API Client Factory

Implement the `createApiClient` function with the following functionality:

```typescript
// /frontend/src/shared/api/core/client.ts
import axios, { AxiosInstance, AxiosRequestConfig, AxiosResponse, AxiosError } from 'axios';

// Types for API client configuration
export interface ApiClientConfig extends AxiosRequestConfig {
  /**
   * Whether to include authentication token in requests
   * @default true
   */
  withAuth?: boolean;

  /**
   * Custom error handler
   */
  errorHandler?: (error: AxiosError) => Promise<never>;
}

/**
 * Default API client configuration
 */
const defaultConfig: ApiClientConfig = {
  baseURL: import.meta.env.VITE_API_URL || '/api',
  timeout: 10000,
  headers: {
    'Content-Type': 'application/json',
  },
  withAuth: true,
};

/**
 * Default error handler
 */
const defaultErrorHandler = (error: AxiosError): Promise<never> => {
  // Log error for debugging
  console.error('API Error:', error);

  // Handle specific error status codes
  if (error.response) {
    const status = error.response.status;

    // Handle unauthorized
    if (status === 401) {
      // Dispatch custom event for unauthorized
      window.dispatchEvent(new CustomEvent('auth:unauthorized'));
    }

    // Handle forbidden
    if (status === 403) {
      // Dispatch custom event for forbidden
      window.dispatchEvent(new CustomEvent('auth:forbidden'));
    }

    // Handle server error
    if (status >= 500) {
      // Dispatch custom event for server error
      window.dispatchEvent(new CustomEvent('api:server-error'));
    }
  }

  // Handle network errors
  if (error.code === 'ECONNABORTED' || error.message.includes('Network Error')) {
    window.dispatchEvent(new CustomEvent('api:network-error'));
  }

  return Promise.reject(error);
};

/**
 * Create an API client with custom configuration
 */
export const createApiClient = (config: ApiClientConfig = {}): AxiosInstance => {
  // Merge default config with custom config
  const mergedConfig: ApiClientConfig = {
    ...defaultConfig,
    ...config,
    headers: {
      ...defaultConfig.headers,
      ...config.headers,
    },
  };

  // Extract non-axios options
  const { withAuth = true, errorHandler, ...axiosConfig } = mergedConfig;

  // Create axios instance
  const instance = axios.create(axiosConfig);

  // Request interceptor for auth token
  instance.interceptors.request.use(
    (config) => {
      // Add auth token if withAuth is true
      if (withAuth) {
        const token = localStorage.getItem('auth_token');
        if (token) {
          config.headers = config.headers || {};
          config.headers.Authorization = `Bearer ${token}`;
        }
      }

      return config;
    },
    (error) => Promise.reject(error)
  );

  // Response interceptor for error handling
  instance.interceptors.response.use(
    (response: AxiosResponse) => response,
    (error: AxiosError) => {
      // Use custom error handler if provided, otherwise use default
      return (errorHandler || defaultErrorHandler)(error);
    }
  );

  return instance;
};

/**
 * Default API client instance
 */
export const apiClient = createApiClient();
```

### Step 3: Create API Response Types

Create standardized API response types:

```typescript
// /frontend/src/shared/api/core/types.ts
export interface ApiResponse<T> {
  data: T;
  message?: string;
  status: number;
}

export interface PaginatedApiResponse<T> {
  data: T[];
  total: number;
  page: number;
  pageSize: number;
  totalPages: number;
}

export interface ApiError {
  message: string;
  code?: string;
  details?: Record<string, string[]>;
}
```

### Step 4: Create API Utilities

Create utility functions for common API operations:

```typescript
// /frontend/src/shared/api/core/utils.ts
import { AxiosError, AxiosResponse } from 'axios';
import { ApiError } from './types';

/**
 * Extract error message from API error
 */
export const getErrorMessage = (error: unknown): string => {
  if (error instanceof AxiosError) {
    // Try to get error from response data
    const responseData = error.response?.data;

    if (responseData) {
      if (typeof responseData === 'string') {
        return responseData;
      }

      if (responseData.message) {
        return responseData.message;
      }

      if (responseData.error) {
        return responseData.error;
      }
    }

    // Fallback to error message
    return error.message;
  }

  // Handle non-axios errors
  if (error instanceof Error) {
    return error.message;
  }

  return 'An unknown error occurred';
};

/**
 * Parse API error response
 */
export const parseApiError = (error: unknown): ApiError => {
  const message = getErrorMessage(error);

  if (error instanceof AxiosError && error.response?.data) {
    const data = error.response.data;

    return {
      message,
      code: data.code || error.code,
      details: data.details || data.errors || undefined,
    };
  }

  return { message };
};

/**
 * Extract data from API response
 */
export const extractData = <T>(response: AxiosResponse): T => {
  // Handle different response formats
  if (response.data.data !== undefined) {
    return response.data.data;
  }

  return response.data;
};
```

### Step 5: Create Barrel Exports

Create an index file to export all API core functionality:

```typescript
// /frontend/src/shared/api/core/index.ts
export * from './client';
export * from './types';
export * from './utils';
```

## 3. Usage Examples

### Basic Usage

```typescript
// /frontend/src/features/authentication/api/auth-api.ts
import { apiClient, extractData, parseApiError } from '@/shared/api/core';

export interface LoginRequest {
  email: string;
  password: string;
}

export interface LoginResponse {
  token: string;
  user: {
    id: string;
    email: string;
    name: string;
  };
}

export const loginUser = async (data: LoginRequest): Promise<LoginResponse> => {
  try {
    const response = await apiClient.post('/auth/login', data);
    return extractData<LoginResponse>(response);
  } catch (error) {
    throw parseApiError(error);
  }
};
```

### Custom API Client

```typescript
// /frontend/src/features/agent-management/api/agent-api.ts
import { createApiClient, extractData, parseApiError } from '@/shared/api/core';

// Create a custom API client for agent endpoints
const agentApiClient = createApiClient({
  baseURL: `${import.meta.env.VITE_API_URL}/agents`,
  timeout: 20000, // Longer timeout for agent operations
});

export interface Agent {
  id: string;
  name: string;
  description: string;
  status: 'active' | 'inactive';
}

export const getAgents = async (): Promise<Agent[]> => {
  try {
    const response = await agentApiClient.get('/');
    return extractData<Agent[]>(response);
  } catch (error) {
    throw parseApiError(error);
  }
};

export const getAgentById = async (id: string): Promise<Agent> => {
  try {
    const response = await agentApiClient.get(`/${id}`);
    return extractData<Agent>(response);
  } catch (error) {
    throw parseApiError(error);
  }
};
```

### API Client with Custom Error Handling

```typescript
// /frontend/src/features/knowledge-graph/api/graph-api.ts
import { createApiClient, extractData } from '@/shared/api/core';
import { toast } from '@/shared/ui/toast';

// Create API client with custom error handling
const graphApiClient = createApiClient({
  baseURL: `${import.meta.env.VITE_API_URL}/knowledge-graph`,
  errorHandler: (error) => {
    // Show toast notification for errors
    toast({
      title: 'Knowledge Graph Error',
      description: error.response?.data?.message || error.message,
      variant: 'destructive',
    });

    // Rethrow the error
    return Promise.reject(error);
  },
});

export const getGraphData = async (graphId: string) => {
  const response = await graphApiClient.get(`/${graphId}`);
  return extractData(response);
};
```

## 4. Migration Guide

Follow these steps to migrate existing API clients to use the centralized factory:

### Step 1: Identify API Clients to Migrate

Identify all files that initialize axios instances or make direct API calls.

### Step 2: Create Feature-Specific API Modules

For each feature, create a dedicated API module:

```typescript
// /frontend/src/features/[feature-name]/api/index.ts
import { apiClient, extractData, parseApiError } from '@/shared/api/core';

// Define request/response types
export interface SomeRequest {
  // ...
}

export interface SomeResponse {
  // ...
}

// Implement API functions
export const someApiFunction = async (data: SomeRequest): Promise<SomeResponse> => {
  try {
    const response = await apiClient.post('/some-endpoint', data);
    return extractData<SomeResponse>(response);
  } catch (error) {
    throw parseApiError(error);
  }
};
```

### Step 3: Update Imports

Update all imports to use the new API modules:

```typescript
// Before
import axios from 'axios';
// ...
const response = await axios.post('/api/some-endpoint', data);

// After
import { someApiFunction } from '@/features/some-feature/api';
// ...
const result = await someApiFunction(data);
```

### Step 4: Test Thoroughly

Test each migrated API client to ensure:
- Authentication works correctly
- Error handling functions as expected
- Response data is correctly extracted
- Edge cases are handled properly

## 5. Testing

### Unit Testing the API Client

Create unit tests for the API client factory:

```typescript
// /frontend/src/shared/api/core/__tests__/client.test.ts
import { createApiClient } from '../client';
import axios from 'axios';

// Mock axios
jest.mock('axios', () => ({
  create: jest.fn(() => ({
    interceptors: {
      request: { use: jest.fn() },
      response: { use: jest.fn() },
    },
  })),
}));

describe('API Client', () => {
  beforeEach(() => {
    jest.clearAllMocks();
    localStorage.clear();
  });

  it('should create an axios instance with default config', () => {
    createApiClient();

    expect(axios.create).toHaveBeenCalledWith(expect.objectContaining({
      baseURL: expect.any(String),
      timeout: 10000,
      headers: expect.objectContaining({
        'Content-Type': 'application/json',
      }),
    }));
  });

  it('should merge custom config with defaults', () => {
    const customConfig = {
      baseURL: 'https://custom-api.example.com',
      timeout: 5000,
      headers: {
        'X-Custom-Header': 'custom-value',
      },
    };

    createApiClient(customConfig);

    expect(axios.create).toHaveBeenCalledWith(expect.objectContaining({
      baseURL: 'https://custom-api.example.com',
      timeout: 5000,
      headers: expect.objectContaining({
        'Content-Type': 'application/json',
        'X-Custom-Header': 'custom-value',
      }),
    }));
  });

  it('should add auth token to requests when withAuth is true', () => {
    // Set auth token in localStorage
    localStorage.setItem('auth_token', 'test-token');

    const instance = createApiClient();

    // Get the request interceptor function
    const requestInterceptor = instance.interceptors.request.use.mock.calls[0][0];

    // Create a mock request config
    const config = { headers: {} };

    // Call the interceptor
    const result = requestInterceptor(config);

    // Check that the token was added
    expect(result.headers.Authorization).toBe('Bearer test-token');
  });

  it('should not add auth token when withAuth is false', () => {
    // Set auth token in localStorage
    localStorage.setItem('auth_token', 'test-token');

    const instance = createApiClient({ withAuth: false });

    // Get the request interceptor function
    const requestInterceptor = instance.interceptors.request.use.mock.calls[0][0];

    // Create a mock request config
    const config = { headers: {} };

    // Call the interceptor
    const result = requestInterceptor(config);

    // Check that the token was not added
    expect(result.headers.Authorization).toBeUndefined();
  });
});
```

### Testing API Functions

Create tests for API functions:

```typescript
// /frontend/src/features/authentication/api/__tests__/auth-api.test.ts
import { loginUser } from '../auth-api';
import { apiClient } from '@/shared/api/core';

// Mock the API client
jest.mock('@/shared/api/core', () => ({
  apiClient: {
    post: jest.fn(),
  },
  extractData: jest.fn((response) => response.data),
  parseApiError: jest.fn((error) => ({ message: error.message })),
}));

describe('Auth API', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  it('should call login endpoint with correct data', async () => {
    // Mock successful response
    (apiClient.post as jest.Mock).mockResolvedValue({
      data: {
        token: 'test-token',
        user: {
          id: '123',
          email: '<EMAIL>',
          name: 'Test User',
        },
      },
    });

    const loginData = {
      email: '<EMAIL>',
      password: 'password123',
    };

    const result = await loginUser(loginData);

    // Check that the API was called correctly
    expect(apiClient.post).toHaveBeenCalledWith('/auth/login', loginData);

    // Check the result
    expect(result).toEqual({
      token: 'test-token',
      user: {
        id: '123',
        email: '<EMAIL>',
        name: 'Test User',
      },
    });
  });

  it('should handle login errors', async () => {
    // Mock error response
    const error = new Error('Invalid credentials');
    (apiClient.post as jest.Mock).mockRejectedValue(error);

    const loginData = {
      email: '<EMAIL>',
      password: 'wrong-password',
    };

    // Expect the function to throw
    await expect(loginUser(loginData)).rejects.toEqual({
      message: 'Invalid credentials',
    });
  });
});
```

## 6. Advanced Configuration

### Request Cancellation

Implement request cancellation for API calls:

```typescript
// /frontend/src/shared/api/core/cancellation.ts
import { CancelToken } from 'axios';

/**
 * Create a cancellable request
 */
export const createCancellableRequest = () => {
  const source = CancelToken.source();

  return {
    cancelToken: source.token,
    cancel: (message?: string) => source.cancel(message),
  };
};

// Usage example
export const searchWithCancellation = async (query: string) => {
  const { cancelToken, cancel } = createCancellableRequest();

  try {
    const response = await apiClient.get('/search', {
      params: { q: query },
      cancelToken,
    });

    return extractData(response);
  } catch (error) {
    if (axios.isCancel(error)) {
      // Request was cancelled
      console.log('Request cancelled:', error.message);
      return null;
    }

    throw parseApiError(error);
  }

  return { cancelRequest: cancel };
};
```

### Request Retries

Implement automatic retries for failed requests:

```typescript
// /frontend/src/shared/api/core/retry.ts
import { AxiosError, AxiosRequestConfig } from 'axios';
import { apiClient } from './client';

interface RetryConfig {
  maxRetries?: number;
  retryDelay?: number;
  retryStatusCodes?: number[];
}

const defaultRetryConfig: RetryConfig = {
  maxRetries: 3,
  retryDelay: 1000,
  retryStatusCodes: [408, 429, 500, 502, 503, 504],
};

/**
 * Make a request with automatic retries
 */
export const requestWithRetry = async <T>(
  config: AxiosRequestConfig,
  retryConfig: RetryConfig = {}
): Promise<T> => {
  const { maxRetries, retryDelay, retryStatusCodes } = {
    ...defaultRetryConfig,
    ...retryConfig,
  };

  let retries = 0;

  const executeRequest = async (): Promise<T> => {
    try {
      const response = await apiClient(config);
      return extractData<T>(response);
    } catch (error) {
      if (
        error instanceof AxiosError &&
        error.response &&
        retryStatusCodes.includes(error.response.status) &&
        retries < maxRetries
      ) {
        retries++;

        // Wait before retrying
        await new Promise((resolve) => setTimeout(resolve, retryDelay));

        // Retry the request
        return executeRequest();
      }

      throw parseApiError(error);
    }
  };

  return executeRequest();
};

// Usage example
export const fetchDataWithRetry = async (id: string) => {
  return requestWithRetry<SomeData>(
    { url: `/data/${id}`, method: 'GET' },
    { maxRetries: 5 }
  );
};
```

### Request Caching

Implement request caching for improved performance:

```typescript
// /frontend/src/shared/api/core/cache.ts
import { AxiosRequestConfig } from 'axios';
import { apiClient } from './client';

interface CacheConfig {
  ttl?: number; // Time to live in milliseconds
  cacheKey?: string; // Custom cache key
}

const defaultCacheConfig: CacheConfig = {
  ttl: 5 * 60 * 1000, // 5 minutes
};

// Simple in-memory cache
const cache: Record<string, { data: any; timestamp: number }> = {};

/**
 * Generate cache key from request config
 */
const generateCacheKey = (config: AxiosRequestConfig): string => {
  const { url, method, params, data } = config;
  return `${method || 'GET'}:${url}:${JSON.stringify(params)}:${JSON.stringify(data)}`;
};

/**
 * Make a request with caching
 */
export const requestWithCache = async <T>(
  config: AxiosRequestConfig,
  cacheConfig: CacheConfig = {}
): Promise<T> => {
  const { ttl } = { ...defaultCacheConfig, ...cacheConfig };
  const cacheKey = cacheConfig.cacheKey || generateCacheKey(config);

  // Check if we have a valid cached response
  const cachedItem = cache[cacheKey];
  if (cachedItem) {
    const now = Date.now();
    if (now - cachedItem.timestamp < ttl) {
      return cachedItem.data;
    }
  }

  // Make the request
  const response = await apiClient(config);
  const data = extractData<T>(response);

  // Cache the response
  cache[cacheKey] = {
    data,
    timestamp: Date.now(),
  };

  return data;
};

// Usage example
export const fetchUserWithCache = async (userId: string) => {
  return requestWithCache<User>(
    { url: `/users/${userId}`, method: 'GET' },
    { ttl: 10 * 60 * 1000 } // 10 minutes
  );
};

/**
 * Clear the entire cache or a specific key
 */
export const clearCache = (cacheKey?: string) => {
  if (cacheKey) {
    delete cache[cacheKey];
  } else {
    Object.keys(cache).forEach((key) => delete cache[key]);
  }
};
```

---

By implementing this centralized API client factory, you'll eliminate redundant API client initialization across the Lonors AI Platform frontend codebase, improving maintainability, consistency, and error handling.

*Last Updated: 2024-12-30*
