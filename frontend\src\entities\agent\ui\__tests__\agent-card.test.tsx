import { render, screen, fireEvent } from '@testing-library/react';
import { describe, expect, it, vi } from 'vitest';
import { AgentStatus, AgentType } from '@/shared/types';
import { AgentCard } from '../agent-card';
import { createAgent } from '../../model';

// Mock the animations module
vi.mock('@/shared/lib/animations', () => ({
  animateAgentCard: vi.fn(),
}));

// Mock the AnimatedBox component
vi.mock('@/shared/ui/animated-box', () => ({
  AnimatedBox: ({ children }: { children: React.ReactNode }) => <div>{children}</div>,
}));

describe('AgentCard', () => {
  const mockAgent = createAgent({
    id: '1',
    name: 'Test Agent',
    description: 'A test agent for unit testing',
    agent_type: AgentType.CHAT,
    status: AgentStatus.IDLE,
    capabilities: [
      { name: 'chat', description: 'Chat capability', parameters: {}, required: true },
      { name: 'search', description: 'Search capability', parameters: {}, required: false },
    ],
    tags: ['test', 'demo', 'ai'],
    metrics: {
      total_executions: 10,
      successful_executions: 8,
      failed_executions: 2,
      average_execution_time: 45.5,
      total_tokens_used: 1000,
      total_cost: 0.05,
    },
  });

  const defaultProps = {
    agent: mockAgent,
    onExecute: vi.fn(),
    onPause: vi.fn(),
    onStop: vi.fn(),
    onEdit: vi.fn(),
    onSelect: vi.fn(),
  };

  beforeEach(() => {
    vi.clearAllMocks();
  });

  it('renders agent information correctly', () => {
    render(<AgentCard {...defaultProps} />);
    
    expect(screen.getByText('Test Agent')).toBeInTheDocument();
    expect(screen.getByText('A test agent for unit testing')).toBeInTheDocument();
    expect(screen.getByText('chat')).toBeInTheDocument();
    expect(screen.getByText('Idle')).toBeInTheDocument();
  });

  it('displays agent type icon correctly', () => {
    render(<AgentCard {...defaultProps} />);
    
    // Chat agent should show MessageSquare icon
    const card = screen.getByRole('button', { name: /Agent Test Agent/ });
    expect(card).toBeInTheDocument();
  });

  it('shows capabilities badges', () => {
    render(<AgentCard {...defaultProps} />);
    
    expect(screen.getByText('chat')).toBeInTheDocument();
    expect(screen.getByText('search')).toBeInTheDocument();
  });

  it('shows limited tags with overflow indicator', () => {
    render(<AgentCard {...defaultProps} />);
    
    expect(screen.getByText('test')).toBeInTheDocument();
    expect(screen.getByText('demo')).toBeInTheDocument();
    expect(screen.getByText('+1')).toBeInTheDocument(); // +1 for the third tag
  });

  it('displays metrics when showMetrics is true', () => {
    render(<AgentCard {...defaultProps} showMetrics />);
    
    expect(screen.getByText('80.0%')).toBeInTheDocument(); // Success rate
    expect(screen.getByText('45.5s')).toBeInTheDocument(); // Avg execution time
  });

  it('hides metrics when showMetrics is false', () => {
    render(<AgentCard {...defaultProps} showMetrics={false} />);
    
    expect(screen.queryByText('80.0%')).not.toBeInTheDocument();
    expect(screen.queryByText('45.5s')).not.toBeInTheDocument();
  });

  it('shows detailed metrics in detailed variant', () => {
    render(<AgentCard {...defaultProps} variant="detailed" />);
    
    expect(screen.getByText('10')).toBeInTheDocument(); // Total runs
    expect(screen.getByText('$0.0500')).toBeInTheDocument(); // Total cost
  });

  it('applies compact styling in compact variant', () => {
    render(<AgentCard {...defaultProps} variant="compact" />);
    
    const card = screen.getByRole('button', { name: /Agent Test Agent/ });
    expect(card).toHaveClass('max-w-xs');
  });

  it('calls onExecute when execute button is clicked', () => {
    render(<AgentCard {...defaultProps} />);
    
    const executeButton = screen.getByRole('button', { name: /Execute agent/ });
    fireEvent.click(executeButton);
    
    expect(defaultProps.onExecute).toHaveBeenCalledWith(mockAgent);
  });

  it('shows pause button for running agent', () => {
    const runningAgent = createAgent({
      ...mockAgent,
      status: AgentStatus.RUNNING,
    });
    
    render(<AgentCard {...defaultProps} agent={runningAgent} />);
    
    expect(screen.getByRole('button', { name: /Pause agent/ })).toBeInTheDocument();
  });

  it('calls onPause when pause button is clicked', () => {
    const runningAgent = createAgent({
      ...mockAgent,
      status: AgentStatus.RUNNING,
    });
    
    render(<AgentCard {...defaultProps} agent={runningAgent} />);
    
    const pauseButton = screen.getByRole('button', { name: /Pause agent/ });
    fireEvent.click(pauseButton);
    
    expect(defaultProps.onPause).toHaveBeenCalledWith(runningAgent);
  });

  it('shows stop button for active agent', () => {
    const runningAgent = createAgent({
      ...mockAgent,
      status: AgentStatus.RUNNING,
    });
    
    render(<AgentCard {...defaultProps} agent={runningAgent} />);
    
    expect(screen.getByRole('button', { name: /Stop agent/ })).toBeInTheDocument();
  });

  it('calls onStop when stop button is clicked', () => {
    const runningAgent = createAgent({
      ...mockAgent,
      status: AgentStatus.RUNNING,
    });
    
    render(<AgentCard {...defaultProps} agent={runningAgent} />);
    
    const stopButton = screen.getByRole('button', { name: /Stop agent/ });
    fireEvent.click(stopButton);
    
    expect(defaultProps.onStop).toHaveBeenCalledWith(runningAgent);
  });

  it('calls onEdit when edit button is clicked', () => {
    render(<AgentCard {...defaultProps} />);
    
    const editButton = screen.getByRole('button', { name: /Edit agent/ });
    fireEvent.click(editButton);
    
    expect(defaultProps.onEdit).toHaveBeenCalledWith(mockAgent);
  });

  it('calls onSelect when card is clicked', () => {
    render(<AgentCard {...defaultProps} />);
    
    const card = screen.getByRole('button', { name: /Agent Test Agent/ });
    fireEvent.click(card);
    
    expect(defaultProps.onSelect).toHaveBeenCalledWith(mockAgent);
  });

  it('applies selected styling when isSelected is true', () => {
    render(<AgentCard {...defaultProps} isSelected />);
    
    const card = screen.getByRole('button', { name: /Agent Test Agent/ });
    expect(card).toHaveClass('ring-2', 'ring-primary', 'ring-offset-2');
  });

  it('shows active indicator for running agent', () => {
    const runningAgent = createAgent({
      ...mockAgent,
      status: AgentStatus.RUNNING,
    });
    
    render(<AgentCard {...defaultProps} agent={runningAgent} />);
    
    // Should show green status indicator
    const statusIndicator = screen.getByRole('status');
    expect(statusIndicator).toBeInTheDocument();
  });

  it('prevents event propagation on button clicks', () => {
    const onSelect = vi.fn();
    render(<AgentCard {...defaultProps} onSelect={onSelect} />);
    
    const executeButton = screen.getByRole('button', { name: /Execute agent/ });
    fireEvent.click(executeButton);
    
    // onSelect should not be called when clicking action buttons
    expect(onSelect).not.toHaveBeenCalled();
    expect(defaultProps.onExecute).toHaveBeenCalled();
  });

  it('handles keyboard navigation', () => {
    render(<AgentCard {...defaultProps} />);
    
    const card = screen.getByRole('button', { name: /Agent Test Agent/ });
    expect(card).toHaveAttribute('tabIndex', '0');
  });

  it('provides proper accessibility labels', () => {
    render(<AgentCard {...defaultProps} />);
    
    expect(screen.getByLabelText(/Agent Test Agent, status: idle/)).toBeInTheDocument();
    expect(screen.getByLabelText(/Execute agent/)).toBeInTheDocument();
    expect(screen.getByLabelText(/Edit agent/)).toBeInTheDocument();
  });
});
