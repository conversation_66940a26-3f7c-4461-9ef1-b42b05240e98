# Lonors Production Environment Configuration
# Docker Compose environment variables for production

# =============================================================================
# PROJECT CONFIGURATION
# =============================================================================
COMPOSE_PROJECT_NAME=lonors-prod
COMPOSE_FILE=docker-compose.prod.yml
DOCKER_BUILDKIT=1
COMPOSE_DOCKER_CLI_BUILD=1

# =============================================================================
# DATABASE CONFIGURATION (PostgreSQL)
# =============================================================================
POSTGRES_DB=lonors_prod_db
POSTGRES_USER=lonors_prod_user
# POSTGRES_PASSWORD - Set via Docker secrets
POSTGRES_HOST=postgres
POSTGRES_PORT=5432

# =============================================================================
# CACHE CONFIGURATION (DragonflyDB)
# =============================================================================
REDIS_HOST=dragonflydb
REDIS_PORT=6379
# REDIS_PASSWORD - Set via Docker secrets

# =============================================================================
# BACKEND CONFIGURATION (Python FastAPI)
# =============================================================================
ENVIRONMENT=production
DEBUG=false
LOG_LEVEL=info

# Security - Set via Docker secrets
# JWT_SECRET - Set via Docker secrets
JWT_ALGORITHM=HS256
JWT_EXPIRES_IN=24h

# Server configuration
BACKEND_HOST=0.0.0.0
BACKEND_PORT=3001
BACKEND_WORKERS=4

# CORS configuration - Update with your production domains
CORS_ORIGINS=https://lonors.com,https://www.lonors.com,https://api.lonors.com

# API configuration
API_V1_PREFIX=/api/v1
API_TITLE=Lonors Backend API
API_VERSION=1.0.0
API_DESCRIPTION=Lonors AI Agent Platform Backend

# =============================================================================
# FRONTEND CONFIGURATION (React + Vite)
# =============================================================================
NODE_ENV=production

# Vite configuration - Update with your production API URL
VITE_API_URL=https://api.lonors.com
VITE_API_BASE_URL=https://api.lonors.com/api/v1
VITE_APP_NAME=Lonors
VITE_APP_VERSION=1.0.0
VITE_ENVIRONMENT=production

# =============================================================================
# SSL/TLS CONFIGURATION
# =============================================================================
SSL_CERT_PATH=/run/secrets/ssl_cert
SSL_KEY_PATH=/run/secrets/ssl_key
SSL_PROTOCOLS=TLSv1.2 TLSv1.3
SSL_CIPHERS=ECDHE-ECDSA-AES128-GCM-SHA256:ECDHE-RSA-AES128-GCM-SHA256:ECDHE-ECDSA-AES256-GCM-SHA384:ECDHE-RSA-AES256-GCM-SHA384

# =============================================================================
# DOCKER RESOURCE LIMITS (Production)
# =============================================================================
# PostgreSQL
POSTGRES_CPU_LIMIT=4
POSTGRES_MEMORY_LIMIT=4g
POSTGRES_CPU_RESERVATION=1
POSTGRES_MEMORY_RESERVATION=1g

# DragonflyDB
DRAGONFLY_CPU_LIMIT=2
DRAGONFLY_MEMORY_LIMIT=2g
DRAGONFLY_CPU_RESERVATION=0.5
DRAGONFLY_MEMORY_RESERVATION=512m

# Backend
BACKEND_CPU_LIMIT=4
BACKEND_MEMORY_LIMIT=4g
BACKEND_CPU_RESERVATION=1
BACKEND_MEMORY_RESERVATION=1g

# Frontend
FRONTEND_CPU_LIMIT=1
FRONTEND_MEMORY_LIMIT=512m
FRONTEND_CPU_RESERVATION=0.25
FRONTEND_MEMORY_RESERVATION=128m

# =============================================================================
# NETWORK CONFIGURATION
# =============================================================================
NETWORK_SUBNET=172.21.0.0/16
NETWORK_GATEWAY=172.21.0.1

# =============================================================================
# HEALTH CHECK CONFIGURATION
# =============================================================================
HEALTH_CHECK_INTERVAL=30s
HEALTH_CHECK_TIMEOUT=10s
HEALTH_CHECK_RETRIES=3
HEALTH_CHECK_START_PERIOD=60s

# =============================================================================
# LOGGING CONFIGURATION
# =============================================================================
LOG_FORMAT=json
LOG_ROTATION=daily
LOG_MAX_SIZE=500m
LOG_MAX_FILES=30

# =============================================================================
# SECURITY CONFIGURATION
# =============================================================================
# Security headers
SECURITY_HEADERS=true
HSTS_MAX_AGE=31536000
CSP_POLICY="default-src 'self'; script-src 'self' 'unsafe-inline'; style-src 'self' 'unsafe-inline'"

# Rate limiting
RATE_LIMIT_ENABLED=true
RATE_LIMIT_REQUESTS=1000
RATE_LIMIT_WINDOW=3600

# =============================================================================
# BACKUP CONFIGURATION
# =============================================================================
BACKUP_ENABLED=true
BACKUP_SCHEDULE="0 2 * * *"
BACKUP_RETENTION_DAYS=30
BACKUP_S3_BUCKET=lonors-backups
BACKUP_S3_REGION=us-east-1

# =============================================================================
# MONITORING CONFIGURATION
# =============================================================================
MONITORING_ENABLED=true
METRICS_ENABLED=true
TRACING_ENABLED=true

# Prometheus
PROMETHEUS_PORT=9090
PROMETHEUS_RETENTION=15d

# Grafana
GRAFANA_PORT=3000
GRAFANA_ADMIN_USER=admin
# GRAFANA_ADMIN_PASSWORD - Set via environment or secrets

# =============================================================================
# PERFORMANCE CONFIGURATION
# =============================================================================
# Database connection pooling
DB_POOL_SIZE=20
DB_MAX_OVERFLOW=30
DB_POOL_TIMEOUT=30

# Cache configuration
CACHE_TTL=3600
CACHE_MAX_SIZE=1000

# =============================================================================
# AI/ML CONFIGURATION (Future)
# =============================================================================
# Placeholder for AI model configuration
OLLAMA_HOST=ollama
OLLAMA_PORT=11434
HUGGINGFACE_CACHE_DIR=/app/.cache/huggingface

# Model serving
MODEL_SERVING_ENABLED=true
MODEL_CACHE_SIZE=10g
MODEL_WORKERS=2

# =============================================================================
# PROTOCOL CONFIGURATION (Future)
# =============================================================================
# MCP Protocol
MCP_WEBSOCKET_URL=wss://api.lonors.com/ws/mcp
MCP_ENABLED=true

# A2A Protocol
A2A_WEBSOCKET_URL=wss://api.lonors.com/ws/a2a
A2A_ENABLED=true

# AG-UI Protocol
AG_UI_WEBSOCKET_URL=wss://api.lonors.com/ws/ag-ui
AG_UI_ENABLED=true

# =============================================================================
# EXTERNAL SERVICES
# =============================================================================
# Email service
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_USE_TLS=true
# SMTP_USERNAME - Set via environment
# SMTP_PASSWORD - Set via environment

# Object storage
S3_BUCKET=lonors-storage
S3_REGION=us-east-1
# AWS_ACCESS_KEY_ID - Set via environment
# AWS_SECRET_ACCESS_KEY - Set via environment

# =============================================================================
# DEPLOYMENT CONFIGURATION
# =============================================================================
DEPLOYMENT_ENVIRONMENT=production
DEPLOYMENT_VERSION=1.0.0
DEPLOYMENT_TIMESTAMP=2024-12-30

# Health check URLs
HEALTH_CHECK_URL=https://api.lonors.com/health
READINESS_CHECK_URL=https://api.lonors.com/ready

# =============================================================================
# COMPLIANCE AND AUDIT
# =============================================================================
AUDIT_LOGGING=true
COMPLIANCE_MODE=true
DATA_RETENTION_DAYS=2555  # 7 years
GDPR_COMPLIANCE=true
