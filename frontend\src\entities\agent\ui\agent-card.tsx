import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/shared/ui/card';
import { Badge } from '@/shared/ui/badge';
import { Button } from '@/shared/ui/button';
import { AgentModel, agentSelectors } from '../model';
import { Bo<PERSON>, Play, Pause, Square } from 'lucide-react';
import { AgentStatus } from '@/shared/types';

interface AgentCardProps {
  agent: AgentModel;
  onExecute?: (agent: AgentModel) => void;
  onPause?: (agent: AgentModel) => void;
  onStop?: (agent: AgentModel) => void;
  onEdit?: (agent: AgentModel) => void;
}

export function AgentCard({ 
  agent, 
  onExecute, 
  onPause, 
  onStop, 
  onEdit 
}: AgentCardProps) {
  const statusColor = agentSelectors.getStatusColor(agent.status);
  const canExecute = agentSelectors.canExecute(agent);
  const isActive = agentSelectors.isActive(agent);

  const handleAction = () => {
    if (agent.status === AgentStatus.RUNNING && onPause) {
      onPause(agent);
    } else if (canExecute && onExecute) {
      onExecute(agent);
    }
  };

  return (
    <Card className="w-full max-w-sm hover:shadow-lg transition-shadow">
      <CardHeader className="pb-3">
        <div className="flex items-start justify-between">
          <div className="flex items-center space-x-2">
            <Bot className="h-5 w-5 text-primary" />
            <CardTitle className="text-lg">{agent.name}</CardTitle>
          </div>
          <Badge 
            variant={statusColor === 'green' ? 'default' : 'secondary'}
            className={`
              ${statusColor === 'green' ? 'bg-green-500' : ''}
              ${statusColor === 'yellow' ? 'bg-yellow-500' : ''}
              ${statusColor === 'red' ? 'bg-red-500' : ''}
              ${statusColor === 'gray' ? 'bg-gray-500' : ''}
            `}
          >
            {agent.status}
          </Badge>
        </div>
        <CardDescription className="line-clamp-2">
          {agent.description || 'No description available'}
        </CardDescription>
      </CardHeader>
      
      <CardContent className="space-y-4">
        <div className="flex flex-wrap gap-1">
          {agent.tags.slice(0, 3).map((tag) => (
            <Badge key={tag} variant="outline" className="text-xs">
              {tag}
            </Badge>
          ))}
          {agent.tags.length > 3 && (
            <Badge variant="outline" className="text-xs">
              +{agent.tags.length - 3}
            </Badge>
          )}
        </div>
        
        <div className="text-sm text-muted-foreground">
          <div>Type: {agent.type}</div>
          <div>Capabilities: {agent.capabilities.length}</div>
          {agent.executionCount > 0 && (
            <div>Executions: {agent.executionCount}</div>
          )}
        </div>
        
        <div className="flex space-x-2">
          <Button
            size="sm"
            onClick={handleAction}
            disabled={!canExecute && agent.status !== AgentStatus.RUNNING}
            className="flex-1"
          >
            {agent.status === AgentStatus.RUNNING ? (
              <>
                <Pause className="h-4 w-4 mr-1" />
                Pause
              </>
            ) : (
              <>
                <Play className="h-4 w-4 mr-1" />
                Execute
              </>
            )}
          </Button>
          
          {isActive && onStop && (
            <Button
              size="sm"
              variant="outline"
              onClick={() => onStop(agent)}
            >
              <Square className="h-4 w-4" />
            </Button>
          )}
          
          {onEdit && (
            <Button
              size="sm"
              variant="outline"
              onClick={() => onEdit(agent)}
            >
              Edit
            </Button>
          )}
        </div>
      </CardContent>
    </Card>
  );
}
