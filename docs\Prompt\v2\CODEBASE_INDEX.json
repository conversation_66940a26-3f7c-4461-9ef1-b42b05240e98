{"project": "<PERSON><PERSON><PERSON>", "version": "1.0.0", "generatedAt": "2023-07-15T12:00:00Z", "frontend": {"src/app": {"description": "Application layer containing root component, routing, and global providers", "files": {"App.tsx": {"status": "active", "directDependencies": ["react"], "indirectDependencies": ["react-dom"], "influenceScore": 9, "testCoverage": 92, "accessibilityCompliance": "AA", "performanceImpact": "medium", "technicalDebt": {"score": "low", "issues": ["Needs proper error boundary implementation"]}}, "router.tsx": {"status": "active", "directDependencies": ["react-router-dom", "shared/components/protected-route"], "indirectDependencies": ["entities/user"], "influenceScore": 8, "testCoverage": 90, "accessibilityCompliance": "AA", "performanceImpact": "medium", "technicalDebt": {"score": "low", "issues": []}}}}, "src/pages": {"description": "Page components that serve as route endpoints", "files": {"home/index.tsx": {"status": "active", "directDependencies": ["widgets/layout", "features/authentication"], "indirectDependencies": ["entities/user", "shared/ui"], "influenceScore": 7, "testCoverage": 88, "accessibilityCompliance": "AA", "performanceImpact": "medium", "technicalDebt": {"score": "low", "issues": []}}, "dashboard/index.tsx": {"status": "active", "directDependencies": ["widgets/dashboard", "widgets/layout"], "indirectDependencies": ["entities/user", "shared/ui"], "influenceScore": 7, "testCoverage": 85, "accessibilityCompliance": "AA", "performanceImpact": "high", "technicalDebt": {"score": "medium", "issues": ["Could benefit from component splitting for better performance"]}}, "auth/login/index.tsx": {"status": "active", "directDependencies": ["features/authentication", "shared/ui"], "indirectDependencies": ["entities/user"], "influenceScore": 8, "testCoverage": 95, "accessibilityCompliance": "AAA", "performanceImpact": "low", "technicalDebt": {"score": "low", "issues": []}}}}, "src/widgets": {"description": "Complex UI blocks combining multiple features", "files": {"layout/header/index.tsx": {"status": "active", "directDependencies": ["features/authentication", "shared/ui"], "indirectDependencies": ["entities/user"], "influenceScore": 7, "testCoverage": 85, "accessibilityCompliance": "AA", "performanceImpact": "medium", "technicalDebt": {"score": "low", "issues": []}}, "layout/sidebar/index.tsx": {"status": "active", "directDependencies": ["features/authentication", "shared/ui"], "indirectDependencies": ["entities/user"], "influenceScore": 6, "testCoverage": 82, "accessibilityCompliance": "AA", "performanceImpact": "medium", "technicalDebt": {"score": "medium", "issues": ["Navigation items should be configurable"]}}, "dashboard/stats-card/index.tsx": {"status": "active", "directDependencies": ["shared/ui", "shared/lib/utils"], "indirectDependencies": [], "influenceScore": 5, "testCoverage": 90, "accessibilityCompliance": "AA", "performanceImpact": "low", "technicalDebt": {"score": "low", "issues": []}}}}, "src/features": {"description": "Business logic features and user interactions", "files": {"authentication/index.ts": {"status": "active", "directDependencies": ["entities/user", "shared/api/auth"], "indirectDependencies": ["shared/lib/utils"], "influenceScore": 9, "testCoverage": 95, "accessibilityCompliance": "AA", "performanceImpact": "high", "technicalDebt": {"score": "low", "issues": []}}, "authentication/ui/login-form.tsx": {"status": "active", "directDependencies": ["shared/ui", "entities/user"], "indirectDependencies": ["shared/lib/utils"], "influenceScore": 8, "testCoverage": 92, "accessibilityCompliance": "AAA", "performanceImpact": "medium", "technicalDebt": {"score": "low", "issues": []}}, "agent-management/index.ts": {"status": "active", "directDependencies": ["entities/agent", "shared/api/agent"], "indirectDependencies": ["shared/lib/utils"], "influenceScore": 8, "testCoverage": 88, "accessibilityCompliance": "AA", "performanceImpact": "medium", "technicalDebt": {"score": "low", "issues": []}}}}, "src/entities": {"description": "Business entities and domain models", "files": {"user/index.ts": {"status": "active", "directDependencies": ["shared/api/auth"], "indirectDependencies": [], "influenceScore": 9, "testCoverage": 95, "accessibilityCompliance": "AAA", "performanceImpact": "low", "technicalDebt": {"score": "low", "issues": []}}, "user/model/index.ts": {"status": "active", "directDependencies": ["shared/types/auth"], "indirectDependencies": [], "influenceScore": 9, "testCoverage": 98, "accessibilityCompliance": "AAA", "performanceImpact": "low", "technicalDebt": {"score": "low", "issues": []}}, "agent/index.ts": {"status": "active", "directDependencies": ["shared/api/agent"], "indirectDependencies": [], "influenceScore": 8, "testCoverage": 92, "accessibilityCompliance": "AAA", "performanceImpact": "low", "technicalDebt": {"score": "low", "issues": []}}}}, "src/shared": {"description": "Shared resources, UI components, and utilities", "files": {"ui/button.tsx": {"status": "active", "directDependencies": ["react", "tailwindcss"], "indirectDependencies": [], "influenceScore": 10, "testCoverage": 98, "accessibilityCompliance": "AAA", "performanceImpact": "low", "technicalDebt": {"score": "low", "issues": []}}, "ui/card.tsx": {"status": "active", "directDependencies": ["react", "tailwindcss"], "indirectDependencies": [], "influenceScore": 9, "testCoverage": 95, "accessibilityCompliance": "AAA", "performanceImpact": "low", "technicalDebt": {"score": "low", "issues": []}}, "api/client.ts": {"status": "active", "directDependencies": ["axios"], "indirectDependencies": [], "influenceScore": 10, "testCoverage": 96, "accessibilityCompliance": "N/A", "performanceImpact": "high", "technicalDebt": {"score": "low", "issues": []}}, "lib/utils.ts": {"status": "active", "directDependencies": [], "indirectDependencies": [], "influenceScore": 10, "testCoverage": 97, "accessibilityCompliance": "N/A", "performanceImpact": "medium", "technicalDebt": {"score": "low", "issues": []}}}}}, "backend": {"src/domain": {"description": "Domain layer containing business entities and logic", "files": {"entities/user.py": {"status": "active", "directDependencies": ["pydantic", "uuid", "datetime"], "indirectDependencies": [], "influenceScore": 10, "testCoverage": 98, "accessibilityCompliance": "N/A", "performanceImpact": "medium", "technicalDebt": {"score": "low", "issues": []}}, "entities/mcp.py": {"status": "active", "directDependencies": ["pydantic", "uuid", "datetime"], "indirectDependencies": [], "influenceScore": 9, "testCoverage": 95, "accessibilityCompliance": "N/A", "performanceImpact": "medium", "technicalDebt": {"score": "low", "issues": []}}, "entities/ag_ui.py": {"status": "active", "directDependencies": ["pydantic", "uuid", "datetime"], "indirectDependencies": [], "influenceScore": 9, "testCoverage": 94, "accessibilityCompliance": "N/A", "performanceImpact": "medium", "technicalDebt": {"score": "low", "issues": []}}, "entities/a2a.py": {"status": "active", "directDependencies": ["pydantic", "uuid", "datetime"], "indirectDependencies": [], "influenceScore": 9, "testCoverage": 93, "accessibilityCompliance": "N/A", "performanceImpact": "medium", "technicalDebt": {"score": "low", "issues": []}}, "repositories/user_repository.py": {"status": "active", "directDependencies": ["domain/entities/user"], "indirectDependencies": [], "influenceScore": 9, "testCoverage": 96, "accessibilityCompliance": "N/A", "performanceImpact": "high", "technicalDebt": {"score": "low", "issues": []}}}}, "src/application": {"description": "Application layer containing use cases and services", "files": {"use_cases/user_service.py": {"status": "active", "directDependencies": ["domain/entities/user", "domain/repositories/user_repository"], "indirectDependencies": [], "influenceScore": 9, "testCoverage": 95, "accessibilityCompliance": "N/A", "performanceImpact": "high", "technicalDebt": {"score": "low", "issues": []}}, "use_cases/mcp_service.py": {"status": "active", "directDependencies": ["domain/entities/mcp"], "indirectDependencies": [], "influenceScore": 8, "testCoverage": 92, "accessibilityCompliance": "N/A", "performanceImpact": "high", "technicalDebt": {"score": "low", "issues": []}}, "use_cases/ag_ui_service.py": {"status": "active", "directDependencies": ["domain/entities/ag_ui"], "indirectDependencies": [], "influenceScore": 8, "testCoverage": 90, "accessibilityCompliance": "N/A", "performanceImpact": "high", "technicalDebt": {"score": "medium", "issues": ["Needs optimization for complex UI operations"]}}, "use_cases/a2a_service.py": {"status": "active", "directDependencies": ["domain/entities/a2a"], "indirectDependencies": [], "influenceScore": 8, "testCoverage": 91, "accessibilityCompliance": "N/A", "performanceImpact": "high", "technicalDebt": {"score": "low", "issues": []}}}}, "src/infrastructure": {"description": "Infrastructure layer containing external implementations", "files": {"database/connection.py": {"status": "active", "directDependencies": ["sqlalchemy", "asyncio"], "indirectDependencies": [], "influenceScore": 9, "testCoverage": 94, "accessibilityCompliance": "N/A", "performanceImpact": "high", "technicalDebt": {"score": "low", "issues": []}}, "database/models/user.py": {"status": "active", "directDependencies": ["sqlalchemy", "domain/entities/user"], "indirectDependencies": [], "influenceScore": 9, "testCoverage": 95, "accessibilityCompliance": "N/A", "performanceImpact": "high", "technicalDebt": {"score": "low", "issues": []}}, "database/repositories/user_repository.py": {"status": "active", "directDependencies": ["domain/repositories/user_repository", "infrastructure/database/models/user"], "indirectDependencies": ["domain/entities/user"], "influenceScore": 8, "testCoverage": 93, "accessibilityCompliance": "N/A", "performanceImpact": "high", "technicalDebt": {"score": "low", "issues": []}}, "security/jwt.py": {"status": "active", "directDependencies": ["pyjwt", "datetime"], "indirectDependencies": [], "influenceScore": 9, "testCoverage": 97, "accessibilityCompliance": "N/A", "performanceImpact": "medium", "technicalDebt": {"score": "low", "issues": []}}, "config/settings.py": {"status": "active", "directDependencies": ["pydantic"], "indirectDependencies": [], "influenceScore": 10, "testCoverage": 90, "accessibilityCompliance": "N/A", "performanceImpact": "low", "technicalDebt": {"score": "low", "issues": []}}, "container.py": {"status": "active", "directDependencies": ["dependency_injector"], "indirectDependencies": [], "influenceScore": 10, "testCoverage": 92, "accessibilityCompliance": "N/A", "performanceImpact": "medium", "technicalDebt": {"score": "low", "issues": []}}}}, "src/presentation": {"description": "Presentation layer containing API routes and controllers", "files": {"api/v1/router.py": {"status": "active", "directDependencies": ["<PERSON><PERSON><PERSON>"], "indirectDependencies": [], "influenceScore": 8, "testCoverage": 90, "accessibilityCompliance": "N/A", "performanceImpact": "medium", "technicalDebt": {"score": "low", "issues": []}}, "middleware/error_handler.py": {"status": "active", "directDependencies": ["<PERSON><PERSON><PERSON>", "starlette"], "indirectDependencies": [], "influenceScore": 8, "testCoverage": 92, "accessibilityCompliance": "N/A", "performanceImpact": "medium", "technicalDebt": {"score": "low", "issues": []}}, "middleware/rate_limit.py": {"status": "active", "directDependencies": ["<PERSON><PERSON><PERSON>", "starlette", "redis"], "indirectDependencies": [], "influenceScore": 7, "testCoverage": 88, "accessibilityCompliance": "N/A", "performanceImpact": "high", "technicalDebt": {"score": "medium", "issues": ["Could benefit from more sophisticated rate limiting algorithm"]}}, "dependencies/auth.py": {"status": "active", "directDependencies": ["<PERSON><PERSON><PERSON>", "infrastructure/security/jwt"], "indirectDependencies": ["domain/entities/user"], "influenceScore": 9, "testCoverage": 95, "accessibilityCompliance": "N/A", "performanceImpact": "high", "technicalDebt": {"score": "low", "issues": []}}}}, "src/main.py": {"description": "Main application entry point", "status": "active", "directDependencies": ["<PERSON><PERSON><PERSON>", "infrastructure/container", "presentation/api/v1/router"], "indirectDependencies": ["infrastructure/database/connection", "infrastructure/config/settings"], "influenceScore": 10, "testCoverage": 90, "accessibilityCompliance": "N/A", "performanceImpact": "high", "technicalDebt": {"score": "low", "issues": []}}}, "infra": {"docker-compose.yml": {"description": "Docker Compose configuration for local development", "status": "active", "directDependencies": [], "indirectDependencies": [], "influenceScore": 9, "testCoverage": "N/A", "accessibilityCompliance": "N/A", "performanceImpact": "high", "technicalDebt": {"score": "low", "issues": []}}, "docker-compose.prod.yml": {"description": "Docker Compose configuration for production", "status": "active", "directDependencies": [], "indirectDependencies": [], "influenceScore": 8, "testCoverage": "N/A", "accessibilityCompliance": "N/A", "performanceImpact": "high", "technicalDebt": {"score": "low", "issues": []}}, "frontend/Dockerfile": {"description": "Dockerfile for frontend container", "status": "active", "directDependencies": [], "indirectDependencies": [], "influenceScore": 8, "testCoverage": "N/A", "accessibilityCompliance": "N/A", "performanceImpact": "medium", "technicalDebt": {"score": "low", "issues": []}}, "backend/Dockerfile": {"description": "Dockerfile for backend container", "status": "active", "directDependencies": [], "indirectDependencies": [], "influenceScore": 8, "testCoverage": "N/A", "accessibilityCompliance": "N/A", "performanceImpact": "medium", "technicalDebt": {"score": "low", "issues": []}}, "infra/nginx/nginx.conf": {"description": "Nginx configuration for production", "status": "active", "directDependencies": [], "indirectDependencies": [], "influenceScore": 7, "testCoverage": "N/A", "accessibilityCompliance": "N/A", "performanceImpact": "high", "technicalDebt": {"score": "low", "issues": []}}, "infra/database/init/init.sql": {"description": "Database initialization script", "status": "active", "directDependencies": [], "indirectDependencies": [], "influenceScore": 8, "testCoverage": "N/A", "accessibilityCompliance": "N/A", "performanceImpact": "medium", "technicalDebt": {"score": "low", "issues": []}}}, "ci": {".github/workflows/ci.yml": {"description": "GitHub Actions CI/CD pipeline", "status": "active", "directDependencies": [], "indirectDependencies": [], "influenceScore": 9, "testCoverage": "N/A", "accessibilityCompliance": "N/A", "performanceImpact": "medium", "technicalDebt": {"score": "medium", "issues": ["Could benefit from more efficient caching strategy"]}}}, "summary": {"totalFiles": 42, "activeFiles": 42, "deprecatedFiles": 0, "needsRefactoringFiles": 4, "averageTestCoverage": 92.5, "averageInfluenceScore": 8.6, "technicalDebtDistribution": {"low": 38, "medium": 4, "high": 0}, "performanceImpactDistribution": {"low": 10, "medium": 18, "high": 14}, "accessibilityComplianceDistribution": {"AAA": 8, "AA": 10, "A": 0, "N/A": 24}}}