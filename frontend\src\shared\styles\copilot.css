/**
 * CopilotKit custom styles to match Lonors design system
 */

/* CopilotKit Sidebar Styling */
.copilot-sidebar {
  @apply border-l border-border bg-background;
  width: 400px;
  max-width: 90vw;
}

/* CopilotKit Chat Interface */
.copilot-chat {
  @apply bg-background text-foreground;
}

.copilot-chat-header {
  @apply border-b border-border bg-muted/50 px-4 py-3;
}

.copilot-chat-title {
  @apply text-lg font-semibold text-foreground;
}

.copilot-chat-messages {
  @apply flex-1 overflow-y-auto p-4 space-y-4;
}

/* Message Styling */
.copilot-message {
  @apply rounded-lg p-3 max-w-[85%];
}

.copilot-message--user {
  @apply bg-primary text-primary-foreground ml-auto;
}

.copilot-message--assistant {
  @apply bg-muted text-muted-foreground mr-auto;
}

.copilot-message--system {
  @apply bg-accent text-accent-foreground mx-auto text-center text-sm;
}

/* Input Styling */
.copilot-input-container {
  @apply border-t border-border bg-background p-4;
}

.copilot-input {
  @apply flex-1 resize-none rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50;
}

.copilot-send-button {
  @apply inline-flex items-center justify-center rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 bg-primary text-primary-foreground hover:bg-primary/90 h-10 px-4 py-2 ml-2;
}

/* Loading States */
.copilot-loading {
  @apply animate-pulse;
}

.copilot-typing-indicator {
  @apply flex items-center space-x-1 text-muted-foreground;
}

.copilot-typing-dot {
  @apply w-2 h-2 bg-current rounded-full animate-bounce;
}

.copilot-typing-dot:nth-child(2) {
  animation-delay: 0.1s;
}

.copilot-typing-dot:nth-child(3) {
  animation-delay: 0.2s;
}

/* Suggestions */
.copilot-suggestions {
  @apply flex flex-wrap gap-2 p-4 border-t border-border;
}

.copilot-suggestion {
  @apply inline-flex items-center rounded-full border border-input bg-background px-3 py-1 text-xs font-medium transition-colors hover:bg-accent hover:text-accent-foreground focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 cursor-pointer;
}

/* Dark mode adjustments */
.dark .copilot-sidebar {
  @apply border-border;
}

.dark .copilot-message--assistant {
  @apply bg-muted/50;
}

/* Responsive Design */
@media (max-width: 768px) {
  .copilot-sidebar {
    width: 100vw;
    max-width: 100vw;
  }
}

/* Animation for sidebar open/close */
.copilot-sidebar-enter {
  transform: translateX(100%);
}

.copilot-sidebar-enter-active {
  transform: translateX(0);
  transition: transform 300ms ease-in-out;
}

.copilot-sidebar-exit {
  transform: translateX(0);
}

.copilot-sidebar-exit-active {
  transform: translateX(100%);
  transition: transform 300ms ease-in-out;
}
