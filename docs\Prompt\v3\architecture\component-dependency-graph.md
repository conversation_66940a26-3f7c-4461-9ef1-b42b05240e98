# Component Dependency Graph

This diagram illustrates the dependencies between components in the Lonors AI Platform, following the Feature Slice Design (FSD) architecture.

```mermaid
graph TD
    %% FSD Layers
    subgraph "App Layer"
        App[App]
        Router[Router]
        Providers[Global Providers]
        Store[Global Store]
    end

    subgraph "Pages Layer"
        HomePage[Home Page]
        DashboardPage[Dashboard Page]
        AuthPages[Auth Pages]
        ProfilePage[Profile Page]
    end

    subgraph "Widgets Layer"
        Header[Header Widget]
        Sidebar[Sidebar Widget]
        Footer[Footer Widget]
        AgentPanel[Agent Panel Widget]
        ChatWidget[Chat Widget]
    end

    subgraph "Features Layer"
        AuthFeature[Authentication Feature]
        UserManagement[User Management Feature]
        AgentInteraction[Agent Interaction Feature]
        Notifications[Notifications Feature]
        DynamicUI[Dynamic UI Feature]
    end

    subgraph "Entities Layer"
        UserEntity[User Entity]
        SessionEntity[Session Entity]
        AgentEntity[Agent Entity]
        MessageEntity[Message Entity]
        NotificationEntity[Notification Entity]
    end

    subgraph "Shared Layer"
        UIComponents[UI Components]
        Hooks[Custom Hooks]
        Utils[Utilities]
        APIClient[API Client]
        Types[Type Definitions]
        Constants[Constants]
    end

    %% Dependencies between layers
    App --> Router
    App --> Providers
    Router --> HomePage
    Router --> DashboardPage
    Router --> AuthPages
    Router --> ProfilePage

    HomePage --> Header
    HomePage --> Footer
    DashboardPage --> Header
    DashboardPage --> Sidebar
    DashboardPage --> AgentPanel
    DashboardPage --> ChatWidget

    Header --> AuthFeature
    Header --> Notifications
    Sidebar --> UserManagement
    AgentPanel --> AgentInteraction
    ChatWidget --> AgentInteraction
    ChatWidget --> DynamicUI

    AuthFeature --> UserEntity
    AuthFeature --> SessionEntity
    UserManagement --> UserEntity
    AgentInteraction --> AgentEntity
    AgentInteraction --> MessageEntity
    Notifications --> NotificationEntity
    DynamicUI --> AgentEntity

    %% Shared layer dependencies
    AuthFeature --> UIComponents
    AuthFeature --> Hooks
    AuthFeature --> APIClient
    UserManagement --> UIComponents
    UserManagement --> Hooks
    UserManagement --> APIClient
    AgentInteraction --> UIComponents
    AgentInteraction --> Hooks
    AgentInteraction --> APIClient
    Notifications --> UIComponents
    Notifications --> Hooks
    Notifications --> APIClient
    DynamicUI --> UIComponents
    DynamicUI --> Hooks
    DynamicUI --> APIClient

    %% Entity dependencies
    UserEntity --> Types
    SessionEntity --> Types
    AgentEntity --> Types
    MessageEntity --> Types
    NotificationEntity --> Types

    %% Shared utilities
    AuthFeature --> Utils
    UserManagement --> Utils
    AgentInteraction --> Utils
    Notifications --> Utils
    DynamicUI --> Utils

    %% Store dependencies
    AuthFeature --> Store
    UserManagement --> Store
    AgentInteraction --> Store
    Notifications --> Store
    DynamicUI --> Store

    %% Constants
    AuthFeature --> Constants
    UserManagement --> Constants
    AgentInteraction --> Constants
    Notifications --> Constants
    DynamicUI --> Constants
```

## Layer Responsibilities

### App Layer
- **App**: Root component that initializes the application
- **Router**: Handles routing and navigation
- **Providers**: Global context providers (auth, theme, etc.)
- **Store**: Global state management

### Pages Layer
- **Home Page**: Landing page for the application
- **Dashboard Page**: Main workspace for authenticated users
- **Auth Pages**: Login, registration, password reset
- **Profile Page**: User profile management

### Widgets Layer
- **Header**: Top navigation and user controls
- **Sidebar**: Navigation menu and context switching
- **Footer**: Footer information and links
- **Agent Panel**: Agent management interface
- **Chat Widget**: Conversation interface with AI agents

### Features Layer
- **Authentication Feature**: User authentication and session management
- **User Management Feature**: User profile and settings
- **Agent Interaction Feature**: Communication with AI agents
- **Notifications Feature**: System and user notifications
- **Dynamic UI Feature**: AG-UI protocol implementation

### Entities Layer
- **User Entity**: User data and operations
- **Session Entity**: Session data and operations
- **Agent Entity**: Agent data and operations
- **Message Entity**: Message data and operations
- **Notification Entity**: Notification data and operations

### Shared Layer
- **UI Components**: Reusable UI components
- **Hooks**: Custom React hooks
- **Utils**: Utility functions
- **API Client**: API communication
- **Types**: TypeScript type definitions
- **Constants**: Application constants

## Dependency Rules

1. **Unidirectional Dependencies**: Higher layers can depend on lower layers, but not vice versa
2. **Layer Isolation**: Each layer should only depend on the layers below it
3. **Shared Layer Access**: All layers can access the shared layer
4. **Entity Independence**: Entities should not depend on features or widgets
5. **Feature Independence**: Features should not depend on other features
6. **Widget Composition**: Widgets can compose multiple features
7. **Page Composition**: Pages can compose multiple widgets
