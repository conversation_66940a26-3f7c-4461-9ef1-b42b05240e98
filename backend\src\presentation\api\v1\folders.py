"""
Folders API Endpoints

This module defines the API endpoints for folders.
"""

from uuid import UUID

from fastapi import APIRouter, Depends, HTTPException, Query, status

from src.application.dto.folder_dto import (
    CreateFolderDTO,
    FolderDTO,
    FolderFilterDTO,
    UpdateFolderDTO,
)
from src.application.use_cases.folder_service import FolderService
from src.presentation.dependencies.auth import get_current_user
from src.presentation.dependencies.services import get_folder_service

router = APIRouter(prefix="/folders", tags=["Folders"])


@router.get("/", response_model=list[FolderDTO])
async def get_folders(
    parent_id: UUID | None = Query(None, description="Filter by parent folder ID"),
    include_archived: bool = Query(False, description="Include archived folders"),
    current_user=Depends(get_current_user),
    folder_service: FolderService = Depends(get_folder_service),
):
    """
    Get all folders for the current user with optional filtering.
    """
    filters = FolderFilterDTO(
        parent_id=parent_id,
        include_archived=include_archived,
    )

    return await folder_service.get_folders(current_user.id, filters)


@router.get("/{folder_id}", response_model=FolderDTO)
async def get_folder(
    folder_id: UUID,
    current_user=Depends(get_current_user),
    folder_service: FolderService = Depends(get_folder_service),
):
    """
    Get a specific folder by ID.
    """
    folder = await folder_service.get_folder(folder_id, current_user.id)

    if folder is None:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Folder not found",
        )

    return folder


@router.post("/", response_model=FolderDTO, status_code=status.HTTP_201_CREATED)
async def create_folder(
    create_dto: CreateFolderDTO,
    current_user=Depends(get_current_user),
    folder_service: FolderService = Depends(get_folder_service),
):
    """
    Create a new folder.
    """
    return await folder_service.create_folder(current_user.id, create_dto)


@router.put("/{folder_id}", response_model=FolderDTO)
async def update_folder(
    folder_id: UUID,
    update_dto: UpdateFolderDTO,
    current_user=Depends(get_current_user),
    folder_service: FolderService = Depends(get_folder_service),
):
    """
    Update an existing folder.
    """
    folder = await folder_service.update_folder(folder_id, current_user.id, update_dto)

    if folder is None:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Folder not found",
        )

    return folder


@router.delete("/{folder_id}", status_code=status.HTTP_204_NO_CONTENT)
async def delete_folder(
    folder_id: UUID,
    current_user=Depends(get_current_user),
    folder_service: FolderService = Depends(get_folder_service),
):
    """
    Delete a folder.
    """
    success = await folder_service.delete_folder(folder_id, current_user.id)

    if not success:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Folder not found",
        )


@router.patch("/{folder_id}/archive", response_model=FolderDTO)
async def archive_folder(
    folder_id: UUID,
    current_user=Depends(get_current_user),
    folder_service: FolderService = Depends(get_folder_service),
):
    """
    Archive a folder.
    """
    folder = await folder_service.archive_folder(folder_id, current_user.id)

    if folder is None:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Folder not found",
        )

    return folder


@router.patch("/{folder_id}/unarchive", response_model=FolderDTO)
async def unarchive_folder(
    folder_id: UUID,
    current_user=Depends(get_current_user),
    folder_service: FolderService = Depends(get_folder_service),
):
    """
    Unarchive a folder.
    """
    folder = await folder_service.unarchive_folder(folder_id, current_user.id)

    if folder is None:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Folder not found",
        )

    return folder
