#!/usr/bin/env python3
"""
Test runner for backend database implementation.

This script validates the database implementation by running
basic functionality tests without requiring a full test environment.
"""

import asyncio
import sys
import uuid
from datetime import datetime
from pathlib import Path

# Add src to path for imports
sys.path.insert(0, str(Path(__file__).parent / "src"))

from src.domain.entities.folder import Folder
from src.domain.entities.note import Note, NoteContent, NoteFormat
from src.domain.entities.tag import Tag
from src.domain.entities.user import User, UserRole, UserStatus
from src.infrastructure.database.models.folder import FolderModel
from src.infrastructure.database.models.note import NoteModel
from src.infrastructure.database.models.tag import TagModel
from src.infrastructure.database.models.user import UserModel


def test_user_model():
    """Test UserModel functionality."""
    print("Testing UserModel...")
    
    # Create user entity
    user = User(
        id=uuid.uuid4(),
        email="<EMAIL>",
        username="testuser",
        full_name="Test User",
        hashed_password="hashed_password",
        role=UserRole.USER,
        status=UserStatus.ACTIVE,
        is_verified=True,
        created_at=datetime.now(),
        updated_at=datetime.now(),
    )
    
    # Convert to model and back
    user_model = UserModel.from_domain_entity(user)
    converted_user = user_model.to_domain_entity()
    
    # Verify conversion
    assert converted_user.id == user.id
    assert converted_user.email == user.email
    assert converted_user.username == user.username
    assert converted_user.role == user.role
    assert converted_user.status == user.status
    
    print("✓ UserModel tests passed")


def test_folder_model():
    """Test FolderModel functionality."""
    print("Testing FolderModel...")
    
    # Create folder entity
    folder = Folder(
        id=uuid.uuid4(),
        name="Test Folder",
        parent_id=None,
        user_id=uuid.uuid4(),
        is_archived=False,
        created_at=datetime.now(),
        updated_at=datetime.now(),
        metadata={"key": "value"}
    )
    
    # Convert to model and back
    folder_model = FolderModel.from_domain_entity(folder)
    converted_folder = folder_model.to_domain_entity()
    
    # Verify conversion
    assert converted_folder.id == folder.id
    assert converted_folder.name == folder.name
    assert converted_folder.parent_id == folder.parent_id
    assert converted_folder.user_id == folder.user_id
    assert converted_folder.is_archived == folder.is_archived
    
    print("✓ FolderModel tests passed")


def test_tag_model():
    """Test TagModel functionality."""
    print("Testing TagModel...")
    
    # Create tag entity
    tag = Tag(
        id=uuid.uuid4(),
        name="test-tag",
        color="#FF0000",
        user_id=uuid.uuid4(),
        created_at=datetime.now(),
        updated_at=datetime.now(),
    )
    
    # Convert to model and back
    tag_model = TagModel.from_domain_entity(tag)
    converted_tag = tag_model.to_domain_entity()
    
    # Verify conversion
    assert converted_tag.id == tag.id
    assert converted_tag.name == tag.name
    assert converted_tag.color == tag.color
    assert converted_tag.user_id == tag.user_id
    
    print("✓ TagModel tests passed")


def test_note_model():
    """Test NoteModel functionality."""
    print("Testing NoteModel...")
    
    # Create note entity
    content = NoteContent(
        content="Test content",
        format=NoteFormat.MARKDOWN,
        version=1
    )
    
    note = Note(
        id=uuid.uuid4(),
        title="Test Note",
        content=content,
        folder_id=uuid.uuid4(),
        tags=["test", "example"],
        is_archived=False,
        is_starred=True,
        created_at=datetime.now(),
        updated_at=datetime.now(),
        created_by=uuid.uuid4(),
        last_edited_by=uuid.uuid4(),
    )
    
    # Convert to model and back
    note_model = NoteModel.from_domain_entity(note)
    converted_note = note_model.to_domain_entity()
    
    # Verify conversion
    assert converted_note.id == note.id
    assert converted_note.title == note.title
    assert converted_note.content.content == note.content.content
    assert converted_note.content.format == note.content.format
    assert converted_note.content.version == note.content.version
    assert converted_note.folder_id == note.folder_id
    assert converted_note.tags == note.tags
    assert converted_note.is_archived == note.is_archived
    assert converted_note.is_starred == note.is_starred
    assert converted_note.created_by == note.created_by
    assert converted_note.last_edited_by == note.last_edited_by
    
    print("✓ NoteModel tests passed")


def test_model_relationships():
    """Test model relationship handling."""
    print("Testing model relationships...")
    
    user_id = uuid.uuid4()
    folder_id = uuid.uuid4()
    
    # Create related entities
    user = User(
        id=user_id,
        email="<EMAIL>",
        username="testuser",
        full_name="Test User",
        hashed_password="hashed_password",
        role=UserRole.USER,
        status=UserStatus.ACTIVE,
        is_verified=True,
        created_at=datetime.now(),
        updated_at=datetime.now(),
    )
    
    folder = Folder(
        id=folder_id,
        name="Test Folder",
        parent_id=None,
        user_id=user_id,
        is_archived=False,
        created_at=datetime.now(),
        updated_at=datetime.now(),
        metadata={}
    )
    
    note = Note(
        id=uuid.uuid4(),
        title="Test Note",
        content=NoteContent(
            content="Test content",
            format=NoteFormat.MARKDOWN,
            version=1
        ),
        folder_id=folder_id,
        tags=["test"],
        is_archived=False,
        is_starred=False,
        created_at=datetime.now(),
        updated_at=datetime.now(),
        created_by=user_id,
        last_edited_by=user_id,
    )
    
    tag = Tag(
        id=uuid.uuid4(),
        name="test",
        color="#FF0000",
        user_id=user_id,
        created_at=datetime.now(),
        updated_at=datetime.now(),
    )
    
    # Verify relationships are properly set
    assert folder.user_id == user.id
    assert note.folder_id == folder.id
    assert note.created_by == user.id
    assert note.last_edited_by == user.id
    assert tag.user_id == user.id
    assert "test" in note.tags
    
    print("✓ Model relationship tests passed")


def test_container_imports():
    """Test that container imports work correctly."""
    print("Testing container imports...")
    
    try:
        from src.infrastructure.container import Container
        from src.infrastructure.database.repositories.note_repository_impl import SQLAlchemyNoteRepository
        from src.infrastructure.database.repositories.folder_repository_impl import SQLAlchemyFolderRepository
        from src.infrastructure.database.repositories.tag_repository_impl import SQLAlchemyTagRepository
        from src.infrastructure.database.repositories.user_repository import UserRepository
        
        # Create container instance
        container = Container()
        
        print("✓ Container import tests passed")
        
    except ImportError as e:
        print(f"✗ Container import failed: {e}")
        return False
    
    return True


def main():
    """Run all tests."""
    print("Running backend database implementation tests...\n")
    
    try:
        test_user_model()
        test_folder_model()
        test_tag_model()
        test_note_model()
        test_model_relationships()
        
        if test_container_imports():
            print("\n✓ All tests passed! Database implementation is working correctly.")
            return 0
        else:
            print("\n✗ Some tests failed.")
            return 1
            
    except Exception as e:
        print(f"\n✗ Test failed with error: {e}")
        import traceback
        traceback.print_exc()
        return 1


if __name__ == "__main__":
    sys.exit(main())
