"""
Tests for main application module.

This module tests the main FastAPI application setup,
middleware configuration, and basic endpoints.
"""

import pytest
from fastapi.testclient import TestClient
from httpx import AsyncClient


class TestMainApplication:
    """Test cases for main application."""
    
    def test_app_creation(self, test_app):
        """Test that the application is created successfully."""
        assert test_app is not None
        assert test_app.title == "Lonors Backend"
        assert test_app.version == "1.0.0"
    
    def test_root_endpoint(self, client: TestClient):
        """Test root endpoint returns correct information."""
        response = client.get("/")
        
        assert response.status_code == 200
        data = response.json()
        
        assert data["message"] == "Lonors Backend API"
        assert data["version"] == "1.0.0"
        assert data["environment"] == "testing"
        assert "docs" in data
    
    def test_health_endpoint(self, client: TestClient):
        """Test health endpoint returns healthy status."""
        response = client.get("/health")
        
        assert response.status_code == 200
        data = response.json()
        
        assert data["status"] == "healthy"
        assert data["version"] == "1.0.0"
        assert data["environment"] == "testing"
        assert "timestamp" in data
    
    @pytest.mark.asyncio
    async def test_async_client(self, async_client: AsyncClient):
        """Test async client functionality."""
        response = await async_client.get("/")
        
        assert response.status_code == 200
        data = response.json()
        assert data["message"] == "Lonors Backend API"


class TestMiddleware:
    """Test cases for middleware functionality."""
    
    def test_cors_headers(self, client: TestClient):
        """Test CORS headers are properly set."""
        response = client.options("/", headers={"Origin": "http://localhost:5500"})
        
        # CORS headers should be present
        assert "access-control-allow-origin" in response.headers
    
    def test_security_headers(self, client: TestClient):
        """Test security headers are properly set."""
        response = client.get("/")
        
        # Security headers should be present
        assert response.headers.get("X-Content-Type-Options") == "nosniff"
        assert response.headers.get("X-Frame-Options") == "DENY"
        assert response.headers.get("X-XSS-Protection") == "1; mode=block"
        assert "Content-Security-Policy" in response.headers
    
    def test_request_id_header(self, client: TestClient):
        """Test request ID header is added."""
        response = client.get("/")
        
        assert "X-Request-ID" in response.headers
        assert len(response.headers["X-Request-ID"]) > 0
    
    def test_compression_header(self, client: TestClient):
        """Test compression is applied for large responses."""
        # This would need a larger response to test compression
        response = client.get("/")
        
        # Just verify the response is successful
        assert response.status_code == 200


class TestErrorHandling:
    """Test cases for error handling middleware."""
    
    def test_404_error(self, client: TestClient):
        """Test 404 error handling."""
        response = client.get("/nonexistent-endpoint")
        
        assert response.status_code == 404
        data = response.json()
        assert "detail" in data
    
    def test_method_not_allowed(self, client: TestClient):
        """Test 405 method not allowed error."""
        response = client.post("/")  # Root only accepts GET
        
        assert response.status_code == 405
        data = response.json()
        assert "detail" in data
