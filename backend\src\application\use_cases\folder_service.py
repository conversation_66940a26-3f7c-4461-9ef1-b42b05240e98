"""
Folder Service Module

This module implements the application service for folders.
"""

from uuid import UUID

from src.application.dto.folder_dto import (
    CreateFolderDTO,
    FolderDTO,
    FolderFilterDTO,
    UpdateFolderDTO,
)
from src.domain.entities.folder import Folder
from src.domain.repositories.folder_repository import FolderRepository


class FolderService:
    """Service for managing folders."""

    def __init__(self, folder_repository: FolderRepository):
        """
        Initialize the folder service.

        Args:
            folder_repository: Repository for folder operations
        """
        self._folder_repository = folder_repository

    async def get_folder(self, folder_id: UUID, user_id: UUID) -> FolderDTO | None:
        """
        Get a folder by ID.

        Args:
            folder_id: ID of the folder to retrieve
            user_id: ID of the user making the request

        Returns:
            The folder if found and owned by the user, None otherwise
        """
        folder = await self._folder_repository.get_by_id(folder_id)

        if folder is None or folder.user_id != user_id:
            return None

        return self._to_folder_dto(folder)

    async def get_folders(
        self, user_id: UUID, filters: FolderFilterDTO
    ) -> list[FolderDTO]:
        """
        Get all folders for a user with optional filtering.

        Args:
            user_id: ID of the user
            filters: Filtering options

        Returns:
            List of folders matching the criteria
        """
        folders = await self._folder_repository.get_all_by_user(
            user_id=user_id,
            parent_id=filters.parent_id,
            include_archived=filters.include_archived,
        )

        return [self._to_folder_dto(folder) for folder in folders]

    async def create_folder(
        self, user_id: UUID, create_dto: CreateFolderDTO
    ) -> FolderDTO:
        """
        Create a new folder.

        Args:
            user_id: ID of the user creating the folder
            create_dto: Data for the new folder

        Returns:
            The created folder
        """
        folder = Folder(
            name=create_dto.name,
            parent_id=create_dto.parent_id,
            user_id=user_id,
            metadata=create_dto.metadata,
        )

        created_folder = await self._folder_repository.create(folder)
        return self._to_folder_dto(created_folder)

    async def update_folder(
        self, folder_id: UUID, user_id: UUID, update_dto: UpdateFolderDTO
    ) -> FolderDTO | None:
        """
        Update an existing folder.

        Args:
            folder_id: ID of the folder to update
            user_id: ID of the user making the update
            update_dto: Data to update

        Returns:
            The updated folder if found and owned by the user, None otherwise
        """
        folder = await self._folder_repository.get_by_id(folder_id)

        if folder is None or folder.user_id != user_id:
            return None

        # Update fields if provided
        if update_dto.name is not None:
            folder.update_name(update_dto.name)

        if update_dto.parent_id is not None:
            folder.update_parent(update_dto.parent_id)

        if update_dto.is_archived is not None:
            if update_dto.is_archived:
                folder.archive()
            else:
                folder.unarchive()

        if update_dto.metadata is not None:
            folder.update_metadata(update_dto.metadata)

        updated_folder = await self._folder_repository.update(folder)
        return self._to_folder_dto(updated_folder)

    async def delete_folder(self, folder_id: UUID, user_id: UUID) -> bool:
        """
        Delete a folder.

        Args:
            folder_id: ID of the folder to delete
            user_id: ID of the user making the request

        Returns:
            True if the folder was deleted, False otherwise
        """
        folder = await self._folder_repository.get_by_id(folder_id)

        if folder is None or folder.user_id != user_id:
            return False

        return await self._folder_repository.delete(folder_id)

    async def archive_folder(self, folder_id: UUID, user_id: UUID) -> FolderDTO | None:
        """
        Archive a folder.

        Args:
            folder_id: ID of the folder to archive
            user_id: ID of the user making the request

        Returns:
            The archived folder if found and owned by the user, None otherwise
        """
        folder = await self._folder_repository.get_by_id(folder_id)

        if folder is None or folder.user_id != user_id:
            return None

        folder.archive()

        updated_folder = await self._folder_repository.update(folder)
        return self._to_folder_dto(updated_folder)

    async def unarchive_folder(
        self, folder_id: UUID, user_id: UUID
    ) -> FolderDTO | None:
        """
        Unarchive a folder.

        Args:
            folder_id: ID of the folder to unarchive
            user_id: ID of the user making the request

        Returns:
            The unarchived folder if found and owned by the user, None otherwise
        """
        folder = await self._folder_repository.get_by_id(folder_id)

        if folder is None or folder.user_id != user_id:
            return None

        folder.unarchive()

        updated_folder = await self._folder_repository.update(folder)
        return self._to_folder_dto(updated_folder)

    def _to_folder_dto(self, folder: Folder) -> FolderDTO:
        """
        Convert a domain Folder entity to a FolderDTO.

        Args:
            folder: The domain entity

        Returns:
            The DTO representation
        """
        return FolderDTO(
            id=folder.id,
            name=folder.name,
            parent_id=folder.parent_id,
            user_id=folder.user_id,
            is_archived=folder.is_archived,
            created_at=folder.created_at,
            updated_at=folder.updated_at,
            metadata=folder.metadata,
        )
