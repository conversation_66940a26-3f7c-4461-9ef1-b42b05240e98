"""
Tag DTOs Module

This module defines the Data Transfer Objects for the tags feature.
"""

from datetime import datetime
from uuid import UUID

from pydantic import BaseModel


class TagDTO(BaseModel):
    """DTO for a tag."""

    id: UUID
    name: str
    color: str | None = None
    user_id: UUID
    created_at: datetime


class CreateTagDTO(BaseModel):
    """DTO for creating a new tag."""

    name: str
    color: str | None = None


class UpdateTagDTO(BaseModel):
    """DTO for updating an existing tag."""

    name: str | None = None
    color: str | None = None
