"""Rate limiting middleware using Redis."""

from typing import Callable, Optional
from datetime import timed<PERSON><PERSON>

from fastapi import Request, Response, status, HTTPException
from fastapi.responses import JSONResponse
from starlette.middleware.base import BaseHTTPMiddleware

from src.infrastructure.cache.cache_service import CacheService
from src.infrastructure.container import get_container


class RateLimitMiddleware(BaseHTTPMiddleware):
    """Middleware for rate limiting API requests."""
    
    def __init__(
        self,
        app,
        requests_per_minute: int = 60,
        burst_size: int = 10,
        exclude_paths: Optional[list[str]] = None
    ):
        """Initialize rate limit middleware.
        
        Args:
            app: FastAPI application
            requests_per_minute: Maximum requests per minute
            burst_size: Additional burst capacity
            exclude_paths: Paths to exclude from rate limiting
        """
        super().__init__(app)
        self.requests_per_minute = requests_per_minute
        self.burst_size = burst_size
        self.exclude_paths = exclude_paths or ["/health", "/docs", "/openapi.json"]
        
    async def dispatch(self, request: Request, call_next: Callable) -> Response:
        """Process the request with rate limiting.
        
        Args:
            request: Incoming request
            call_next: Next middleware/handler
            
        Returns:
            Response object
        """
        # Skip rate limiting for excluded paths
        if any(request.url.path.startswith(path) for path in self.exclude_paths):
            return await call_next(request)
            
        # Get cache service
        container = get_container()
        cache_service = container.get(CacheService)
        
        # Get client identifier (IP address or user ID)
        client_id = self._get_client_id(request)
        
        # Check rate limit
        is_allowed, current_count = await cache_service.check_rate_limit(
            identifier=client_id,
            limit=self.requests_per_minute + self.burst_size,
            window=timedelta(minutes=1)
        )
        
        if not is_allowed:
            return JSONResponse(
                status_code=status.HTTP_429_TOO_MANY_REQUESTS,
                content={
                    "detail": "Too many requests",
                    "retry_after": 60
                },
                headers={
                    "X-RateLimit-Limit": str(self.requests_per_minute),
                    "X-RateLimit-Remaining": "0",
                    "X-RateLimit-Reset": "60",
                    "Retry-After": "60"
                }
            )
            
        # Process request
        response = await call_next(request)
        
        # Add rate limit headers
        remaining = max(0, self.requests_per_minute - current_count)
        response.headers["X-RateLimit-Limit"] = str(self.requests_per_minute)
        response.headers["X-RateLimit-Remaining"] = str(remaining)
        response.headers["X-RateLimit-Reset"] = "60"
        
        return response
        
    def _get_client_id(self, request: Request) -> str:
        """Get client identifier from request.
        
        Args:
            request: Incoming request
            
        Returns:
            Client identifier string
        """
        # Try to get authenticated user ID
        if hasattr(request.state, "user") and request.state.user:
            return f"user:{request.state.user.id}"
            
        # Fall back to IP address
        forwarded_for = request.headers.get("X-Forwarded-For")
        if forwarded_for:
            client_ip = forwarded_for.split(",")[0].strip()
        else:
            client_ip = request.client.host if request.client else "unknown"
            
        return f"ip:{client_ip}"


def rate_limit_dependency(
    requests_per_minute: int = 60,
    burst_size: int = 10
) -> Callable:
    """Create a rate limit dependency for specific endpoints.
    
    Args:
        requests_per_minute: Maximum requests per minute
        burst_size: Additional burst capacity
        
    Returns:
        Dependency function
    """
    async def _rate_limit(request: Request) -> None:
        """Check rate limit for the request."""
        container = get_container()
        cache_service = container.get(CacheService)
        
        # Get client identifier
        forwarded_for = request.headers.get("X-Forwarded-For")
        if forwarded_for:
            client_ip = forwarded_for.split(",")[0].strip()
        else:
            client_ip = request.client.host if request.client else "unknown"
            
        client_id = f"endpoint:{request.url.path}:ip:{client_ip}"
        
        # Check rate limit
        is_allowed, _ = await cache_service.check_rate_limit(
            identifier=client_id,
            limit=requests_per_minute + burst_size,
            window=timedelta(minutes=1)
        )
        
        if not is_allowed:
            raise HTTPException(
                status_code=status.HTTP_429_TOO_MANY_REQUESTS,
                detail="Too many requests"
            )
            
    return _rate_limit 