"""
Note DTOs Module

This module defines the Data Transfer Objects for the notes feature.
"""

from datetime import datetime
from uuid import UUID

from pydantic import BaseModel, Field

from src.domain.entities.note import NoteFormat


class NoteContentDTO(BaseModel):
    """DTO for note content."""

    content: str
    format: NoteFormat
    version: int


class NoteMetadataDTO(BaseModel):
    """DTO for note metadata."""

    id: UUID
    title: str
    folder_id: UUID | None = None
    tags: list[str] = Field(default_factory=list)
    is_archived: bool
    is_starred: bool
    created_at: datetime
    updated_at: datetime
    created_by: UUID
    last_edited_by: UUID


class NoteDTO(BaseModel):
    """DTO for a complete note."""

    metadata: NoteMetadataDTO
    content: NoteContentDTO


class CreateNoteDTO(BaseModel):
    """DTO for creating a new note."""

    title: str
    content: str
    format: NoteFormat = NoteFormat.MARKDOWN
    folder_id: UUID | None = None
    tags: list[str] = Field(default_factory=list)


class UpdateNoteDTO(BaseModel):
    """DTO for updating an existing note."""

    title: str | None = None
    content: str | None = None
    format: NoteFormat | None = None
    folder_id: UUID | None = None
    tags: list[str] | None = None
    is_archived: bool | None = None
    is_starred: bool | None = None


class NoteFilterDTO(BaseModel):
    """DTO for filtering notes."""

    folder_id: UUID | None = None
    include_archived: bool = False
    only_starred: bool = False
    tags: list[str] | None = None
    search_query: str | None = None
    sort_by: str | None = "updated_at"
    sort_direction: str | None = "desc"
