# Production Environment Configuration
# Copy this file to .env.production and fill in the values

# Database Configuration
POSTGRES_DB=lonors_db
POSTGRES_USER=lonors_user
POSTGRES_PASSWORD=your-secure-postgres-password

# Redis Configuration
REDIS_PASSWORD=your-secure-redis-password

# Backend Configuration
JWT_SECRET=your-super-secure-jwt-secret-change-this-in-production
JWT_ALGORITHM=HS256
JWT_EXPIRES_IN=7d
JWT_REFRESH_EXPIRES_IN=30d

# CORS Configuration
CORS_ORIGINS=https://app.lonors.com,https://www.lonors.com

# API Configuration
API_V1_PREFIX=/api/v1
LOG_LEVEL=info

# Frontend Configuration
VITE_API_URL=https://api.lonors.com
VITE_APP_NAME=Lonors
VITE_APP_VERSION=1.0.0
VITE_ENVIRONMENT=production

# Email Configuration (if needed)
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_USER=<EMAIL>
SMTP_PASSWORD=your-smtp-password
SMTP_FROM_EMAIL=<EMAIL>
SMTP_FROM_NAME=Lonors

# AWS S3 Configuration (if needed)
AWS_ACCESS_KEY_ID=your-aws-access-key
AWS_SECRET_ACCESS_KEY=your-aws-secret-key
AWS_REGION=us-east-1
AWS_S3_BUCKET=lonors-production

# Monitoring Configuration
SENTRY_DSN=your-sentry-dsn
NEW_RELIC_LICENSE_KEY=your-new-relic-key

# Feature Flags
ENABLE_SIGNUP=true
ENABLE_SOCIAL_AUTH=false
ENABLE_MFA=true

# Rate Limiting
RATE_LIMIT_PER_MINUTE=100
RATE_LIMIT_BURST=20

# Session Configuration
SESSION_LIFETIME=86400
SESSION_EXTENSION=3600 