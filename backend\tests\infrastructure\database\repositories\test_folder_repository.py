"""
Unit tests for SQLAlchemyFolderRepository.

This module contains comprehensive tests for the folder repository
implementation including CRUD operations and filtering.
"""

import uuid
from datetime import datetime
from unittest.mock import AsyncMock, MagicMock

import pytest
from sqlalchemy.ext.asyncio import AsyncSession

from src.domain.entities.folder import Folder
from src.infrastructure.database.models.folder import FolderModel
from src.infrastructure.database.repositories.folder_repository_impl import SQLAlchemyFolderRepository


class TestSQLAlchemyFolderRepository:
    """Test cases for SQLAlchemyFolderRepository."""

    @pytest.fixture
    def mock_session(self):
        """Create a mock async session."""
        return AsyncMock(spec=AsyncSession)

    @pytest.fixture
    def repository(self, mock_session):
        """Create a repository instance with mock session."""
        return SQLAlchemyFolderRepository(mock_session)

    @pytest.fixture
    def sample_folder(self):
        """Create a sample folder entity."""
        return Folder(
            id=uuid.uuid4(),
            name="Test Folder",
            parent_id=None,
            user_id=uuid.uuid4(),
            is_archived=False,
            created_at=datetime.now(),
            updated_at=datetime.now(),
            metadata={"key": "value"}
        )

    @pytest.fixture
    def sample_folder_model(self, sample_folder):
        """Create a sample folder model."""
        return FolderModel.from_domain_entity(sample_folder)

    async def test_get_by_id_found(self, repository, mock_session, sample_folder_model):
        """Test getting a folder by ID when it exists."""
        # Setup
        mock_result = MagicMock()
        mock_result.scalar_one_or_none.return_value = sample_folder_model
        mock_session.execute.return_value = mock_result

        # Execute
        result = await repository.get_by_id(sample_folder_model.id)

        # Verify
        assert result is not None
        assert result.id == sample_folder_model.id
        assert result.name == sample_folder_model.name
        mock_session.execute.assert_called_once()

    async def test_get_by_id_not_found(self, repository, mock_session):
        """Test getting a folder by ID when it doesn't exist."""
        # Setup
        mock_result = MagicMock()
        mock_result.scalar_one_or_none.return_value = None
        mock_session.execute.return_value = mock_result

        # Execute
        result = await repository.get_by_id(uuid.uuid4())

        # Verify
        assert result is None
        mock_session.execute.assert_called_once()

    async def test_get_all_by_user_basic(self, repository, mock_session, sample_folder_model):
        """Test getting all folders for a user with basic filtering."""
        # Setup
        mock_result = MagicMock()
        mock_result.scalars.return_value.all.return_value = [sample_folder_model]
        mock_session.execute.return_value = mock_result

        # Execute
        result = await repository.get_all_by_user(sample_folder_model.user_id)

        # Verify
        assert len(result) == 1
        assert result[0].id == sample_folder_model.id
        mock_session.execute.assert_called_once()

    async def test_get_all_by_user_with_parent_filter(self, repository, mock_session, sample_folder_model):
        """Test getting folders with parent filter."""
        # Setup
        mock_result = MagicMock()
        mock_result.scalars.return_value.all.return_value = [sample_folder_model]
        mock_session.execute.return_value = mock_result

        # Execute
        result = await repository.get_all_by_user(
            user_id=sample_folder_model.user_id,
            parent_id=sample_folder_model.parent_id
        )

        # Verify
        assert len(result) == 1
        mock_session.execute.assert_called_once()

    async def test_get_all_by_user_include_archived(self, repository, mock_session, sample_folder_model):
        """Test getting folders including archived ones."""
        # Setup
        mock_result = MagicMock()
        mock_result.scalars.return_value.all.return_value = [sample_folder_model]
        mock_session.execute.return_value = mock_result

        # Execute
        result = await repository.get_all_by_user(
            user_id=sample_folder_model.user_id,
            include_archived=True
        )

        # Verify
        assert len(result) == 1
        mock_session.execute.assert_called_once()

    async def test_create_folder(self, repository, mock_session, sample_folder):
        """Test creating a new folder."""
        # Setup
        mock_session.flush = AsyncMock()
        mock_session.refresh = AsyncMock()

        # Execute
        result = await repository.create(sample_folder)

        # Verify
        assert result.id == sample_folder.id
        assert result.name == sample_folder.name
        mock_session.add.assert_called_once()
        mock_session.flush.assert_called_once()
        mock_session.refresh.assert_called_once()

    async def test_update_folder_found(self, repository, mock_session, sample_folder, sample_folder_model):
        """Test updating an existing folder."""
        # Setup
        mock_result = MagicMock()
        mock_result.scalar_one_or_none.return_value = sample_folder_model
        mock_session.execute.return_value = mock_result
        mock_session.flush = AsyncMock()
        mock_session.refresh = AsyncMock()

        # Modify the folder
        updated_folder = sample_folder.model_copy()
        updated_folder.name = "Updated Folder"

        # Execute
        result = await repository.update(updated_folder)

        # Verify
        assert result.name == "Updated Folder"
        mock_session.execute.assert_called_once()
        mock_session.flush.assert_called_once()
        mock_session.refresh.assert_called_once()

    async def test_update_folder_not_found(self, repository, mock_session, sample_folder):
        """Test updating a folder that doesn't exist."""
        # Setup
        mock_result = MagicMock()
        mock_result.scalar_one_or_none.return_value = None
        mock_session.execute.return_value = mock_result

        # Execute & Verify
        with pytest.raises(ValueError, match="Folder with ID .* not found"):
            await repository.update(sample_folder)

    async def test_delete_folder_found(self, repository, mock_session, sample_folder_model):
        """Test deleting an existing folder."""
        # Setup
        mock_result = MagicMock()
        mock_result.scalar_one_or_none.return_value = sample_folder_model
        mock_session.execute.return_value = mock_result
        mock_session.delete = AsyncMock()
        mock_session.flush = AsyncMock()

        # Execute
        result = await repository.delete(sample_folder_model.id)

        # Verify
        assert result is True
        mock_session.execute.assert_called_once()
        mock_session.delete.assert_called_once_with(sample_folder_model)
        mock_session.flush.assert_called_once()

    async def test_delete_folder_not_found(self, repository, mock_session):
        """Test deleting a folder that doesn't exist."""
        # Setup
        mock_result = MagicMock()
        mock_result.scalar_one_or_none.return_value = None
        mock_session.execute.return_value = mock_result

        # Execute
        result = await repository.delete(uuid.uuid4())

        # Verify
        assert result is False
        mock_session.execute.assert_called_once()

    async def test_get_all_by_user_empty_result(self, repository, mock_session):
        """Test getting folders when no folders exist."""
        # Setup
        mock_result = MagicMock()
        mock_result.scalars.return_value.all.return_value = []
        mock_session.execute.return_value = mock_result

        # Execute
        result = await repository.get_all_by_user(uuid.uuid4())

        # Verify
        assert len(result) == 0
        mock_session.execute.assert_called_once()

    async def test_get_all_by_user_archived_filter(self, repository, mock_session):
        """Test filtering archived folders."""
        # Setup
        mock_result = MagicMock()
        mock_result.scalars.return_value.all.return_value = []
        mock_session.execute.return_value = mock_result

        # Execute with include_archived=False (default)
        await repository.get_all_by_user(uuid.uuid4(), include_archived=False)

        # Verify that the query was executed
        mock_session.execute.assert_called_once()

    async def test_get_root_folders(self, repository, mock_session):
        """Test getting root folders (parent_id=None)."""
        # Setup
        mock_result = MagicMock()
        mock_result.scalars.return_value.all.return_value = []
        mock_session.execute.return_value = mock_result

        # Execute with parent_id=None
        await repository.get_all_by_user(uuid.uuid4(), parent_id=None)

        # Verify that the query was executed
        mock_session.execute.assert_called_once()

    async def test_get_child_folders(self, repository, mock_session):
        """Test getting child folders of a specific parent."""
        # Setup
        mock_result = MagicMock()
        mock_result.scalars.return_value.all.return_value = []
        mock_session.execute.return_value = mock_result

        # Execute with specific parent_id
        parent_id = uuid.uuid4()
        await repository.get_all_by_user(uuid.uuid4(), parent_id=parent_id)

        # Verify that the query was executed
        mock_session.execute.assert_called_once()

    async def test_folder_hierarchy_operations(self, repository, mock_session):
        """Test folder hierarchy operations."""
        # Create parent folder
        parent_folder = Folder(
            id=uuid.uuid4(),
            name="Parent Folder",
            parent_id=None,
            user_id=uuid.uuid4(),
            is_archived=False,
            created_at=datetime.now(),
            updated_at=datetime.now(),
            metadata={}
        )

        # Create child folder
        child_folder = Folder(
            id=uuid.uuid4(),
            name="Child Folder",
            parent_id=parent_folder.id,
            user_id=parent_folder.user_id,
            is_archived=False,
            created_at=datetime.now(),
            updated_at=datetime.now(),
            metadata={}
        )

        # Setup mocks for create operations
        mock_session.flush = AsyncMock()
        mock_session.refresh = AsyncMock()

        # Test creating parent folder
        await repository.create(parent_folder)
        assert mock_session.add.call_count == 1

        # Test creating child folder
        await repository.create(child_folder)
        assert mock_session.add.call_count == 2

        # Verify both folders were added
        mock_session.add.assert_called()
        assert mock_session.flush.call_count == 2
        assert mock_session.refresh.call_count == 2
