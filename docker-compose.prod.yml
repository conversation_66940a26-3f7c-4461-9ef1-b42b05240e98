version: '3.8'

# Docker secrets for production security
secrets:
  postgres_password:
    file: ./secrets/postgres_password.txt
  redis_password:
    file: ./secrets/redis_password.txt
  jwt_secret:
    file: ./secrets/jwt_secret.txt
  ssl_cert:
    file: ./secrets/ssl_cert.pem
  ssl_key:
    file: ./secrets/ssl_key.pem

services:
  # PostgreSQL Database with production optimizations
  postgres:
    image: postgres:15-alpine
    container_name: lonors-postgres-prod
    environment:
      POSTGRES_DB: ${POSTGRES_DB:-lonors_db}
      POSTGRES_USER: ${POSTGRES_USER:-lonors_user}
      POSTGRES_PASSWORD_FILE: /run/secrets/postgres_password
      POSTGRES_INITDB_ARGS: "--encoding=UTF8 --locale=C"
      # Production optimizations
      POSTGRES_SHARED_PRELOAD_LIBRARIES: "pg_stat_statements"
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./infra/database/init:/docker-entrypoint-initdb.d:ro
    networks:
      - lonors-network
    secrets:
      - postgres_password
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U ${POSTGRES_USER:-lonors_user} -d ${POSTGRES_DB:-lonors_db}"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s
    restart: unless-stopped
    deploy:
      resources:
        limits:
          cpus: '4'
          memory: 4G
        reservations:
          cpus: '1'
          memory: 1G
    command: >
      postgres
      -c shared_preload_libraries=pg_stat_statements
      -c pg_stat_statements.track=all
      -c max_connections=200
      -c shared_buffers=1GB
      -c effective_cache_size=3GB
      -c maintenance_work_mem=256MB
      -c checkpoint_completion_target=0.9
      -c wal_buffers=16MB
      -c default_statistics_target=100
      -c random_page_cost=1.1
      -c effective_io_concurrency=200

  # DragonflyDB for production caching
  dragonflydb:
    image: docker.dragonflydb.io/dragonflydb/dragonfly:v1.15.1
    container_name: lonors-dragonfly-prod
    volumes:
      - dragonfly_data:/data
    networks:
      - lonors-network
    secrets:
      - redis_password
    healthcheck:
      test: ["CMD", "redis-cli", "-p", "6379", "-a", "$(cat /run/secrets/redis_password)", "ping"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 30s
    restart: unless-stopped
    deploy:
      resources:
        limits:
          cpus: '2'
          memory: 2G
        reservations:
          cpus: '0.5'
          memory: 512M
    command: >
      dragonfly
      --logtostderr
      --alsologtostderr=false
      --port=6379
      --maxmemory=1gb
      --cache_mode=false
      --requirepass_file=/run/secrets/redis_password

  # Backend API (Python FastAPI) with production security
  backend:
    build:
      context: ./backend
      dockerfile: Dockerfile
      target: production
      args:
        BUILDKIT_INLINE_CACHE: 1
    container_name: lonors-backend-prod
    environment:
      ENVIRONMENT: production
      DATABASE_URL_FILE: /run/secrets/database_url
      REDIS_URL_FILE: /run/secrets/redis_url
      JWT_SECRET_FILE: /run/secrets/jwt_secret
      JWT_ALGORITHM: ${JWT_ALGORITHM:-HS256}
      JWT_EXPIRES_IN: ${JWT_EXPIRES_IN:-7d}
      PORT: 3001
      HOST: 0.0.0.0
      CORS_ORIGINS: ${CORS_ORIGINS}
      LOG_LEVEL: ${LOG_LEVEL:-info}
      # Production optimizations
      PYTHONOPTIMIZE: 2
      PYTHONHASHSEED: random
      WORKERS: ${BACKEND_WORKERS:-4}
    networks:
      - lonors-network
    secrets:
      - postgres_password
      - redis_password
      - jwt_secret
    depends_on:
      postgres:
        condition: service_healthy
      dragonflydb:
        condition: service_healthy
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:3001/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 60s
    restart: unless-stopped
    deploy:
      resources:
        limits:
          cpus: '4'
          memory: 4G
        reservations:
          cpus: '1'
          memory: 1G
      replicas: 1
    security_opt:
      - no-new-privileges:true
    read_only: true
    tmpfs:
      - /tmp:noexec,nosuid,size=100m
      - /var/tmp:noexec,nosuid,size=50m



  # Nginx reverse proxy (optional, for production)
  nginx:
    image: nginx:alpine
    container_name: lonors-nginx-prod
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./infra/nginx/nginx.prod.conf:/etc/nginx/nginx.conf:ro
      - ./infra/nginx/ssl:/etc/nginx/ssl:ro
      - nginx_logs:/var/log/nginx
    networks:
      - lonors-network
    depends_on:
      backend:
        condition: service_healthy
    healthcheck:
      test: ["CMD", "wget", "--no-verbose", "--tries=1", "--spider", "http://localhost/health"]
      interval: 30s
      timeout: 10s
      retries: 3
    restart: unless-stopped

volumes:
  # Database volumes with production settings
  postgres_data:
    driver: local
    driver_opts:
      type: none
      o: bind
      device: /var/lib/lonors/postgres
  dragonfly_data:
    driver: local
    driver_opts:
      type: none
      o: bind
      device: /var/lib/lonors/dragonfly

  # Application volumes
  nginx_logs:
    driver: local
    driver_opts:
      type: none
      o: bind
      device: /var/log/lonors/nginx

networks:
  lonors-network:
    driver: bridge
    ipam:
      config:
        - subnet: **********/16
          gateway: **********
    driver_opts:
      com.docker.network.bridge.name: lonors-prod-bridge
      com.docker.network.bridge.enable_icc: "true"
      com.docker.network.bridge.enable_ip_masquerade: "true"
      com.docker.network.driver.mtu: "1500"
