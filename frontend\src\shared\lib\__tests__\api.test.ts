import { afterEach, beforeEach, describe, expect, it, vi } from 'vitest';
import {
    API_CONFIG,
    API_ENDPOINTS,
    apiClient,
    ApiException,
    createApiClient,
    HTTP_STATUS,
    removeAuthToken,
    setAuthToken,
} from '../api';

// Mock fetch
const mockFetch = vi.fn();
global.fetch = mockFetch;

// Mock localStorage
const mockLocalStorage = {
  getItem: vi.fn(),
  setItem: vi.fn(),
  removeItem: vi.fn(),
  clear: vi.fn(),
};
Object.defineProperty(window, 'localStorage', {
  value: mockLocalStorage,
});

describe('API Configuration', () => {
  describe('API_CONFIG', () => {
    it('has correct default values', () => {
      expect(API_CONFIG.BASE_URL).toBe('http://localhost:3001');
      expect(API_CONFIG.API_VERSION).toBe('v1');
      expect(API_CONFIG.TIMEOUT).toBe(10000);
      expect(API_CONFIG.RETRY_ATTEMPTS).toBe(3);
      expect(API_CONFIG.RETRY_DELAY).toBe(1000);
    });

    it('uses environment variable for BASE_URL when available', () => {
      const originalEnv = process.env.NEXT_PUBLIC_API_URL;
      process.env.NEXT_PUBLIC_API_URL = 'https://api.example.com';

      // Re-import to get updated config
      vi.resetModules();

      process.env.NEXT_PUBLIC_API_URL = originalEnv;
    });
  });

  describe('API_ENDPOINTS', () => {
    it('has all required endpoint categories', () => {
      expect(API_ENDPOINTS).toHaveProperty('HEALTH');
      expect(API_ENDPOINTS).toHaveProperty('AUTH');
      expect(API_ENDPOINTS).toHaveProperty('USERS');
      expect(API_ENDPOINTS).toHaveProperty('AGENTS');
      expect(API_ENDPOINTS).toHaveProperty('WORKFLOWS');
      expect(API_ENDPOINTS).toHaveProperty('KNOWLEDGE');
      expect(API_ENDPOINTS).toHaveProperty('WEBSOCKET');
    });

    it('generates dynamic endpoints correctly', () => {
      expect(API_ENDPOINTS.USERS.BY_ID('123')).toBe('/api/v1/users/123');
      expect(API_ENDPOINTS.AGENTS.BY_ID('agent-456')).toBe('/api/v1/agents/agent-456');
      expect(API_ENDPOINTS.WORKFLOWS.EXECUTE('workflow-789')).toBe('/api/v1/workflows/workflow-789/execute');
    });

    it('has correct WebSocket endpoints', () => {
      expect(API_ENDPOINTS.WEBSOCKET.MCP).toBe('/ws/mcp');
      expect(API_ENDPOINTS.WEBSOCKET.A2A).toBe('/ws/a2a');
      expect(API_ENDPOINTS.WEBSOCKET.AG_UI).toBe('/ws/ag-ui');
    });
  });

  describe('HTTP_STATUS', () => {
    it('contains all standard HTTP status codes', () => {
      expect(HTTP_STATUS.OK).toBe(200);
      expect(HTTP_STATUS.CREATED).toBe(201);
      expect(HTTP_STATUS.BAD_REQUEST).toBe(400);
      expect(HTTP_STATUS.UNAUTHORIZED).toBe(401);
      expect(HTTP_STATUS.NOT_FOUND).toBe(404);
      expect(HTTP_STATUS.INTERNAL_SERVER_ERROR).toBe(500);
    });
  });
});

describe('ApiException', () => {
  it('creates exception with all properties', () => {
    const error = {
      message: 'Test error',
      status: 400,
      code: 'TEST_ERROR',
      details: { field: 'value' },
    };

    const exception = new ApiException(error);

    expect(exception.message).toBe('Test error');
    expect(exception.status).toBe(400);
    expect(exception.code).toBe('TEST_ERROR');
    expect(exception.details).toEqual({ field: 'value' });
    expect(exception.name).toBe('ApiException');
  });

  it('creates exception with minimal properties', () => {
    const error = {
      message: 'Simple error',
      status: 500,
    };

    const exception = new ApiException(error);

    expect(exception.message).toBe('Simple error');
    expect(exception.status).toBe(500);
    expect(exception.code).toBeUndefined();
    expect(exception.details).toBeUndefined();
  });
});

describe('Authentication Token Management', () => {
  beforeEach(() => {
    mockLocalStorage.getItem.mockClear();
    mockLocalStorage.setItem.mockClear();
    mockLocalStorage.removeItem.mockClear();
  });

  it('sets auth token', () => {
    setAuthToken('test-token');
    expect(mockLocalStorage.setItem).toHaveBeenCalledWith('auth_token', 'test-token');
  });

  it('removes auth token', () => {
    removeAuthToken();
    expect(mockLocalStorage.removeItem).toHaveBeenCalledWith('auth_token');
  });

  it('handles server-side rendering gracefully', () => {
    const originalWindow = global.window;
    delete (global as any).window;

    expect(() => setAuthToken('token')).not.toThrow();
    expect(() => removeAuthToken()).not.toThrow();

    global.window = originalWindow;
  });
});

describe('API Client', () => {
  let client: ReturnType<typeof createApiClient>;

  beforeEach(() => {
    client = createApiClient();
    mockFetch.mockClear();
    mockLocalStorage.getItem.mockReturnValue(null);
  });

  afterEach(() => {
    vi.clearAllTimers();
  });

  describe('Successful Requests', () => {
    it('makes GET request successfully', async () => {
      const responseData = { data: { id: 1, name: 'Test' } };
      mockFetch.mockResolvedValueOnce({
        ok: true,
        status: 200,
        headers: new Headers({ 'content-type': 'application/json' }),
        json: () => Promise.resolve(responseData),
      });

      const result = await client.get('/test');

      expect(mockFetch).toHaveBeenCalledWith(
        'http://localhost:3001/test',
        expect.objectContaining({
          method: 'GET',
          headers: expect.objectContaining({
            'Content-Type': 'application/json',
            'Accept': 'application/json',
          }),
        })
      );

      expect(result).toEqual({
        data: responseData.data,
        message: undefined,
        status: 200,
      });
    });

    it('makes POST request with body', async () => {
      const requestBody = { name: 'New Item' };
      const responseData = { data: { id: 2, name: 'New Item' } };

      mockFetch.mockResolvedValueOnce({
        ok: true,
        status: 201,
        headers: new Headers({ 'content-type': 'application/json' }),
        json: () => Promise.resolve(responseData),
      });

      const result = await client.post('/items', requestBody);

      expect(mockFetch).toHaveBeenCalledWith(
        'http://localhost:3001/items',
        expect.objectContaining({
          method: 'POST',
          body: JSON.stringify(requestBody),
        })
      );

      expect(result.status).toBe(201);
    });

    it('includes authorization header when token exists', async () => {
      mockLocalStorage.getItem.mockReturnValue('test-token');

      mockFetch.mockResolvedValueOnce({
        ok: true,
        status: 200,
        headers: new Headers({ 'content-type': 'application/json' }),
        json: () => Promise.resolve({ data: {} }),
      });

      await client.get('/protected');

      expect(mockFetch).toHaveBeenCalledWith(
        expect.any(String),
        expect.objectContaining({
          headers: expect.objectContaining({
            'Authorization': 'Bearer test-token',
          }),
        })
      );
    });

    it('handles non-JSON responses', async () => {
      const textResponse = 'Plain text response';
      mockFetch.mockResolvedValueOnce({
        ok: true,
        status: 200,
        headers: new Headers({ 'content-type': 'text/plain' }),
        text: () => Promise.resolve(textResponse),
      });

      const result = await client.get('/text');

      expect(result.data).toBe(textResponse);
    });
  });

  describe('Error Handling', () => {
    it('handles HTTP error responses', async () => {
      const errorResponse = {
        message: 'Not found',
        code: 'NOT_FOUND',
        details: { resource: 'user' },
      };

      mockFetch.mockResolvedValueOnce({
        ok: false,
        status: 404,
        headers: new Headers({ 'content-type': 'application/json' }),
        json: () => Promise.resolve(errorResponse),
      });

      try {
        await client.get('/nonexistent');
        // Should not reach here
        expect(true).toBe(false);
      } catch (error) {
        expect(error).toBeInstanceOf(ApiException);
        expect((error as ApiException).status).toBe(404);
        expect((error as ApiException).message).toBe('Not found');
        expect((error as ApiException).code).toBe('NOT_FOUND');
      }
    });

    it('handles network errors', async () => {
      mockFetch.mockRejectedValueOnce(new TypeError('Network error'));

      try {
        await client.get('/test');
        // Should not reach here
        expect(true).toBe(false);
      } catch (error) {
        expect(error).toBeInstanceOf(ApiException);
        expect((error as ApiException).status).toBe(0);
        expect((error as ApiException).code).toBe('NETWORK_ERROR');
      }
    });

    it('handles timeout errors', async () => {
      // Mock AbortController
      const mockAbortController = {
        signal: { aborted: false },
        abort: vi.fn(),
      };
      global.AbortController = vi.fn(() => mockAbortController) as any;

      // Mock fetch to simulate timeout
      mockFetch.mockImplementationOnce(() => {
        // Simulate timeout by throwing AbortError
        const error = new Error('The operation was aborted');
        error.name = 'AbortError';
        return Promise.reject(error);
      });

      try {
        await client.get('/slow', { timeout: 1000 });
        // Should not reach here
        expect(true).toBe(false);
      } catch (error) {
        expect(error).toBeInstanceOf(ApiException);
        expect((error as ApiException).status).toBe(408);
        expect((error as ApiException).code).toBe('TIMEOUT');
      }
    }, 1000);

    it('handles unknown errors', async () => {
      mockFetch.mockRejectedValueOnce(new Error('Unknown error'));

      try {
        await client.get('/test');
        // Should not reach here
        expect(true).toBe(false);
      } catch (error) {
        expect(error).toBeInstanceOf(ApiException);
        expect((error as ApiException).status).toBe(500);
        expect((error as ApiException).code).toBe('UNKNOWN_ERROR');
      }
    });
  });

  describe('HTTP Methods', () => {
    beforeEach(() => {
      mockFetch.mockResolvedValue({
        ok: true,
        status: 200,
        headers: new Headers({ 'content-type': 'application/json' }),
        json: () => Promise.resolve({ data: {} }),
      });
    });

    it('supports PUT requests', async () => {
      await client.put('/items/1', { name: 'Updated' });

      expect(mockFetch).toHaveBeenCalledWith(
        expect.any(String),
        expect.objectContaining({ method: 'PUT' })
      );
    });

    it('supports PATCH requests', async () => {
      await client.patch('/items/1', { name: 'Patched' });

      expect(mockFetch).toHaveBeenCalledWith(
        expect.any(String),
        expect.objectContaining({ method: 'PATCH' })
      );
    });

    it('supports DELETE requests', async () => {
      await client.delete('/items/1');

      expect(mockFetch).toHaveBeenCalledWith(
        expect.any(String),
        expect.objectContaining({ method: 'DELETE' })
      );
    });
  });

  describe('Request Options', () => {
    it('supports custom headers', async () => {
      mockFetch.mockResolvedValueOnce({
        ok: true,
        status: 200,
        headers: new Headers({ 'content-type': 'application/json' }),
        json: () => Promise.resolve({ data: {} }),
      });

      await client.get('/test', {
        headers: { 'X-Custom-Header': 'custom-value' },
      });

      expect(mockFetch).toHaveBeenCalledWith(
        expect.any(String),
        expect.objectContaining({
          headers: expect.objectContaining({
            'X-Custom-Header': 'custom-value',
          }),
        })
      );
    });

    it('supports abort signal', async () => {
      const controller = new AbortController();

      mockFetch.mockResolvedValueOnce({
        ok: true,
        status: 200,
        headers: new Headers({ 'content-type': 'application/json' }),
        json: () => Promise.resolve({ data: {} }),
      });

      await client.get('/test', { signal: controller.signal });

      expect(mockFetch).toHaveBeenCalledWith(
        expect.any(String),
        expect.objectContaining({
          signal: controller.signal,
        })
      );
    });
  });
});

describe('Default API Client', () => {
  it('exports a default client instance', () => {
    expect(apiClient).toBeDefined();
    expect(typeof apiClient.get).toBe('function');
    expect(typeof apiClient.post).toBe('function');
    expect(typeof apiClient.put).toBe('function');
    expect(typeof apiClient.patch).toBe('function');
    expect(typeof apiClient.delete).toBe('function');
  });
});
