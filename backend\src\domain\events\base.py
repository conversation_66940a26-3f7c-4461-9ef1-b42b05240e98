"""
Base domain event for the Lonors application.

This module contains the base DomainEvent class that all domain events
should inherit from following Domain-Driven Design principles.
"""

from abc import ABC
from datetime import datetime, UTC
from typing import Any, Dict
from uuid import uuid4


class DomainEvent(ABC):
    """Base class for all domain events."""

    def __init__(self, **kwargs: Any) -> None:
        """
        Initialize domain event.

        Args:
            **kwargs: Event-specific data
        """
        self.event_id = str(uuid4())
        self.occurred_at = datetime.now(UTC)
        self.event_type = self.__class__.__name__
        self.data = kwargs

    def to_dict(self) -> Dict[str, Any]:
        """
        Convert event to dictionary representation.

        Returns:
            Dictionary representation of the event
        """
        return {
            "event_id": self.event_id,
            "event_type": self.event_type,
            "occurred_at": self.occurred_at.isoformat(),
            "data": self.data,
        }

    def __eq__(self, other: Any) -> bool:
        """Check equality with another DomainEvent."""
        if not isinstance(other, DomainEvent):
            return False
        return self.event_id == other.event_id

    def __hash__(self) -> int:
        """Return hash of the event ID."""
        return hash(self.event_id)

    def __str__(self) -> str:
        """Return string representation of the event."""
        return f"{self.event_type}(id={self.event_id}, occurred_at={self.occurred_at})"

    def __repr__(self) -> str:
        """Return detailed string representation of the event."""
        return f"{self.event_type}(event_id='{self.event_id}', occurred_at='{self.occurred_at}', data={self.data})"
