"""
LangGraph integration service for AI agent orchestration.

This service provides LangGraph-based agent workflow orchestration
with support for complex multi-agent scenarios and state management.
"""

import asyncio
import json
import logging
from typing import Any, Dict, List, Optional, TypedDict
from uuid import UUID

from langgraph.graph import StateGraph, END
from langgraph.prebuilt import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, ToolInvocation
from langchain.schema import BaseMessage, HumanMessage, AIMessage
from langchain_openai import ChatOpenAI
from langchain.tools import BaseTool

from src.domain.entities.agent import Agent, AgentStatus, AgentType
from src.infrastructure.ai.models.local_model_service import LocalModelService
from src.infrastructure.ai.tools.tool_registry import ToolRegistry

logger = logging.getLogger(__name__)


class AgentState(TypedDict):
    """State structure for LangGraph agent workflows."""
    messages: List[BaseMessage]
    agent_id: str
    task_id: str
    context: Dict[str, Any]
    tools_used: List[str]
    execution_metadata: Dict[str, Any]
    next_action: Optional[str]


class LangGraphService:
    """
    LangGraph-based agent orchestration service.
    
    Provides advanced agent workflow capabilities using LangGraph
    for complex multi-agent scenarios and state management.
    """
    
    def __init__(
        self,
        local_model_service: LocalModelService,
        tool_registry: ToolRegistry,
        default_model: str = "gpt-4",
    ):
        self.local_model_service = local_model_service
        self.tool_registry = tool_registry
        self.default_model = default_model
        self.active_workflows: Dict[str, StateGraph] = {}
        self.workflow_states: Dict[str, AgentState] = {}
        
    async def create_agent_workflow(
        self,
        agent: Agent,
        tools: Optional[List[str]] = None,
        custom_nodes: Optional[Dict[str, Any]] = None,
    ) -> str:
        """
        Create a LangGraph workflow for an agent.
        
        Args:
            agent: Agent entity
            tools: List of tool names to include
            custom_nodes: Custom workflow nodes
            
        Returns:
            str: Workflow ID
        """
        workflow_id = f"workflow_{agent.id}"
        
        # Initialize state
        initial_state: AgentState = {
            "messages": [],
            "agent_id": str(agent.id),
            "task_id": "",
            "context": {},
            "tools_used": [],
            "execution_metadata": {
                "agent_type": agent.agent_type.value,
                "model": agent.configuration.model_name,
                "temperature": agent.configuration.temperature,
            },
            "next_action": None,
        }
        
        # Create workflow graph
        workflow = StateGraph(AgentState)
        
        # Add standard nodes
        workflow.add_node("agent", self._create_agent_node(agent))
        workflow.add_node("tools", self._create_tools_node(tools or []))
        workflow.add_node("supervisor", self._create_supervisor_node())
        
        # Add custom nodes if provided
        if custom_nodes:
            for node_name, node_func in custom_nodes.items():
                workflow.add_node(node_name, node_func)
        
        # Define workflow edges
        workflow.set_entry_point("supervisor")
        workflow.add_conditional_edges(
            "supervisor",
            self._should_continue,
            {
                "continue": "agent",
                "tools": "tools",
                "end": END,
            }
        )
        workflow.add_edge("agent", "supervisor")
        workflow.add_edge("tools", "supervisor")
        
        # Compile workflow
        compiled_workflow = workflow.compile()
        
        # Store workflow and state
        self.active_workflows[workflow_id] = compiled_workflow
        self.workflow_states[workflow_id] = initial_state
        
        logger.info(f"Created LangGraph workflow for agent {agent.id}")
        return workflow_id
    
    async def execute_workflow(
        self,
        workflow_id: str,
        input_message: str,
        context: Optional[Dict[str, Any]] = None,
    ) -> Dict[str, Any]:
        """
        Execute a workflow with input message.
        
        Args:
            workflow_id: Workflow identifier
            input_message: Input message to process
            context: Additional context data
            
        Returns:
            Dict[str, Any]: Execution result
        """
        if workflow_id not in self.active_workflows:
            raise ValueError(f"Workflow {workflow_id} not found")
        
        workflow = self.active_workflows[workflow_id]
        state = self.workflow_states[workflow_id].copy()
        
        # Update state with input
        state["messages"].append(HumanMessage(content=input_message))
        if context:
            state["context"].update(context)
        
        try:
            # Execute workflow
            result = await workflow.ainvoke(state)
            
            # Update stored state
            self.workflow_states[workflow_id] = result
            
            # Extract response
            response = {
                "workflow_id": workflow_id,
                "agent_id": result["agent_id"],
                "response": result["messages"][-1].content if result["messages"] else "",
                "tools_used": result["tools_used"],
                "execution_metadata": result["execution_metadata"],
                "status": "completed",
            }
            
            logger.info(f"Workflow {workflow_id} executed successfully")
            return response
            
        except Exception as e:
            logger.error(f"Workflow execution failed: {e}")
            return {
                "workflow_id": workflow_id,
                "agent_id": state["agent_id"],
                "error": str(e),
                "status": "failed",
            }
    
    async def pause_workflow(self, workflow_id: str) -> bool:
        """Pause a running workflow."""
        if workflow_id in self.workflow_states:
            self.workflow_states[workflow_id]["execution_metadata"]["paused"] = True
            logger.info(f"Workflow {workflow_id} paused")
            return True
        return False
    
    async def resume_workflow(self, workflow_id: str) -> bool:
        """Resume a paused workflow."""
        if workflow_id in self.workflow_states:
            self.workflow_states[workflow_id]["execution_metadata"]["paused"] = False
            logger.info(f"Workflow {workflow_id} resumed")
            return True
        return False
    
    async def cancel_workflow(self, workflow_id: str) -> bool:
        """Cancel a workflow."""
        if workflow_id in self.active_workflows:
            del self.active_workflows[workflow_id]
            del self.workflow_states[workflow_id]
            logger.info(f"Workflow {workflow_id} cancelled")
            return True
        return False
    
    def _create_agent_node(self, agent: Agent):
        """Create agent processing node."""
        async def agent_node(state: AgentState) -> AgentState:
            # Get model based on agent configuration
            if agent.configuration.model_name.startswith("local:"):
                model_name = agent.configuration.model_name.replace("local:", "")
                model = await self.local_model_service.get_model(model_name)
            else:
                model = ChatOpenAI(
                    model=agent.configuration.model_name,
                    temperature=agent.configuration.temperature,
                    max_tokens=agent.configuration.max_tokens,
                )
            
            # Process messages
            messages = state["messages"]
            if messages:
                response = await model.ainvoke(messages)
                state["messages"].append(response)
                
                # Update execution metadata
                state["execution_metadata"]["last_response_time"] = asyncio.get_event_loop().time()
                
                # Check if tools should be called
                if hasattr(response, 'tool_calls') and response.tool_calls:
                    state["next_action"] = "tools"
                else:
                    state["next_action"] = "end"
            
            return state
        
        return agent_node
    
    def _create_tools_node(self, tool_names: List[str]):
        """Create tools execution node."""
        async def tools_node(state: AgentState) -> AgentState:
            # Get available tools
            tools = []
            for tool_name in tool_names:
                tool = await self.tool_registry.get_tool(tool_name)
                if tool:
                    tools.append(tool)
            
            if not tools:
                state["next_action"] = "agent"
                return state
            
            # Execute tools if needed
            last_message = state["messages"][-1] if state["messages"] else None
            if last_message and hasattr(last_message, 'tool_calls'):
                tool_executor = ToolExecutor(tools)
                
                for tool_call in last_message.tool_calls:
                    tool_invocation = ToolInvocation(
                        tool=tool_call["name"],
                        tool_input=tool_call["args"],
                    )
                    
                    try:
                        result = await tool_executor.ainvoke(tool_invocation)
                        state["tools_used"].append(tool_call["name"])
                        
                        # Add tool result to messages
                        state["messages"].append(
                            AIMessage(content=f"Tool {tool_call['name']} result: {result}")
                        )
                        
                    except Exception as e:
                        logger.error(f"Tool execution failed: {e}")
                        state["messages"].append(
                            AIMessage(content=f"Tool {tool_call['name']} failed: {str(e)}")
                        )
            
            state["next_action"] = "agent"
            return state
        
        return tools_node
    
    def _create_supervisor_node(self):
        """Create supervisor node for workflow control."""
        async def supervisor_node(state: AgentState) -> AgentState:
            # Check if workflow is paused
            if state["execution_metadata"].get("paused", False):
                await asyncio.sleep(1)  # Wait before checking again
                return state
            
            # Determine next action based on state
            if not state["messages"]:
                state["next_action"] = "agent"
            elif state["next_action"] == "tools":
                state["next_action"] = "tools"
            elif len(state["messages"]) > 10:  # Prevent infinite loops
                state["next_action"] = "end"
            else:
                state["next_action"] = "agent"
            
            return state
        
        return supervisor_node
    
    def _should_continue(self, state: AgentState) -> str:
        """Determine workflow continuation."""
        next_action = state.get("next_action", "end")
        
        if next_action == "tools":
            return "tools"
        elif next_action == "agent":
            return "continue"
        else:
            return "end"
    
    async def get_workflow_status(self, workflow_id: str) -> Optional[Dict[str, Any]]:
        """Get current workflow status."""
        if workflow_id not in self.workflow_states:
            return None
        
        state = self.workflow_states[workflow_id]
        return {
            "workflow_id": workflow_id,
            "agent_id": state["agent_id"],
            "message_count": len(state["messages"]),
            "tools_used": state["tools_used"],
            "execution_metadata": state["execution_metadata"],
            "next_action": state.get("next_action"),
        }
    
    async def list_active_workflows(self) -> List[str]:
        """List all active workflow IDs."""
        return list(self.active_workflows.keys())
    
    async def cleanup_completed_workflows(self) -> int:
        """Clean up completed workflows and return count."""
        completed_workflows = []
        
        for workflow_id, state in self.workflow_states.items():
            if state.get("next_action") == "end":
                completed_workflows.append(workflow_id)
        
        for workflow_id in completed_workflows:
            if workflow_id in self.active_workflows:
                del self.active_workflows[workflow_id]
            if workflow_id in self.workflow_states:
                del self.workflow_states[workflow_id]
        
        logger.info(f"Cleaned up {len(completed_workflows)} completed workflows")
        return len(completed_workflows)
