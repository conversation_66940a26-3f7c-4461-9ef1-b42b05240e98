# Lonors
## Modern Full Stack Application

A comprehensive full-stack application built with cutting-edge technologies, following best practices for scalability, security, and maintainability.

## Technology Stack

### Frontend
- **React 18+** with TypeScript
- **Vite** for build tooling and development server
- **ShadCN UI** components with Radix UI primitives
- **Tailwind CSS** for styling with custom design system
- **Anime.js** for smooth animations and transitions
- **pnpm** package manager for efficient dependency management
- **Feature Slice Design (FSD)** architecture for scalable code organization
- **Port**: 5500

### Backend
- **Python 3.11+** with FastAPI framework
- **uv** package manager for fast Python dependency management
- **PostgreSQL 15** with async SQLAlchemy for database operations
- **Redis 7** for caching, sessions, and real-time features
- **Clean Architecture** with dependency injection and SOLID principles
- **Port**: 3001

### Infrastructure
- **Docker** and Docker Compose
- **GitHub Actions** for CI/CD
- **PostgreSQL 15** database
- **Redis 7** cache

## Key Features

### Protocol Support
- **Model Context Protocol (MCP)** for AI integration
- **AG-UI Protocol** for advanced UI interactions
- **A2A Protocol** for application communication

### Development Principles
- **SOLID** principles implementation
- **DRY, KISS, YAGNI** methodologies
- **OWASP** security standards
- **Test-Driven Development** (TDD)

## Quick Start

### Prerequisites
- **Node.js 18+** (for frontend development)
- **Python 3.11+** (for backend development)
- **Docker and Docker Compose** (for containerized development)
- **pnpm** (for frontend package management)
- **uv** (for Python package management)

### Installation

1. **Clone the repository**
   ```bash
   git clone <repository-url>
   cd lonors
   ```

2. **Install dependencies**
   ```bash
   # Install root dependencies
   pnpm install

   # Install backend dependencies (Python + FastAPI)
   cd backend && uv sync --dev

   # Install frontend dependencies (when available)
   # cd frontend && pnpm install
   ```

3. **Environment setup**
   ```bash
   # Copy environment template
   cp .env.example .env

   # Edit environment variables as needed
   ```

4. **Start with Docker**
   ```bash
   # Start all services
   docker-compose up -d

   # View logs
   docker-compose logs -f
   ```

5. **Access the application**
   - Frontend: http://localhost:5500
   - Backend API: http://localhost:3001
   - API Documentation: http://localhost:3001/docs
   - API Redoc: http://localhost:3001/redoc

### Development Mode

```bash
# Start all services in development mode
pnpm dev

# Or start individually
pnpm dev:frontend  # Starts React + Vite frontend on port 5500
pnpm dev:backend   # Starts Python FastAPI backend on port 3001
```

## Project Structure

```
lonors/
├── frontend/              # React TypeScript application
│   ├── src/
│   │   ├── app/          # Application layer
│   │   ├── pages/        # Page components
│   │   ├── widgets/      # Complex UI blocks
│   │   ├── features/     # Business features
│   │   ├── entities/     # Business entities
│   │   └── shared/       # Shared resources
│   └── package.json
├── backend/               # Python FastAPI application
│   ├── src/
│   │   ├── domain/       # Business logic and entities
│   │   ├── application/  # Use cases and services
│   │   ├── infrastructure/ # Database, external APIs, etc.
│   │   └── presentation/ # FastAPI routes and schemas
│   ├── tests/            # Backend tests
│   ├── pyproject.toml    # Python dependencies (uv)
│   └── Dockerfile        # Python container config
├── infra/                 # Infrastructure configurations
├── docs/                  # Documentation
├── docker-compose.yml     # Development environment
└── package.json          # Root workspace configuration
```

## Available Scripts

### Root Level
- `pnpm dev` - Start all services in development mode
- `pnpm build` - Build all applications
- `pnpm test` - Run all tests
- `pnpm lint` - Lint all code
- `pnpm docker:up` - Start Docker services
- `pnpm docker:down` - Stop Docker services

### Frontend (React + TypeScript + Vite)
- `pnpm dev` - Start Vite development server (port 5500)
- `pnpm build` - Build for production with Vite
- `pnpm test` - Run Jest and React Testing Library tests
- `pnpm lint` - Lint TypeScript code with ESLint
- `pnpm lint:fix` - Fix linting issues automatically
- `pnpm storybook` - Start Storybook component documentation

### Backend (Python + FastAPI + uv)
- `uv run fastapi dev src/main.py` - Start FastAPI development server
- `uv run pytest` - Run tests with pytest
- `uv run pytest --cov=src` - Run tests with coverage
- `uv run black src/` - Format Python code with Black
- `uv run ruff check src/` - Lint Python code with Ruff
- `uv run ruff check --fix src/` - Fix linting issues automatically

## Testing

### Frontend Testing
- **Unit Tests**: Jest + React Testing Library
- **E2E Tests**: Playwright
- **Visual Tests**: Storybook + Chromatic
- **Accessibility**: axe-core

### Backend Testing
- **Unit Tests**: pytest with async support
- **Integration Tests**: pytest with test database containers
- **API Tests**: httpx async test client with FastAPI
- **Performance Tests**: locust for load testing
- **Security Tests**: bandit for security scanning

### Running Tests
```bash
# Run all tests (when frontend is implemented)
pnpm test

# Run backend tests
cd backend && uv run pytest

# Run backend tests with coverage
cd backend && uv run pytest --cov=src --cov-report=html

# Run backend tests in watch mode
cd backend && uv run pytest --watch

# Run specific test file
cd backend && uv run pytest tests/test_main.py

# Run with coverage report
pnpm test:coverage
```

## Documentation

- **[Comprehensive Product Requirements Document](docs/product/comprehensive-prd.md)** - Detailed product requirements
- **[API Documentation](docs/API_DOCUMENTATION.md)** - API endpoints and usage
- **[Protocols Documentation](docs/PROTOCOLS.md)** - Protocol specifications and usage

### Architecture Documentation

- **[System Architecture Diagram](docs/architecture/system-architecture-diagram.md)** - High-level system architecture
- **[Data Flow Diagram](docs/architecture/data-flow-diagram.md)** - Data flow between components
- **[Component Dependency Graph](docs/architecture/component-dependency-graph.md)** - Component dependencies
- **[CI/CD Pipeline Diagram](docs/architecture/cicd-pipeline-diagram.md)** - CI/CD workflow
- **[Docker Orchestration Diagram](docs/architecture/docker-orchestration-diagram.md)** - Container orchestration
- **[Protocol Integration Diagram](docs/architecture/protocol-integration-diagram.md)** - Protocol integration
- **[Database Schema](docs/architecture/database-schema.md)** - Database design

- **[Interactive API Documentation](http://localhost:3001/docs)** - Interactive API docs (when running)

## Development Workflow

### Branching Strategy
- `main` - Production-ready code
- `develop` - Integration branch
- `staging` - Pre-production testing
- `feature/*` - Feature development
- `hotfix/*` - Critical fixes

### Code Quality
- **Frontend**: ESLint + Prettier for TypeScript/React code formatting
- **Backend**: Black + Ruff for Python code formatting and linting
- **Pre-commit hooks**: Automated code formatting and linting
- **CI/CD**: Automated testing, linting, and security scanning
- **Coverage**: 90%+ test coverage requirement for both frontend and backend

### Security
- OWASP compliance
- Automated security scanning
- Dependency vulnerability checks
- Regular security audits

## Deployment

### Staging
- Automatic deployment from `develop` branch
- Full test suite execution
- Performance testing
- Security scanning

### Production
- Manual deployment from `main` branch
- Blue-green deployment strategy
- Automated rollback capabilities
- Health checks and monitoring

## Contributing

1. Fork the repository
2. Create a feature branch (`git checkout -b feature/amazing-feature`)
3. Commit your changes (`git commit -m 'Add amazing feature'`)
4. Push to the branch (`git push origin feature/amazing-feature`)
5. Open a Pull Request

Please read [CONTRIBUTING.md](docs/CONTRIBUTING.md) for detailed guidelines.

## License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## Support

For support and questions:
- Create an issue in the repository
- Check the documentation in the `docs/` directory
- Review the API documentation at `/docs` endpoint

## Acknowledgments

- Built with modern web technologies
- Follows industry best practices
- Implements cutting-edge protocols
- Designed for scalability and maintainability
