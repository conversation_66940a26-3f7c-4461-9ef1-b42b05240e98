"use client";

import { Co<PERSON>lotKit } from "@copilotkit/react-core";
import { CopilotSidebar } from "@copilotkit/react-ui";
import { ReactNode } from "react";

interface CopilotProviderProps {
  children: ReactNode;
}

/**
 * CopilotKit provider for AI-powered assistance throughout the application.
 * Provides context-aware AI chat and assistance for agent orchestration.
 */
export function CopilotProvider({ children }: CopilotProviderProps) {
  return (
    <CopilotKit
      runtimeUrl="/api/copilotkit"
      // Enable context preservation for agent interactions
      showDevConsole={process.env.NODE_ENV === "development"}
      {...(process.env.NEXT_PUBLIC_COPILOT_API_KEY && {
        publicApiKey: process.env.NEXT_PUBLIC_COPILOT_API_KEY
      })}
    >
      <CopilotSidebar
        // Configure sidebar for agent workflow assistance
        defaultOpen={false}
        clickOutsideToClose={true}
        // Custom styling to match our design system
        className="copilot-sidebar"
        // Instructions for AI assistant
        instructions="You are an AI assistant for the Lonors agent orchestration platform. Help users create, configure, and manage AI agents and workflows. Provide guidance on agent setup, workflow design, and troubleshooting."
        // Labels for better UX
        labels={{
          title: "Lonors AI Assistant",
          initial: "How can I help you with your AI agents today?",
        }}
      >
        {children}
      </CopilotSidebar>
    </CopilotKit>
  );
}

/**
 * Hook to access CopilotKit context and utilities
 */
export { useCopilotAction, useCopilotReadable } from "@copilotkit/react-core";
