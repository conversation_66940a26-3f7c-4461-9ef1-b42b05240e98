"""
User database model.

This module defines the SQLAlchemy model for users
with proper database constraints and relationships.
"""

from datetime import datetime
from typing import TYPE_CHECKING

from sqlalchemy import <PERSON><PERSON><PERSON>, DateTime, Enum, String, Text
from sqlalchemy.orm import Mapped, mapped_column, relationship

from src.domain.entities.user import UserRole, UserStatus
from src.infrastructure.database.models.base import Base

if TYPE_CHECKING:
    from src.domain.entities.user import User
    from src.infrastructure.database.models.folder import FolderModel
    from src.infrastructure.database.models.note import NoteModel
    from src.infrastructure.database.models.tag import TagModel


class UserModel(Base):
    """
    User SQLAlchemy model.

    Represents the users table in the database with all
    necessary constraints and indexes.
    """

    # User identification
    email: Mapped[str] = mapped_column(
        String(255),
        unique=True,
        index=True,
        nullable=False,
        doc="User email address (unique)",
    )

    username: Mapped[str] = mapped_column(
        String(50), unique=True, index=True, nullable=False, doc="Username (unique)"
    )

    full_name: Mapped[str | None] = mapped_column(
        String(100), nullable=True, doc="User's full name"
    )

    # Authentication
    hashed_password: Mapped[str] = mapped_column(
        Text, nullable=False, doc="Bcrypt hashed password"
    )

    # User status and role
    role: Mapped[UserRole] = mapped_column(
        Enum(UserRole), default=UserRole.USER, nullable=False, doc="User role"
    )

    status: Mapped[UserStatus] = mapped_column(
        Enum(UserStatus),
        default=UserStatus.PENDING_VERIFICATION,
        nullable=False,
        doc="User account status",
    )

    is_verified: Mapped[bool] = mapped_column(
        Boolean, default=False, nullable=False, doc="Whether user email is verified"
    )

    # Activity tracking
    last_login: Mapped[datetime | None] = mapped_column(
        DateTime(timezone=True), nullable=True, doc="Last login timestamp"
    )

    # Relationships
    folders: Mapped[list["FolderModel"]] = relationship(
        "FolderModel", back_populates="user", cascade="all, delete-orphan"
    )

    tags: Mapped[list["TagModel"]] = relationship(
        "TagModel", back_populates="user", cascade="all, delete-orphan"
    )

    created_notes: Mapped[list["NoteModel"]] = relationship(
        "NoteModel", foreign_keys="NoteModel.created_by", back_populates="creator"
    )

    edited_notes: Mapped[list["NoteModel"]] = relationship(
        "NoteModel", foreign_keys="NoteModel.last_edited_by", back_populates="editor"
    )

    def __repr__(self) -> str:
        """String representation of the user."""
        return (
            f"<UserModel(id={self.id}, email={self.email}, username={self.username})>"
        )

    def to_domain_entity(self) -> "User":
        """
        Convert database model to domain entity.

        Returns:
            User: Domain entity
        """
        from src.domain.entities.user import User

        return User(
            id=self.id,
            email=self.email,
            username=self.username,
            full_name=self.full_name,
            hashed_password=self.hashed_password,
            role=self.role,
            status=self.status,
            is_verified=self.is_verified,
            last_login=self.last_login,
            created_at=self.created_at,
            updated_at=self.updated_at,
        )

    @classmethod
    def from_domain_entity(cls, user: "User") -> "UserModel":
        """
        Create database model from domain entity.

        Args:
            user: Domain entity

        Returns:
            UserModel: Database model
        """
        return cls(
            id=user.id,
            email=user.email,
            username=user.username,
            full_name=user.full_name,
            hashed_password=user.hashed_password,
            role=user.role,
            status=user.status,
            is_verified=user.is_verified,
            last_login=user.last_login,
            created_at=user.created_at,
            updated_at=user.updated_at,
        )
