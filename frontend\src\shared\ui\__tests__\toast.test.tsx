import { render, screen, fireEvent } from '@testing-library/react';
import { describe, it, expect, vi } from 'vitest';
import {
  Toast,
  ToastProvider,
  ToastViewport,
  ToastAction,
  ToastClose,
  ToastTitle,
  ToastDescription,
} from '../toast';

// Mock Lucide React icons
vi.mock('lucide-react', () => ({
  X: () => <div data-testid="x-icon">X</div>,
}));

// Mock Radix UI Toast
vi.mock('@radix-ui/react-toast', () => ({
  Provider: ({ children, ...props }: any) => <div data-testid="toast-provider" {...props}>{children}</div>,
  Viewport: ({ children, className, ...props }: any) => (
    <div data-testid="toast-viewport" className={className} {...props}>{children}</div>
  ),
  Root: ({ children, className, ...props }: any) => (
    <div data-testid="toast-root" className={className} {...props}>{children}</div>
  ),
  Action: ({ children, className, ...props }: any) => (
    <button data-testid="toast-action" className={className} {...props}>{children}</button>
  ),
  Close: ({ children, className, ...props }: any) => (
    <button data-testid="toast-close" className={className} {...props}>{children}</button>
  ),
  Title: ({ children, className, ...props }: any) => (
    <div data-testid="toast-title" className={className} {...props}>{children}</div>
  ),
  Description: ({ children, className, ...props }: any) => (
    <div data-testid="toast-description" className={className} {...props}>{children}</div>
  ),
}));

describe('Toast Components', () => {
  describe('ToastProvider', () => {
    it('renders provider wrapper', () => {
      render(
        <ToastProvider>
          <div>Toast content</div>
        </ToastProvider>
      );

      expect(screen.getByTestId('toast-provider')).toBeInTheDocument();
      expect(screen.getByText('Toast content')).toBeInTheDocument();
    });
  });

  describe('ToastViewport', () => {
    it('renders viewport with correct classes', () => {
      render(<ToastViewport />);

      const viewport = screen.getByTestId('toast-viewport');
      expect(viewport).toBeInTheDocument();
      expect(viewport).toHaveClass('fixed', 'top-0', 'z-[100]', 'flex');
    });

    it('accepts custom className', () => {
      render(<ToastViewport className="custom-viewport" />);

      const viewport = screen.getByTestId('toast-viewport');
      expect(viewport).toHaveClass('custom-viewport');
    });

    it('forwards ref correctly', () => {
      const ref = vi.fn();
      render(<ToastViewport ref={ref} />);
      
      expect(ref).toHaveBeenCalled();
    });
  });

  describe('Toast', () => {
    it('renders toast with default variant', () => {
      render(
        <Toast>
          <div>Toast content</div>
        </Toast>
      );

      const toast = screen.getByTestId('toast-root');
      expect(toast).toBeInTheDocument();
      expect(toast).toHaveClass('group', 'pointer-events-auto', 'relative');
      expect(screen.getByText('Toast content')).toBeInTheDocument();
    });

    it('applies default variant styling', () => {
      render(<Toast />);

      const toast = screen.getByTestId('toast-root');
      expect(toast).toHaveClass('border', 'bg-background', 'text-foreground');
    });

    it('applies destructive variant styling', () => {
      render(<Toast variant="destructive" />);

      const toast = screen.getByTestId('toast-root');
      expect(toast).toHaveClass('destructive', 'border-destructive');
    });

    it('accepts custom className', () => {
      render(<Toast className="custom-toast" />);

      const toast = screen.getByTestId('toast-root');
      expect(toast).toHaveClass('custom-toast');
    });

    it('forwards ref correctly', () => {
      const ref = vi.fn();
      render(<Toast ref={ref} />);
      
      expect(ref).toHaveBeenCalled();
    });
  });

  describe('ToastAction', () => {
    it('renders action button with correct styling', () => {
      render(
        <ToastAction>
          Undo
        </ToastAction>
      );

      const action = screen.getByTestId('toast-action');
      expect(action).toBeInTheDocument();
      expect(action).toHaveTextContent('Undo');
      expect(action).toHaveClass('inline-flex', 'h-8', 'shrink-0');
    });

    it('handles click events', () => {
      const handleClick = vi.fn();
      
      render(
        <ToastAction onClick={handleClick}>
          Action
        </ToastAction>
      );

      fireEvent.click(screen.getByTestId('toast-action'));
      expect(handleClick).toHaveBeenCalled();
    });

    it('accepts custom className', () => {
      render(<ToastAction className="custom-action">Action</ToastAction>);

      const action = screen.getByTestId('toast-action');
      expect(action).toHaveClass('custom-action');
    });

    it('forwards ref correctly', () => {
      const ref = vi.fn();
      render(<ToastAction ref={ref}>Action</ToastAction>);
      
      expect(ref).toHaveBeenCalled();
    });
  });

  describe('ToastClose', () => {
    it('renders close button with X icon', () => {
      render(<ToastClose />);

      const closeButton = screen.getByTestId('toast-close');
      expect(closeButton).toBeInTheDocument();
      expect(closeButton).toHaveClass('absolute', 'right-2', 'top-2');
      expect(screen.getByTestId('x-icon')).toBeInTheDocument();
    });

    it('handles click events', () => {
      const handleClick = vi.fn();
      
      render(<ToastClose onClick={handleClick} />);

      fireEvent.click(screen.getByTestId('toast-close'));
      expect(handleClick).toHaveBeenCalled();
    });

    it('accepts custom className', () => {
      render(<ToastClose className="custom-close" />);

      const closeButton = screen.getByTestId('toast-close');
      expect(closeButton).toHaveClass('custom-close');
    });

    it('has toast-close attribute', () => {
      render(<ToastClose />);

      const closeButton = screen.getByTestId('toast-close');
      expect(closeButton).toHaveAttribute('toast-close', '');
    });

    it('forwards ref correctly', () => {
      const ref = vi.fn();
      render(<ToastClose ref={ref} />);
      
      expect(ref).toHaveBeenCalled();
    });
  });

  describe('ToastTitle', () => {
    it('renders title with correct styling', () => {
      render(
        <ToastTitle>
          Toast Title
        </ToastTitle>
      );

      const title = screen.getByTestId('toast-title');
      expect(title).toBeInTheDocument();
      expect(title).toHaveTextContent('Toast Title');
      expect(title).toHaveClass('text-sm', 'font-semibold');
    });

    it('accepts custom className', () => {
      render(<ToastTitle className="custom-title">Title</ToastTitle>);

      const title = screen.getByTestId('toast-title');
      expect(title).toHaveClass('custom-title');
    });

    it('forwards ref correctly', () => {
      const ref = vi.fn();
      render(<ToastTitle ref={ref}>Title</ToastTitle>);
      
      expect(ref).toHaveBeenCalled();
    });
  });

  describe('ToastDescription', () => {
    it('renders description with correct styling', () => {
      render(
        <ToastDescription>
          Toast description text
        </ToastDescription>
      );

      const description = screen.getByTestId('toast-description');
      expect(description).toBeInTheDocument();
      expect(description).toHaveTextContent('Toast description text');
      expect(description).toHaveClass('text-sm', 'opacity-90');
    });

    it('accepts custom className', () => {
      render(<ToastDescription className="custom-description">Description</ToastDescription>);

      const description = screen.getByTestId('toast-description');
      expect(description).toHaveClass('custom-description');
    });

    it('forwards ref correctly', () => {
      const ref = vi.fn();
      render(<ToastDescription ref={ref}>Description</ToastDescription>);
      
      expect(ref).toHaveBeenCalled();
    });
  });

  describe('Complete Toast Example', () => {
    it('renders complete toast structure', () => {
      render(
        <ToastProvider>
          <ToastViewport>
            <Toast>
              <ToastTitle>Success!</ToastTitle>
              <ToastDescription>Your action was completed successfully.</ToastDescription>
              <ToastAction>Undo</ToastAction>
              <ToastClose />
            </Toast>
          </ToastViewport>
        </ToastProvider>
      );

      expect(screen.getByTestId('toast-provider')).toBeInTheDocument();
      expect(screen.getByTestId('toast-viewport')).toBeInTheDocument();
      expect(screen.getByTestId('toast-root')).toBeInTheDocument();
      expect(screen.getByText('Success!')).toBeInTheDocument();
      expect(screen.getByText('Your action was completed successfully.')).toBeInTheDocument();
      expect(screen.getByText('Undo')).toBeInTheDocument();
      expect(screen.getByTestId('toast-close')).toBeInTheDocument();
    });

    it('renders destructive toast variant', () => {
      render(
        <Toast variant="destructive">
          <ToastTitle>Error!</ToastTitle>
          <ToastDescription>Something went wrong.</ToastDescription>
          <ToastClose />
        </Toast>
      );

      const toast = screen.getByTestId('toast-root');
      expect(toast).toHaveClass('destructive');
      expect(screen.getByText('Error!')).toBeInTheDocument();
      expect(screen.getByText('Something went wrong.')).toBeInTheDocument();
    });
  });
});
