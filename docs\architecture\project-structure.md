# Project Structure

## Frontend

```
/frontend
  /src
    /features
      /notes
        /api
          note-api.ts
          types.ts
        /components
          Editor.tsx
          NoteCard.tsx
          NoteList.tsx
          NoteView.tsx
          TagSelector.tsx
        /hooks
          useNotes.ts
          useEditor.ts
          useSearch.ts
        /lib
          editor-utils.ts
          markdown.ts
        /store
          note-slice.ts
      /folders
        /api
          folder-api.ts
          types.ts
        /components
          FolderTree.tsx
          FolderSelector.tsx
        /hooks
          useFolders.ts
      /tags
        /api
          tag-api.ts
          types.ts
        /components
          TagList.tsx
          TagBadge.tsx
        /hooks
          useTags.ts
      /ai
        /api
          ai-api.ts
          types.ts
        /components
          AIAssistant.tsx
          CompletionSuggestion.tsx
        /hooks
          useCompletion.ts
          useSummarization.ts
      /auth
        /api
          auth-api.ts
          types.ts
        /components
          LoginForm.tsx
          SignupForm.tsx
        /hooks
          useAuth.ts
      /ui
        /components
          Button.tsx
          Input.tsx
          Modal.tsx
          Dropdown.tsx
          Sidebar.tsx
    /shared
      /api
        core.ts
        types.ts
      /hooks
        useLocalStorage.ts
        useDebounce.ts
      /utils
        date.ts
        string.ts
    /app
      App.tsx
      routes.tsx
      providers.tsx
    /styles
      globals.css
      tailwind.config.js
```

## Backend

```
/backend
  /src
    /api
      /routes
        notes.ts
        folders.ts
        tags.ts
        ai.ts
        auth.ts
      /controllers
        note-controller.ts
        folder-controller.ts
        tag-controller.ts
        ai-controller.ts
        auth-controller.ts
      /middlewares
        auth.ts
        error-handler.ts
        validation.ts
      /validators
        note-validator.ts
        folder-validator.ts
        tag-validator.ts
    /services
      note-service.ts
      folder-service.ts
      tag-service.ts
      ai-service.ts
      auth-service.ts
    /models
      note.ts
      folder.ts
      tag.ts
      user.ts
      connection.ts
    /db
      postgres.ts
      mongodb.ts
      neo4j.ts
      qdrant.ts
    /ai
      /models
        model-manager.ts
        embedding.ts
        completion.ts
      /rag
        retriever.ts
        generator.ts
      /knowledge-graph
        extractor.ts
        graph-builder.ts
    /utils
      logger.ts
      error.ts
      config.ts
    /types
      index.ts
    app.ts
    server.ts
```

## Local Model Manager

```
/model-manager
  /src
    /models
      model-registry.ts
      model-downloader.ts
      model-loader.ts
    /adapters
      ollama-adapter.ts
      huggingface-adapter.ts
      whisper-adapter.ts
    /api
      model-api.ts
      routes.ts
      controllers.ts
    /utils
      cache.ts
      file-utils.ts
    /types
      index.ts
    app.ts
    server.ts
```

## Database Schema

### PostgreSQL

```sql
-- Users
CREATE TABLE users (
  id UUID PRIMARY KEY,
  email VARCHAR(255) UNIQUE NOT NULL,
  password_hash VARCHAR(255) NOT NULL,
  name VARCHAR(255) NOT NULL,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Folders
CREATE TABLE folders (
  id UUID PRIMARY KEY,
  name VARCHAR(255) NOT NULL,
  parent_id UUID REFERENCES folders(id),
  user_id UUID REFERENCES users(id) NOT NULL,
  is_archived BOOLEAN DEFAULT FALSE,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  metadata JSONB DEFAULT '{}'
);

-- Tags
CREATE TABLE tags (
  id UUID PRIMARY KEY,
  name VARCHAR(255) NOT NULL,
  color VARCHAR(50),
  user_id UUID REFERENCES users(id) NOT NULL,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  UNIQUE(name, user_id)
);

-- Note metadata (stored in PostgreSQL)
CREATE TABLE notes_metadata (
  id UUID PRIMARY KEY,
  title VARCHAR(255) NOT NULL,
  user_id UUID REFERENCES users(id) NOT NULL,
  folder_id UUID REFERENCES folders(id),
  is_archived BOOLEAN DEFAULT FALSE,
  is_starred BOOLEAN DEFAULT FALSE,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  metadata JSONB DEFAULT '{}'
);

-- Note tags
CREATE TABLE note_tags (
  note_id UUID REFERENCES notes_metadata(id) ON DELETE CASCADE,
  tag_id UUID REFERENCES tags(id) ON DELETE CASCADE,
  PRIMARY KEY (note_id, tag_id)
);
```

### MongoDB

```javascript
// Notes collection
{
  _id: ObjectId,
  metadata_id: UUID,  // References PostgreSQL notes_metadata.id
  content: Object,    // Slate/ProseMirror document
  format: String,     // 'markdown' or 'richtext'
  version: Number,    // For versioning
  created_at: Date,
  updated_at: Date
}

// Note versions collection
{
  _id: ObjectId,
  note_id: ObjectId,  // References notes._id
  content: Object,    // Slate/ProseMirror document
  version: Number,
  created_at: Date,
  created_by: UUID    // References users.id
}
```

### Neo4j

```cypher
// Nodes
CREATE (n:Note {id: "uuid", title: "Note Title"})
CREATE (t:Tag {id: "uuid", name: "Tag Name"})
CREATE (f:Folder {id: "uuid", name: "Folder Name"})

// Relationships
CREATE (n1:Note)-[:LINKS_TO {context: "Mentioned in paragraph 2"}]->(n2:Note)
CREATE (n:Note)-[:HAS_TAG]->(t:Tag)
CREATE (n:Note)-[:IN_FOLDER]->(f:Folder)
CREATE (f1:Folder)-[:PARENT_OF]->(f2:Folder)
```

### Qdrant

```javascript
// Vector collection for notes
{
  id: "note-uuid",
  vector: [0.1, 0.2, ...],  // Embedding vector
  payload: {
    title: "Note Title",
    snippet: "First 100 characters...",
    metadata_id: "uuid"     // References PostgreSQL notes_metadata.id
  }
}
```
