'use client';

import React, { memo, useCallback } from 'react';
import { <PERSON><PERSON>, Position, NodeProps } from 'reactflow';
import { cn } from '@/shared/lib/utils';
import { Badge } from '@/shared/ui/badge';
import { Button } from '@/shared/ui/button';
import { NodeData } from '../../types';
import {
  GitBranch,
  Settings,
  CheckCircle,
  XCircle,
  AlertTriangle,
  Equal,
  MoreThan,
  LessThan,
  NotEqual,
} from 'lucide-react';

interface ConditionNodeData extends NodeData {
  conditionType: ConditionType;
  condition: string;
  leftOperand?: string;
  operator?: ComparisonOperator;
  rightOperand?: string;
  result?: boolean;
  lastEvaluated?: string;
}

interface ConditionNodeProps extends NodeProps {
  data: ConditionNodeData;
}

export enum ConditionType {
  SIMPLE = 'simple',
  COMPARISON = 'comparison',
  LOGICAL = 'logical',
  CUSTOM = 'custom',
}

export enum ComparisonOperator {
  EQUALS = 'equals',
  NOT_EQUALS = 'not_equals',
  GREATER_THAN = 'greater_than',
  LESS_THAN = 'less_than',
  GREATER_EQUAL = 'greater_equal',
  LESS_EQUAL = 'less_equal',
  CONTAINS = 'contains',
  STARTS_WITH = 'starts_with',
  ENDS_WITH = 'ends_with',
  REGEX = 'regex',
}

// Operator icon mapping
const getOperatorIcon = (operator: ComparisonOperator) => {
  switch (operator) {
    case ComparisonOperator.EQUALS:
      return Equal;
    case ComparisonOperator.NOT_EQUALS:
      return NotEqual;
    case ComparisonOperator.GREATER_THAN:
    case ComparisonOperator.GREATER_EQUAL:
      return MoreThan;
    case ComparisonOperator.LESS_THAN:
    case ComparisonOperator.LESS_EQUAL:
      return LessThan;
    default:
      return Equal;
  }
};

// Operator symbol mapping
const getOperatorSymbol = (operator: ComparisonOperator) => {
  switch (operator) {
    case ComparisonOperator.EQUALS:
      return '==';
    case ComparisonOperator.NOT_EQUALS:
      return '!=';
    case ComparisonOperator.GREATER_THAN:
      return '>';
    case ComparisonOperator.LESS_THAN:
      return '<';
    case ComparisonOperator.GREATER_EQUAL:
      return '>=';
    case ComparisonOperator.LESS_EQUAL:
      return '<=';
    case ComparisonOperator.CONTAINS:
      return 'contains';
    case ComparisonOperator.STARTS_WITH:
      return 'starts with';
    case ComparisonOperator.ENDS_WITH:
      return 'ends with';
    case ComparisonOperator.REGEX:
      return 'matches';
    default:
      return '==';
  }
};

export const ConditionNode = memo<ConditionNodeProps>(({ 
  data, 
  selected, 
  dragging,
  id 
}) => {
  const { 
    conditionType,
    condition,
    leftOperand,
    operator,
    rightOperand,
    result,
    lastEvaluated
  } = data;

  const handleConfigure = useCallback((e: React.MouseEvent) => {
    e.stopPropagation();
    console.log('Configure condition:', condition);
  }, [condition]);

  const hasResult = result !== undefined;
  const OperatorIcon = operator ? getOperatorIcon(operator) : Equal;

  return (
    <div
      className={cn(
        'relative bg-background border-2 rounded-lg shadow-sm transition-all duration-200',
        'min-w-[200px] max-w-[280px]',
        selected && 'border-primary ring-2 ring-primary/20',
        dragging && 'shadow-lg scale-105',
        hasResult && result && 'border-green-400 shadow-green-100',
        hasResult && !result && 'border-red-400 shadow-red-100'
      )}
    >
      {/* Input Handle */}
      <Handle
        type="target"
        position={Position.Top}
        className="w-3 h-3 border-2 border-background bg-muted-foreground hover:bg-primary transition-colors"
        style={{ top: -6 }}
      />

      {/* True Output Handle */}
      <Handle
        type="source"
        position={Position.Right}
        id="true"
        className="w-3 h-3 border-2 border-background bg-green-500 hover:bg-green-600 transition-colors"
        style={{ right: -6, top: '40%' }}
      />

      {/* False Output Handle */}
      <Handle
        type="source"
        position={Position.Right}
        id="false"
        className="w-3 h-3 border-2 border-background bg-red-500 hover:bg-red-600 transition-colors"
        style={{ right: -6, top: '60%' }}
      />

      {/* Status indicator overlay */}
      <div className={cn(
        'absolute top-0 left-0 w-full h-1 rounded-t-lg transition-all duration-300',
        hasResult && result && 'bg-gradient-to-r from-green-400 to-green-600',
        hasResult && !result && 'bg-gradient-to-r from-red-400 to-red-600',
        !hasResult && 'bg-gradient-to-r from-orange-300 to-orange-400'
      )} />

      {/* Header */}
      <div className="p-3">
        <div className="flex items-start justify-between mb-2">
          <div className="flex items-center space-x-2 min-w-0 flex-1">
            <div className="p-1.5 rounded-md border bg-orange-50 border-orange-200 text-orange-600">
              <GitBranch className="h-4 w-4" />
            </div>
            <div className="min-w-0 flex-1">
              <h3 className="font-semibold text-sm">
                Condition
              </h3>
              <Badge variant="outline" className="text-xs mt-1">
                {conditionType}
              </Badge>
            </div>
          </div>
          
          {hasResult && (
            <div className="flex items-center">
              {result ? (
                <CheckCircle className="h-4 w-4 text-green-600" />
              ) : (
                <XCircle className="h-4 w-4 text-red-600" />
              )}
            </div>
          )}
        </div>

        {/* Condition Display */}
        <div className="mb-3">
          {conditionType === ConditionType.COMPARISON && leftOperand && operator && rightOperand ? (
            <div className="space-y-1">
              <div className="text-xs text-muted-foreground">Comparison:</div>
              <div className="flex items-center gap-2 text-sm font-mono bg-muted p-2 rounded">
                <span className="truncate max-w-[60px]" title={leftOperand}>
                  {leftOperand}
                </span>
                <OperatorIcon className="h-3 w-3 text-muted-foreground" />
                <span className="text-xs text-muted-foreground">
                  {getOperatorSymbol(operator)}
                </span>
                <span className="truncate max-w-[60px]" title={rightOperand}>
                  {rightOperand}
                </span>
              </div>
            </div>
          ) : (
            <div className="space-y-1">
              <div className="text-xs text-muted-foreground">Expression:</div>
              <div className="text-sm font-mono bg-muted p-2 rounded truncate" title={condition}>
                {condition || 'No condition set'}
              </div>
            </div>
          )}
        </div>

        {/* Result Display */}
        {hasResult && (
          <div className="mb-3">
            <div className="flex items-center justify-between text-sm">
              <span className="text-muted-foreground">Result:</span>
              <Badge 
                variant={result ? "default" : "destructive"}
                className="text-xs"
              >
                {result ? 'TRUE' : 'FALSE'}
              </Badge>
            </div>
            {lastEvaluated && (
              <div className="text-xs text-muted-foreground mt-1">
                Last evaluated: {new Date(lastEvaluated).toLocaleTimeString()}
              </div>
            )}
          </div>
        )}

        {/* Output Labels */}
        <div className="flex justify-between text-xs text-muted-foreground mb-2">
          <span></span>
          <div className="flex flex-col items-end gap-1">
            <span className="text-green-600">TRUE</span>
            <span className="text-red-600">FALSE</span>
          </div>
        </div>

        {/* Controls */}
        <div className="flex gap-1">
          <Button size="sm" variant="outline" onClick={handleConfigure} className="flex-1">
            <Settings className="h-3 w-3 mr-1" />
            Configure
          </Button>
        </div>
      </div>

      {/* Result indicator overlay */}
      {hasResult && (
        <div className={cn(
          'absolute inset-0 rounded-lg pointer-events-none',
          result 
            ? 'bg-gradient-to-br from-green-500/5 to-green-600/10' 
            : 'bg-gradient-to-br from-red-500/5 to-red-600/10'
        )} />
      )}

      {/* Warning indicator for unconfigured condition */}
      {!condition && (
        <div className="absolute -top-2 -right-2 bg-orange-500 text-white text-xs px-2 py-1 rounded-full">
          <AlertTriangle className="h-3 w-3" />
        </div>
      )}
    </div>
  );
});

ConditionNode.displayName = 'ConditionNode';
