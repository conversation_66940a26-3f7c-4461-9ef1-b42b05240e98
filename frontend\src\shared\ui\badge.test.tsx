import { describe, it, expect } from 'vitest';
import { render, screen } from '@testing-library/react';
import { Badge } from './badge';

describe('Badge', () => {
  it('renders with default props', () => {
    render(<Badge>Default Badge</Badge>);
    const badge = screen.getByText('Default Badge');
    
    expect(badge).toBeInTheDocument();
    expect(badge).toHaveClass(
      'inline-flex',
      'items-center',
      'rounded-full',
      'border',
      'px-2.5',
      'py-0.5',
      'text-xs',
      'font-semibold',
      'transition-colors',
      'border-transparent',
      'bg-primary',
      'text-primary-foreground'
    );
  });

  it('renders with different variants', () => {
    const { rerender } = render(<Badge variant="default">Default</Badge>);
    expect(screen.getByText('Default')).toHaveClass('bg-primary', 'text-primary-foreground');

    rerender(<Badge variant="secondary">Secondary</Badge>);
    expect(screen.getByText('Secondary')).toHaveClass('bg-secondary', 'text-secondary-foreground');

    rerender(<Badge variant="destructive">Destructive</Badge>);
    expect(screen.getByText('Destructive')).toHaveClass('bg-destructive', 'text-destructive-foreground');

    rerender(<Badge variant="outline">Outline</Badge>);
    expect(screen.getByText('Outline')).toHaveClass('text-foreground');
    expect(screen.getByText('Outline')).not.toHaveClass('border-transparent');
  });

  it('applies custom className', () => {
    render(<Badge className="custom-badge">Custom</Badge>);
    const badge = screen.getByText('Custom');
    
    expect(badge).toHaveClass('custom-badge');
    // Should still have base classes
    expect(badge).toHaveClass('inline-flex', 'items-center', 'rounded-full');
  });

  it('forwards HTML attributes', () => {
    render(
      <Badge data-testid="test-badge" title="Test title" role="status">
        Test Badge
      </Badge>
    );
    
    const badge = screen.getByTestId('test-badge');
    expect(badge).toHaveAttribute('title', 'Test title');
    expect(badge).toHaveAttribute('role', 'status');
  });

  it('renders children correctly', () => {
    render(
      <Badge>
        <span>Icon</span>
        Badge Text
      </Badge>
    );
    
    expect(screen.getByText('Icon')).toBeInTheDocument();
    expect(screen.getByText('Badge Text')).toBeInTheDocument();
  });

  it('handles empty content', () => {
    render(<Badge data-testid="empty-badge"></Badge>);
    const badge = screen.getByTestId('empty-badge');
    
    expect(badge).toBeInTheDocument();
    expect(badge).toBeEmptyDOMElement();
  });

  it('supports focus states for accessibility', () => {
    render(<Badge tabIndex={0}>Focusable Badge</Badge>);
    const badge = screen.getByText('Focusable Badge');
    
    expect(badge).toHaveClass('focus:outline-none', 'focus:ring-2', 'focus:ring-ring', 'focus:ring-offset-2');
    
    badge.focus();
    expect(badge).toHaveFocus();
  });

  it('renders as a div element by default', () => {
    render(<Badge data-testid="badge-element">Test</Badge>);
    const badge = screen.getByTestId('badge-element');
    
    expect(badge.tagName).toBe('DIV');
  });

  it('maintains consistent styling across variants', () => {
    const variants = ['default', 'secondary', 'destructive', 'outline'] as const;
    
    variants.forEach((variant) => {
      const { unmount } = render(<Badge variant={variant}>Test {variant}</Badge>);
      const badge = screen.getByText(`Test ${variant}`);
      
      // All variants should have these base classes
      expect(badge).toHaveClass(
        'inline-flex',
        'items-center',
        'rounded-full',
        'border',
        'px-2.5',
        'py-0.5',
        'text-xs',
        'font-semibold',
        'transition-colors'
      );
      
      unmount();
    });
  });

  it('supports hover states', () => {
    render(<Badge>Hoverable Badge</Badge>);
    const badge = screen.getByText('Hoverable Badge');
    
    expect(badge).toHaveClass('hover:bg-primary/80');
  });

  describe('Accessibility', () => {
    it('can be used as a status indicator', () => {
      render(<Badge role="status" aria-live="polite">Online</Badge>);
      const badge = screen.getByRole('status');
      
      expect(badge).toBeInTheDocument();
      expect(badge).toHaveAttribute('aria-live', 'polite');
    });

    it('supports aria-label for screen readers', () => {
      render(<Badge aria-label="3 unread messages">3</Badge>);
      const badge = screen.getByLabelText('3 unread messages');
      
      expect(badge).toBeInTheDocument();
      expect(badge).toHaveTextContent('3');
    });

    it('can be hidden from screen readers when decorative', () => {
      render(<Badge aria-hidden="true">Decorative</Badge>);
      const badge = screen.getByText('Decorative');
      
      expect(badge).toHaveAttribute('aria-hidden', 'true');
    });
  });
});
