import type { <PERSON><PERSON>, StoryObj } from '@storybook/react';
import { action } from '@storybook/addon-actions';
import { WorkflowBuilder } from '@/features/workflow-builder/components/workflow-builder';
import { WorkflowNode, WorkflowEdge, NodeType, PortType } from '@/features/workflow-builder/types';
import { createAgent } from '@/entities/agent/model';
import { AgentType, AgentStatus } from '@/shared/types';

const meta: Meta<typeof WorkflowBuilder> = {
  title: 'Features/WorkflowBuilder/WorkflowBuilder',
  component: WorkflowBuilder,
  parameters: {
    layout: 'fullscreen',
    docs: {
      description: {
        component: 'A comprehensive drag-and-drop workflow builder for creating AI agent workflows. Features include a visual canvas, node library, real-time validation, and execution controls.',
      },
    },
  },
  tags: ['autodocs'],
  argTypes: {
    readonly: {
      control: 'boolean',
      description: 'Whether the workflow is in read-only mode',
    },
  },
};

export default meta;
type Story = StoryObj<typeof meta>;

// Sample nodes for stories
const sampleNodes: WorkflowNode[] = [
  {
    id: 'input-1',
    type: NodeType.INPUT,
    position: { x: 100, y: 200 },
    data: {
      inputType: PortType.STRING,
      inputName: 'User Message',
      description: 'Input message from user',
      currentValue: 'Hello, how can you help me?',
    },
  },
  {
    id: 'agent-1',
    type: NodeType.AGENT,
    position: { x: 400, y: 150 },
    data: {
      agent: createAgent({
        id: 'agent-1',
        name: 'Customer Support Agent',
        description: 'AI agent for customer support',
        agent_type: AgentType.CHAT,
        status: AgentStatus.IDLE,
        capabilities: [
          { name: 'chat', description: 'Chat capability', parameters: {}, required: true },
          { name: 'knowledge-base', description: 'Knowledge base access', parameters: {}, required: false },
        ],
      }),
    },
  },
  {
    id: 'condition-1',
    type: NodeType.CONDITION,
    position: { x: 700, y: 200 },
    data: {
      conditionType: 'simple',
      condition: 'response.confidence > 0.8',
      description: 'Check if response confidence is high',
    },
  },
  {
    id: 'output-1',
    type: NodeType.OUTPUT,
    position: { x: 1000, y: 150 },
    data: {
      outputType: PortType.STRING,
      outputName: 'Agent Response',
      description: 'Response from the agent',
      currentValue: 'I can help you with your inquiry. What specific information do you need?',
    },
  },
  {
    id: 'tool-1',
    type: NodeType.TOOL,
    position: { x: 1000, y: 300 },
    data: {
      toolType: 'api',
      toolName: 'Escalation API',
      description: 'Escalate to human agent',
    },
  },
];

const sampleEdges: WorkflowEdge[] = [
  {
    id: 'edge-1',
    source: 'input-1',
    target: 'agent-1',
    type: 'smoothstep',
  },
  {
    id: 'edge-2',
    source: 'agent-1',
    target: 'condition-1',
    type: 'smoothstep',
  },
  {
    id: 'edge-3',
    source: 'condition-1',
    target: 'output-1',
    sourceHandle: 'true',
    type: 'smoothstep',
  },
  {
    id: 'edge-4',
    source: 'condition-1',
    target: 'tool-1',
    sourceHandle: 'false',
    type: 'smoothstep',
  },
];

// Story actions
const actions = {
  onSave: action('onSave'),
  onExecute: action('onExecute'),
  onNodesChange: action('onNodesChange'),
  onEdgesChange: action('onEdgesChange'),
};

export const Default: Story = {
  args: {
    workflowId: 'demo-workflow',
    ...actions,
  },
};

export const WithSampleWorkflow: Story = {
  args: {
    workflowId: 'sample-workflow',
    initialNodes: sampleNodes,
    initialEdges: sampleEdges,
    ...actions,
  },
};

export const ReadOnlyMode: Story = {
  args: {
    workflowId: 'readonly-workflow',
    initialNodes: sampleNodes,
    initialEdges: sampleEdges,
    readonly: true,
    ...actions,
  },
  parameters: {
    docs: {
      description: {
        story: 'Workflow builder in read-only mode. Users can view the workflow but cannot make changes.',
      },
    },
  },
};

export const EmptyWorkflow: Story = {
  args: {
    workflowId: 'empty-workflow',
    initialNodes: [],
    initialEdges: [],
    ...actions,
  },
  parameters: {
    docs: {
      description: {
        story: 'Empty workflow builder ready for creating new workflows from scratch.',
      },
    },
  },
};

export const ComplexWorkflow: Story = {
  args: {
    workflowId: 'complex-workflow',
    initialNodes: [
      ...sampleNodes,
      {
        id: 'agent-2',
        type: NodeType.AGENT,
        position: { x: 400, y: 400 },
        data: {
          agent: createAgent({
            id: 'agent-2',
            name: 'Reasoning Agent',
            description: 'Advanced reasoning agent',
            agent_type: AgentType.REASONING,
            status: AgentStatus.RUNNING,
            capabilities: [
              { name: 'reasoning', description: 'Logical reasoning', parameters: {}, required: true },
              { name: 'analysis', description: 'Data analysis', parameters: {}, required: true },
            ],
          }),
        },
      },
      {
        id: 'tool-2',
        type: NodeType.TOOL,
        position: { x: 700, y: 450 },
        data: {
          toolType: 'database',
          toolName: 'Knowledge DB',
          description: 'Query knowledge database',
        },
      },
      {
        id: 'condition-2',
        type: NodeType.CONDITION,
        position: { x: 1000, y: 500 },
        data: {
          conditionType: 'comparison',
          condition: 'results.length > 0',
          description: 'Check if database returned results',
        },
      },
    ],
    initialEdges: [
      ...sampleEdges,
      {
        id: 'edge-5',
        source: 'tool-1',
        target: 'agent-2',
        type: 'smoothstep',
      },
      {
        id: 'edge-6',
        source: 'agent-2',
        target: 'tool-2',
        type: 'smoothstep',
      },
      {
        id: 'edge-7',
        source: 'tool-2',
        target: 'condition-2',
        type: 'smoothstep',
      },
    ],
    ...actions,
  },
  parameters: {
    docs: {
      description: {
        story: 'Complex workflow with multiple agents, tools, and conditional logic demonstrating advanced workflow capabilities.',
      },
    },
  },
};

export const AgentFocused: Story = {
  args: {
    workflowId: 'agent-workflow',
    initialNodes: [
      {
        id: 'chat-agent',
        type: NodeType.AGENT,
        position: { x: 200, y: 100 },
        data: {
          agent: createAgent({
            id: 'chat-agent',
            name: 'Chat Agent',
            agent_type: AgentType.CHAT,
            status: AgentStatus.IDLE,
          }),
        },
      },
      {
        id: 'reasoning-agent',
        type: NodeType.AGENT,
        position: { x: 500, y: 100 },
        data: {
          agent: createAgent({
            id: 'reasoning-agent',
            name: 'Reasoning Agent',
            agent_type: AgentType.REASONING,
            status: AgentStatus.RUNNING,
          }),
        },
      },
      {
        id: 'multimodal-agent',
        type: NodeType.AGENT,
        position: { x: 800, y: 100 },
        data: {
          agent: createAgent({
            id: 'multimodal-agent',
            name: 'Multimodal Agent',
            agent_type: AgentType.MULTIMODAL,
            status: AgentStatus.COMPLETED,
          }),
        },
      },
      {
        id: 'tool-agent',
        type: NodeType.AGENT,
        position: { x: 350, y: 300 },
        data: {
          agent: createAgent({
            id: 'tool-agent',
            name: 'Tool Agent',
            agent_type: AgentType.TOOL,
            status: AgentStatus.FAILED,
          }),
        },
      },
    ],
    initialEdges: [
      {
        id: 'edge-1',
        source: 'chat-agent',
        target: 'reasoning-agent',
        type: 'smoothstep',
      },
      {
        id: 'edge-2',
        source: 'reasoning-agent',
        target: 'multimodal-agent',
        type: 'smoothstep',
      },
      {
        id: 'edge-3',
        source: 'reasoning-agent',
        target: 'tool-agent',
        type: 'smoothstep',
      },
    ],
    ...actions,
  },
  parameters: {
    docs: {
      description: {
        story: 'Workflow focused on different types of AI agents showing various states and capabilities.',
      },
    },
  },
};

export const DataProcessingWorkflow: Story = {
  args: {
    workflowId: 'data-processing',
    initialNodes: [
      {
        id: 'data-input',
        type: NodeType.INPUT,
        position: { x: 100, y: 200 },
        data: {
          inputType: PortType.FILE,
          inputName: 'Data File',
          description: 'Input data file for processing',
        },
      },
      {
        id: 'validation-tool',
        type: NodeType.TOOL,
        position: { x: 350, y: 150 },
        data: {
          toolType: 'validation',
          toolName: 'Data Validator',
          description: 'Validate input data format',
        },
      },
      {
        id: 'processing-agent',
        type: NodeType.AGENT,
        position: { x: 600, y: 200 },
        data: {
          agent: createAgent({
            id: 'processing-agent',
            name: 'Data Processing Agent',
            agent_type: AgentType.TOOL,
            status: AgentStatus.IDLE,
          }),
        },
      },
      {
        id: 'quality-check',
        type: NodeType.CONDITION,
        position: { x: 850, y: 200 },
        data: {
          conditionType: 'comparison',
          condition: 'quality_score > 0.9',
          description: 'Check data quality',
        },
      },
      {
        id: 'output-success',
        type: NodeType.OUTPUT,
        position: { x: 1100, y: 150 },
        data: {
          outputType: PortType.FILE,
          outputName: 'Processed Data',
          description: 'Successfully processed data',
        },
      },
      {
        id: 'error-handler',
        type: NodeType.TOOL,
        position: { x: 1100, y: 300 },
        data: {
          toolType: 'error',
          toolName: 'Error Handler',
          description: 'Handle processing errors',
        },
      },
    ],
    initialEdges: [
      { id: 'e1', source: 'data-input', target: 'validation-tool', type: 'smoothstep' },
      { id: 'e2', source: 'validation-tool', target: 'processing-agent', type: 'smoothstep' },
      { id: 'e3', source: 'processing-agent', target: 'quality-check', type: 'smoothstep' },
      { id: 'e4', source: 'quality-check', target: 'output-success', sourceHandle: 'true', type: 'smoothstep' },
      { id: 'e5', source: 'quality-check', target: 'error-handler', sourceHandle: 'false', type: 'smoothstep' },
    ],
    ...actions,
  },
  parameters: {
    docs: {
      description: {
        story: 'Data processing workflow demonstrating input validation, processing, quality checks, and error handling.',
      },
    },
  },
};
