"""
User service use cases.

This module contains use cases for user management operations
orchestrating domain logic and repository interactions.
"""

import uuid
from datetime import datetime, timezone
from typing import List, Optional

from src.domain.entities.user import (
    User,
    UserCreate,
    UserResponse,
    UserUpdate,
    LoginRequest,
    LoginResponse,
    PasswordChangeRequest,
    UserStatus,
)
from src.domain.repositories.user_repository import UserRepositoryInterface
from src.infrastructure.logging.setup import LoggerMixin
from src.infrastructure.security.jwt import jwt_manager, password_manager


class UserService(LoggerMixin):
    """
    User service containing business logic for user operations.
    
    Orchestrates user-related use cases and coordinates between
    domain entities and infrastructure services.
    """
    
    def __init__(self, user_repository: UserRepositoryInterface) -> None:
        """
        Initialize user service.
        
        Args:
            user_repository: User repository implementation
        """
        self.user_repository = user_repository
    
    async def register_user(self, user_data: UserCreate) -> UserResponse:
        """
        Register a new user.
        
        Args:
            user_data: User registration data
            
        Returns:
            UserResponse: Created user information
            
        Raises:
            ValueError: If registration fails
        """
        self.logger.info(f"Registering new user: {user_data.email}")
        
        # Check if email or username already exists
        if await self.user_repository.email_exists(user_data.email):
            raise ValueError("Email already registered")
        
        if await self.user_repository.username_exists(user_data.username):
            raise ValueError("Username already taken")
        
        # Hash password
        hashed_password = password_manager.hash_password(user_data.password)
        
        # Create user entity
        user = User(
            email=user_data.email.lower(),
            username=user_data.username.lower(),
            full_name=user_data.full_name,
            hashed_password=hashed_password,
            status=UserStatus.PENDING_VERIFICATION,
            is_verified=False,
        )
        
        # Save user
        created_user = await self.user_repository.create(user)
        
        # TODO: Send verification email
        self.logger.info(f"User registered successfully: {created_user.email}")
        
        return UserResponse.from_attributes(created_user)
    
    async def authenticate_user(self, login_data: LoginRequest) -> LoginResponse:
        """
        Authenticate user and return tokens.
        
        Args:
            login_data: Login credentials
            
        Returns:
            LoginResponse: Access and refresh tokens with user info
            
        Raises:
            ValueError: If authentication fails
        """
        self.logger.info(f"Authentication attempt: {login_data.email}")
        
        # Get user by email
        user = await self.user_repository.get_by_email(login_data.email)
        if not user:
            raise ValueError("Invalid email or password")
        
        # Verify password
        if not password_manager.verify_password(login_data.password, user.hashed_password):
            self.logger.warning(f"Invalid password for user: {user.email}")
            raise ValueError("Invalid email or password")
        
        # Check if user can login
        if not user.can_login():
            self.logger.warning(f"User cannot login: {user.email} (status: {user.status})")
            raise ValueError("Account is not active or verified")
        
        # Update last login
        user.update_last_login()
        await self.user_repository.update(user)
        
        # Generate tokens
        access_token = jwt_manager.create_access_token(
            subject=user.email,
            user_id=str(user.id),
            additional_claims={
                "username": user.username,
                "role": user.role.value,
            }
        )
        
        refresh_token = jwt_manager.create_refresh_token(
            subject=user.email,
            user_id=str(user.id)
        )
        
        self.logger.info(f"User authenticated successfully: {user.email}")
        
        return LoginResponse(
            access_token=access_token,
            refresh_token=refresh_token,
            expires_in=int(jwt_manager.access_token_expire.total_seconds()),
            user=UserResponse.from_attributes(user)
        )
    
    async def get_user_by_id(self, user_id: uuid.UUID) -> Optional[UserResponse]:
        """
        Get user by ID.
        
        Args:
            user_id: User ID
            
        Returns:
            UserResponse: User information or None if not found
        """
        user = await self.user_repository.get_by_id(user_id)
        if user:
            return UserResponse.from_attributes(user)
        return None
    
    async def update_user_profile(
        self,
        user_id: uuid.UUID,
        update_data: UserUpdate
    ) -> UserResponse:
        """
        Update user profile.
        
        Args:
            user_id: User ID
            update_data: Profile update data
            
        Returns:
            UserResponse: Updated user information
            
        Raises:
            ValueError: If update fails
        """
        user = await self.user_repository.get_by_id(user_id)
        if not user:
            raise ValueError("User not found")
        
        # Check if username is being changed and if it's available
        if update_data.username and update_data.username != user.username:
            if await self.user_repository.username_exists(update_data.username):
                raise ValueError("Username already taken")
        
        # Update user profile
        user.update_profile(
            full_name=update_data.full_name,
            username=update_data.username.lower() if update_data.username else None
        )
        
        # Save changes
        updated_user = await self.user_repository.update(user)
        
        self.logger.info(f"User profile updated: {user.email}")
        return UserResponse.from_attributes(updated_user)
    
    async def change_password(
        self,
        user_id: uuid.UUID,
        password_data: PasswordChangeRequest
    ) -> None:
        """
        Change user password.
        
        Args:
            user_id: User ID
            password_data: Password change data
            
        Raises:
            ValueError: If password change fails
        """
        user = await self.user_repository.get_by_id(user_id)
        if not user:
            raise ValueError("User not found")
        
        # Verify current password
        if not password_manager.verify_password(
            password_data.current_password,
            user.hashed_password
        ):
            raise ValueError("Current password is incorrect")
        
        # Hash new password
        new_hashed_password = password_manager.hash_password(password_data.new_password)
        
        # Update password
        user.hashed_password = new_hashed_password
        user.updated_at = datetime.now(timezone.utc)
        
        await self.user_repository.update(user)
        
        self.logger.info(f"Password changed for user: {user.email}")
    
    async def list_users(
        self,
        skip: int = 0,
        limit: int = 100,
        active_only: bool = False
    ) -> List[UserResponse]:
        """
        List users with pagination.
        
        Args:
            skip: Number of users to skip
            limit: Maximum number of users to return
            active_only: Whether to return only active users
            
        Returns:
            List[UserResponse]: List of user information
        """
        users = await self.user_repository.list_users(skip, limit, active_only)
        return [UserResponse.from_attributes(user) for user in users]
    
    async def get_user_count(self, active_only: bool = False) -> int:
        """
        Get total number of users.
        
        Args:
            active_only: Whether to count only active users
            
        Returns:
            int: Total number of users
        """
        return await self.user_repository.count_users(active_only)
    
    async def activate_user(self, user_id: uuid.UUID) -> UserResponse:
        """
        Activate user account.
        
        Args:
            user_id: User ID
            
        Returns:
            UserResponse: Updated user information
            
        Raises:
            ValueError: If user not found
        """
        user = await self.user_repository.get_by_id(user_id)
        if not user:
            raise ValueError("User not found")
        
        user.activate()
        updated_user = await self.user_repository.update(user)
        
        self.logger.info(f"User activated: {user.email}")
        return UserResponse.from_attributes(updated_user)
    
    async def suspend_user(self, user_id: uuid.UUID) -> UserResponse:
        """
        Suspend user account.
        
        Args:
            user_id: User ID
            
        Returns:
            UserResponse: Updated user information
            
        Raises:
            ValueError: If user not found
        """
        user = await self.user_repository.get_by_id(user_id)
        if not user:
            raise ValueError("User not found")
        
        user.suspend()
        updated_user = await self.user_repository.update(user)
        
        self.logger.info(f"User suspended: {user.email}")
        return UserResponse.from_attributes(updated_user)
