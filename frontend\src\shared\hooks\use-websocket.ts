import { useEffect, useRef, useState, useCallback } from 'react';
import { WebSocketClient, createWebSocketClient } from '@/shared/lib/websocket';

export function useWebSocket(type: 'mcp' | 'a2a' | 'ag-ui') {
  const [isConnected, setIsConnected] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const clientRef = useRef<WebSocketClient | null>(null);

  useEffect(() => {
    const client = createWebSocketClient(type);
    clientRef.current = client;

    // Set up connection status handler
    const unsubscribeConnection = client.onConnectionChange((connected) => {
      setIsConnected(connected);
      if (!connected) {
        setError('Connection lost');
      } else {
        setError(null);
      }
    });

    // Connect
    client.connect().catch((err) => {
      setError(err.message || 'Failed to connect');
    });

    // Cleanup on unmount
    return () => {
      unsubscribeConnection();
      client.disconnect();
    };
  }, [type]);

  const send = useCallback((messageType: string, payload: any) => {
    if (clientRef.current) {
      try {
        clientRef.current.send(messageType, payload);
      } catch (err) {
        setError(err instanceof Error ? err.message : 'Failed to send message');
      }
    }
  }, []);

  const subscribe = useCallback((messageType: string, handler: (data: any) => void) => {
    if (clientRef.current) {
      return clientRef.current.subscribe(messageType, handler);
    }
    return () => {};
  }, []);

  return {
    isConnected,
    error,
    send,
    subscribe,
    client: clientRef.current,
  };
}

export function useMCPWebSocket() {
  const { isConnected, error, send, subscribe, client } = useWebSocket('mcp');

  const sendMCPRequest = useCallback((method: string, params: any) => {
    send('mcp_request', { method, params });
  }, [send]);

  return {
    isConnected,
    error,
    sendMCPRequest,
    subscribe,
    client,
  };
}

export function useA2AWebSocket() {
  const { isConnected, error, send, subscribe, client } = useWebSocket('a2a');

  const sendAgentMessage = useCallback((targetAgentId: string, message: any) => {
    send('agent_message', { targetAgentId, message });
  }, [send]);

  return {
    isConnected,
    error,
    sendAgentMessage,
    subscribe,
    client,
  };
}

export function useAGUIWebSocket() {
  const { isConnected, error, send, subscribe, client } = useWebSocket('ag-ui');

  const sendUIUpdate = useCallback((componentId: string, update: any) => {
    send('ui_update', { componentId, update });
  }, [send]);

  return {
    isConnected,
    error,
    sendUIUpdate,
    subscribe,
    client,
  };
}
