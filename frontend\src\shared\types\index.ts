/**
 * Shared TypeScript types for the Lonors AI Agent Platform
 */

// Base entity interface
export interface BaseEntity {
  id: string;
  createdAt: string;
  updatedAt: string;
}

// User types
export interface User extends BaseEntity {
  email: string;
  username: string;
  firstName?: string;
  lastName?: string;
  avatar?: string;
  role: UserRole;
  isActive: boolean;
  lastLoginAt?: string;
}

export enum UserRole {
  ADMIN = 'admin',
  USER = 'user',
  AGENT = 'agent',
}

// Authentication types
export interface AuthTokens {
  accessToken: string;
  refreshToken: string;
  expiresIn: number;
}

export interface LoginCredentials {
  email: string;
  password: string;
}

export interface RegisterData {
  email: string;
  username: string;
  password: string;
  firstName?: string;
  lastName?: string;
}

// AI Agent types
export interface Agent extends BaseEntity {
  name: string;
  description: string;
  type: AgentType;
  status: AgentStatus;
  configuration: AgentConfiguration;
  capabilities: string[];
  ownerId: string;
  isPublic: boolean;
  tags: string[];
}

export enum AgentType {
  CHAT = 'chat',
  WORKFLOW = 'workflow',
  KNOWLEDGE = 'knowledge',
  AUTOMATION = 'automation',
}

export enum AgentStatus {
  IDLE = 'idle',
  RUNNING = 'running',
  PAUSED = 'paused',
  ERROR = 'error',
  STOPPED = 'stopped',
}

export interface AgentConfiguration {
  model?: string;
  temperature?: number;
  maxTokens?: number;
  systemPrompt?: string;
  tools?: string[];
  [key: string]: any;
}

// Workflow types
export interface Workflow extends BaseEntity {
  name: string;
  description: string;
  nodes: WorkflowNode[];
  edges: WorkflowEdge[];
  status: WorkflowStatus;
  ownerId: string;
  isPublic: boolean;
  tags: string[];
}

export interface WorkflowNode {
  id: string;
  type: NodeType;
  position: { x: number; y: number };
  data: NodeData;
}

export interface WorkflowEdge {
  id: string;
  source: string;
  target: string;
  sourceHandle?: string;
  targetHandle?: string;
}

export enum NodeType {
  INPUT = 'input',
  OUTPUT = 'output',
  AGENT = 'agent',
  CONDITION = 'condition',
  TRANSFORM = 'transform',
  API_CALL = 'api_call',
  DELAY = 'delay',
}

export interface NodeData {
  label: string;
  description?: string;
  configuration: Record<string, any>;
}

export enum WorkflowStatus {
  DRAFT = 'draft',
  ACTIVE = 'active',
  PAUSED = 'paused',
  ARCHIVED = 'archived',
}

// Knowledge Graph types
export interface KnowledgeEntity extends BaseEntity {
  name: string;
  type: string;
  properties: Record<string, any>;
  description?: string;
}

export interface KnowledgeRelationship extends BaseEntity {
  sourceId: string;
  targetId: string;
  type: string;
  properties: Record<string, any>;
  weight?: number;
}

export interface KnowledgeGraph {
  entities: KnowledgeEntity[];
  relationships: KnowledgeRelationship[];
}

// UI Component types
export interface ComponentProps {
  className?: string;
  children?: React.ReactNode;
}

export interface ButtonProps extends ComponentProps {
  variant?: 'default' | 'destructive' | 'outline' | 'secondary' | 'ghost' | 'link';
  size?: 'default' | 'sm' | 'lg' | 'icon';
  disabled?: boolean;
  loading?: boolean;
  onClick?: () => void;
}

export interface InputProps extends ComponentProps {
  type?: string;
  placeholder?: string;
  value?: string;
  onChange?: (value: string) => void;
  disabled?: boolean;
  error?: string;
}

// Animation types
export interface AnimationConfig {
  duration?: number;
  delay?: number;
  easing?: string;
  direction?: 'normal' | 'reverse' | 'alternate' | 'alternate-reverse';
  loop?: boolean | number;
}

// Theme types
export interface Theme {
  colors: {
    primary: string;
    secondary: string;
    background: string;
    foreground: string;
    muted: string;
    accent: string;
    destructive: string;
    border: string;
    input: string;
    ring: string;
  };
  fonts: {
    sans: string;
    mono: string;
  };
  spacing: Record<string, string>;
  borderRadius: Record<string, string>;
}

// Error types
export interface AppError {
  message: string;
  code?: string;
  status?: number;
  details?: Record<string, any>;
}

// Loading states
export interface LoadingState {
  isLoading: boolean;
  error?: AppError | null;
}

// Pagination types
export interface PaginationParams {
  page: number;
  limit: number;
  sortBy?: string;
  sortOrder?: 'asc' | 'desc';
  search?: string;
}

export interface PaginationMeta {
  page: number;
  limit: number;
  total: number;
  totalPages: number;
  hasNextPage: boolean;
  hasPreviousPage: boolean;
}

// WebSocket types
export interface WebSocketMessage {
  type: string;
  payload: any;
  timestamp: string;
  id?: string;
}

export interface WebSocketConnection {
  url: string;
  protocols?: string[];
  reconnect?: boolean;
  maxReconnectAttempts?: number;
  reconnectInterval?: number;
}

// File upload types
export interface FileUpload {
  file: File;
  progress: number;
  status: 'pending' | 'uploading' | 'completed' | 'error';
  error?: string;
}

// Search types
export interface SearchResult<T = any> {
  items: T[];
  total: number;
  query: string;
  filters?: Record<string, any>;
  facets?: Record<string, any>;
}

// Notification types
export interface Notification {
  id: string;
  type: 'info' | 'success' | 'warning' | 'error';
  title: string;
  message: string;
  duration?: number;
  actions?: NotificationAction[];
}

export interface NotificationAction {
  label: string;
  action: () => void;
}

// Feature flags
export interface FeatureFlags {
  [key: string]: boolean;
}

// Environment configuration
export interface AppConfig {
  apiUrl: string;
  appName: string;
  appVersion: string;
  environment: 'development' | 'staging' | 'production';
  features: FeatureFlags;
}
