# Lonors Documentation

Welcome to the Lonors AI Platform documentation. This directory contains comprehensive documentation for all aspects of the platform.

## Documentation Categories

### [Product Documentation](product/README.md)

Documentation related to product requirements, features, and specifications.

- [Comprehensive Product Requirements Document (PRD)](product/comprehensive-prd.md)

### [Architecture Documentation](architecture/README.md)

Documentation related to system architecture, component design, and technical implementation.

- [System Architecture Diagram](architecture/system-architecture-diagram.md)
- [Data Flow Diagram](architecture/data-flow-diagram.md)
- [Component Dependency Graph](architecture/component-dependency-graph.md)
- [CI/CD Pipeline Diagram](architecture/cicd-pipeline-diagram.md)
- [Docker Orchestration Diagram](architecture/docker-orchestration-diagram.md)
- [Protocol Integration Diagram](architecture/protocol-integration-diagram.md)
- [Database Schema](architecture/database-schema.md)

### API Documentation

- [API Documentation](API_DOCUMENTATION.md) - Comprehensive API reference

### Protocol Documentation

- [Protocols Documentation](PROTOCOLS.md) - Protocol specifications and usage

## Getting Started

For developers new to the Lonors platform, we recommend starting with the following documents:

1. [Comprehensive Product Requirements Document (PRD)](product/comprehensive-prd.md) - Understand the product vision and requirements
2. [System Architecture Diagram](architecture/system-architecture-diagram.md) - Get an overview of the system architecture
3. [API Documentation](API_DOCUMENTATION.md) - Learn about the available API endpoints
4. [Protocols Documentation](PROTOCOLS.md) - Understand the core protocols

## Contributing to Documentation

When contributing to the documentation, please follow these guidelines:

1. Use Markdown for all documentation files
2. Include diagrams using Mermaid where appropriate
3. Keep documentation up-to-date with code changes
4. Follow the established structure and formatting
5. Include examples and use cases where possible

## Documentation Structure

```
docs/
├── README.md                     # This file
├── API_DOCUMENTATION.md          # API reference
├── PROTOCOLS.md                  # Protocol specifications
├── architecture/                 # Architecture documentation
│   ├── README.md                 # Architecture index
│   ├── system-architecture-diagram.md
│   ├── data-flow-diagram.md
│   ├── component-dependency-graph.md
│   ├── cicd-pipeline-diagram.md
│   ├── docker-orchestration-diagram.md
│   ├── protocol-integration-diagram.md
│   └── database-schema.md
└── product/                      # Product documentation
    ├── README.md                 # Product index
    └── comprehensive-prd.md      # Product requirements document
```
