import { act, render, renderHook, screen } from '@testing-library/react';
import { describe, expect, it, vi } from 'vitest';
import {
    LoadingButton,
    LoadingCard,
    LoadingOverlay,
    LoadingPage,
    LoadingSkeleton,
    LoadingSpinner,
    useLoading,
    withLoading,
} from '../loading';

// Mock Lucide React icons
vi.mock('lucide-react', () => ({
  Loader2: ({ className, ...props }: any) => (
    <div data-testid="loader2-icon" className={className} {...props}>
      Loading Icon
    </div>
  ),
}));

describe('Loading Components', () => {
  describe('LoadingSpinner', () => {
    it('renders with default props', () => {
      render(<LoadingSpinner />);

      const spinner = screen.getByTestId('loader2-icon');
      expect(spinner).toBeInTheDocument();
      expect(spinner).toHaveClass('animate-spin', 'h-6', 'w-6');
      expect(spinner).toHaveAttribute('aria-label', 'Loading');
    });

    it('applies size classes correctly', () => {
      const { rerender } = render(<LoadingSpinner size="sm" />);
      expect(screen.getByTestId('loader2-icon')).toHaveClass('h-4', 'w-4');

      rerender(<LoadingSpinner size="md" />);
      expect(screen.getByTestId('loader2-icon')).toHaveClass('h-6', 'w-6');

      rerender(<LoadingSpinner size="lg" />);
      expect(screen.getByTestId('loader2-icon')).toHaveClass('h-8', 'w-8');
    });

    it('accepts custom className', () => {
      render(<LoadingSpinner className="custom-spinner" />);

      const spinner = screen.getByTestId('loader2-icon');
      expect(spinner).toHaveClass('custom-spinner');
    });

    it('always includes animate-spin class', () => {
      render(<LoadingSpinner size="lg" className="custom-class" />);

      const spinner = screen.getByTestId('loader2-icon');
      expect(spinner).toHaveClass('animate-spin');
    });
  });

  describe('LoadingOverlay', () => {
    it('renders children when not loading', () => {
      render(
        <LoadingOverlay isLoading={false}>
          <div>Content</div>
        </LoadingOverlay>
      );

      expect(screen.getByText('Content')).toBeInTheDocument();
      expect(screen.queryByTestId('loader2-icon')).not.toBeInTheDocument();
      expect(screen.queryByText('Loading...')).not.toBeInTheDocument();
    });

    it('shows overlay when loading', () => {
      render(
        <LoadingOverlay isLoading={true}>
          <div>Content</div>
        </LoadingOverlay>
      );

      expect(screen.getByText('Content')).toBeInTheDocument();
      expect(screen.getByTestId('loader2-icon')).toBeInTheDocument();
      expect(screen.getByText('Loading...')).toBeInTheDocument();
    });

    it('accepts custom message', () => {
      render(
        <LoadingOverlay isLoading={true} message="Please wait...">
          <div>Content</div>
        </LoadingOverlay>
      );

      expect(screen.getByText('Please wait...')).toBeInTheDocument();
      expect(screen.queryByText('Loading...')).not.toBeInTheDocument();
    });

    it('accepts custom spinner size', () => {
      render(
        <LoadingOverlay isLoading={true} spinnerSize="lg">
          <div>Content</div>
        </LoadingOverlay>
      );

      const spinner = screen.getByTestId('loader2-icon');
      expect(spinner).toHaveClass('h-8', 'w-8');
    });

    it('hides message when message is empty', () => {
      render(
        <LoadingOverlay isLoading={true} message="">
          <div>Content</div>
        </LoadingOverlay>
      );

      expect(screen.getByTestId('loader2-icon')).toBeInTheDocument();
      expect(screen.queryByText('Loading...')).not.toBeInTheDocument();
    });

    it('accepts custom className', () => {
      const { container } = render(
        <LoadingOverlay isLoading={false} className="custom-overlay">
          <div>Content</div>
        </LoadingOverlay>
      );

      const wrapper = container.firstChild as HTMLElement;
      expect(wrapper).toHaveClass('custom-overlay');
    });

    it('applies overlay styling when loading', () => {
      const { container } = render(
        <LoadingOverlay isLoading={true}>
          <div>Content</div>
        </LoadingOverlay>
      );

      const overlay = container.querySelector('.absolute.inset-0');
      expect(overlay).toBeInTheDocument();
      expect(overlay).toHaveClass('bg-background/80', 'backdrop-blur-sm');
    });
  });

  describe('LoadingSkeleton', () => {
    it('renders with default props', () => {
      const { container } = render(<LoadingSkeleton />);

      const skeletonLines = container.querySelectorAll('.h-4.bg-muted.rounded.animate-pulse');
      expect(skeletonLines).toHaveLength(3);

      // Should not show avatar by default
      const avatar = container.querySelector('.h-10.w-10.rounded-full');
      expect(avatar).not.toBeInTheDocument();
    });

    it('renders custom number of lines', () => {
      const { container } = render(<LoadingSkeleton lines={5} />);

      const skeletonLines = container.querySelectorAll('.h-4.bg-muted.rounded.animate-pulse');
      expect(skeletonLines).toHaveLength(5);
    });

    it('renders avatar when enabled', () => {
      const { container } = render(<LoadingSkeleton avatar={true} />);

      const avatar = container.querySelector('.h-10.w-10.rounded-full.bg-muted.animate-pulse');
      expect(avatar).toBeInTheDocument();

      // Should also have avatar text lines
      const avatarTextLines = container.querySelectorAll('.h-4.w-24.bg-muted.rounded.animate-pulse, .h-3.w-16.bg-muted.rounded.animate-pulse');
      expect(avatarTextLines.length).toBeGreaterThan(0);
    });

    it('applies different widths to last line', () => {
      const { container } = render(<LoadingSkeleton lines={3} />);

      const skeletonLines = container.querySelectorAll('.h-4.bg-muted.rounded.animate-pulse');
      const lastLine = skeletonLines[skeletonLines.length - 1];

      expect(lastLine).toHaveClass('w-3/4');
      expect(skeletonLines[0]).toHaveClass('w-full');
    });

    it('accepts custom className', () => {
      const { container } = render(<LoadingSkeleton className="custom-skeleton" />);

      const wrapper = container.firstChild as HTMLElement;
      expect(wrapper).toHaveClass('custom-skeleton');
    });
  });

  describe('LoadingCard', () => {
    it('renders basic card structure', () => {
      const { container } = render(<LoadingCard />);

      const card = container.querySelector('.rounded-lg.border.bg-card.p-6.shadow-sm');
      expect(card).toBeInTheDocument();

      // Should have content lines
      const contentLines = container.querySelectorAll('.bg-muted.rounded.animate-pulse');
      expect(contentLines.length).toBeGreaterThan(0);
    });

    it('shows avatar when enabled', () => {
      const { container } = render(<LoadingCard showAvatar={true} />);

      const avatar = container.querySelector('.h-10.w-10.rounded-full.bg-muted.animate-pulse');
      expect(avatar).toBeInTheDocument();
    });

    it('shows actions when enabled', () => {
      const { container } = render(<LoadingCard showActions={true} />);

      const actionButtons = container.querySelectorAll('.h-9.bg-muted.rounded.animate-pulse');
      expect(actionButtons.length).toBeGreaterThanOrEqual(2);
    });

    it('accepts custom className', () => {
      const { container } = render(<LoadingCard className="custom-card" />);

      const card = container.firstChild as HTMLElement;
      expect(card).toHaveClass('custom-card');
    });

    it('renders all elements when all props enabled', () => {
      const { container } = render(
        <LoadingCard showAvatar={true} showActions={true} className="full-card" />
      );

      const card = container.firstChild as HTMLElement;
      expect(card).toHaveClass('full-card');

      // Should have avatar
      const avatar = container.querySelector('.h-10.w-10.rounded-full');
      expect(avatar).toBeInTheDocument();

      // Should have action buttons
      const actions = container.querySelectorAll('.h-9.bg-muted');
      expect(actions.length).toBeGreaterThanOrEqual(2);
    });
  });

  describe('LoadingPage', () => {
    it('renders with default message', () => {
      render(<LoadingPage />);

      expect(screen.getByText('Loading page...')).toBeInTheDocument();
      expect(screen.getByTestId('loader2-icon')).toBeInTheDocument();
      expect(screen.getByTestId('loader2-icon')).toHaveClass('h-8', 'w-8'); // lg size
    });

    it('accepts custom message', () => {
      render(<LoadingPage message="Initializing application..." />);

      expect(screen.getByText('Initializing application...')).toBeInTheDocument();
      expect(screen.queryByText('Loading page...')).not.toBeInTheDocument();
    });

    it('accepts custom className', () => {
      const { container } = render(<LoadingPage className="custom-page" />);

      const wrapper = container.firstChild as HTMLElement;
      expect(wrapper).toHaveClass('custom-page');
    });

    it('has proper layout classes', () => {
      const { container } = render(<LoadingPage />);

      const wrapper = container.firstChild as HTMLElement;
      expect(wrapper).toHaveClass('flex', 'items-center', 'justify-center', 'min-h-[50vh]');
    });
  });

  describe('LoadingButton', () => {
    it('renders children when not loading', () => {
      render(
        <LoadingButton isLoading={false}>
          <span>Click me</span>
        </LoadingButton>
      );

      expect(screen.getByText('Click me')).toBeInTheDocument();
      expect(screen.queryByTestId('loader2-icon')).not.toBeInTheDocument();
      expect(screen.queryByText('Loading...')).not.toBeInTheDocument();
    });

    it('shows loading state when loading', () => {
      render(
        <LoadingButton isLoading={true}>
          <span>Click me</span>
        </LoadingButton>
      );

      expect(screen.getByTestId('loader2-icon')).toBeInTheDocument();
      expect(screen.getByText('Loading...')).toBeInTheDocument();
      expect(screen.queryByText('Click me')).not.toBeInTheDocument();
    });

    it('accepts custom loading text', () => {
      render(
        <LoadingButton isLoading={true} loadingText="Processing...">
          <span>Submit</span>
        </LoadingButton>
      );

      expect(screen.getByText('Processing...')).toBeInTheDocument();
      expect(screen.queryByText('Loading...')).not.toBeInTheDocument();
    });

    it('is disabled when loading', () => {
      render(
        <LoadingButton isLoading={true}>
          <span>Click me</span>
        </LoadingButton>
      );

      const button = screen.getByRole('button');
      expect(button).toBeDisabled();
      expect(button).toHaveClass('cursor-not-allowed', 'opacity-50');
    });

    it('is not disabled when not loading', () => {
      render(
        <LoadingButton isLoading={false}>
          <span>Click me</span>
        </LoadingButton>
      );

      const button = screen.getByRole('button');
      expect(button).not.toBeDisabled();
      expect(button).not.toHaveClass('cursor-not-allowed', 'opacity-50');
    });

    it('accepts custom className', () => {
      render(
        <LoadingButton isLoading={false} className="custom-button">
          <span>Click me</span>
        </LoadingButton>
      );

      const button = screen.getByRole('button');
      expect(button).toHaveClass('custom-button');
    });

    it('has proper base classes', () => {
      render(
        <LoadingButton isLoading={false}>
          <span>Click me</span>
        </LoadingButton>
      );

      const button = screen.getByRole('button');
      expect(button).toHaveClass('inline-flex', 'items-center', 'justify-center');
    });

    it('shows small spinner when loading', () => {
      render(
        <LoadingButton isLoading={true}>
          <span>Click me</span>
        </LoadingButton>
      );

      const spinner = screen.getByTestId('loader2-icon');
      expect(spinner).toHaveClass('h-4', 'w-4', 'mr-2'); // sm size with margin
    });
  });

  describe('useLoading', () => {
    it('initializes with default state', () => {
      const { result } = renderHook(() => useLoading());

      expect(result.current.isLoading).toBe(false);
      expect(typeof result.current.startLoading).toBe('function');
      expect(typeof result.current.stopLoading).toBe('function');
      expect(typeof result.current.toggleLoading).toBe('function');
      expect(typeof result.current.setIsLoading).toBe('function');
    });

    it('initializes with custom initial state', () => {
      const { result } = renderHook(() => useLoading(true));

      expect(result.current.isLoading).toBe(true);
    });

    it('starts loading', () => {
      const { result } = renderHook(() => useLoading());

      act(() => {
        result.current.startLoading();
      });

      expect(result.current.isLoading).toBe(true);
    });

    it('stops loading', () => {
      const { result } = renderHook(() => useLoading(true));

      act(() => {
        result.current.stopLoading();
      });

      expect(result.current.isLoading).toBe(false);
    });

    it('toggles loading state', () => {
      const { result } = renderHook(() => useLoading());

      act(() => {
        result.current.toggleLoading();
      });

      expect(result.current.isLoading).toBe(true);

      act(() => {
        result.current.toggleLoading();
      });

      expect(result.current.isLoading).toBe(false);
    });

    it('sets loading state directly', () => {
      const { result } = renderHook(() => useLoading());

      act(() => {
        result.current.setIsLoading(true);
      });

      expect(result.current.isLoading).toBe(true);

      act(() => {
        result.current.setIsLoading(false);
      });

      expect(result.current.isLoading).toBe(false);
    });

    it('maintains stable function references', () => {
      const { result, rerender } = renderHook(() => useLoading());

      const initialFunctions = {
        startLoading: result.current.startLoading,
        stopLoading: result.current.stopLoading,
        toggleLoading: result.current.toggleLoading,
      };

      rerender();

      expect(result.current.startLoading).toBe(initialFunctions.startLoading);
      expect(result.current.stopLoading).toBe(initialFunctions.stopLoading);
      expect(result.current.toggleLoading).toBe(initialFunctions.toggleLoading);
    });
  });

  describe('withLoading', () => {
    const TestComponent = ({ message }: { message: string }) => (
      <div data-testid="test-component">{message}</div>
    );

    it('wraps component with loading overlay', () => {
      const WrappedComponent = withLoading(TestComponent);

      render(<WrappedComponent message="Test message" isLoading={false} />);

      expect(screen.getByTestId('test-component')).toBeInTheDocument();
      expect(screen.getByText('Test message')).toBeInTheDocument();
      expect(screen.queryByTestId('loader2-icon')).not.toBeInTheDocument();
    });

    it('shows loading overlay when isLoading is true', () => {
      const WrappedComponent = withLoading(TestComponent);

      render(<WrappedComponent message="Test message" isLoading={true} />);

      expect(screen.getByTestId('test-component')).toBeInTheDocument();
      expect(screen.getByText('Test message')).toBeInTheDocument();
      expect(screen.getByTestId('loader2-icon')).toBeInTheDocument();
    });

    it('passes loading props to overlay', () => {
      const WrappedComponent = withLoading(TestComponent, {
        message: 'Custom loading message',
        spinnerSize: 'lg',
      });

      render(<WrappedComponent message="Test message" isLoading={true} />);

      expect(screen.getByText('Custom loading message')).toBeInTheDocument();
      const spinner = screen.getByTestId('loader2-icon');
      expect(spinner).toHaveClass('h-8', 'w-8'); // lg size
    });

    it('sets correct display name', () => {
      TestComponent.displayName = 'TestComponent';
      const WrappedComponent = withLoading(TestComponent);

      expect(WrappedComponent.displayName).toBe('withLoading(TestComponent)');
    });

    it('uses component name when displayName is not available', () => {
      const WrappedComponent = withLoading(TestComponent);

      expect(WrappedComponent.displayName).toBe('withLoading(TestComponent)');
    });

    it('filters out isLoading prop from component props', () => {
      const TestComponentWithProps = (props: { message: string; otherProp: string }) => (
        <div data-testid="test-component">
          {props.message} - {props.otherProp}
        </div>
      );

      const WrappedComponent = withLoading(TestComponentWithProps);

      render(
        <WrappedComponent
          message="Test message"
          otherProp="Other value"
          isLoading={false}
        />
      );

      expect(screen.getByText('Test message - Other value')).toBeInTheDocument();
    });
  });
});
