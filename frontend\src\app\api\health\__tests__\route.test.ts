import { describe, it, expect } from 'vitest';
import { GET } from '../route';

describe('/api/health', () => {
  describe('GET /api/health', () => {
    it('returns health check response', async () => {
      const response = await GET();
      const data = await response.json();

      expect(response.status).toBe(200);
      expect(data).toHaveProperty('status', 'healthy');
      expect(data).toHaveProperty('service', 'lonors-frontend');
      expect(data).toHaveProperty('timestamp');
      expect(data).toHaveProperty('version');
      expect(data).toHaveProperty('environment');
    });

    it('returns proper content type', async () => {
      const response = await GET();
      
      expect(response.headers.get('Content-Type')).toBe('application/json');
    });

    it('includes valid timestamp', async () => {
      const response = await GET();
      const data = await response.json();

      const timestamp = new Date(data.timestamp);
      expect(timestamp).toBeInstanceOf(Date);
      expect(timestamp.getTime()).not.toBeNaN();
    });

    it('includes version information', async () => {
      const response = await GET();
      const data = await response.json();

      expect(data.version).toBeDefined();
      expect(typeof data.version).toBe('string');
    });

    it('includes environment information', async () => {
      const response = await GET();
      const data = await response.json();

      expect(data.environment).toBeDefined();
      expect(typeof data.environment).toBe('string');
    });

    it('returns consistent response structure', async () => {
      const response1 = await GET();
      const response2 = await GET();
      
      const data1 = await response1.json();
      const data2 = await response2.json();

      // Structure should be consistent
      expect(Object.keys(data1)).toEqual(Object.keys(data2));
      expect(data1.status).toBe(data2.status);
      expect(data1.service).toBe(data2.service);
    });

    it('responds quickly', async () => {
      const startTime = Date.now();
      await GET();
      const endTime = Date.now();

      const responseTime = endTime - startTime;
      expect(responseTime).toBeLessThan(100); // Should respond within 100ms
    });

    it('handles multiple concurrent requests', async () => {
      const requests = Array(5).fill(null).map(() => GET());
      const responses = await Promise.all(requests);

      responses.forEach(response => {
        expect(response.status).toBe(200);
      });
    });

    it('returns valid JSON', async () => {
      const response = await GET();
      const text = await response.text();

      expect(() => JSON.parse(text)).not.toThrow();
    });

    it('includes all required health check fields', async () => {
      const response = await GET();
      const data = await response.json();

      const requiredFields = ['status', 'service', 'timestamp', 'version', 'environment'];
      requiredFields.forEach(field => {
        expect(data).toHaveProperty(field);
        expect(data[field]).toBeDefined();
      });
    });

    it('returns healthy status', async () => {
      const response = await GET();
      const data = await response.json();

      expect(data.status).toBe('healthy');
    });
  });
});
