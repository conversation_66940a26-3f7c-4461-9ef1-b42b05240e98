"""
Tag Repository Implementation

This module implements the tag repository interface using SQLAlchemy.
"""

from uuid import UUID

from sqlalchemy import and_, select
from sqlalchemy.ext.asyncio import AsyncSession

from src.domain.entities.tag import Tag
from src.domain.repositories.tag_repository import TagRepository
from src.infrastructure.database.models.tag import TagModel


class SQLAlchemyTagRepository(TagRepository):
    """SQLAlchemy implementation of the tag repository."""

    def __init__(self, session: AsyncSession):
        """
        Initialize the repository.

        Args:
            session: SQLAlchemy async session
        """
        self._session = session

    async def get_by_id(self, tag_id: UUID) -> Tag | None:
        """
        Get a tag by its ID.

        Args:
            tag_id: The ID of the tag to retrieve

        Returns:
            The tag if found, None otherwise
        """
        query = select(TagModel).where(TagModel.id == tag_id)
        result = await self._session.execute(query)
        tag_model = result.scalar_one_or_none()

        if tag_model is None:
            return None

        return tag_model.to_domain_entity()

    async def get_by_name(self, user_id: UUID, name: str) -> Tag | None:
        """
        Get a tag by its name for a specific user.

        Args:
            user_id: The ID of the user
            name: The name of the tag

        Returns:
            The tag if found, None otherwise
        """
        query = select(TagModel).where(
            and_(TagModel.user_id == user_id, TagModel.name == name)
        )

        result = await self._session.execute(query)
        tag_model = result.scalar_one_or_none()

        if tag_model is None:
            return None

        return tag_model.to_domain_entity()

    async def get_all_by_user(self, user_id: UUID) -> list[Tag]:
        """
        Get all tags for a user.

        Args:
            user_id: The ID of the user

        Returns:
            List of tags for the user
        """
        query = select(TagModel).where(TagModel.user_id == user_id)
        result = await self._session.execute(query)
        tag_models = result.scalars().all()

        return [tag_model.to_domain_entity() for tag_model in tag_models]

    async def create(self, tag: Tag) -> Tag:
        """
        Create a new tag.

        Args:
            tag: The tag to create

        Returns:
            The created tag with any generated fields
        """
        # Create tag model using the from_domain_entity method
        tag_model = TagModel.from_domain_entity(tag)

        self._session.add(tag_model)
        await self._session.flush()
        await self._session.refresh(tag_model)

        return tag_model.to_domain_entity()

    async def update(self, tag: Tag) -> Tag:
        """
        Update an existing tag.

        Args:
            tag: The tag to update

        Returns:
            The updated tag
        """
        query = select(TagModel).where(TagModel.id == tag.id)
        result = await self._session.execute(query)
        tag_model = result.scalar_one_or_none()

        if tag_model is None:
            raise ValueError(f"Tag with ID {tag.id} not found")

        tag_model.name = tag.name
        tag_model.color = tag.color
        tag_model.updated_at = tag.updated_at

        await self._session.flush()
        await self._session.refresh(tag_model)

        return tag_model.to_domain_entity()

    async def delete(self, tag_id: UUID) -> bool:
        """
        Delete a tag by its ID.

        Args:
            tag_id: The ID of the tag to delete

        Returns:
            True if the tag was deleted, False otherwise
        """
        query = select(TagModel).where(TagModel.id == tag_id)
        result = await self._session.execute(query)
        tag_model = result.scalar_one_or_none()

        if tag_model is None:
            return False

        await self._session.delete(tag_model)
        await self._session.flush()

        return True
