# Frontend Architecture

This document describes the frontend architecture of the Lonors platform, built with React, TypeScript, and modern development tools.

## Table of Contents

- [Overview](#overview)
- [Technology Stack](#technology-stack)
- [Feature Slice Design (FSD)](#feature-slice-design-fsd)
- [Project Structure](#project-structure)
- [State Management](#state-management)
- [Routing](#routing)
- [API Integration](#api-integration)
- [Component Architecture](#component-architecture)
- [Development Workflow](#development-workflow)

## Overview

The Lonors frontend is a modern React application built with TypeScript, following Feature Slice Design (FSD) architecture principles. It provides a responsive, accessible, and performant user interface for interacting with AI agents, managing protocols, and building dynamic UIs.

### Key Principles

- **Feature-First Architecture**: Code organized by features rather than technical layers
- **Type Safety**: Comprehensive TypeScript coverage with strict configuration
- **Component Reusability**: Shared UI components with consistent design system
- **Performance**: Code splitting, lazy loading, and optimized bundle sizes
- **Accessibility**: WCAG 2.1 AA compliance with proper ARIA attributes
- **Developer Experience**: Hot reload, comprehensive tooling, and clear conventions

## Technology Stack

### Core Technologies

- **React 18**: Modern React with concurrent features and hooks
- **TypeScript 5**: Strict type checking with advanced type features
- **Vite**: Fast build tool with HMR and optimized production builds
- **pnpm**: Efficient package manager with workspace support

### UI Framework

- **ShadCN UI**: Customizable component library built on Radix UI primitives
- **Tailwind CSS**: Utility-first CSS framework with custom design system
- **Radix UI**: Unstyled, accessible UI primitives
- **Lucide React**: Beautiful, customizable SVG icons

### State Management & Data Fetching

- **Zustand**: Lightweight state management for global state
- **TanStack Query (React Query)**: Server state management with caching
- **React Hook Form**: Performant forms with minimal re-renders
- **Zod**: Runtime type validation for forms and API responses

### Development Tools

- **ESLint**: Code linting with TypeScript and React rules
- **Prettier**: Code formatting with Tailwind CSS plugin
- **Husky**: Git hooks for pre-commit quality checks
- **Storybook**: Component documentation and development
- **Vitest**: Fast unit testing framework

### Animation & Interactions

- **Anime.js**: Lightweight animation library for micro-interactions
- **CSS Animations**: Tailwind CSS animations and transitions
- **Framer Motion**: Advanced animations (planned for future)

## Feature Slice Design (FSD)

The frontend follows Feature Slice Design (FSD) architecture, organizing code by features and business logic rather than technical layers.

### FSD Layers

```
src/
├── app/           # Application layer (routing, providers, global config)
├── pages/         # Page layer (route components)
├── widgets/       # Widget layer (complex UI blocks)
├── features/      # Feature layer (business logic features)
├── entities/      # Entity layer (business entities)
└── shared/        # Shared layer (reusable code)
```

### Layer Responsibilities

#### App Layer (`src/app/`)
- Application initialization and configuration
- Global providers (theme, auth, query client)
- Root routing configuration
- Global error boundaries

#### Pages Layer (`src/pages/`)
- Route-level components
- Page-specific layouts
- Route guards and authentication
- SEO and meta tags

#### Widgets Layer (`src/widgets/`)
- Complex UI blocks combining multiple features
- Dashboard widgets
- Navigation components
- Layout components

#### Features Layer (`src/features/`)
- Business logic features
- User interactions and workflows
- Feature-specific state management
- Integration with external services

#### Entities Layer (`src/entities/`)
- Business entities and domain models
- Entity-specific operations
- Type definitions
- API integration for entities

#### Shared Layer (`src/shared/`)
- Reusable UI components
- Utility functions and helpers
- API client configuration
- Design system tokens

## Project Structure

```
frontend/
├── public/                 # Static assets
├── src/
│   ├── app/               # Application layer
│   │   ├── App.tsx        # Root component
│   │   ├── router.tsx     # Routing configuration
│   │   └── providers/     # Global providers
│   ├── pages/             # Page components
│   │   ├── home/          # Home page
│   │   ├── auth/          # Authentication pages
│   │   ├── dashboard/     # Dashboard page
│   │   └── ...
│   ├── widgets/           # Complex UI widgets
│   ├── features/          # Business features
│   ├── entities/          # Business entities
│   └── shared/            # Shared resources
│       ├── ui/            # UI components
│       ├── lib/           # Utility functions
│       ├── hooks/         # Custom hooks
│       ├── types/         # Type definitions
│       ├── api/           # API client
│       ├── store/         # Global state
│       ├── config/        # Configuration
│       ├── assets/        # Assets
│       └── styles/        # Global styles
├── .storybook/            # Storybook configuration
├── package.json           # Dependencies and scripts
├── vite.config.ts         # Vite configuration
├── tailwind.config.js     # Tailwind CSS configuration
├── tsconfig.json          # TypeScript configuration
└── ...
```

## State Management

### Global State (Zustand)

Used for application-wide state that needs to persist across components:

```typescript
// Example: Auth store
interface AuthState {
  user: User | null
  isAuthenticated: boolean
  login: (credentials: LoginCredentials) => Promise<void>
  logout: () => void
}

export const useAuthStore = create<AuthState>((set) => ({
  user: null,
  isAuthenticated: false,
  login: async (credentials) => {
    // Login logic
  },
  logout: () => {
    // Logout logic
  },
}))
```

### Server State (TanStack Query)

Used for server data with caching, synchronization, and background updates:

```typescript
// Example: Agents query
export function useAgents() {
  return useQuery({
    queryKey: ['agents'],
    queryFn: agentApi.getAgents,
    staleTime: 1000 * 60 * 5, // 5 minutes
  })
}
```

### Local State (React Hooks)

Used for component-specific state:

```typescript
// Example: Component state
function AgentCard() {
  const [isExpanded, setIsExpanded] = useState(false)
  const [isLoading, setIsLoading] = useState(false)
  
  // Component logic
}
```

## Routing

### Route Structure

```typescript
// Route configuration
const routes = [
  { path: '/', element: <HomePage /> },
  { path: '/login', element: <LoginPage /> },
  { path: '/dashboard', element: <ProtectedRoute><DashboardPage /></ProtectedRoute> },
  { path: '/agents', element: <ProtectedRoute><AgentsPage /></ProtectedRoute> },
  // ...
]
```

### Protected Routes

```typescript
function ProtectedRoute({ children }: { children: React.ReactNode }) {
  const { isAuthenticated, isLoading } = useAuth()
  
  if (isLoading) return <LoadingSpinner />
  if (!isAuthenticated) return <Navigate to="/login" />
  
  return <>{children}</>
}
```

## API Integration

### API Client Configuration

```typescript
// Axios client with interceptors
export const apiClient = axios.create({
  baseURL: '/api/v1',
  timeout: 30000,
})

// Request interceptor for auth
apiClient.interceptors.request.use((config) => {
  const token = localStorage.getItem('access_token')
  if (token) {
    config.headers.Authorization = `Bearer ${token}`
  }
  return config
})

// Response interceptor for error handling
apiClient.interceptors.response.use(
  (response) => response,
  async (error) => {
    // Handle token refresh, errors, etc.
  }
)
```

### API Layer Structure

```typescript
// Entity-specific API modules
export const agentApi = {
  getAgents: () => apiClient.get('/agents'),
  createAgent: (data) => apiClient.post('/agents', data),
  updateAgent: (id, data) => apiClient.put(`/agents/${id}`, data),
  deleteAgent: (id) => apiClient.delete(`/agents/${id}`),
}
```

## Component Architecture

### Component Categories

1. **UI Components** (`shared/ui/`): Reusable, design system components
2. **Feature Components** (`features/`): Business logic components
3. **Page Components** (`pages/`): Route-level components
4. **Layout Components** (`widgets/`): Complex layout structures

### Component Patterns

#### Compound Components

```typescript
// Example: Card compound component
export const Card = {
  Root: CardRoot,
  Header: CardHeader,
  Title: CardTitle,
  Content: CardContent,
  Footer: CardFooter,
}

// Usage
<Card.Root>
  <Card.Header>
    <Card.Title>Agent Status</Card.Title>
  </Card.Header>
  <Card.Content>
    {/* Content */}
  </Card.Content>
</Card.Root>
```

#### Render Props

```typescript
// Example: Data fetching component
function AgentList({ children }: { children: (data: Agent[]) => React.ReactNode }) {
  const { data, isLoading, error } = useAgents()
  
  if (isLoading) return <LoadingSpinner />
  if (error) return <ErrorMessage error={error} />
  
  return children(data || [])
}
```

#### Custom Hooks

```typescript
// Example: Agent management hook
export function useAgentManagement() {
  const queryClient = useQueryClient()
  
  const createAgent = useMutation({
    mutationFn: agentApi.createAgent,
    onSuccess: () => {
      queryClient.invalidateQueries(['agents'])
    },
  })
  
  return {
    createAgent,
    // Other operations
  }
}
```

## Development Workflow

### Code Quality

1. **Type Safety**: All components and functions are fully typed
2. **Linting**: ESLint with TypeScript and React rules
3. **Formatting**: Prettier with Tailwind CSS plugin
4. **Testing**: Unit tests with Vitest and React Testing Library

### Component Development

1. **Storybook**: Develop components in isolation
2. **Design System**: Follow established design tokens
3. **Accessibility**: Include ARIA attributes and keyboard navigation
4. **Performance**: Optimize with React.memo, useMemo, useCallback

### Best Practices

1. **File Naming**: Use kebab-case for files, PascalCase for components
2. **Import Organization**: Group imports by type (React, libraries, internal)
3. **Component Size**: Keep components under 200 lines
4. **Single Responsibility**: One component per file, clear purpose
5. **Error Boundaries**: Wrap components with error boundaries
6. **Loading States**: Always handle loading and error states

### Performance Optimization

1. **Code Splitting**: Lazy load pages and heavy components
2. **Bundle Analysis**: Regular bundle size monitoring
3. **Image Optimization**: WebP format with fallbacks
4. **Caching**: Aggressive caching with React Query
5. **Memoization**: Strategic use of React.memo and hooks

This architecture provides a scalable, maintainable foundation for the Lonors frontend application while ensuring excellent developer experience and user performance.
