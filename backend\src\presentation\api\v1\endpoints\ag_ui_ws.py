"""
AG-UI WebSocket endpoints.

This module provides WebSocket endpoints for real-time AG-UI
communication and state synchronization.
"""

import json
import uuid
from typing import Dict, Set

from fastapi import APIRouter, Depends, WebSocket, WebSocketDisconnect, status
from fastapi.websockets import WebSocketState

from src.application.use_cases.ag_ui_service import AGUIService
from src.infrastructure.logging.setup import get_logger
from src.infrastructure.security.jwt import jwt_manager

logger = get_logger(__name__)

router = APIRouter()


class AGUIConnectionManager:
    """
    WebSocket connection manager for AG-UI sessions.
    
    Manages WebSocket connections, session mapping, and message broadcasting.
    """
    
    def __init__(self):
        # Active connections: connection_id -> WebSocket
        self.active_connections: Dict[str, WebSocket] = {}
        # User connections: user_id -> Set[connection_id]
        self.user_connections: Dict[str, Set[str]] = {}
        # Session connections: session_id -> connection_id
        self.session_connections: Dict[str, str] = {}
    
    async def connect(
        self,
        websocket: WebSocket,
        connection_id: str,
        user_id: str,
        session_id: str
    ) -> None:
        """
        Accept WebSocket connection and register it.
        
        Args:
            websocket: WebSocket connection
            connection_id: Unique connection identifier
            user_id: User ID
            session_id: Session ID
        """
        await websocket.accept()
        
        # Store connection
        self.active_connections[connection_id] = websocket
        
        # Map user to connection
        if user_id not in self.user_connections:
            self.user_connections[user_id] = set()
        self.user_connections[user_id].add(connection_id)
        
        # Map session to connection
        self.session_connections[session_id] = connection_id
        
        logger.info(f"AG-UI WebSocket connected: {connection_id} for user: {user_id}")
    
    def disconnect(self, connection_id: str, user_id: str, session_id: str) -> None:
        """
        Disconnect and clean up WebSocket connection.
        
        Args:
            connection_id: Connection identifier
            user_id: User ID
            session_id: Session ID
        """
        # Remove connection
        if connection_id in self.active_connections:
            del self.active_connections[connection_id]
        
        # Remove user mapping
        if user_id in self.user_connections:
            self.user_connections[user_id].discard(connection_id)
            if not self.user_connections[user_id]:
                del self.user_connections[user_id]
        
        # Remove session mapping
        if session_id in self.session_connections:
            del self.session_connections[session_id]
        
        logger.info(f"AG-UI WebSocket disconnected: {connection_id}")
    
    async def send_personal_message(self, message: dict, connection_id: str) -> bool:
        """
        Send message to specific connection.
        
        Args:
            message: Message to send
            connection_id: Target connection ID
            
        Returns:
            bool: True if sent successfully, False otherwise
        """
        websocket = self.active_connections.get(connection_id)
        if websocket and websocket.client_state == WebSocketState.CONNECTED:
            try:
                await websocket.send_text(json.dumps(message))
                return True
            except Exception as e:
                logger.error(f"Failed to send message to {connection_id}: {e}")
                return False
        return False
    
    async def send_to_user(self, message: dict, user_id: str) -> int:
        """
        Send message to all connections of a user.
        
        Args:
            message: Message to send
            user_id: Target user ID
            
        Returns:
            int: Number of connections message was sent to
        """
        sent_count = 0
        connection_ids = self.user_connections.get(user_id, set()).copy()
        
        for connection_id in connection_ids:
            if await self.send_personal_message(message, connection_id):
                sent_count += 1
        
        return sent_count
    
    async def broadcast_to_session(self, message: dict, session_id: str) -> bool:
        """
        Send message to session connection.
        
        Args:
            message: Message to send
            session_id: Target session ID
            
        Returns:
            bool: True if sent successfully, False otherwise
        """
        connection_id = self.session_connections.get(session_id)
        if connection_id:
            return await self.send_personal_message(message, connection_id)
        return False


# Global connection manager instance
connection_manager = AGUIConnectionManager()


def get_ag_ui_service() -> AGUIService:
    """Get AG-UI service dependency."""
    return AGUIService()


async def authenticate_websocket(websocket: WebSocket, token: str) -> dict:
    """
    Authenticate WebSocket connection using JWT token.
    
    Args:
        websocket: WebSocket connection
        token: JWT token
        
    Returns:
        dict: User payload from token
        
    Raises:
        Exception: If authentication fails
    """
    try:
        payload = jwt_manager.verify_token(token, "access")
        return payload
    except Exception as e:
        logger.warning(f"WebSocket authentication failed: {e}")
        await websocket.close(code=status.WS_1008_POLICY_VIOLATION)
        raise


@router.websocket("/ws/{session_id}")
async def ag_ui_websocket_endpoint(
    websocket: WebSocket,
    session_id: str,
    token: str,
    ag_ui_service: AGUIService = Depends(get_ag_ui_service)
):
    """
    AG-UI WebSocket endpoint for real-time communication.
    
    Args:
        websocket: WebSocket connection
        session_id: Session identifier
        token: JWT authentication token
        ag_ui_service: AG-UI service dependency
    """
    connection_id = f"conn_{uuid.uuid4().hex[:12]}"
    user_payload = None
    
    try:
        # Authenticate connection
        user_payload = await authenticate_websocket(websocket, token)
        user_id = user_payload["user_id"]
        
        # Create or get session
        session = await ag_ui_service.create_session(
            user_id=uuid.UUID(user_id),
            connection_id=connection_id,
            layout_id=uuid.UUID(session_id) if session_id != "new" else None
        )
        
        # Connect to manager
        await connection_manager.connect(
            websocket=websocket,
            connection_id=connection_id,
            user_id=user_id,
            session_id=str(session.id)
        )
        
        # Send initial session data
        await websocket.send_text(json.dumps({
            "type": "session_connected",
            "session_id": str(session.id),
            "connection_id": connection_id,
            "timestamp": session.created_at.isoformat()
        }))
        
        # If session has a layout, send initial layout data
        if session.layout_id:
            layout = await ag_ui_service.get_layout(
                layout_id=session.layout_id,
                user_id=uuid.UUID(user_id)
            )
            if layout:
                await websocket.send_text(json.dumps({
                    "type": "layout_loaded",
                    "layout": layout.dict(),
                    "timestamp": datetime.now(timezone.utc).isoformat()
                }))
        
        # Message handling loop
        while True:
            try:
                # Receive message from client
                data = await websocket.receive_text()
                message = json.loads(data)
                
                logger.debug(f"Received AG-UI message: {message.get('type')} from {connection_id}")
                
                # Handle different message types
                await handle_websocket_message(
                    websocket=websocket,
                    connection_id=connection_id,
                    message=message,
                    ag_ui_service=ag_ui_service
                )
                
            except json.JSONDecodeError:
                await websocket.send_text(json.dumps({
                    "type": "error",
                    "error": "Invalid JSON format",
                    "timestamp": datetime.now(timezone.utc).isoformat()
                }))
            except Exception as e:
                logger.error(f"Error handling WebSocket message: {e}")
                await websocket.send_text(json.dumps({
                    "type": "error",
                    "error": "Internal server error",
                    "timestamp": datetime.now(timezone.utc).isoformat()
                }))
    
    except WebSocketDisconnect:
        logger.info(f"AG-UI WebSocket disconnected: {connection_id}")
    except Exception as e:
        logger.error(f"AG-UI WebSocket error: {e}")
    finally:
        # Clean up connection
        if user_payload:
            user_id = user_payload["user_id"]
            connection_manager.disconnect(connection_id, user_id, session_id)
            
            # Close session in service
            await ag_ui_service.close_session(connection_id)


async def handle_websocket_message(
    websocket: WebSocket,
    connection_id: str,
    message: dict,
    ag_ui_service: AGUIService
) -> None:
    """
    Handle incoming WebSocket message.
    
    Args:
        websocket: WebSocket connection
        connection_id: Connection identifier
        message: Received message
        ag_ui_service: AG-UI service
    """
    from datetime import datetime, timezone
    
    message_type = message.get("type")
    
    if message_type == "ping":
        # Handle ping/pong for connection health
        await websocket.send_text(json.dumps({
            "type": "pong",
            "timestamp": datetime.now(timezone.utc).isoformat()
        }))
    
    elif message_type == "event":
        # Handle UI events
        event_data = message.get("data", {})
        state_update = await ag_ui_service.handle_event(connection_id, event_data)
        
        if state_update:
            # Send state update back to client
            await websocket.send_text(json.dumps({
                "type": "state_update",
                "data": state_update.dict(),
                "timestamp": datetime.now(timezone.utc).isoformat()
            }))
    
    elif message_type == "get_layout":
        # Handle layout request
        session = await ag_ui_service.get_session_by_connection(connection_id)
        if session and session.layout_id:
            # Get user from session (would need to be stored in session)
            # For now, we'll skip authorization check
            layout = await ag_ui_service.get_layout(
                layout_id=session.layout_id,
                user_id=session.user_id
            )
            if layout:
                await websocket.send_text(json.dumps({
                    "type": "layout_data",
                    "layout": layout.dict(),
                    "timestamp": datetime.now(timezone.utc).isoformat()
                }))
    
    elif message_type == "update_component":
        # Handle component update
        component_id = message.get("component_id")
        update_data = message.get("update_data", {})
        
        session = await ag_ui_service.get_session_by_connection(connection_id)
        if session and session.layout_id and component_id:
            from src.domain.entities.ag_ui import AGUIComponentUpdate
            
            component_update = AGUIComponentUpdate(**update_data)
            updated_component = await ag_ui_service.update_component(
                layout_id=session.layout_id,
                component_id=component_id,
                user_id=session.user_id,
                update_data=component_update
            )
            
            if updated_component:
                await websocket.send_text(json.dumps({
                    "type": "component_updated",
                    "component": updated_component.dict(),
                    "timestamp": datetime.now(timezone.utc).isoformat()
                }))
    
    elif message_type == "subscribe_events":
        # Handle event subscription
        event_types = message.get("event_types", [])
        # Store subscription preferences in session
        # This would be implemented based on specific requirements
        await websocket.send_text(json.dumps({
            "type": "subscribed",
            "event_types": event_types,
            "timestamp": datetime.now(timezone.utc).isoformat()
        }))
    
    else:
        # Unknown message type
        await websocket.send_text(json.dumps({
            "type": "error",
            "error": f"Unknown message type: {message_type}",
            "timestamp": datetime.now(timezone.utc).isoformat()
        }))


@router.get("/connections/stats")
async def get_connection_stats() -> dict:
    """
    Get WebSocket connection statistics.
    
    Returns:
        dict: Connection statistics
    """
    return {
        "active_connections": len(connection_manager.active_connections),
        "connected_users": len(connection_manager.user_connections),
        "active_sessions": len(connection_manager.session_connections),
        "timestamp": datetime.now(timezone.utc).isoformat()
    }


# Export connection manager for use in other modules
__all__ = ["router", "connection_manager"]
