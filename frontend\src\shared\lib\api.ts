/**
 * API client configuration for Lonors backend integration
 */

// API Configuration
export const API_CONFIG = {
  BASE_URL: process.env.NEXT_PUBLIC_API_URL || 'http://localhost:3003',
  API_VERSION: 'v1',
  TIMEOUT: 10000, // 10 seconds
  RETRY_ATTEMPTS: 3,
  RETRY_DELAY: 1000, // 1 second
} as const;

// API Endpoints
export const API_ENDPOINTS = {
  // Health check
  HEALTH: '/health',

  // Authentication
  AUTH: {
    LOGIN: '/api/v1/auth/login',
    LOGOUT: '/api/v1/auth/logout',
    REGISTER: '/api/v1/auth/register',
    REFRESH: '/api/v1/auth/refresh',
    PROFILE: '/api/v1/auth/profile',
  },

  // Users
  USERS: {
    BASE: '/api/v1/users',
    BY_ID: (id: string) => `/api/v1/users/${id}`,
  },

  // AI Agents
  AGENTS: {
    BASE: '/api/v1/agents',
    BY_ID: (id: string) => `/api/v1/agents/${id}`,
    EXECUTE: (id: string) => `/api/v1/agents/${id}/execute`,
  },

  // Workflows
  WORKFLOWS: {
    BASE: '/api/v1/workflows',
    BY_ID: (id: string) => `/api/v1/workflows/${id}`,
    EXECUTE: (id: string) => `/api/v1/workflows/${id}/execute`,
  },

  // Knowledge Graph
  KNOWLEDGE: {
    BASE: '/api/v1/knowledge',
    SEARCH: '/api/v1/knowledge/search',
    ENTITIES: '/api/v1/knowledge/entities',
    RELATIONSHIPS: '/api/v1/knowledge/relationships',
  },

  // WebSocket endpoints
  WEBSOCKET: {
    MCP: '/ws/mcp',
    A2A: '/ws/a2a',
    AG_UI: '/ws/ag-ui',
  },
} as const;

// HTTP Status Codes
export const HTTP_STATUS = {
  OK: 200,
  CREATED: 201,
  NO_CONTENT: 204,
  BAD_REQUEST: 400,
  UNAUTHORIZED: 401,
  FORBIDDEN: 403,
  NOT_FOUND: 404,
  CONFLICT: 409,
  UNPROCESSABLE_ENTITY: 422,
  INTERNAL_SERVER_ERROR: 500,
  BAD_GATEWAY: 502,
  SERVICE_UNAVAILABLE: 503,
} as const;

// API Error types
export interface ApiError {
  message: string;
  status: number;
  code?: string;
  details?: Record<string, any>;
}

export class ApiException extends Error {
  public status: number;
  public code?: string | undefined;
  public details?: Record<string, any> | undefined;

  constructor(error: ApiError) {
    super(error.message);
    this.name = 'ApiException';
    this.status = error.status;
    this.code = error.code || undefined;
    this.details = error.details || undefined;
  }
}

// Request/Response types
export interface ApiResponse<T = any> {
  data: T;
  message?: string;
  status: number;
}

export interface PaginatedResponse<T = any> {
  data: T[];
  pagination: {
    page: number;
    limit: number;
    total: number;
    totalPages: number;
  };
}

// Request options
export interface RequestOptions {
  method?: 'GET' | 'POST' | 'PUT' | 'PATCH' | 'DELETE';
  headers?: Record<string, string>;
  body?: any;
  timeout?: number;
  retries?: number;
  signal?: AbortSignal;
}

/**
 * Create API client with base configuration
 */
export function createApiClient() {
  const baseURL = API_CONFIG.BASE_URL;

  /**
   * Make an API request with error handling and retries
   */
  async function request<T = any>(
    endpoint: string,
    options: RequestOptions = {}
  ): Promise<ApiResponse<T>> {
    const {
      method = 'GET',
      headers = {},
      body,
      timeout = API_CONFIG.TIMEOUT,
      // retries = API_CONFIG.RETRY_ATTEMPTS, // TODO: Implement retry logic
      signal,
    } = options;

    const url = `${baseURL}${endpoint}`;

    // Default headers
    const defaultHeaders: Record<string, string> = {
      'Content-Type': 'application/json',
      'Accept': 'application/json',
    };

    // Add authorization header if token exists
    const token = getAuthToken();
    if (token) {
      defaultHeaders.Authorization = `Bearer ${token}`;
    }

    const requestOptions: RequestInit = {
      method,
      headers: { ...defaultHeaders, ...headers },
      ...(signal ? { signal } : {}),
    };

    // Add body for non-GET requests
    if (body && method !== 'GET') {
      requestOptions.body = JSON.stringify(body);
    }

    // Create timeout controller
    const timeoutController = new AbortController();
    const timeoutId = setTimeout(() => timeoutController.abort(), timeout);

    try {
      const response = await fetch(url, {
        ...requestOptions,
        signal: signal || timeoutController.signal,
      });

      clearTimeout(timeoutId);

      // Handle non-JSON responses
      const contentType = response.headers.get('content-type');
      let responseData: any;

      if (contentType && contentType.includes('application/json')) {
        responseData = await response.json();
      } else {
        responseData = await response.text();
      }

      // Handle error responses
      if (!response.ok) {
        const error: ApiError = {
          message: responseData.message || `HTTP ${response.status}`,
          status: response.status,
          code: responseData.code,
          details: responseData.details,
        };
        throw new ApiException(error);
      }

      return {
        data: responseData.data || responseData,
        message: responseData.message,
        status: response.status,
      };
    } catch (error) {
      clearTimeout(timeoutId);

      // Handle timeout
      if (error instanceof Error && error.name === 'AbortError') {
        throw new ApiException({
          message: 'Request timeout',
          status: 408,
          code: 'TIMEOUT',
        });
      }

      // Handle network errors
      if (error instanceof TypeError) {
        throw new ApiException({
          message: 'Network error',
          status: 0,
          code: 'NETWORK_ERROR',
        });
      }

      // Re-throw API exceptions
      if (error instanceof ApiException) {
        throw error;
      }

      // Handle unknown errors
      throw new ApiException({
        message: 'Unknown error occurred',
        status: 500,
        code: 'UNKNOWN_ERROR',
      });
    }
  }

  return {
    get: <T = any>(endpoint: string, options?: Omit<RequestOptions, 'method'>) =>
      request<T>(endpoint, { ...options, method: 'GET' }),

    post: <T = any>(endpoint: string, body?: any, options?: Omit<RequestOptions, 'method' | 'body'>) =>
      request<T>(endpoint, { ...options, method: 'POST', body }),

    put: <T = any>(endpoint: string, body?: any, options?: Omit<RequestOptions, 'method' | 'body'>) =>
      request<T>(endpoint, { ...options, method: 'PUT', body }),

    patch: <T = any>(endpoint: string, body?: any, options?: Omit<RequestOptions, 'method' | 'body'>) =>
      request<T>(endpoint, { ...options, method: 'PATCH', body }),

    delete: <T = any>(endpoint: string, options?: Omit<RequestOptions, 'method'>) =>
      request<T>(endpoint, { ...options, method: 'DELETE' }),
  };
}

/**
 * Get authentication token from storage
 */
function getAuthToken(): string | null {
  if (typeof window === 'undefined') return null;
  return localStorage.getItem('auth_token');
}

/**
 * Set authentication token in storage
 */
export function setAuthToken(token: string): void {
  if (typeof window === 'undefined') return;
  localStorage.setItem('auth_token', token);
}

/**
 * Remove authentication token from storage
 */
export function removeAuthToken(): void {
  if (typeof window === 'undefined') return;
  localStorage.removeItem('auth_token');
}

// Export default API client instance
export const apiClient = createApiClient();
