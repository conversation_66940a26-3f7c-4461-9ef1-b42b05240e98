# Note-Taking Application Architecture

## Overview

The note-taking application will provide a modern, feature-rich experience similar to Notion and Obsidian, with AI-powered features for content generation, organization, and recall.

## Core Features

1. **Rich Text Editing**
   - Markdown-based editor with WYSIWYG capabilities
   - Support for blocks, tables, code snippets, and embeds
   - Real-time collaboration

2. **Organization**
   - Hierarchical folder/page structure
   - Tagging system
   - Backlinks and bidirectional linking
   - Graph view of connections between notes

3. **AI Integration**
   - Content generation and completion
   - Automatic summarization
   - Smart search and recall
   - Content organization suggestions

4. **Sync & Storage**
   - Local-first approach with offline capabilities
   - Cloud sync for multi-device access
   - Version history and conflict resolution

## Technical Architecture

### Frontend

- **Framework**: React with TypeScript
- **State Management**: Context API + React Query for remote data
- **Editor**: Slate.js or ProseMirror for rich text editing
- **Styling**: Tailwind CSS with custom components
- **Offline Support**: IndexedDB for local storage

### Backend

- **API**: Node.js with Express or Fastify
- **Database**:
  - PostgreSQL for structured data (users, metadata)
  - MongoDB for document storage
  - Neo4j for graph relationships (backlinks, connections)
  - Qdrant for vector embeddings (semantic search)
- **Authentication**: JWT-based auth with refresh tokens
- **File Storage**: Local filesystem with cloud backup options
- **Real-time**: WebSockets for collaboration features

### AI Integration

- **Local Models**: Integration with Ollama for on-device inference
- **Embeddings**: Local embedding models for semantic search
- **RAG**: Retrieval-augmented generation for contextual AI responses
- **Knowledge Graph**: Automatic extraction of entities and relationships

## Implementation Phases

### Phase 1: Core Note-Taking

- Basic editor with Markdown support
- Local storage of notes
- Simple folder organization
- Basic search functionality

### Phase 2: Advanced Organization

- Tagging system
- Backlinks and connections
- Graph visualization
- Advanced search with filters

### Phase 3: AI Features

- Integration with local models
- Content generation and completion
- Automatic summarization
- Smart search with semantic capabilities

### Phase 4: Collaboration & Sync

- User accounts and authentication
- Cloud synchronization
- Real-time collaboration
- Version history and conflict resolution

## Data Models

### Note

```typescript
interface Note {
  id: string;
  title: string;
  content: any; // Slate/ProseMirror document
  format: 'markdown' | 'richtext';
  tags: string[];
  parentId: string | null;
  createdAt: Date;
  updatedAt: Date;
  createdBy: string;
  lastEditedBy: string;
  isArchived: boolean;
  isStarred: boolean;
  metadata: {
    icon?: string;
    cover?: string;
    color?: string;
    embeddings?: number[]; // Vector embeddings
  };
}
```

### Folder

```typescript
interface Folder {
  id: string;
  name: string;
  parentId: string | null;
  createdAt: Date;
  updatedAt: Date;
  createdBy: string;
  isArchived: boolean;
  metadata: {
    icon?: string;
    color?: string;
  };
}
```

### Tag

```typescript
interface Tag {
  id: string;
  name: string;
  color: string;
  createdAt: Date;
  createdBy: string;
}
```

### Connection

```typescript
interface Connection {
  id: string;
  sourceId: string;
  targetId: string;
  type: 'backlink' | 'explicit' | 'suggested';
  context: string;
  createdAt: Date;
  createdBy: string;
}
```

## API Endpoints

### Notes

- `GET /api/notes` - List notes with filtering options
- `GET /api/notes/:id` - Get a specific note
- `POST /api/notes` - Create a new note
- `PUT /api/notes/:id` - Update a note
- `DELETE /api/notes/:id` - Delete a note
- `GET /api/notes/:id/connections` - Get connections for a note

### Folders

- `GET /api/folders` - List folders
- `GET /api/folders/:id` - Get a specific folder
- `POST /api/folders` - Create a new folder
- `PUT /api/folders/:id` - Update a folder
- `DELETE /api/folders/:id` - Delete a folder
- `GET /api/folders/:id/notes` - Get notes in a folder

### Tags

- `GET /api/tags` - List all tags
- `POST /api/tags` - Create a new tag
- `PUT /api/tags/:id` - Update a tag
- `DELETE /api/tags/:id` - Delete a tag
- `GET /api/tags/:id/notes` - Get notes with a specific tag

### AI

- `POST /api/ai/complete` - Complete text based on prompt
- `POST /api/ai/summarize` - Summarize a note or selection
- `POST /api/ai/suggest-connections` - Suggest connections between notes
- `POST /api/ai/search` - Semantic search across notes

## Next Steps

1. Set up the basic project structure
2. Implement the core editor component
3. Create local storage mechanisms
4. Build the basic UI for note organization
5. Implement the backend API for CRUD operations
