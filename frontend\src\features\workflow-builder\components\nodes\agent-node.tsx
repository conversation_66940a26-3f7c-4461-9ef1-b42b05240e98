'use client';

import React, { memo, useCallback } from 'react';
import { <PERSON><PERSON>, Position, NodeProps } from 'reactflow';
import { cn } from '@/shared/lib/utils';
import { AgentStatusIndicator } from '@/shared/ui/agent-status';
import { Badge } from '@/shared/ui/badge';
import { But<PERSON> } from '@/shared/ui/button';
import { AgentModel } from '@/entities/agent/model';
import { AgentStatus, AgentType } from '@/shared/types';
import { NodeData } from '../../types';
import {
  Bo<PERSON>,
  Brain,
  MessageSquare,
  Sparkles,
  Tool,
  Workflow,
  Settings,
  Play,
  Pause,
  Square,
  AlertCircle,
  CheckCircle,
  Clock,
} from 'lucide-react';

interface AgentNodeData extends NodeData {
  agent: AgentModel;
  showControls?: boolean;
  compact?: boolean;
}

interface AgentNodeProps extends NodeProps {
  data: AgentNodeData;
}

// Agent type icon mapping
const getAgentTypeIcon = (type: AgentType) => {
  switch (type) {
    case AgentType.CHAT:
      return MessageSquare;
    case AgentType.WORKFLOW:
      return Workflow;
    case AgentType.TOOL:
      return Tool;
    case AgentType.REASONING:
      return Brain;
    case AgentType.MULTIMODAL:
      return Sparkles;
    default:
      return Bot;
  }
};

// Status icon mapping
const getStatusIcon = (status: AgentStatus) => {
  switch (status) {
    case AgentStatus.RUNNING:
      return Clock;
    case AgentStatus.COMPLETED:
      return CheckCircle;
    case AgentStatus.FAILED:
      return AlertCircle;
    default:
      return null;
  }
};

export const AgentNode = memo<AgentNodeProps>(({ 
  data, 
  selected, 
  dragging,
  id 
}) => {
  const { agent, showControls = true, compact = false } = data;
  const AgentIcon = getAgentTypeIcon(agent.agent_type);
  const StatusIcon = getStatusIcon(agent.status);

  const handleExecute = useCallback((e: React.MouseEvent) => {
    e.stopPropagation();
    // TODO: Implement agent execution logic
    console.log('Execute agent:', agent.id);
  }, [agent.id]);

  const handlePause = useCallback((e: React.MouseEvent) => {
    e.stopPropagation();
    // TODO: Implement agent pause logic
    console.log('Pause agent:', agent.id);
  }, [agent.id]);

  const handleStop = useCallback((e: React.MouseEvent) => {
    e.stopPropagation();
    // TODO: Implement agent stop logic
    console.log('Stop agent:', agent.id);
  }, [agent.id]);

  const handleConfigure = useCallback((e: React.MouseEvent) => {
    e.stopPropagation();
    // TODO: Implement agent configuration logic
    console.log('Configure agent:', agent.id);
  }, [agent.id]);

  const isActive = agent.status === AgentStatus.RUNNING || agent.status === AgentStatus.PAUSED;
  const isCompleted = agent.status === AgentStatus.COMPLETED;
  const isFailed = agent.status === AgentStatus.FAILED;

  return (
    <div
      className={cn(
        'relative bg-background border-2 rounded-lg shadow-sm transition-all duration-200',
        'min-w-[280px] max-w-[320px]',
        selected && 'border-primary ring-2 ring-primary/20',
        dragging && 'shadow-lg scale-105',
        isActive && 'border-green-400 shadow-green-100',
        isFailed && 'border-red-400 shadow-red-100',
        isCompleted && 'border-blue-400 shadow-blue-100',
        compact ? 'min-w-[200px] max-w-[240px]' : 'min-w-[280px] max-w-[320px]'
      )}
    >
      {/* Input Handle */}
      <Handle
        type="target"
        position={Position.Left}
        className="w-3 h-3 border-2 border-background bg-muted-foreground hover:bg-primary transition-colors"
        style={{ left: -6 }}
      />

      {/* Output Handle */}
      <Handle
        type="source"
        position={Position.Right}
        className="w-3 h-3 border-2 border-background bg-muted-foreground hover:bg-primary transition-colors"
        style={{ right: -6 }}
      />

      {/* Status indicator overlay */}
      <div className={cn(
        'absolute top-0 left-0 w-full h-1 rounded-t-lg transition-all duration-300',
        isActive && 'bg-gradient-to-r from-green-400 to-green-600',
        isCompleted && 'bg-gradient-to-r from-blue-400 to-blue-600',
        isFailed && 'bg-gradient-to-r from-red-400 to-red-600',
        agent.status === AgentStatus.IDLE && 'bg-gradient-to-r from-slate-300 to-slate-400'
      )} />

      {/* Header */}
      <div className="p-3 pb-2">
        <div className="flex items-start justify-between mb-2">
          <div className="flex items-center space-x-2 min-w-0 flex-1">
            <div className="relative">
              <AgentIcon className={cn(
                'h-5 w-5 transition-colors duration-200',
                isActive ? 'text-green-600' : 'text-primary'
              )} />
              {isActive && (
                <div className="absolute -top-1 -right-1 h-2 w-2 bg-green-500 rounded-full animate-pulse" />
              )}
            </div>
            <div className="min-w-0 flex-1">
              <h3 className="font-semibold text-sm truncate">
                {agent.name}
              </h3>
              <div className="flex items-center gap-1 mt-1">
                <AgentStatusIndicator 
                  status={agent.status}
                  size="sm"
                  variant="minimal"
                />
                <Badge variant="outline" className="text-xs">
                  {agent.agent_type}
                </Badge>
              </div>
            </div>
          </div>
          
          {StatusIcon && (
            <StatusIcon className={cn(
              'h-4 w-4',
              isActive && 'text-green-600 animate-pulse',
              isCompleted && 'text-blue-600',
              isFailed && 'text-red-600'
            )} />
          )}
        </div>

        {!compact && (
          <p className="text-xs text-muted-foreground line-clamp-2 mb-2">
            {agent.description || 'No description available'}
          </p>
        )}

        {/* Capabilities */}
        {agent.capabilities.length > 0 && (
          <div className="flex flex-wrap gap-1 mb-2">
            {agent.capabilities.slice(0, compact ? 2 : 3).map((capability) => (
              <Badge key={capability.name} variant="secondary" className="text-xs">
                {capability.name}
              </Badge>
            ))}
            {agent.capabilities.length > (compact ? 2 : 3) && (
              <Badge variant="secondary" className="text-xs">
                +{agent.capabilities.length - (compact ? 2 : 3)}
              </Badge>
            )}
          </div>
        )}

        {/* Metrics */}
        {!compact && agent.metrics.total_executions > 0 && (
          <div className="grid grid-cols-2 gap-2 text-xs mb-2">
            <div className="text-center">
              <div className="font-medium">
                {((agent.metrics.successful_executions / agent.metrics.total_executions) * 100).toFixed(0)}%
              </div>
              <div className="text-muted-foreground">Success</div>
            </div>
            <div className="text-center">
              <div className="font-medium">
                {agent.metrics.average_execution_time < 60 
                  ? `${agent.metrics.average_execution_time.toFixed(1)}s`
                  : `${(agent.metrics.average_execution_time / 60).toFixed(1)}m`
                }
              </div>
              <div className="text-muted-foreground">Avg Time</div>
            </div>
          </div>
        )}
      </div>

      {/* Controls */}
      {showControls && (
        <div className="px-3 pb-3">
          <div className="flex gap-1">
            {agent.status === AgentStatus.IDLE && (
              <Button size="sm" onClick={handleExecute} className="flex-1">
                <Play className="h-3 w-3 mr-1" />
                Run
              </Button>
            )}
            
            {agent.status === AgentStatus.RUNNING && (
              <>
                <Button size="sm" variant="outline" onClick={handlePause} className="flex-1">
                  <Pause className="h-3 w-3 mr-1" />
                  Pause
                </Button>
                <Button size="sm" variant="destructive" onClick={handleStop}>
                  <Square className="h-3 w-3" />
                </Button>
              </>
            )}
            
            {agent.status === AgentStatus.PAUSED && (
              <>
                <Button size="sm" onClick={handleExecute} className="flex-1">
                  <Play className="h-3 w-3 mr-1" />
                  Resume
                </Button>
                <Button size="sm" variant="destructive" onClick={handleStop}>
                  <Square className="h-3 w-3" />
                </Button>
              </>
            )}
            
            {(agent.status === AgentStatus.COMPLETED || agent.status === AgentStatus.FAILED) && (
              <Button size="sm" onClick={handleExecute} className="flex-1">
                <Play className="h-3 w-3 mr-1" />
                Restart
              </Button>
            )}
            
            <Button size="sm" variant="ghost" onClick={handleConfigure}>
              <Settings className="h-3 w-3" />
            </Button>
          </div>
        </div>
      )}

      {/* Execution indicator */}
      {isActive && (
        <div className="absolute inset-0 bg-gradient-to-br from-green-500/5 to-green-600/10 rounded-lg pointer-events-none" />
      )}
      
      {/* Error indicator */}
      {isFailed && data.errorMessage && (
        <div className="absolute -top-2 -right-2 bg-red-500 text-white text-xs px-2 py-1 rounded-full">
          Error
        </div>
      )}
      
      {/* Execution time indicator */}
      {data.executionTime && (
        <div className="absolute -top-2 -left-2 bg-blue-500 text-white text-xs px-2 py-1 rounded-full">
          {data.executionTime < 60 
            ? `${data.executionTime.toFixed(1)}s`
            : `${(data.executionTime / 60).toFixed(1)}m`
          }
        </div>
      )}
    </div>
  );
});

AgentNode.displayName = 'AgentNode';
