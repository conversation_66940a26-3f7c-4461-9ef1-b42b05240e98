import { afterEach, beforeEach, describe, expect, it, vi } from 'vitest';
import {
    A2AWebSocketClient,
    AGUIWebSocketClient,
    createWebSocketClient,
    MCPWebSocketClient,
    WebSocketClient,
    WebSocketConfig,
    WebSocketMessage,
} from '../websocket';

// Mock the API config
vi.mock('../api', () => ({
  API_CONFIG: {
    BASE_URL: 'http://localhost:3001',
  },
}));

// Mock WebSocket
class MockWebSocket {
  static CONNECTING = 0;
  static OPEN = 1;
  static CLOSING = 2;
  static CLOSED = 3;

  readyState = MockWebSocket.CONNECTING;
  url: string;
  protocols?: string | string[];
  onopen: ((event: Event) => void) | null = null;
  onclose: ((event: CloseEvent) => void) | null = null;
  onerror: ((event: Event) => void) | null = null;
  onmessage: ((event: MessageEvent) => void) | null = null;

  constructor(url: string, protocols?: string | string[]) {
    this.url = url;
    this.protocols = protocols;

    // Simulate async connection
    setTimeout(() => {
      this.readyState = MockWebSocket.OPEN;
      if (this.onopen) {
        this.onopen(new Event('open'));
      }
    }, 10);
  }

  send(data: string): void {
    if (this.readyState !== MockWebSocket.OPEN) {
      throw new Error('WebSocket is not open');
    }
    // Mock sending data
  }

  close(): void {
    this.readyState = MockWebSocket.CLOSED;
    if (this.onclose) {
      this.onclose(new CloseEvent('close'));
    }
  }

  // Helper method to simulate receiving messages
  simulateMessage(data: string): void {
    if (this.onmessage) {
      this.onmessage(new MessageEvent('message', { data }));
    }
  }

  // Helper method to simulate errors
  simulateError(): void {
    if (this.onerror) {
      this.onerror(new Event('error'));
    }
  }
}

// Replace global WebSocket with mock
global.WebSocket = MockWebSocket as any;

describe('WebSocket Client', () => {
  let client: WebSocketClient;
  let config: WebSocketConfig;

  beforeEach(() => {
    vi.clearAllTimers();
    vi.useFakeTimers();

    config = {
      url: 'ws://localhost:3001/ws/test',
      protocols: ['test-protocol'],
      reconnect: true,
      maxReconnectAttempts: 3,
      reconnectInterval: 1000,
      heartbeatInterval: 5000,
    };

    client = new WebSocketClient(config);
  });

  afterEach(() => {
    client.disconnect();
    vi.useRealTimers();
    vi.clearAllMocks();
  });

  describe('Constructor', () => {
    it('initializes with default config values', () => {
      const minimalConfig = { url: 'ws://test.com' };
      const testClient = new WebSocketClient(minimalConfig);

      expect(testClient.getConnectionStatus()).toBe(false);
    });

    it('merges provided config with defaults', () => {
      const customConfig = {
        url: 'ws://custom.com',
        maxReconnectAttempts: 10,
      };

      const testClient = new WebSocketClient(customConfig);
      expect(testClient.getConnectionStatus()).toBe(false);
    });
  });

  describe('Connection Management', () => {
    it('connects successfully', async () => {
      const connectPromise = client.connect();

      // Fast-forward timers to trigger connection
      vi.advanceTimersByTime(20);

      await expect(connectPromise).resolves.toBeUndefined();
      expect(client.getConnectionStatus()).toBe(true);
    });

    it('handles connection errors', async () => {
      const errorClient = new WebSocketClient({ url: 'ws://invalid' });

      // Mock WebSocket constructor to throw
      const originalWebSocket = global.WebSocket;
      global.WebSocket = vi.fn().mockImplementation(() => {
        throw new Error('Connection failed');
      });

      await expect(errorClient.connect()).rejects.toThrow('Connection failed');

      global.WebSocket = originalWebSocket;
    });

    it('disconnects properly', () => {
      // Mock a connected state
      const mockWs = { close: vi.fn() };
      (client as any).ws = mockWs;
      (client as any).isConnected = true;

      client.disconnect();

      expect(mockWs.close).toHaveBeenCalled();
      expect(client.getConnectionStatus()).toBe(false);
    });

    it('handles reconnection configuration', () => {
      const limitedClient = new WebSocketClient({
        url: 'ws://test.com',
        maxReconnectAttempts: 2,
        reconnectInterval: 100,
      });

      // Test that client was created with correct config
      expect(limitedClient).toBeInstanceOf(WebSocketClient);
      expect(limitedClient.getConnectionStatus()).toBe(false);

      limitedClient.disconnect();
    });
  });

  describe('Message Handling', () => {
    beforeEach(() => {
      // Don't await connection in beforeEach to avoid timeouts
      vi.clearAllTimers();
    });

    it('sends messages correctly', () => {
      // Create a simple mock WebSocket
      const mockWs = {
        send: vi.fn(),
        close: vi.fn(),
        readyState: 1, // OPEN
      };
      (client as any).ws = mockWs;
      (client as any).isConnected = true;

      client.send('test_type', { data: 'test' });

      expect(mockWs.send).toHaveBeenCalledWith(
        expect.stringContaining('"type":"test_type"')
      );
      expect(mockWs.send).toHaveBeenCalledWith(
        expect.stringContaining('"payload":{"data":"test"}')
      );
    });

    it('throws error when sending while disconnected', () => {
      (client as any).isConnected = false;
      (client as any).ws = null;

      expect(() => {
        client.send('test', {});
      }).toThrow('WebSocket is not connected');
    });

    it('handles incoming messages', () => {
      const handler = vi.fn();
      client.subscribe('test_message', handler);

      const message: WebSocketMessage = {
        type: 'test_message',
        payload: { data: 'received' },
        timestamp: new Date().toISOString(),
      };

      // Directly call the message handler
      (client as any).handleMessage(message);

      expect(handler).toHaveBeenCalledWith({ data: 'received' });
    });

    it('handles malformed messages gracefully', () => {
      const consoleSpy = vi.spyOn(console, 'error').mockImplementation(() => {});

      // Create a mock WebSocket with close method
      const mockWs = {
        send: vi.fn(),
        close: vi.fn(),
        readyState: 1,
        onmessage: null,
      };
      (client as any).ws = mockWs;

      // Directly test the message parsing logic
      try {
        JSON.parse('invalid json');
      } catch (error) {
        console.error('Failed to parse WebSocket message:', error);
      }

      expect(consoleSpy).toHaveBeenCalledWith(
        'Failed to parse WebSocket message:',
        expect.any(Error)
      );

      consoleSpy.mockRestore();
    });
  });

  describe('Subscription Management', () => {
    it('subscribes and unsubscribes message handlers', () => {
      const handler1 = vi.fn();
      const handler2 = vi.fn();

      const unsubscribe1 = client.subscribe('test', handler1);
      const unsubscribe2 = client.subscribe('test', handler2);

      const message: WebSocketMessage = {
        type: 'test',
        payload: { data: 'test' },
        timestamp: new Date().toISOString(),
      };

      // Directly call the message handler
      (client as any).handleMessage(message);

      expect(handler1).toHaveBeenCalledTimes(1);
      expect(handler2).toHaveBeenCalledTimes(1);

      unsubscribe1();
      (client as any).handleMessage(message);

      expect(handler1).toHaveBeenCalledTimes(1);
      expect(handler2).toHaveBeenCalledTimes(2);

      unsubscribe2();
      (client as any).handleMessage(message);

      expect(handler1).toHaveBeenCalledTimes(1);
      expect(handler2).toHaveBeenCalledTimes(2);
    });

    it('manages connection change handlers', () => {
      const handler = vi.fn();
      const unsubscribe = client.onConnectionChange(handler);

      // Directly call the notification method
      (client as any).notifyConnectionHandlers(false);
      expect(handler).toHaveBeenCalledWith(false);

      unsubscribe();
      handler.mockClear();

      (client as any).notifyConnectionHandlers(true);
      expect(handler).not.toHaveBeenCalled();
    });

    it('handles errors in message handlers gracefully', () => {
      const consoleSpy = vi.spyOn(console, 'error').mockImplementation(() => {});
      const faultyHandler = vi.fn().mockImplementation(() => {
        throw new Error('Handler error');
      });

      client.subscribe('test', faultyHandler);

      const message: WebSocketMessage = {
        type: 'test',
        payload: {},
        timestamp: new Date().toISOString(),
      };

      // Directly call the message handler
      (client as any).handleMessage(message);

      expect(consoleSpy).toHaveBeenCalledWith(
        'Error in message handler for type test:',
        expect.any(Error)
      );

      consoleSpy.mockRestore();
    });
  });

  describe('Heartbeat', () => {
    it('sends heartbeat messages', () => {
      const sendSpy = vi.spyOn(client, 'send').mockImplementation(() => {});
      (client as any).isConnected = true;

      // Directly call the heartbeat method
      (client as any).startHeartbeat();

      // Advance time to trigger heartbeat
      vi.advanceTimersByTime(30000);

      expect(sendSpy).toHaveBeenCalledWith('ping', {
        timestamp: expect.any(Number),
      });

      sendSpy.mockRestore();
    });

    it('stops heartbeat on disconnect', () => {
      (client as any).startHeartbeat();

      // Should have a timer
      expect((client as any).heartbeatTimer).toBeTruthy();

      (client as any).stopHeartbeat();

      // Timer should be cleared
      expect((client as any).heartbeatTimer).toBeNull();
    });
  });
});

describe('Protocol-Specific Clients', () => {
  describe('MCPWebSocketClient', () => {
    it('creates client with correct URL and protocol', () => {
      const client = new MCPWebSocketClient();
      expect(client).toBeInstanceOf(WebSocketClient);
    });

    it('sends MCP requests', () => {
      const client = new MCPWebSocketClient();
      const sendSpy = vi.spyOn(client, 'send').mockImplementation(() => {});

      client.sendMCPRequest('test_method', { param: 'value' });

      expect(sendSpy).toHaveBeenCalledWith('mcp_request', {
        method: 'test_method',
        params: { param: 'value' },
      });

      sendSpy.mockRestore();
    });
  });

  describe('A2AWebSocketClient', () => {
    it('creates client with correct configuration', () => {
      const client = new A2AWebSocketClient();
      expect(client).toBeInstanceOf(WebSocketClient);
    });

    it('sends agent messages', () => {
      const client = new A2AWebSocketClient();
      const sendSpy = vi.spyOn(client, 'send').mockImplementation(() => {});

      client.sendAgentMessage('agent123', { action: 'test' });

      expect(sendSpy).toHaveBeenCalledWith('agent_message', {
        targetAgentId: 'agent123',
        message: { action: 'test' },
      });

      sendSpy.mockRestore();
    });
  });

  describe('AGUIWebSocketClient', () => {
    it('creates client with correct configuration', () => {
      const client = new AGUIWebSocketClient();
      expect(client).toBeInstanceOf(WebSocketClient);
    });

    it('sends UI updates', () => {
      const client = new AGUIWebSocketClient();
      const sendSpy = vi.spyOn(client, 'send').mockImplementation(() => {});

      client.sendUIUpdate('component123', { visible: true });

      expect(sendSpy).toHaveBeenCalledWith('ui_update', {
        componentId: 'component123',
        update: { visible: true },
      });

      sendSpy.mockRestore();
    });
  });
});

describe('WebSocket Factory', () => {
  it('creates MCP client', () => {
    const client = createWebSocketClient('mcp');
    expect(client).toBeInstanceOf(MCPWebSocketClient);
  });

  it('creates A2A client', () => {
    const client = createWebSocketClient('a2a');
    expect(client).toBeInstanceOf(A2AWebSocketClient);
  });

  it('creates AG-UI client', () => {
    const client = createWebSocketClient('ag-ui');
    expect(client).toBeInstanceOf(AGUIWebSocketClient);
  });

  it('throws error for unknown client type', () => {
    expect(() => {
      createWebSocketClient('unknown' as any);
    }).toThrow('Unknown WebSocket client type: unknown');
  });
});
