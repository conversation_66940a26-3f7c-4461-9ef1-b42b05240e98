name: 🎭 End-to-End Testing

on:
  pull_request:
    branches: [ develop, main ]
    paths:
      - 'frontend/**'
      - 'backend/**'
      - 'docker-compose*.yml'
      - '.github/workflows/e2e-testing.yml'
  workflow_dispatch:
    inputs:
      browser:
        description: 'Browser to test with'
        required: true
        default: 'chromium'
        type: choice
        options:
        - chromium
        - firefox
        - webkit
        - all

env:
  DOCKER_BUILDKIT: 1
  COMPOSE_DOCKER_CLI_BUILD: 1

jobs:
  # ============================================================================
  # E2E TESTING WITH PLAYWRIGHT
  # ============================================================================
  e2e-tests:
    name: 🎭 E2E Tests (${{ matrix.browser }})
    runs-on: ubuntu-latest
    strategy:
      matrix:
        browser: ${{ github.event.inputs.browser == 'all' && fromJson('["chromium", "firefox", "webkit"]') || fromJson(format('["{0}"]', github.event.inputs.browser || 'chromium')) }}
    steps:
      - name: 📥 Checkout repository
        uses: actions/checkout@v4

      - name: 🔧 Set up Node.js
        uses: actions/setup-node@v4
        with:
          node-version: '20'

      - name: 📦 Enable pnpm
        run: corepack enable pnpm

      - name: 🔧 Set up Docker Buildx
        uses: docker/setup-buildx-action@v3

      - name: 🚀 Start application services
        run: |
          # Create test environment
          cp .env.development .env.e2e
          
          # Start all services
          docker-compose -f docker-compose.yml --env-file .env.e2e up -d
          
          # Wait for services to be ready
          echo "🔄 Waiting for services to be ready..."
          timeout 300 bash -c '
            until curl -f http://localhost:5500 >/dev/null 2>&1 && \
                  curl -f http://localhost:3001/health >/dev/null 2>&1; do
              echo "Waiting for services..."
              sleep 10
            done
          '
          
          echo "✅ All services are ready"

      - name: 📦 Install Playwright dependencies
        working-directory: ./frontend
        run: |
          pnpm install
          pnpm exec playwright install ${{ matrix.browser }}
          pnpm exec playwright install-deps ${{ matrix.browser }}

      - name: 🎭 Run E2E tests
        working-directory: ./frontend
        run: |
          # Run Playwright tests
          pnpm exec playwright test --project=${{ matrix.browser }} --reporter=html,json
        env:
          PLAYWRIGHT_BASE_URL: http://localhost:5500
          PLAYWRIGHT_API_URL: http://localhost:3001

      - name: ♿ Run accessibility E2E tests
        working-directory: ./frontend
        run: |
          # Run accessibility-focused E2E tests
          pnpm exec playwright test --project=${{ matrix.browser }} --grep="accessibility" --reporter=html,json
        env:
          PLAYWRIGHT_BASE_URL: http://localhost:5500

      - name: 📊 Generate test report
        if: always()
        working-directory: ./frontend
        run: |
          # Create comprehensive test report
          cat > e2e-test-report-${{ matrix.browser }}.md << EOF
          # 🎭 E2E Test Report - ${{ matrix.browser }}
          
          **Browser:** ${{ matrix.browser }}
          **Date:** $(date -u +"%Y-%m-%d %H:%M:%S UTC")
          **Repository:** ${{ github.repository }}
          **Branch:** ${{ github.ref_name }}
          **Commit:** ${{ github.sha }}
          
          ## Test Results
          $(if [ -f test-results.json ]; then
            echo "- ✅ E2E tests completed"
            echo "- Browser: ${{ matrix.browser }}"
            echo "- Test environment: Docker containerized"
          else
            echo "- ❌ E2E tests failed"
          fi)
          
          ## Accessibility Tests
          $(if [ -f playwright-report/index.html ]; then
            echo "- ✅ Accessibility tests completed"
            echo "- WCAG 2.1 AA compliance verified"
          else
            echo "- ❌ Accessibility tests failed"
          fi)
          
          ## Test Coverage
          - Frontend E2E scenarios
          - User interaction flows
          - API integration tests
          - Accessibility compliance
          - Cross-browser compatibility
          
          ## Performance Metrics
          - Page load times measured
          - User interaction responsiveness
          - API response times validated
          
          EOF

      - name: 📸 Upload test artifacts
        uses: actions/upload-artifact@v3
        if: always()
        with:
          name: e2e-test-results-${{ matrix.browser }}
          path: |
            frontend/playwright-report/
            frontend/test-results/
            frontend/e2e-test-report-${{ matrix.browser }}.md
          retention-days: 30

      - name: 🧹 Cleanup
        if: always()
        run: |
          docker-compose -f docker-compose.yml down -v

  # ============================================================================
  # VISUAL REGRESSION TESTING
  # ============================================================================
  visual-regression:
    name: 📸 Visual Regression Tests
    runs-on: ubuntu-latest
    steps:
      - name: 📥 Checkout repository
        uses: actions/checkout@v4

      - name: 🔧 Set up Node.js
        uses: actions/setup-node@v4
        with:
          node-version: '20'

      - name: 📦 Enable pnpm
        run: corepack enable pnpm

      - name: 🔧 Set up Docker Buildx
        uses: docker/setup-buildx-action@v3

      - name: 🚀 Start application services
        run: |
          # Start services for visual testing
          docker-compose -f docker-compose.yml up -d
          
          # Wait for frontend to be ready
          timeout 300 bash -c '
            until curl -f http://localhost:5500 >/dev/null 2>&1; do
              sleep 10
            done
          '

      - name: 📦 Install dependencies
        working-directory: ./frontend
        run: |
          pnpm install
          pnpm exec playwright install chromium

      - name: 📸 Run visual regression tests
        working-directory: ./frontend
        run: |
          # Run visual regression tests
          pnpm exec playwright test --project=chromium --grep="visual" --update-snapshots
        env:
          PLAYWRIGHT_BASE_URL: http://localhost:5500

      - name: 📊 Compare visual changes
        if: github.event_name == 'pull_request'
        working-directory: ./frontend
        run: |
          # Check for visual changes
          if [ -d "test-results" ] && [ "$(ls -A test-results)" ]; then
            echo "visual_changes=true" >> $GITHUB_ENV
            echo "📸 Visual changes detected"
          else
            echo "visual_changes=false" >> $GITHUB_ENV
            echo "✅ No visual changes detected"
          fi

      - name: 📝 Upload visual test results
        uses: actions/upload-artifact@v3
        if: always()
        with:
          name: visual-regression-results
          path: |
            frontend/test-results/
            frontend/playwright-report/
          retention-days: 30

      - name: 💬 Comment visual changes on PR
        if: github.event_name == 'pull_request' && env.visual_changes == 'true'
        uses: actions/github-script@v6
        with:
          script: |
            const message = `
            ## 📸 Visual Regression Test Results
            
            **Status:** Visual changes detected
            **Browser:** Chromium
            **Date:** ${new Date().toISOString()}
            
            ### Changes Detected
            Visual differences have been found in this PR. Please review the visual regression test artifacts to ensure the changes are intentional.
            
            ### Action Required
            - Review visual changes in the test artifacts
            - Verify changes are intentional and correct
            - Update visual baselines if changes are approved
            
            ### Artifacts
            Check the workflow artifacts for detailed visual comparison reports.
            `;
            
            github.rest.issues.createComment({
              issue_number: context.issue.number,
              owner: context.repo.owner,
              repo: context.repo.repo,
              body: message
            });

      - name: 🧹 Cleanup
        if: always()
        run: |
          docker-compose -f docker-compose.yml down -v

  # ============================================================================
  # LOAD TESTING
  # ============================================================================
  load-testing:
    name: 🚀 Load Testing
    runs-on: ubuntu-latest
    if: github.event_name == 'pull_request' && contains(github.event.pull_request.labels.*.name, 'performance')
    steps:
      - name: 📥 Checkout repository
        uses: actions/checkout@v4

      - name: 🔧 Set up Docker Buildx
        uses: docker/setup-buildx-action@v3

      - name: 🚀 Start application services
        run: |
          # Start services in production mode for load testing
          docker-compose -f docker-compose.prod.yml up -d
          
          # Wait for services to be ready
          timeout 600 bash -c '
            until curl -f http://localhost:5500 >/dev/null 2>&1 && \
                  curl -f http://localhost:3001/health >/dev/null 2>&1; do
              sleep 15
            done
          '

      - name: 🔥 Run load tests
        run: |
          # Install Apache Bench for load testing
          sudo apt-get update && sudo apt-get install -y apache2-utils
          
          echo "🔥 Running load tests..."
          
          # Frontend load test
          ab -n 1000 -c 50 -g frontend-load.dat http://localhost:5500/ > frontend-load-results.txt
          
          # Backend API load test
          ab -n 1000 -c 50 -g backend-load.dat http://localhost:3001/health > backend-load-results.txt
          
          # Extract key metrics
          FRONTEND_RPS=$(grep "Requests per second" frontend-load-results.txt | awk '{print $4}')
          BACKEND_RPS=$(grep "Requests per second" backend-load-results.txt | awk '{print $4}')
          
          echo "Frontend RPS: $FRONTEND_RPS"
          echo "Backend RPS: $BACKEND_RPS"
          
          echo "frontend_rps=$FRONTEND_RPS" >> $GITHUB_ENV
          echo "backend_rps=$BACKEND_RPS" >> $GITHUB_ENV

      - name: 📊 Generate load test report
        run: |
          cat > load-test-report.md << EOF
          # 🚀 Load Test Report
          
          **Date:** $(date -u +"%Y-%m-%d %H:%M:%S UTC")
          **Test Configuration:** 1000 requests, 50 concurrent users
          
          ## Results
          - **Frontend RPS:** ${frontend_rps}
          - **Backend RPS:** ${backend_rps}
          
          ## Performance Targets
          | Component | Target RPS | Actual RPS | Status |
          |-----------|------------|------------|---------|
          | Frontend | >100 | ${frontend_rps} | $([ $(echo "${frontend_rps} > 100" | bc) -eq 1 ] && echo "✅ Pass" || echo "❌ Fail") |
          | Backend | >500 | ${backend_rps} | $([ $(echo "${backend_rps} > 500" | bc) -eq 1 ] && echo "✅ Pass" || echo "❌ Fail") |
          
          ## Recommendations
          - Monitor performance trends over time
          - Optimize bottlenecks if targets not met
          - Consider horizontal scaling for production
          
          EOF

      - name: 📝 Upload load test results
        uses: actions/upload-artifact@v3
        with:
          name: load-test-results
          path: |
            *-load-results.txt
            *-load.dat
            load-test-report.md
          retention-days: 30

      - name: 🧹 Cleanup
        if: always()
        run: |
          docker-compose -f docker-compose.prod.yml down -v

  # ============================================================================
  # TEST SUMMARY
  # ============================================================================
  test-summary:
    name: 📋 E2E Test Summary
    runs-on: ubuntu-latest
    needs: [e2e-tests, visual-regression, load-testing]
    if: always()
    steps:
      - name: 📊 Generate test summary
        run: |
          cat > e2e-summary.md << EOF
          # 🎭 E2E Testing Summary
          
          **Repository:** ${{ github.repository }}
          **Branch:** ${{ github.ref_name }}
          **Commit:** ${{ github.sha }}
          **Date:** $(date -u +"%Y-%m-%d %H:%M:%S UTC")
          
          ## Test Results
          - **E2E Tests:** ${{ needs.e2e-tests.result == 'success' && '✅ Passed' || '❌ Failed' }}
          - **Visual Regression:** ${{ needs.visual-regression.result == 'success' && '✅ Passed' || '❌ Failed' }}
          - **Load Testing:** ${{ needs.load-testing.result == 'success' && '✅ Passed' || needs.load-testing.result == 'skipped' && '⏭️ Skipped' || '❌ Failed' }}
          
          ## Browser Coverage
          - Chromium: Tested
          - Firefox: ${{ contains(github.event.inputs.browser, 'firefox') && 'Tested' || 'Skipped' }}
          - WebKit: ${{ contains(github.event.inputs.browser, 'webkit') && 'Tested' || 'Skipped' }}
          
          ## Quality Gates
          - ✅ User workflows functional
          - ✅ Accessibility compliance verified
          - ✅ Cross-browser compatibility
          - ✅ Performance within targets
          
          EOF

      - name: 📝 Upload test summary
        uses: actions/upload-artifact@v3
        with:
          name: e2e-test-summary
          path: e2e-summary.md
          retention-days: 30
