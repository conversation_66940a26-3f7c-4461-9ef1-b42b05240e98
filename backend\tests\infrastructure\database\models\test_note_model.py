"""
Unit tests for NoteModel.

This module contains comprehensive tests for the NoteModel
database model including domain entity conversion.
"""

import uuid
from datetime import datetime

import pytest

from src.domain.entities.note import Note, NoteContent, NoteFormat
from src.infrastructure.database.models.note import NoteModel


class TestNoteModel:
    """Test cases for NoteModel."""

    def test_note_model_creation(self):
        """Test creating a NoteModel instance."""
        note_id = uuid.uuid4()
        user_id = uuid.uuid4()
        folder_id = uuid.uuid4()
        now = datetime.now()

        note_model = NoteModel(
            id=note_id,
            title="Test Note",
            content="This is test content",
            content_format=NoteFormat.MARKDOWN,
            content_version=1,
            folder_id=folder_id,
            created_by=user_id,
            last_edited_by=user_id,
            is_archived=False,
            is_starred=True,
            tags=["test", "example"],
            created_at=now,
            updated_at=now,
        )

        assert note_model.id == note_id
        assert note_model.title == "Test Note"
        assert note_model.content == "This is test content"
        assert note_model.content_format == NoteFormat.MARKDOWN
        assert note_model.content_version == 1
        assert note_model.folder_id == folder_id
        assert note_model.created_by == user_id
        assert note_model.last_edited_by == user_id
        assert note_model.is_archived is False
        assert note_model.is_starred is True
        assert note_model.tags == ["test", "example"]
        assert note_model.created_at == now
        assert note_model.updated_at == now

    def test_note_model_defaults(self):
        """Test NoteModel default values."""
        user_id = uuid.uuid4()

        note_model = NoteModel(
            title="Test Note",
            content="This is test content",
            created_by=user_id,
            last_edited_by=user_id,
        )

        assert note_model.content_format == NoteFormat.MARKDOWN
        assert note_model.content_version == 1
        assert note_model.is_archived is False
        assert note_model.is_starred is False
        assert note_model.tags == []

    def test_to_domain_entity(self):
        """Test converting NoteModel to domain entity."""
        note_id = uuid.uuid4()
        user_id = uuid.uuid4()
        folder_id = uuid.uuid4()
        now = datetime.now()

        note_model = NoteModel(
            id=note_id,
            title="Test Note",
            content="This is test content",
            content_format=NoteFormat.MARKDOWN,
            content_version=2,
            folder_id=folder_id,
            created_by=user_id,
            last_edited_by=user_id,
            is_archived=True,
            is_starred=False,
            tags=["test", "example"],
            created_at=now,
            updated_at=now,
        )

        domain_entity = note_model.to_domain_entity()

        assert isinstance(domain_entity, Note)
        assert domain_entity.id == note_id
        assert domain_entity.title == "Test Note"
        assert domain_entity.content.content == "This is test content"
        assert domain_entity.content.format == NoteFormat.MARKDOWN
        assert domain_entity.content.version == 2
        assert domain_entity.folder_id == folder_id
        assert domain_entity.created_by == user_id
        assert domain_entity.last_edited_by == user_id
        assert domain_entity.is_archived is True
        assert domain_entity.is_starred is False
        assert domain_entity.tags == ["test", "example"]
        assert domain_entity.created_at == now
        assert domain_entity.updated_at == now

    def test_from_domain_entity(self):
        """Test creating NoteModel from domain entity."""
        note_id = uuid.uuid4()
        user_id = uuid.uuid4()
        folder_id = uuid.uuid4()
        now = datetime.now()

        content = NoteContent(
            content="This is test content",
            format=NoteFormat.RICHTEXT,
            version=3
        )

        domain_entity = Note(
            id=note_id,
            title="Test Note",
            content=content,
            folder_id=folder_id,
            tags=["test", "example"],
            is_archived=False,
            is_starred=True,
            created_at=now,
            updated_at=now,
            created_by=user_id,
            last_edited_by=user_id,
        )

        note_model = NoteModel.from_domain_entity(domain_entity)

        assert note_model.id == note_id
        assert note_model.title == "Test Note"
        assert note_model.content == "This is test content"
        assert note_model.content_format == NoteFormat.RICHTEXT
        assert note_model.content_version == 3
        assert note_model.folder_id == folder_id
        assert note_model.created_by == user_id
        assert note_model.last_edited_by == user_id
        assert note_model.is_archived is False
        assert note_model.is_starred is True
        assert note_model.tags == ["test", "example"]
        assert note_model.created_at == now
        assert note_model.updated_at == now

    def test_round_trip_conversion(self):
        """Test converting from domain entity to model and back."""
        note_id = uuid.uuid4()
        user_id = uuid.uuid4()
        folder_id = uuid.uuid4()
        now = datetime.now()

        content = NoteContent(
            content="This is test content",
            format=NoteFormat.MARKDOWN,
            version=1
        )

        original_entity = Note(
            id=note_id,
            title="Test Note",
            content=content,
            folder_id=folder_id,
            tags=["test", "example"],
            is_archived=False,
            is_starred=True,
            created_at=now,
            updated_at=now,
            created_by=user_id,
            last_edited_by=user_id,
        )

        # Convert to model and back
        note_model = NoteModel.from_domain_entity(original_entity)
        converted_entity = note_model.to_domain_entity()

        # Verify all fields match
        assert converted_entity.id == original_entity.id
        assert converted_entity.title == original_entity.title
        assert converted_entity.content.content == original_entity.content.content
        assert converted_entity.content.format == original_entity.content.format
        assert converted_entity.content.version == original_entity.content.version
        assert converted_entity.folder_id == original_entity.folder_id
        assert converted_entity.created_by == original_entity.created_by
        assert converted_entity.last_edited_by == original_entity.last_edited_by
        assert converted_entity.is_archived == original_entity.is_archived
        assert converted_entity.is_starred == original_entity.is_starred
        assert converted_entity.tags == original_entity.tags
        assert converted_entity.created_at == original_entity.created_at
        assert converted_entity.updated_at == original_entity.updated_at

    def test_note_model_repr(self):
        """Test NoteModel string representation."""
        note_model = NoteModel(
            title="This is a very long title that should be truncated",
            content="Test content",
            created_by=uuid.uuid4(),
            last_edited_by=uuid.uuid4(),
        )

        repr_str = repr(note_model)
        assert "NoteModel" in repr_str
        assert "This is a very long title that" in repr_str
        assert len(repr_str) < 200  # Should be truncated

    def test_empty_tags_handling(self):
        """Test handling of empty tags."""
        user_id = uuid.uuid4()

        note_model = NoteModel(
            title="Test Note",
            content="Test content",
            created_by=user_id,
            last_edited_by=user_id,
            tags=None,
        )

        domain_entity = note_model.to_domain_entity()
        assert domain_entity.tags == []

    def test_none_folder_id_handling(self):
        """Test handling of None folder_id."""
        user_id = uuid.uuid4()

        note_model = NoteModel(
            title="Test Note",
            content="Test content",
            created_by=user_id,
            last_edited_by=user_id,
            folder_id=None,
        )

        domain_entity = note_model.to_domain_entity()
        assert domain_entity.folder_id is None
