"""
A2A (Agent-to-Agent) WebSocket endpoints.

This module provides WebSocket endpoints for A2A protocol communication
enabling real-time agent-to-agent communication and coordination.
"""

import json
import logging
from typing import Any, Dict, Set

from fastapi import APIRouter, WebSocket, WebSocketDisconnect
from fastapi.websockets import WebSocketState

logger = logging.getLogger(__name__)

router = APIRouter()


class A2AConnectionManager:
    """Manages A2A WebSocket connections."""
    
    def __init__(self):
        self.active_connections: Dict[str, WebSocket] = {}
        self.agent_groups: Dict[str, Set[str]] = {}  # group_id -> set of agent_ids
    
    async def connect(self, websocket: WebSocket, agent_id: str):
        """Accept a new A2A connection."""
        await websocket.accept()
        self.active_connections[agent_id] = websocket
        logger.info(f"A2A agent connected: {agent_id}")
    
    def disconnect(self, agent_id: str):
        """Remove an A2A connection."""
        if agent_id in self.active_connections:
            del self.active_connections[agent_id]
            # Remove from all groups
            for group_id, agents in self.agent_groups.items():
                agents.discard(agent_id)
            logger.info(f"A2A agent disconnected: {agent_id}")
    
    async def send_to_agent(self, message: str, agent_id: str):
        """Send a message to a specific agent."""
        if agent_id in self.active_connections:
            websocket = self.active_connections[agent_id]
            if websocket.client_state == WebSocketState.CONNECTED:
                await websocket.send_text(message)
    
    async def broadcast_to_group(self, message: str, group_id: str):
        """Broadcast a message to all agents in a group."""
        if group_id in self.agent_groups:
            for agent_id in self.agent_groups[group_id]:
                await self.send_to_agent(message, agent_id)
    
    async def broadcast_to_all(self, message: str):
        """Broadcast a message to all connected agents."""
        for agent_id, websocket in self.active_connections.items():
            if websocket.client_state == WebSocketState.CONNECTED:
                try:
                    await websocket.send_text(message)
                except Exception as e:
                    logger.error(f"Error broadcasting to A2A agent {agent_id}: {e}")
    
    def join_group(self, agent_id: str, group_id: str):
        """Add an agent to a group."""
        if group_id not in self.agent_groups:
            self.agent_groups[group_id] = set()
        self.agent_groups[group_id].add(agent_id)
        logger.info(f"Agent {agent_id} joined group {group_id}")
    
    def leave_group(self, agent_id: str, group_id: str):
        """Remove an agent from a group."""
        if group_id in self.agent_groups:
            self.agent_groups[group_id].discard(agent_id)
            if not self.agent_groups[group_id]:
                del self.agent_groups[group_id]
        logger.info(f"Agent {agent_id} left group {group_id}")


# Global A2A connection manager
a2a_manager = A2AConnectionManager()


@router.websocket("/ws/a2a/{agent_id}")
async def a2a_websocket_endpoint(websocket: WebSocket, agent_id: str):
    """
    A2A WebSocket endpoint for agent-to-agent communication.
    
    Args:
        websocket: WebSocket connection
        agent_id: Unique agent identifier
    """
    await a2a_manager.connect(websocket, agent_id)
    
    try:
        while True:
            # Receive message from agent
            data = await websocket.receive_text()
            
            try:
                # Parse A2A message
                message = json.loads(data)
                
                # Process A2A message based on type
                response = await process_a2a_message(message, agent_id)
                
                # Send response back to agent if needed
                if response:
                    await websocket.send_text(json.dumps(response))
                    
            except json.JSONDecodeError:
                error_response = {
                    "type": "error",
                    "error": "Invalid JSON format",
                    "timestamp": "2024-01-01T00:00:00Z"
                }
                await websocket.send_text(json.dumps(error_response))
                
            except Exception as e:
                logger.error(f"Error processing A2A message: {e}")
                error_response = {
                    "type": "error",
                    "error": "Internal server error",
                    "timestamp": "2024-01-01T00:00:00Z"
                }
                await websocket.send_text(json.dumps(error_response))
                
    except WebSocketDisconnect:
        a2a_manager.disconnect(agent_id)
        logger.info(f"A2A agent {agent_id} disconnected")


async def process_a2a_message(message: Dict[str, Any], agent_id: str) -> Dict[str, Any] | None:
    """
    Process incoming A2A protocol message.
    
    Args:
        message: A2A message data
        agent_id: Agent identifier
        
    Returns:
        Dict[str, Any] | None: Response message or None
    """
    message_type = message.get("type", "unknown")
    
    logger.info(f"Processing A2A message type: {message_type} from agent: {agent_id}")
    
    if message_type == "agent_message":
        await handle_agent_message(message, agent_id)
        return None  # No direct response needed
    elif message_type == "join_group":
        return await handle_join_group(message, agent_id)
    elif message_type == "leave_group":
        return await handle_leave_group(message, agent_id)
    elif message_type == "task_request":
        return await handle_task_request(message, agent_id)
    elif message_type == "task_response":
        await handle_task_response(message, agent_id)
        return None
    elif message_type == "ping":
        return {"type": "pong", "timestamp": "2024-01-01T00:00:00Z"}
    else:
        return {
            "type": "error",
            "error": f"Unknown message type: {message_type}",
            "timestamp": "2024-01-01T00:00:00Z"
        }


async def handle_agent_message(message: Dict[str, Any], sender_id: str):
    """Handle agent-to-agent message."""
    target_agent = message.get("target_agent")
    target_group = message.get("target_group")
    content = message.get("content", {})
    
    # Prepare message for forwarding
    forwarded_message = {
        "type": "agent_message",
        "from_agent": sender_id,
        "content": content,
        "timestamp": "2024-01-01T00:00:00Z"
    }
    
    if target_agent:
        # Send to specific agent
        await a2a_manager.send_to_agent(json.dumps(forwarded_message), target_agent)
        logger.info(f"Message sent from {sender_id} to {target_agent}")
    elif target_group:
        # Broadcast to group
        await a2a_manager.broadcast_to_group(json.dumps(forwarded_message), target_group)
        logger.info(f"Message broadcast from {sender_id} to group {target_group}")


async def handle_join_group(message: Dict[str, Any], agent_id: str) -> Dict[str, Any]:
    """Handle agent joining a group."""
    group_id = message.get("group_id")
    
    if group_id:
        a2a_manager.join_group(agent_id, group_id)
        return {
            "type": "group_joined",
            "group_id": group_id,
            "status": "success",
            "timestamp": "2024-01-01T00:00:00Z"
        }
    else:
        return {
            "type": "error",
            "error": "Missing group_id",
            "timestamp": "2024-01-01T00:00:00Z"
        }


async def handle_leave_group(message: Dict[str, Any], agent_id: str) -> Dict[str, Any]:
    """Handle agent leaving a group."""
    group_id = message.get("group_id")
    
    if group_id:
        a2a_manager.leave_group(agent_id, group_id)
        return {
            "type": "group_left",
            "group_id": group_id,
            "status": "success",
            "timestamp": "2024-01-01T00:00:00Z"
        }
    else:
        return {
            "type": "error",
            "error": "Missing group_id",
            "timestamp": "2024-01-01T00:00:00Z"
        }


async def handle_task_request(message: Dict[str, Any], agent_id: str) -> Dict[str, Any]:
    """Handle task request from agent."""
    task_id = message.get("task_id")
    task_type = message.get("task_type")
    task_data = message.get("task_data", {})
    
    # TODO: Implement actual task processing logic
    # This would typically queue tasks and coordinate execution
    
    logger.info(f"Task request {task_id} of type {task_type} from agent {agent_id}")
    
    return {
        "type": "task_accepted",
        "task_id": task_id,
        "status": "queued",
        "timestamp": "2024-01-01T00:00:00Z"
    }


async def handle_task_response(message: Dict[str, Any], agent_id: str):
    """Handle task response from agent."""
    task_id = message.get("task_id")
    result = message.get("result", {})
    status = message.get("status", "completed")
    
    # TODO: Implement actual task result processing
    # This would typically update task status and notify requesters
    
    logger.info(f"Task response {task_id} with status {status} from agent {agent_id}")
    
    # Notify other agents about task completion if needed
    notification = {
        "type": "task_completed",
        "task_id": task_id,
        "completed_by": agent_id,
        "result": result,
        "timestamp": "2024-01-01T00:00:00Z"
    }
    
    # Broadcast to all agents (in real implementation, this would be more targeted)
    await a2a_manager.broadcast_to_all(json.dumps(notification))
