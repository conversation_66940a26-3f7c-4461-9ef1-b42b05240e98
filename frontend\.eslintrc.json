{"extends": ["next/core-web-vitals", "next/typescript", "plugin:storybook/recommended"], "rules": {"@typescript-eslint/no-unused-vars": "warn", "@typescript-eslint/no-explicit-any": "warn", "prefer-const": "error", "no-var": "error", "no-console": "warn", "react-hooks/exhaustive-deps": "warn", "react/jsx-key": "error", "react/no-unescaped-entities": "off"}, "parser": "@typescript-eslint/parser", "parserOptions": {"ecmaVersion": "latest", "sourceType": "module"}}