import { fireEvent, render, screen, waitFor } from '@testing-library/react';
import { beforeEach, describe, expect, it, vi } from 'vitest';
import { SimpleThemeToggle, ThemeToggle } from '../theme-toggle';

// Mock next-themes
const mockSetTheme = vi.fn();

vi.mock('next-themes', () => ({
  useTheme: vi.fn(() => ({
    theme: 'light',
    setTheme: mockSetTheme,
  })),
}));

// Mock Lucide React icons
vi.mock('lucide-react', () => ({
  Sun: ({ className, ...props }: any) => (
    <div data-testid="sun-icon" className={className} {...props}>
      Sun
    </div>
  ),
  Moon: ({ className, ...props }: any) => (
    <div data-testid="moon-icon" className={className} {...props}>
      Moon
    </div>
  ),
}));

// Mock <PERSON>ton component
vi.mock('../button', () => ({
  Button: ({ children, onClick, variant, size, ...props }: any) => (
    <button
      data-testid="theme-button"
      onClick={onClick}
      data-variant={variant}
      data-size={size}
      {...props}
    >
      {children}
    </button>
  ),
}));

// Mock DropdownMenu components
vi.mock('../dropdown-menu', () => ({
  DropdownMenu: ({ children }: any) => <div data-testid="dropdown-menu">{children}</div>,
  DropdownMenuTrigger: ({ children, asChild }: any) => (
    <div data-testid="dropdown-trigger">{children}</div>
  ),
  DropdownMenuContent: ({ children, align }: any) => (
    <div data-testid="dropdown-content" data-align={align}>{children}</div>
  ),
  DropdownMenuItem: ({ children, onClick }: any) => (
    <button data-testid="dropdown-item" onClick={onClick}>
      {children}
    </button>
  ),
}));

describe('Theme Toggle Components', () => {
  beforeEach(() => {
    vi.clearAllMocks();
  });

  describe('ThemeToggle', () => {
    it('renders dropdown menu structure', () => {
      render(<ThemeToggle />);

      expect(screen.getByTestId('dropdown-menu')).toBeInTheDocument();
      expect(screen.getByTestId('dropdown-trigger')).toBeInTheDocument();
      expect(screen.getByTestId('dropdown-content')).toBeInTheDocument();
    });

    it('renders button with correct props', () => {
      render(<ThemeToggle />);

      const button = screen.getByTestId('theme-button');
      expect(button).toBeInTheDocument();
      expect(button).toHaveAttribute('data-variant', 'outline');
      expect(button).toHaveAttribute('data-size', 'icon');
    });

    it('renders sun and moon icons', () => {
      render(<ThemeToggle />);

      expect(screen.getByTestId('sun-icon')).toBeInTheDocument();
      expect(screen.getByTestId('moon-icon')).toBeInTheDocument();
    });

    it('applies correct classes to sun icon', () => {
      render(<ThemeToggle />);

      const sunIcon = screen.getByTestId('sun-icon');
      expect(sunIcon).toHaveClass(
        'h-[1.2rem]',
        'w-[1.2rem]',
        'rotate-0',
        'scale-100',
        'transition-all',
        'dark:-rotate-90',
        'dark:scale-0'
      );
    });

    it('applies correct classes to moon icon', () => {
      render(<ThemeToggle />);

      const moonIcon = screen.getByTestId('moon-icon');
      expect(moonIcon).toHaveClass(
        'absolute',
        'h-[1.2rem]',
        'w-[1.2rem]',
        'rotate-90',
        'scale-0',
        'transition-all',
        'dark:rotate-0',
        'dark:scale-100'
      );
    });

    it('includes screen reader text', () => {
      render(<ThemeToggle />);

      expect(screen.getByText('Toggle theme')).toBeInTheDocument();
      const srText = screen.getByText('Toggle theme');
      expect(srText).toHaveClass('sr-only');
    });

    it('renders all theme options', () => {
      render(<ThemeToggle />);

      expect(screen.getByText('Light')).toBeInTheDocument();
      expect(screen.getByText('Dark')).toBeInTheDocument();
      expect(screen.getByText('System')).toBeInTheDocument();
    });

    it('sets light theme when light option clicked', () => {
      render(<ThemeToggle />);

      const lightOption = screen.getByText('Light');
      fireEvent.click(lightOption);

      expect(mockSetTheme).toHaveBeenCalledWith('light');
    });

    it('sets dark theme when dark option clicked', () => {
      render(<ThemeToggle />);

      const darkOption = screen.getByText('Dark');
      fireEvent.click(darkOption);

      expect(mockSetTheme).toHaveBeenCalledWith('dark');
    });

    it('sets system theme when system option clicked', () => {
      render(<ThemeToggle />);

      const systemOption = screen.getByText('System');
      fireEvent.click(systemOption);

      expect(mockSetTheme).toHaveBeenCalledWith('system');
    });

    it('dropdown content has correct alignment', () => {
      render(<ThemeToggle />);

      const dropdownContent = screen.getByTestId('dropdown-content');
      expect(dropdownContent).toHaveAttribute('data-align', 'end');
    });

    it('calls useTheme hook', () => {
      render(<ThemeToggle />);

      // Just verify the component renders without errors
      expect(screen.getByTestId('dropdown-menu')).toBeInTheDocument();
    });
  });

  describe('SimpleThemeToggle', () => {
    it('renders component structure', () => {
      const { container } = render(<SimpleThemeToggle />);

      // Component should render (useEffect runs synchronously in tests)
      expect(container.firstChild).not.toBeNull();
    });

    it('renders button after mounting', async () => {
      render(<SimpleThemeToggle />);

      await waitFor(() => {
        expect(screen.getByTestId('theme-button')).toBeInTheDocument();
      });
    });

    it('renders button with correct props after mounting', async () => {
      render(<SimpleThemeToggle />);

      await waitFor(() => {
        const button = screen.getByTestId('theme-button');
        expect(button).toHaveAttribute('data-variant', 'outline');
        expect(button).toHaveAttribute('data-size', 'icon');
      });
    });

    it('renders sun and moon icons after mounting', async () => {
      render(<SimpleThemeToggle />);

      await waitFor(() => {
        expect(screen.getByTestId('sun-icon')).toBeInTheDocument();
        expect(screen.getByTestId('moon-icon')).toBeInTheDocument();
      });
    });

    it('applies correct classes to icons after mounting', async () => {
      render(<SimpleThemeToggle />);

      await waitFor(() => {
        const sunIcon = screen.getByTestId('sun-icon');
        expect(sunIcon).toHaveClass(
          'h-[1.2rem]',
          'w-[1.2rem]',
          'rotate-0',
          'scale-100',
          'transition-all',
          'dark:-rotate-90',
          'dark:scale-0'
        );

        const moonIcon = screen.getByTestId('moon-icon');
        expect(moonIcon).toHaveClass(
          'absolute',
          'h-[1.2rem]',
          'w-[1.2rem]',
          'rotate-90',
          'scale-0',
          'transition-all',
          'dark:rotate-0',
          'dark:scale-100'
        );
      });
    });

    it('includes screen reader text after mounting', async () => {
      render(<SimpleThemeToggle />);

      await waitFor(() => {
        expect(screen.getByText('Toggle theme')).toBeInTheDocument();
        const srText = screen.getByText('Toggle theme');
        expect(srText).toHaveClass('sr-only');
      });
    });

    it('toggles theme when clicked', async () => {
      render(<SimpleThemeToggle />);

      await waitFor(() => {
        const button = screen.getByTestId('theme-button');
        fireEvent.click(button);
        expect(mockSetTheme).toHaveBeenCalled();
      });
    });

    it('calls useTheme hook', () => {
      render(<SimpleThemeToggle />);

      // Just verify the component renders without errors
      expect(screen.getByTestId('theme-button')).toBeInTheDocument();
    });

    it('mounts correctly with useEffect', async () => {
      const { rerender } = render(<SimpleThemeToggle />);

      // Component should be visible (useEffect runs synchronously in tests)
      expect(screen.getByTestId('theme-button')).toBeInTheDocument();

      // Rerender should maintain mounted state
      rerender(<SimpleThemeToggle />);
      expect(screen.getByTestId('theme-button')).toBeInTheDocument();
    });
  });
});
