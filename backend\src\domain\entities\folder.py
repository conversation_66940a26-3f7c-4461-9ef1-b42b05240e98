"""
Folder Entity Module

This module defines the Folder entity for the domain layer.
"""

from datetime import datetime
from uuid import UUID, uuid4

from pydantic import BaseModel, Field


class Folder(BaseModel):
    """Folder entity representing a container for notes."""

    id: UUID = Field(default_factory=uuid4)
    name: str
    parent_id: UUID | None = None
    user_id: UUID
    is_archived: bool = False
    created_at: datetime = Field(default_factory=datetime.now)
    updated_at: datetime = Field(default_factory=datetime.now)
    metadata: dict[str, str] = Field(default_factory=dict)

    def archive(self) -> None:
        """Archive the folder."""
        self.is_archived = True
        self.updated_at = datetime.now()

    def unarchive(self) -> None:
        """Unarchive the folder."""
        self.is_archived = False
        self.updated_at = datetime.now()

    def update_name(self, name: str) -> None:
        """
        Update the folder name.

        Args:
            name: The new name
        """
        self.name = name
        self.updated_at = datetime.now()

    def update_parent(self, parent_id: UUID | None) -> None:
        """
        Move the folder to a different parent.

        Args:
            parent_id: The ID of the new parent folder, or None for root
        """
        self.parent_id = parent_id
        self.updated_at = datetime.now()

    def update_metadata(self, metadata: dict[str, str]) -> None:
        """
        Update the folder metadata.

        Args:
            metadata: The new metadata
        """
        self.metadata = metadata
        self.updated_at = datetime.now()
