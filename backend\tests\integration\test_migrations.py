"""
Database migration tests.

This module contains tests for Alembic database migrations
to ensure schema changes work correctly.
"""

import asyncio
import tempfile
from pathlib import Path

import pytest
import pytest_asyncio
from alembic import command
from alembic.config import Config
from sqlalchemy import text
from sqlalchemy.ext.asyncio import create_async_engine


class TestDatabaseMigrations:
    """Test cases for database migrations."""

    @pytest_asyncio.fixture
    async def temp_db_engine(self):
        """Create a temporary database engine for migration testing."""
        # Create temporary database file
        with tempfile.NamedTemporaryFile(suffix=".db", delete=False) as temp_file:
            temp_db_path = temp_file.name

        # Create engine with temporary database
        engine = create_async_engine(
            f"sqlite+aiosqlite:///{temp_db_path}",
            echo=False,
            future=True,
        )

        yield engine

        # Cleanup
        await engine.dispose()
        Path(temp_db_path).unlink(missing_ok=True)

    @pytest.fixture
    def alembic_config(self):
        """Create Alembic configuration for testing."""
        # Get the project root directory
        project_root = Path(__file__).parent.parent.parent
        alembic_ini_path = project_root / "alembic.ini"
        
        if not alembic_ini_path.exists():
            pytest.skip("alembic.ini not found - skipping migration tests")

        config = Config(str(alembic_ini_path))
        
        # Set the script location to the migrations directory
        migrations_dir = project_root / "migrations"
        config.set_main_option("script_location", str(migrations_dir))
        
        return config

    async def test_migration_upgrade_downgrade(self, temp_db_engine, alembic_config):
        """Test migration upgrade and downgrade operations."""
        # Update the database URL in the config
        database_url = str(temp_db_engine.url).replace("+aiosqlite", "")
        alembic_config.set_main_option("sqlalchemy.url", database_url)

        # Test upgrade to head
        try:
            command.upgrade(alembic_config, "head")
        except Exception as e:
            pytest.skip(f"Migration upgrade failed: {e}")

        # Verify tables were created
        async with temp_db_engine.begin() as conn:
            # Check if main tables exist
            tables_to_check = ["users", "folder_model", "tag_model", "note_model"]
            
            for table_name in tables_to_check:
                result = await conn.execute(
                    text(f"SELECT name FROM sqlite_master WHERE type='table' AND name='{table_name}'")
                )
                table_exists = result.fetchone() is not None
                assert table_exists, f"Table {table_name} was not created"

        # Test downgrade
        try:
            command.downgrade(alembic_config, "base")
        except Exception as e:
            pytest.skip(f"Migration downgrade failed: {e}")

        # Verify tables were dropped
        async with temp_db_engine.begin() as conn:
            for table_name in tables_to_check:
                result = await conn.execute(
                    text(f"SELECT name FROM sqlite_master WHERE type='table' AND name='{table_name}'")
                )
                table_exists = result.fetchone() is not None
                # Note: Some tables might still exist due to migration dependencies
                # This is normal behavior

    async def test_user_table_schema(self, temp_db_engine, alembic_config):
        """Test user table schema after migration."""
        # Update the database URL in the config
        database_url = str(temp_db_engine.url).replace("+aiosqlite", "")
        alembic_config.set_main_option("sqlalchemy.url", database_url)

        try:
            command.upgrade(alembic_config, "head")
        except Exception as e:
            pytest.skip(f"Migration upgrade failed: {e}")

        # Check user table schema
        async with temp_db_engine.begin() as conn:
            result = await conn.execute(text("PRAGMA table_info(users)"))
            columns = result.fetchall()
            
            # Convert to dict for easier checking
            column_info = {col[1]: col[2] for col in columns}  # name: type
            
            # Verify required columns exist
            required_columns = {
                "id": "UUID",
                "email": "VARCHAR(255)",
                "username": "VARCHAR(100)",
                "full_name": "VARCHAR(255)",
                "hashed_password": "VARCHAR(255)",
                "role": "USERROLE",
                "status": "USERSTATUS",
                "is_verified": "BOOLEAN",
                "created_at": "DATETIME",
                "updated_at": "DATETIME",
                "last_login": "DATETIME"
            }
            
            for column_name, expected_type in required_columns.items():
                assert column_name in column_info, f"Column {column_name} missing from users table"

    async def test_note_table_schema(self, temp_db_engine, alembic_config):
        """Test note table schema after migration."""
        # Update the database URL in the config
        database_url = str(temp_db_engine.url).replace("+aiosqlite", "")
        alembic_config.set_main_option("sqlalchemy.url", database_url)

        try:
            command.upgrade(alembic_config, "head")
        except Exception as e:
            pytest.skip(f"Migration upgrade failed: {e}")

        # Check note table schema
        async with temp_db_engine.begin() as conn:
            result = await conn.execute(text("PRAGMA table_info(note_model)"))
            columns = result.fetchall()
            
            # Convert to dict for easier checking
            column_info = {col[1]: col[2] for col in columns}  # name: type
            
            # Verify required columns exist
            required_columns = {
                "id": "UUID",
                "title": "VARCHAR(255)",
                "content": "TEXT",
                "content_format": "NOTEFORMAT",
                "content_version": "INTEGER",
                "folder_id": "UUID",
                "created_by": "UUID",
                "last_edited_by": "UUID",
                "is_archived": "BOOLEAN",
                "is_starred": "BOOLEAN",
                "tags": "JSON",
                "created_at": "DATETIME",
                "updated_at": "DATETIME"
            }
            
            for column_name in required_columns.keys():
                assert column_name in column_info, f"Column {column_name} missing from note_model table"

    async def test_foreign_key_constraints(self, temp_db_engine, alembic_config):
        """Test foreign key constraints are properly created."""
        # Update the database URL in the config
        database_url = str(temp_db_engine.url).replace("+aiosqlite", "")
        alembic_config.set_main_option("sqlalchemy.url", database_url)

        try:
            command.upgrade(alembic_config, "head")
        except Exception as e:
            pytest.skip(f"Migration upgrade failed: {e}")

        # Check foreign key constraints
        async with temp_db_engine.begin() as conn:
            # Check note_model foreign keys
            result = await conn.execute(text("PRAGMA foreign_key_list(note_model)"))
            foreign_keys = result.fetchall()
            
            # Should have foreign keys to users and folder_model tables
            fk_tables = {fk[2] for fk in foreign_keys}  # table name is at index 2
            expected_fk_tables = {"users", "folder_model"}
            
            for expected_table in expected_fk_tables:
                assert expected_table in fk_tables, f"Foreign key to {expected_table} missing"

    async def test_indexes_created(self, temp_db_engine, alembic_config):
        """Test that indexes are properly created."""
        # Update the database URL in the config
        database_url = str(temp_db_engine.url).replace("+aiosqlite", "")
        alembic_config.set_main_option("sqlalchemy.url", database_url)

        try:
            command.upgrade(alembic_config, "head")
        except Exception as e:
            pytest.skip(f"Migration upgrade failed: {e}")

        # Check indexes
        async with temp_db_engine.begin() as conn:
            # Check for indexes on important columns
            result = await conn.execute(
                text("SELECT name FROM sqlite_master WHERE type='index'")
            )
            indexes = {row[0] for row in result.fetchall()}
            
            # Should have indexes on commonly queried columns
            expected_index_patterns = [
                "ix_note_model_title",
                "ix_note_model_created_by",
                "ix_note_model_folder_id",
                "ix_folder_model_user_id",
                "ix_tag_model_user_id"
            ]
            
            for pattern in expected_index_patterns:
                matching_indexes = [idx for idx in indexes if pattern in idx]
                assert len(matching_indexes) > 0, f"No index found matching pattern {pattern}"

    async def test_migration_idempotency(self, temp_db_engine, alembic_config):
        """Test that running migrations multiple times is safe."""
        # Update the database URL in the config
        database_url = str(temp_db_engine.url).replace("+aiosqlite", "")
        alembic_config.set_main_option("sqlalchemy.url", database_url)

        try:
            # Run migration twice
            command.upgrade(alembic_config, "head")
            command.upgrade(alembic_config, "head")  # Should be safe to run again
        except Exception as e:
            pytest.skip(f"Migration idempotency test failed: {e}")

        # Verify database is still in good state
        async with temp_db_engine.begin() as conn:
            result = await conn.execute(
                text("SELECT name FROM sqlite_master WHERE type='table'")
            )
            tables = {row[0] for row in result.fetchall()}
            
            expected_tables = {"users", "note_model", "folder_model", "tag_model"}
            for table in expected_tables:
                assert table in tables, f"Table {table} missing after idempotent migration"

    async def test_enum_types_created(self, temp_db_engine, alembic_config):
        """Test that enum types are properly created."""
        # Update the database URL in the config
        database_url = str(temp_db_engine.url).replace("+aiosqlite", "")
        alembic_config.set_main_option("sqlalchemy.url", database_url)

        try:
            command.upgrade(alembic_config, "head")
        except Exception as e:
            pytest.skip(f"Migration upgrade failed: {e}")

        # For SQLite, enums are typically stored as strings with CHECK constraints
        # We can verify the tables were created successfully
        async with temp_db_engine.begin() as conn:
            # Check that tables with enum columns exist
            result = await conn.execute(
                text("SELECT sql FROM sqlite_master WHERE type='table' AND name='note_model'")
            )
            table_sql = result.fetchone()
            
            if table_sql:
                # Verify enum column exists
                assert "content_format" in table_sql[0], "content_format enum column missing"

    def test_migration_files_exist(self):
        """Test that migration files exist and are properly structured."""
        project_root = Path(__file__).parent.parent.parent
        migrations_dir = project_root / "migrations" / "versions"
        
        if not migrations_dir.exists():
            pytest.skip("Migrations directory not found")

        # Check for migration files
        migration_files = list(migrations_dir.glob("*.py"))
        assert len(migration_files) > 0, "No migration files found"

        # Check for required migrations
        migration_names = [f.stem for f in migration_files]
        
        # Should have initial user migration
        user_migrations = [name for name in migration_names if "user" in name.lower()]
        assert len(user_migrations) > 0, "No user migration found"

        # Should have notes schema migration
        note_migrations = [name for name in migration_names if "note" in name.lower() or "0002" in name]
        assert len(note_migrations) > 0, "No note schema migration found"
