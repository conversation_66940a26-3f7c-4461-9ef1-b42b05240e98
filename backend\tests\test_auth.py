"""
Tests for authentication system.

This module tests JWT token management, password hashing,
and authentication endpoints.
"""

import pytest
from datetime import datetime, timedelta, timezone
from jose import JW<PERSON>rror

from src.infrastructure.security.jwt import <PERSON><PERSON><PERSON><PERSON><PERSON>, PasswordManager
from src.domain.entities.user import User, UserR<PERSON>, UserStatus


class TestJWTManager:
    """Test cases for JWT token management."""
    
    def test_jwt_manager_initialization(self):
        """Test JWT manager initialization."""
        jwt_manager = JWTManager()
        
        assert jwt_manager.secret_key is not None
        assert jwt_manager.algorithm == "HS256"
        assert jwt_manager.access_token_expire is not None
        assert jwt_manager.refresh_token_expire is not None
    
    def test_create_access_token(self):
        """Test access token creation."""
        jwt_manager = JWTManager()
        
        token = jwt_manager.create_access_token(
            subject="<EMAIL>",
            user_id="123e4567-e89b-12d3-a456-426614174000"
        )
        
        assert isinstance(token, str)
        assert len(token) > 0
    
    def test_create_refresh_token(self):
        """Test refresh token creation."""
        jwt_manager = JWTManager()
        
        token = jwt_manager.create_refresh_token(
            subject="<EMAIL>",
            user_id="123e4567-e89b-12d3-a456-426614174000"
        )
        
        assert isinstance(token, str)
        assert len(token) > 0
    
    def test_verify_access_token(self):
        """Test access token verification."""
        jwt_manager = JWTManager()
        
        # Create token
        token = jwt_manager.create_access_token(
            subject="<EMAIL>",
            user_id="123e4567-e89b-12d3-a456-426614174000"
        )
        
        # Verify token
        payload = jwt_manager.verify_token(token, "access")
        
        assert payload["sub"] == "<EMAIL>"
        assert payload["user_id"] == "123e4567-e89b-12d3-a456-426614174000"
        assert payload["type"] == "access"
    
    def test_verify_refresh_token(self):
        """Test refresh token verification."""
        jwt_manager = JWTManager()
        
        # Create token
        token = jwt_manager.create_refresh_token(
            subject="<EMAIL>",
            user_id="123e4567-e89b-12d3-a456-426614174000"
        )
        
        # Verify token
        payload = jwt_manager.verify_token(token, "refresh")
        
        assert payload["sub"] == "<EMAIL>"
        assert payload["user_id"] == "123e4567-e89b-12d3-a456-426614174000"
        assert payload["type"] == "refresh"
    
    def test_verify_invalid_token(self):
        """Test verification of invalid token."""
        jwt_manager = JWTManager()
        
        with pytest.raises(JWTError):
            jwt_manager.verify_token("invalid_token", "access")
    
    def test_verify_wrong_token_type(self):
        """Test verification with wrong token type."""
        jwt_manager = JWTManager()
        
        # Create access token
        token = jwt_manager.create_access_token(
            subject="<EMAIL>",
            user_id="123e4567-e89b-12d3-a456-426614174000"
        )
        
        # Try to verify as refresh token
        with pytest.raises(JWTError):
            jwt_manager.verify_token(token, "refresh")
    
    def test_refresh_access_token(self):
        """Test access token refresh."""
        jwt_manager = JWTManager()
        
        # Create refresh token
        refresh_token = jwt_manager.create_refresh_token(
            subject="<EMAIL>",
            user_id="123e4567-e89b-12d3-a456-426614174000"
        )
        
        # Refresh access token
        new_access_token = jwt_manager.refresh_access_token(refresh_token)
        
        assert isinstance(new_access_token, str)
        assert len(new_access_token) > 0
        
        # Verify new token
        payload = jwt_manager.verify_token(new_access_token, "access")
        assert payload["sub"] == "<EMAIL>"
        assert payload["user_id"] == "123e4567-e89b-12d3-a456-426614174000"


class TestPasswordManager:
    """Test cases for password management."""
    
    def test_password_manager_initialization(self):
        """Test password manager initialization."""
        password_manager = PasswordManager()
        
        assert password_manager.pwd_context is not None
    
    def test_hash_password(self):
        """Test password hashing."""
        password_manager = PasswordManager()
        
        password = "test_password_123"
        hashed = password_manager.hash_password(password)
        
        assert isinstance(hashed, str)
        assert len(hashed) > 0
        assert hashed != password  # Should be different from original
    
    def test_verify_password_correct(self):
        """Test password verification with correct password."""
        password_manager = PasswordManager()
        
        password = "test_password_123"
        hashed = password_manager.hash_password(password)
        
        assert password_manager.verify_password(password, hashed) is True
    
    def test_verify_password_incorrect(self):
        """Test password verification with incorrect password."""
        password_manager = PasswordManager()
        
        password = "test_password_123"
        wrong_password = "wrong_password"
        hashed = password_manager.hash_password(password)
        
        assert password_manager.verify_password(wrong_password, hashed) is False
    
    def test_different_hashes_for_same_password(self):
        """Test that same password produces different hashes (salt)."""
        password_manager = PasswordManager()
        
        password = "test_password_123"
        hash1 = password_manager.hash_password(password)
        hash2 = password_manager.hash_password(password)
        
        assert hash1 != hash2  # Should be different due to salt


class TestUserEntity:
    """Test cases for User domain entity."""
    
    def test_user_creation(self):
        """Test user entity creation."""
        user = User(
            email="<EMAIL>",
            username="testuser",
            full_name="Test User",
            hashed_password="hashed_password",
        )
        
        assert user.email == "<EMAIL>"
        assert user.username == "testuser"
        assert user.full_name == "Test User"
        assert user.role == UserRole.USER
        assert user.status == UserStatus.PENDING_VERIFICATION
        assert user.is_verified is False
    
    def test_user_is_active(self):
        """Test user active status check."""
        user = User(
            email="<EMAIL>",
            username="testuser",
            hashed_password="hashed_password",
            status=UserStatus.ACTIVE,
            is_verified=True,
        )
        
        assert user.is_active() is True
    
    def test_user_is_not_active(self):
        """Test user inactive status check."""
        user = User(
            email="<EMAIL>",
            username="testuser",
            hashed_password="hashed_password",
            status=UserStatus.PENDING_VERIFICATION,
            is_verified=False,
        )
        
        assert user.is_active() is False
    
    def test_user_can_login(self):
        """Test user login capability check."""
        user = User(
            email="<EMAIL>",
            username="testuser",
            hashed_password="hashed_password",
            status=UserStatus.ACTIVE,
            is_verified=True,
        )
        
        assert user.can_login() is True
    
    def test_user_cannot_login_suspended(self):
        """Test suspended user cannot login."""
        user = User(
            email="<EMAIL>",
            username="testuser",
            hashed_password="hashed_password",
            status=UserStatus.SUSPENDED,
            is_verified=True,
        )
        
        assert user.can_login() is False
    
    def test_user_activate(self):
        """Test user activation."""
        user = User(
            email="<EMAIL>",
            username="testuser",
            hashed_password="hashed_password",
        )
        
        user.activate()
        
        assert user.status == UserStatus.ACTIVE
        assert user.is_verified is True
    
    def test_user_suspend(self):
        """Test user suspension."""
        user = User(
            email="<EMAIL>",
            username="testuser",
            hashed_password="hashed_password",
            status=UserStatus.ACTIVE,
        )
        
        user.suspend()
        
        assert user.status == UserStatus.SUSPENDED
    
    def test_user_update_last_login(self):
        """Test updating last login timestamp."""
        user = User(
            email="<EMAIL>",
            username="testuser",
            hashed_password="hashed_password",
        )
        
        assert user.last_login is None
        
        user.update_last_login()
        
        assert user.last_login is not None
        assert isinstance(user.last_login, datetime)
