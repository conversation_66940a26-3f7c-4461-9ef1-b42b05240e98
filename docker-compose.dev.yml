version: '3.8'

services:


  backend-dev:
    build:
      context: ./backend
      dockerfile: Dockerfile.dev
    ports:
      - "3002:3001"
    volumes:
      - ./backend:/app:cached
      - backend_venv:/app/.venv
    environment:
      - PYTHONPATH=/app
      - ENVIRONMENT=development
      - DATABASE_URL=********************************************/lonors_dev
      - REDIS_URL=redis://redis:6379/0
    command: uv run uvicorn simple_main:app --host 0.0.0.0 --port 3001 --reload
    depends_on:
      - postgres
      - redis
    networks:
      - lonors-dev

  postgres:
    image: postgres:15-alpine
    environment:
      - POSTGRES_DB=lonors_dev
      - POSTGRES_USER=postgres
      - POSTGRES_PASSWORD=postgres
    volumes:
      - postgres_data:/var/lib/postgresql/data
    ports:
      - "5432:5432"
    networks:
      - lonors-dev

  redis:
    image: redis:7-alpine
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    networks:
      - lonors-dev

volumes:
  backend_venv:
  postgres_data:
  redis_data:

networks:
  lonors-dev:
    driver: bridge
