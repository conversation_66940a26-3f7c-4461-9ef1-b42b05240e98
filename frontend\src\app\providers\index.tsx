'use client';

import { CopilotProvider } from './copilot-provider';
import { ThemeProvider } from './theme-provider';
import { ToastProviderWrapper } from './toast-provider';

interface ProvidersProps {
  children: React.ReactNode;
}

export function Providers({ children }: ProvidersProps) {
  return (
    <ThemeProvider
      attribute="class"
      defaultTheme="system"
      enableSystem
      disableTransitionOnChange
    >
      <ToastProviderWrapper>
        <CopilotProvider>
          {children}
        </CopilotProvider>
      </ToastProviderWrapper>
    </ThemeProvider>
  );
}
