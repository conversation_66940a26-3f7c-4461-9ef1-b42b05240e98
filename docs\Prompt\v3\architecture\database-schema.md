# Database Schema Documentation

This document provides a comprehensive overview of the database schema for the Lonors AI Platform.

## Database Technology Stack

- **Primary Database**: PostgreSQL 15
- **ORM**: SQLAlchemy with async support
- **Migrations**: Alembic
- **Cache**: Redis 7
- **Connection Pooling**: Async connection pool with configurable limits

## Entity Relationship Diagram

```mermaid
erDiagram
    USERS {
        uuid id PK
        string email UK
        string username UK
        string full_name
        string hashed_password
        enum role
        enum status
        boolean is_verified
        datetime last_login
        datetime created_at
        datetime updated_at
    }

    SESSIONS {
        uuid id PK
        uuid user_id FK
        string refresh_token
        datetime expires_at
        string user_agent
        string ip_address
        boolean is_active
        datetime created_at
        datetime updated_at
    }

    AGENTS {
        uuid id PK
        uuid user_id FK
        string name
        string description
        enum type
        jsonb configuration
        enum status
        datetime created_at
        datetime updated_at
    }

    CONTEXTS {
        uuid id PK
        uuid user_id FK
        uuid agent_id FK
        string title
        string description
        enum type
        string model_id
        int max_length
        int current_length
        datetime expires_at
        boolean is_expired
        boolean is_full
        datetime created_at
        datetime updated_at
    }

    MESSAGES {
        uuid id PK
        uuid context_id FK
        enum role
        text content
        jsonb metadata
        datetime created_at
    }

    UI_LAYOUTS {
        uuid id PK
        uuid user_id FK
        string name
        string description
        jsonb components
        enum status
        datetime created_at
        datetime updated_at
    }

    UI_COMPONENTS {
        uuid id PK
        uuid layout_id FK
        string name
        enum type
        jsonb properties
        jsonb events
        int order
        uuid parent_id FK
        datetime created_at
        datetime updated_at
    }

    NOTIFICATIONS {
        uuid id PK
        uuid user_id FK
        string title
        text content
        enum type
        enum status
        boolean is_read
        datetime read_at
        datetime created_at
    }

    USERS ||--o{ SESSIONS : "has"
    USERS ||--o{ AGENTS : "owns"
    USERS ||--o{ CONTEXTS : "creates"
    USERS ||--o{ UI_LAYOUTS : "designs"
    USERS ||--o{ NOTIFICATIONS : "receives"

    AGENTS ||--o{ CONTEXTS : "used in"

    CONTEXTS ||--o{ MESSAGES : "contains"

    UI_LAYOUTS ||--o{ UI_COMPONENTS : "composed of"
    UI_COMPONENTS ||--o{ UI_COMPONENTS : "contains"
```

## Table Descriptions

### Users Table

Stores user account information and authentication details.

| Column | Type | Constraints | Description |
|--------|------|-------------|-------------|
| id | UUID | PK, NOT NULL | Unique identifier |
| email | VARCHAR(255) | UNIQUE, NOT NULL | User email address |
| username | VARCHAR(50) | UNIQUE, NOT NULL | Username |
| full_name | VARCHAR(100) | NULL | User's full name |
| hashed_password | TEXT | NOT NULL | Bcrypt hashed password |
| role | ENUM | NOT NULL, DEFAULT 'user' | User role (admin, user, moderator) |
| status | ENUM | NOT NULL, DEFAULT 'pending_verification' | Account status |
| is_verified | BOOLEAN | NOT NULL, DEFAULT false | Email verification status |
| last_login | TIMESTAMP WITH TZ | NULL | Last login timestamp |
| created_at | TIMESTAMP WITH TZ | NOT NULL | Record creation timestamp |
| updated_at | TIMESTAMP WITH TZ | NOT NULL | Record update timestamp |

**Indexes:**
- Primary Key: id
- Unique Index: email
- Unique Index: username

### Sessions Table

Stores user session information for authentication and refresh tokens.

| Column | Type | Constraints | Description |
|--------|------|-------------|-------------|
| id | UUID | PK, NOT NULL | Unique identifier |
| user_id | UUID | FK, NOT NULL | Reference to users.id |
| refresh_token | TEXT | NOT NULL | JWT refresh token |
| expires_at | TIMESTAMP WITH TZ | NOT NULL | Token expiration timestamp |
| user_agent | TEXT | NULL | User agent string |
| ip_address | VARCHAR(45) | NULL | IP address |
| is_active | BOOLEAN | NOT NULL, DEFAULT true | Session active status |
| created_at | TIMESTAMP WITH TZ | NOT NULL | Record creation timestamp |
| updated_at | TIMESTAMP WITH TZ | NOT NULL | Record update timestamp |

**Indexes:**
- Primary Key: id
- Index: user_id
- Index: refresh_token
- Index: expires_at

**Foreign Keys:**
- user_id REFERENCES users(id) ON DELETE CASCADE

### Agents Table

Stores information about AI agents configured in the system.

| Column | Type | Constraints | Description |
|--------|------|-------------|-------------|
| id | UUID | PK, NOT NULL | Unique identifier |
| user_id | UUID | FK, NOT NULL | Reference to users.id |
| name | VARCHAR(100) | NOT NULL | Agent name |
| description | TEXT | NULL | Agent description |
| type | ENUM | NOT NULL | Agent type |
| configuration | JSONB | NOT NULL | Agent configuration |
| status | ENUM | NOT NULL, DEFAULT 'active' | Agent status |
| created_at | TIMESTAMP WITH TZ | NOT NULL | Record creation timestamp |
| updated_at | TIMESTAMP WITH TZ | NOT NULL | Record update timestamp |

**Indexes:**
- Primary Key: id
- Index: user_id
- Index: type
- Index: status

**Foreign Keys:**
- user_id REFERENCES users(id) ON DELETE CASCADE

### Contexts Table

Stores conversation contexts for AI interactions.

| Column | Type | Constraints | Description |
|--------|------|-------------|-------------|
| id | UUID | PK, NOT NULL | Unique identifier |
| user_id | UUID | FK, NOT NULL | Reference to users.id |
| agent_id | UUID | FK, NULL | Reference to agents.id |
| title | VARCHAR(255) | NOT NULL | Context title |
| description | TEXT | NULL | Context description |
| type | ENUM | NOT NULL | Context type |
| model_id | VARCHAR(100) | NOT NULL | AI model identifier |
| max_length | INTEGER | NOT NULL | Maximum context length |
| current_length | INTEGER | NOT NULL, DEFAULT 0 | Current context length |
| expires_at | TIMESTAMP WITH TZ | NOT NULL | Context expiration timestamp |
| is_expired | BOOLEAN | NOT NULL, DEFAULT false | Expiration status |
| is_full | BOOLEAN | NOT NULL, DEFAULT false | Context full status |
| created_at | TIMESTAMP WITH TZ | NOT NULL | Record creation timestamp |
| updated_at | TIMESTAMP WITH TZ | NOT NULL | Record update timestamp |

**Indexes:**
- Primary Key: id
- Index: user_id
- Index: agent_id
- Index: type
- Index: expires_at

**Foreign Keys:**
- user_id REFERENCES users(id) ON DELETE CASCADE
- agent_id REFERENCES agents(id) ON DELETE SET NULL

### Messages Table

Stores individual messages within conversation contexts.

| Column | Type | Constraints | Description |
|--------|------|-------------|-------------|
| id | UUID | PK, NOT NULL | Unique identifier |
| context_id | UUID | FK, NOT NULL | Reference to contexts.id |
| role | ENUM | NOT NULL | Message role (user, assistant, system) |
| content | TEXT | NOT NULL | Message content |
| metadata | JSONB | NULL | Additional message metadata |
| created_at | TIMESTAMP WITH TZ | NOT NULL | Record creation timestamp |

**Indexes:**
- Primary Key: id
- Index: context_id
- Index: role
- Index: created_at

**Foreign Keys:**
- context_id REFERENCES contexts(id) ON DELETE CASCADE

### UI_Layouts Table

Stores dynamic UI layout configurations.

| Column | Type | Constraints | Description |
|--------|------|-------------|-------------|
| id | UUID | PK, NOT NULL | Unique identifier |
| user_id | UUID | FK, NOT NULL | Reference to users.id |
| name | VARCHAR(255) | NOT NULL | Layout name |
| description | TEXT | NULL | Layout description |
| components | JSONB | NULL | Layout component structure |
| status | ENUM | NOT NULL, DEFAULT 'draft' | Layout status |
| created_at | TIMESTAMP WITH TZ | NOT NULL | Record creation timestamp |
| updated_at | TIMESTAMP WITH TZ | NOT NULL | Record update timestamp |

**Indexes:**
- Primary Key: id
- Index: user_id
- Index: status

**Foreign Keys:**
- user_id REFERENCES users(id) ON DELETE CASCADE

### UI_Components Table

Stores individual UI components within layouts.

| Column | Type | Constraints | Description |
|--------|------|-------------|-------------|
| id | UUID | PK, NOT NULL | Unique identifier |
| layout_id | UUID | FK, NOT NULL | Reference to ui_layouts.id |
| name | VARCHAR(100) | NOT NULL | Component name |
| type | ENUM | NOT NULL | Component type |
| properties | JSONB | NOT NULL | Component properties |
| events | JSONB | NULL | Component event handlers |
| order | INTEGER | NOT NULL, DEFAULT 0 | Component order |
| parent_id | UUID | FK, NULL | Reference to parent component |
| created_at | TIMESTAMP WITH TZ | NOT NULL | Record creation timestamp |
| updated_at | TIMESTAMP WITH TZ | NOT NULL | Record update timestamp |

**Indexes:**
- Primary Key: id
- Index: layout_id
- Index: parent_id
- Index: type

**Foreign Keys:**
- layout_id REFERENCES ui_layouts(id) ON DELETE CASCADE
- parent_id REFERENCES ui_components(id) ON DELETE CASCADE

### Notifications Table

Stores user notifications.

| Column | Type | Constraints | Description |
|--------|------|-------------|-------------|
| id | UUID | PK, NOT NULL | Unique identifier |
| user_id | UUID | FK, NOT NULL | Reference to users.id |
| title | VARCHAR(255) | NOT NULL | Notification title |
| content | TEXT | NOT NULL | Notification content |
| type | ENUM | NOT NULL | Notification type |
| status | ENUM | NOT NULL, DEFAULT 'pending' | Notification status |
| is_read | BOOLEAN | NOT NULL, DEFAULT false | Read status |
| read_at | TIMESTAMP WITH TZ | NULL | Read timestamp |
| created_at | TIMESTAMP WITH TZ | NOT NULL | Record creation timestamp |

**Indexes:**
- Primary Key: id
- Index: user_id
- Index: type
- Index: status
- Index: is_read

**Foreign Keys:**
- user_id REFERENCES users(id) ON DELETE CASCADE

## Redis Schema

Redis is used for caching, session management, and real-time features.

### Key Patterns

| Key Pattern | Type | TTL | Description |
|-------------|------|-----|-------------|
| `session:{session_id}` | Hash | 7 days | Active session data |
| `user:{user_id}:sessions` | Set | None | User's active sessions |
| `token:{refresh_token}` | String | 7 days | Refresh token mapping |
| `rate_limit:ip:{ip_address}` | Hash | 1 hour | IP-based rate limiting |
| `rate_limit:user:{user_id}` | Hash | 1 hour | User-based rate limiting |
| `cache:api:{endpoint}:{params}` | String | 5 minutes | API response cache |
| `ws:connections:{user_id}` | Set | None | Active WebSocket connections |
| `ws:channels:{channel_id}` | Set | None | WebSocket channel subscribers |
| `mcp:context:{context_id}` | Hash | 24 hours | MCP context metadata |
| `agui:layout:{layout_id}` | Hash | 1 hour | AG-UI layout cache |
| `online:users` | Sorted Set | None | Online users with last activity |

### Redis Data Structures

#### Session Hash
```
session:{session_id} = {
  user_id: "user-uuid",
  expires_at: "2024-01-15T12:00:00Z",
  user_agent: "Mozilla/5.0...",
  ip_address: "***********",
  created_at: "2024-01-14T12:00:00Z"
}
```

#### Rate Limit Hash
```
rate_limit:user:{user_id} = {
  count: "5",
  reset_at: "2024-01-15T12:00:00Z"
}
```

#### WebSocket Connections Set
```
ws:connections:{user_id} = [
  "conn_id_1",
  "conn_id_2"
]
```

#### Online Users Sorted Set
```
online:users = {
  "user_id_1": 1705320000,  # Unix timestamp of last activity
  "user_id_2": 1705319900
}
```

## Database Migration Strategy

Alembic is used for database migrations with the following workflow:

1. **Create Migration**: `alembic revision --autogenerate -m "description"`
2. **Apply Migration**: `alembic upgrade head`
3. **Rollback Migration**: `alembic downgrade -1`
4. **Migration History**: `alembic history`

### Migration Versioning

Migrations are versioned with a timestamp and descriptive name:
- `0001_create_users_table.py`
- `0002_add_sessions_table.py`
- `0003_alter_user_add_last_login.py`

### Migration Best Practices

1. **Atomic Changes**: Each migration should make a single logical change
2. **Backward Compatibility**: Ensure migrations can be rolled back
3. **Data Migration**: Separate schema changes from data migrations
4. **Testing**: Test migrations in staging before production
5. **Documentation**: Document complex migrations with comments

## Database Performance Optimization

### Indexing Strategy

1. **Primary Keys**: UUID for all tables
2. **Foreign Keys**: Index all foreign key columns
3. **Search Fields**: Index commonly searched fields
4. **Composite Indexes**: For frequently combined filters
5. **Partial Indexes**: For filtered queries on subsets

### Query Optimization

1. **Async Queries**: Use async/await for non-blocking database operations
2. **Query Batching**: Combine related queries to reduce roundtrips
3. **Pagination**: Limit result sets with skip/limit
4. **Eager Loading**: Load related entities to avoid N+1 queries
5. **Query Caching**: Cache frequent queries in Redis

### Connection Pooling

1. **Pool Size**: 20 connections (configurable)
2. **Max Overflow**: 30 additional connections
3. **Pool Timeout**: 30 seconds
4. **Connection Recycling**: 3600 seconds (1 hour)
5. **Pool Monitoring**: Track pool utilization metrics
