# Lonors Docker Comprehensive Development Environment

## Overview

This guide covers the comprehensive Docker development environment for the Lonors AI Agent Platform, featuring advanced Docker capabilities, multi-stage builds, security hardening, and production-ready configurations.

## Architecture

### Services
- **Frontend**: React + TypeScript + Vite (Port 5500)
- **Backend**: Python + FastAPI + uv (Port 3001)
- **Database**: PostgreSQL 15 (Port 5432)
- **Cache**: DragonflyDB (Port 6379)
- **Monitoring**: Prometheus (Port 9090) - Optional

### Advanced Features
- ✅ Multi-stage Dockerfiles with BuildKit optimizations
- ✅ Multi-platform builds (AMD64/ARM64)
- ✅ Docker secrets for production security
- ✅ Health checks with proper startup dependencies
- ✅ Resource limits and monitoring
- ✅ Network isolation with custom bridge networks
- ✅ Volume optimization (named volumes + bind mounts)
- ✅ Security hardening (non-root users, read-only filesystems)

## Quick Start

### Prerequisites
- Docker Desktop 4.0+ with BuildKit enabled
- PowerShell 7+ (Windows) or Bash (Linux/macOS)
- 8GB+ RAM available for Docker
- 20GB+ disk space

### Development Environment

```powershell
# Start all services
.\scripts\docker-dev.ps1 -Action start

# Check status
.\scripts\docker-dev.ps1 -Action status

# View logs
.\scripts\docker-dev.ps1 -Action logs

# Stop services
.\scripts\docker-dev.ps1 -Action stop
```

### Production Environment

```powershell
# Build and start production services
.\scripts\docker-dev.ps1 -Action start -Production

# Monitor production services
.\scripts\docker-dev.ps1 -Action monitor -Production
```

## Configuration Files

### Environment Files
- `.env.development` - Development configuration
- `.env.production` - Production configuration

### Docker Compose Files
- `docker-compose.yml` - Development environment
- `docker-compose.prod.yml` - Production environment

### Dockerfiles
- `frontend/Dockerfile` - Multi-stage frontend build
- `backend/Dockerfile` - Multi-stage backend build

## Service Details

### Frontend (React + Vite)
```yaml
# Development features:
- Hot reload with optimized volume mounts
- Source map support
- Development tools integration
- WCAG 2.1 AA compliance testing

# Production features:
- Optimized nginx serving
- Gzip compression
- Security headers
- Non-root user execution
```

### Backend (Python + FastAPI)
```yaml
# Development features:
- Hot reload with uv package manager
- Debug mode enabled
- Development tools (ipython, ipdb)
- Test coverage reporting

# Production features:
- Multi-worker uvicorn
- Security hardening
- Read-only filesystem
- Resource optimization
```

### Database (PostgreSQL)
```yaml
# Features:
- Performance optimizations
- Health checks
- Persistent volumes
- Connection pooling
- Statistics collection (pg_stat_statements)
```

### Cache (DragonflyDB)
```yaml
# Features:
- High-performance Redis alternative
- Memory optimization
- Persistence configuration
- Health monitoring
```

## Security Features

### Development Security
- Non-root users in all containers
- Network isolation
- Resource limits
- Health checks

### Production Security
- Docker secrets for sensitive data
- Read-only filesystems
- Security options (no-new-privileges)
- Encrypted communication
- SSL/TLS termination

## Performance Optimizations

### Build Optimizations
- BuildKit cache mounts
- Multi-stage builds
- Layer optimization
- Dependency caching

### Runtime Optimizations
- Resource limits and reservations
- Volume mount optimization (cached)
- Network performance tuning
- Health check optimization

## Monitoring and Observability

### Metrics Collection
- Prometheus integration
- Container metrics (cAdvisor)
- Application metrics
- Database metrics

### Logging
- Structured logging (JSON)
- Log rotation
- Centralized log collection
- Log level configuration

### Health Checks
- Service health monitoring
- Dependency health checks
- Startup probes
- Readiness probes

## Development Workflow

### Daily Development
```powershell
# Start development environment
.\scripts\docker-dev.ps1 -Action start

# Open shell in backend container
.\scripts\docker-dev.ps1 -Action shell -Service backend

# Run tests
.\scripts\docker-dev.ps1 -Action test

# View real-time logs
.\scripts\docker-dev.ps1 -Action logs -Service frontend
```

### Building and Testing
```powershell
# Rebuild with no cache
.\scripts\docker-dev.ps1 -Action build -Force

# Run specific service tests
docker-compose exec backend uv run pytest tests/
docker-compose exec frontend pnpm test:run
```

### Debugging
```powershell
# Access container shell
docker-compose exec backend /bin/bash
docker-compose exec frontend /bin/sh

# View container logs
docker-compose logs -f backend
docker-compose logs -f frontend

# Inspect container
docker inspect lonors-backend-dev
```

## Production Deployment

### Prerequisites
1. Create production secrets:
```bash
# Generate secure passwords
openssl rand -base64 32 > secrets/postgres_password.txt
openssl rand -base64 32 > secrets/redis_password.txt
openssl rand -hex 64 > secrets/jwt_secret.txt

# Set proper permissions
chmod 600 secrets/*.txt
```

2. Configure SSL certificates:
```bash
# Copy SSL certificates
cp your-cert.pem secrets/ssl_cert.pem
cp your-key.pem secrets/ssl_key.pem
chmod 600 secrets/*.pem
```

### Deployment Commands
```powershell
# Deploy production environment
docker-compose -f docker-compose.prod.yml up -d --build

# Scale backend service
docker-compose -f docker-compose.prod.yml up -d --scale backend=3

# Update single service
docker-compose -f docker-compose.prod.yml up -d --no-deps backend
```

## Troubleshooting

### Common Issues

1. **Port conflicts**
```powershell
# Check port usage
netstat -an | findstr :5500
netstat -an | findstr :3001
```

2. **Volume permission issues**
```powershell
# Reset volumes
docker-compose down -v
docker-compose up -d
```

3. **Build failures**
```powershell
# Clean build
.\scripts\docker-dev.ps1 -Action clean -Force
.\scripts\docker-dev.ps1 -Action build -Force
```

4. **Memory issues**
```powershell
# Check Docker resource usage
docker system df
docker stats
```

### Performance Tuning

1. **Increase Docker resources**
   - Memory: 8GB minimum, 16GB recommended
   - CPU: 4 cores minimum
   - Disk: 50GB+ available space

2. **Optimize volume mounts**
   - Use `cached` option for bind mounts
   - Use named volumes for node_modules
   - Avoid mounting large directories

3. **Network optimization**
   - Use custom networks
   - Optimize health check intervals
   - Configure proper DNS settings

## Best Practices

### Development
- Use `.env.development` for local configuration
- Keep containers running for faster iteration
- Use volume mounts for hot reload
- Monitor resource usage regularly

### Production
- Always use Docker secrets
- Implement proper backup strategies
- Monitor container health
- Use multi-stage builds for optimization
- Implement proper logging and monitoring

### Security
- Never commit secrets to version control
- Use non-root users in containers
- Implement network segmentation
- Regular security updates
- Audit container configurations

## Integration with CI/CD

### GitHub Actions Integration
```yaml
# Example workflow step
- name: Build and test Docker images
  run: |
    docker-compose -f docker-compose.yml build
    docker-compose -f docker-compose.yml up -d
    docker-compose -f docker-compose.yml exec -T backend uv run pytest
    docker-compose -f docker-compose.yml exec -T frontend pnpm test:run
```

### Performance Benchmarks

| Metric | Target | Development | Production |
|--------|--------|-------------|------------|
| Frontend Build Time | <2min | ~1.5min | ~2min |
| Backend Startup | <30s | ~20s | ~15s |
| Database Ready | <40s | ~30s | ~25s |
| Memory Usage | <4GB | ~2GB | ~3GB |
| CPU Usage | <50% | ~20% | ~30% |

## Support and Maintenance

### Regular Maintenance
- Update base images monthly
- Rotate secrets quarterly
- Review resource usage weekly
- Update dependencies regularly

### Monitoring Alerts
- Container health failures
- Resource usage thresholds
- Security vulnerabilities
- Performance degradation

For additional support, refer to the project documentation or create an issue in the repository.

## Performance Benchmarking

Use the included benchmarking script to compare containerized vs native performance:

```powershell
# Run performance benchmark
.\scripts\docker-benchmark.ps1

# Compare development vs production
.\scripts\docker-benchmark.ps1 -CompareEnvironments
```

## Multi-Platform Support

Build images for multiple architectures:

```bash
# Enable experimental features
export DOCKER_CLI_EXPERIMENTAL=enabled

# Build multi-platform images
docker buildx build --platform linux/amd64,linux/arm64 -t lonors-frontend:latest ./frontend
docker buildx build --platform linux/amd64,linux/arm64 -t lonors-backend:latest ./backend
```
