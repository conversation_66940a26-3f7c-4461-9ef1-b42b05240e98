{"name": "@lonors/frontend", "version": "1.0.0", "description": "Lonors AI Agent Platform Frontend - Next.js Application", "private": true, "scripts": {"dev": "next dev --port 5500", "build": "next build", "start": "next start --port 5500", "lint": "next lint", "lint:fix": "next lint --fix", "type-check": "tsc --noEmit", "test": "vitest", "test:ui": "vitest --ui", "test:coverage": "vitest --coverage", "test:watch": "vitest --watch", "storybook": "storybook dev -p 6006", "build-storybook": "storybook build", "analyze": "cross-env ANALYZE=true next build", "analyze:server": "cross-env BUNDLE_ANALYZE=server next build", "analyze:browser": "cross-env BUNDLE_ANALYZE=browser next build"}, "dependencies": {"@copilotkit/backend": "^0.37.0", "@copilotkit/react-core": "^1.8.13", "@copilotkit/react-textarea": "^1.8.13", "@copilotkit/react-ui": "^1.8.13", "@radix-ui/react-dialog": "^1.1.14", "@radix-ui/react-dropdown-menu": "^2.1.15", "@radix-ui/react-slot": "^1.2.3", "@radix-ui/react-toast": "^1.2.14", "animejs": "^4.0.2", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "cross-env": "^7.0.3", "lucide-react": "^0.511.0", "next": "^15.3.3", "next-themes": "^0.4.6", "react": "^19.1.0", "react-dom": "^19.1.0", "tailwind-merge": "^3.3.0"}, "devDependencies": {"@next/bundle-analyzer": "^15.3.3", "@storybook/addon-a11y": "^9.0.1", "@storybook/addon-docs": "9.0.1", "@storybook/addon-essentials": "^8.6.14", "@storybook/addon-interactions": "^8.6.14", "@storybook/addon-links": "^9.0.1", "@storybook/addon-onboarding": "9.0.1", "@storybook/nextjs": "^9.0.1", "@storybook/react": "^9.0.1", "@tailwindcss/postcss": "4.1.8", "@tailwindcss/typography": "^0.5.16", "@testing-library/jest-dom": "^6.6.3", "@testing-library/react": "^16.3.0", "@testing-library/user-event": "^14.6.1", "@types/animejs": "^3.1.13", "@types/node": "^22.10.5", "@types/react": "^19.0.2", "@types/react-dom": "^19.0.2", "@vitejs/plugin-react": "^4.5.0", "@vitest/coverage-v8": "^3.1.4", "@vitest/ui": "^3.1.4", "autoprefixer": "^10.4.21", "eslint": "^9.27.0", "eslint-config-next": "^15.3.3", "eslint-plugin-storybook": "9.0.1", "jsdom": "^26.1.0", "postcss": "^8.5.4", "storybook": "^9.0.1", "tailwindcss": "^3.4.17", "typescript": "^5.7.3", "vitest": "^3.1.4", "webpack-bundle-analyzer": "^4.10.2"}, "packageManager": "pnpm@10.11.0", "engines": {"node": ">=18.0.0", "pnpm": ">=8.0.0"}, "author": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "license": "MIT"}