"""
Note Service Module

This module implements the application service for notes.
"""

from uuid import UUID

from src.application.dto.note_dto import (
    CreateNoteDTO,
    NoteDTO,
    NoteFilterDTO,
    NoteMetadataDTO,
    UpdateNoteDTO,
)
from src.domain.entities.note import Note, NoteContent
from src.domain.repositories.note_repository import NoteRepository


class NoteService:
    """Service for managing notes."""

    def __init__(self, note_repository: NoteRepository):
        """
        Initialize the note service.

        Args:
            note_repository: Repository for note operations
        """
        self._note_repository = note_repository

    async def get_note(self, note_id: UUID, user_id: UUID) -> NoteDTO | None:
        """
        Get a note by ID.

        Args:
            note_id: ID of the note to retrieve
            user_id: ID of the user making the request

        Returns:
            The note if found and owned by the user, None otherwise
        """
        note = await self._note_repository.get_by_id(note_id)

        if note is None or note.created_by != user_id:
            return None

        return self._to_note_dto(note)

    async def get_notes(
        self, user_id: UUID, filters: NoteFilterDTO
    ) -> list[NoteMetadataDTO]:
        """
        Get all notes for a user with optional filtering.

        Args:
            user_id: ID of the user
            filters: Filtering options

        Returns:
            List of notes matching the criteria
        """
        notes = await self._note_repository.get_all_by_user(
            user_id=user_id,
            folder_id=filters.folder_id,
            include_archived=filters.include_archived,
            only_starred=filters.only_starred,
            tags=filters.tags,
            search_query=filters.search_query,
        )

        # Sort notes based on filters
        if filters.sort_by:
            reverse = filters.sort_direction.lower() == "desc"
            notes.sort(
                key=lambda note: getattr(note, filters.sort_by),
                reverse=reverse,
            )

        return [self._to_note_metadata_dto(note) for note in notes]

    async def create_note(self, user_id: UUID, create_dto: CreateNoteDTO) -> NoteDTO:
        """
        Create a new note.

        Args:
            user_id: ID of the user creating the note
            create_dto: Data for the new note

        Returns:
            The created note
        """
        note = Note(
            title=create_dto.title,
            content=NoteContent(
                content=create_dto.content,
                format=create_dto.format,
                version=1,
            ),
            folder_id=create_dto.folder_id,
            tags=create_dto.tags,
            created_by=user_id,
            last_edited_by=user_id,
        )

        created_note = await self._note_repository.create(note)
        return self._to_note_dto(created_note)

    async def update_note(
        self, note_id: UUID, user_id: UUID, update_dto: UpdateNoteDTO
    ) -> NoteDTO | None:
        """
        Update an existing note.

        Args:
            note_id: ID of the note to update
            user_id: ID of the user making the update
            update_dto: Data to update

        Returns:
            The updated note if found and owned by the user, None otherwise
        """
        note = await self._note_repository.get_by_id(note_id)

        if note is None or note.created_by != user_id:
            return None

        # Update fields if provided
        if update_dto.title is not None:
            note.update_title(update_dto.title)

        if update_dto.content is not None:
            note.update_content(update_dto.content, update_dto.format)

        if update_dto.folder_id is not None:
            note.update_folder(update_dto.folder_id)

        if update_dto.tags is not None:
            note.update_tags(update_dto.tags)

        if update_dto.is_archived is not None:
            if update_dto.is_archived:
                note.archive()
            else:
                note.unarchive()

        if update_dto.is_starred is not None:
            if update_dto.is_starred:
                note.star()
            else:
                note.unstar()

        note.update_editor(user_id)

        updated_note = await self._note_repository.update(note)
        return self._to_note_dto(updated_note)

    async def delete_note(self, note_id: UUID, user_id: UUID) -> bool:
        """
        Delete a note.

        Args:
            note_id: ID of the note to delete
            user_id: ID of the user making the request

        Returns:
            True if the note was deleted, False otherwise
        """
        note = await self._note_repository.get_by_id(note_id)

        if note is None or note.created_by != user_id:
            return False

        return await self._note_repository.delete(note_id)

    async def archive_note(self, note_id: UUID, user_id: UUID) -> NoteDTO | None:
        """
        Archive a note.

        Args:
            note_id: ID of the note to archive
            user_id: ID of the user making the request

        Returns:
            The archived note if found and owned by the user, None otherwise
        """
        note = await self._note_repository.get_by_id(note_id)

        if note is None or note.created_by != user_id:
            return None

        note.archive()
        note.update_editor(user_id)

        updated_note = await self._note_repository.update(note)
        return self._to_note_dto(updated_note)

    async def unarchive_note(self, note_id: UUID, user_id: UUID) -> NoteDTO | None:
        """
        Unarchive a note.

        Args:
            note_id: ID of the note to unarchive
            user_id: ID of the user making the request

        Returns:
            The unarchived note if found and owned by the user, None otherwise
        """
        note = await self._note_repository.get_by_id(note_id)

        if note is None or note.created_by != user_id:
            return None

        note.unarchive()
        note.update_editor(user_id)

        updated_note = await self._note_repository.update(note)
        return self._to_note_dto(updated_note)

    async def star_note(self, note_id: UUID, user_id: UUID) -> NoteDTO | None:
        """
        Star a note.

        Args:
            note_id: ID of the note to star
            user_id: ID of the user making the request

        Returns:
            The starred note if found and owned by the user, None otherwise
        """
        note = await self._note_repository.get_by_id(note_id)

        if note is None or note.created_by != user_id:
            return None

        note.star()
        note.update_editor(user_id)

        updated_note = await self._note_repository.update(note)
        return self._to_note_dto(updated_note)

    async def unstar_note(self, note_id: UUID, user_id: UUID) -> NoteDTO | None:
        """
        Unstar a note.

        Args:
            note_id: ID of the note to unstar
            user_id: ID of the user making the request

        Returns:
            The unstarred note if found and owned by the user, None otherwise
        """
        note = await self._note_repository.get_by_id(note_id)

        if note is None or note.created_by != user_id:
            return None

        note.unstar()
        note.update_editor(user_id)

        updated_note = await self._note_repository.update(note)
        return self._to_note_dto(updated_note)

    def _to_note_dto(self, note: Note) -> NoteDTO:
        """
        Convert a domain Note entity to a NoteDTO.

        Args:
            note: The domain entity

        Returns:
            The DTO representation
        """
        return NoteDTO(
            metadata=self._to_note_metadata_dto(note),
            content=self._to_note_content_dto(note),
        )

    def _to_note_metadata_dto(self, note: Note) -> NoteMetadataDTO:
        """
        Convert a domain Note entity to a NoteMetadataDTO.

        Args:
            note: The domain entity

        Returns:
            The metadata DTO representation
        """
        return NoteMetadataDTO(
            id=note.id,
            title=note.title,
            folder_id=note.folder_id,
            tags=note.tags,
            is_archived=note.is_archived,
            is_starred=note.is_starred,
            created_at=note.created_at,
            updated_at=note.updated_at,
            created_by=note.created_by,
            last_edited_by=note.last_edited_by,
        )

    def _to_note_content_dto(self, note: Note) -> NoteContent:
        """
        Convert a domain Note entity's content to a NoteContentDTO.

        Args:
            note: The domain entity

        Returns:
            The content DTO representation
        """
        return note.content
