{"name": "lonors", "version": "1.0.0", "description": "<PERSON><PERSON>s - AI Agent Platform with Next.js Frontend and FastAPI Backend", "private": true, "workspaces": ["frontend"], "packageManager": "pnpm@10.11.0", "scripts": {"dev": "concurrently \"pnpm dev:backend\" \"pnpm dev:frontend\"", "dev:backend": "cd backend && uv run fastapi dev src/main.py --host 0.0.0.0 --port 3001", "dev:frontend": "pnpm --filter frontend dev", "build": "pnpm build:backend && pnpm build:frontend", "build:backend": "cd backend && uv run python -m build", "build:frontend": "pnpm --filter frontend build", "start": "pnpm start:backend && pnpm start:frontend", "start:backend": "cd backend && uv run uvicorn src.main:app --host 0.0.0.0 --port 3001", "start:frontend": "pnpm --filter frontend start", "test": "pnpm test:backend && pnpm test:frontend", "test:backend": "cd backend && uv run pytest", "test:frontend": "pnpm --filter frontend test", "test:coverage": "pnpm test:backend:coverage && pnpm test:frontend:coverage", "test:backend:coverage": "cd backend && uv run pytest --cov=src --cov-report=html", "test:frontend:coverage": "pnpm --filter frontend test:coverage", "lint": "pnpm lint:backend && pnpm lint:frontend", "lint:backend": "cd backend && uv run ruff check src/ && uv run black --check src/", "lint:frontend": "pnpm --filter frontend lint", "lint:fix": "pnpm lint:backend:fix && pnpm lint:frontend:fix", "lint:backend:fix": "cd backend && uv run ruff check --fix src/ && uv run black src/", "lint:frontend:fix": "pnpm --filter frontend lint:fix", "type-check": "pnpm type-check:frontend", "type-check:frontend": "pnpm --filter frontend type-check", "docker:build": "docker-compose build", "docker:up": "docker-compose up -d", "docker:down": "docker-compose down", "docker:logs": "docker-compose logs -f", "docker:clean": "docker-compose down -v --remove-orphans", "setup": "pnpm install && cd backend && uv sync", "setup:dev": "pnpm setup && pnpm docker:up"}, "devDependencies": {"concurrently": "^8.2.2"}, "engines": {"node": ">=18.0.0", "pnpm": ">=8.0.0"}, "author": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "license": "MIT", "repository": {"type": "git", "url": "https://github.com/simyropandos/Lonor.git"}, "keywords": ["ai", "agents", "automation", "workflow", "knowledge-graph", "nextjs", "<PERSON><PERSON><PERSON>", "typescript", "python"]}