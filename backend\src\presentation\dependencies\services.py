"""
Service dependencies for FastAPI.

This module provides dependency functions for injecting
application services into FastAPI endpoints.
"""

from fastapi import Depends
from sqlalchemy.ext.asyncio import AsyncSession

from src.application.use_cases.folder_service import FolderService
from src.application.use_cases.note_service import NoteService
from src.application.use_cases.tag_service import TagService
from src.application.use_cases.user_service import UserService
from src.infrastructure.database.connection import get_db_session
from src.infrastructure.database.repositories.folder_repository_impl import (
    SQLAlchemyFolderRepository,
)
from src.infrastructure.database.repositories.note_repository_impl import (
    SQLAlchemyNoteRepository,
)
from src.infrastructure.database.repositories.tag_repository_impl import (
    SQLAlchemyTagRepository,
)
from src.infrastructure.database.repositories.user_repository import UserRepository


async def get_user_service(db: AsyncSession = Depends(get_db_session)) -> UserService:
    """
    Get user service dependency.

    Args:
        db: Database session dependency

    Returns:
        UserService: User service instance
    """
    user_repository = UserRepository(db)
    return UserService(user_repository)


async def get_note_service(db: AsyncSession = Depends(get_db_session)) -> NoteService:
    """
    Get note service dependency.

    Args:
        db: Database session dependency

    Returns:
        NoteService: Note service instance
    """
    note_repository = SQLAlchemyNoteRepository(db)
    return NoteService(note_repository)


async def get_folder_service(
    db: AsyncSession = Depends(get_db_session),
) -> FolderService:
    """
    Get folder service dependency.

    Args:
        db: Database session dependency

    Returns:
        FolderService: Folder service instance
    """
    folder_repository = SQLAlchemyFolderRepository(db)
    return FolderService(folder_repository)


async def get_tag_service(db: AsyncSession = Depends(get_db_session)) -> TagService:
    """
    Get tag service dependency.

    Args:
        db: Database session dependency

    Returns:
        TagService: Tag service instance
    """
    tag_repository = SQLAlchemyTagRepository(db)
    return TagService(tag_repository)
