"""
Database connection management.

This module provides async database connection management using SQLAlchemy
with connection pooling and proper lifecycle management.
"""

import logging
from typing import AsyncGenerator, Optional

from sqlalchemy.ext.asyncio import (
    AsyncEngine,
    AsyncSession,
    async_sessionmaker,
    create_async_engine,
)
from sqlalchemy.pool import NullP<PERSON>, QueuePool

from src.infrastructure.config.settings import get_settings
from src.infrastructure.logging.setup import LoggerMixin

logger = logging.getLogger(__name__)


class DatabaseManager(LoggerMixin):
    """
    Database connection manager with async support.
    
    Manages database connections, sessions, and connection pooling
    following best practices for async SQLAlchemy usage.
    """
    
    def __init__(self, database_url: str) -> None:
        """
        Initialize database manager.
        
        Args:
            database_url: PostgreSQL database URL
        """
        self.database_url = database_url
        self.engine: Optional[AsyncEngine] = None
        self.session_factory: Optional[async_sessionmaker[AsyncSession]] = None
        self.settings = get_settings()
    
    async def initialize(self) -> None:
        """Initialize database engine and session factory."""
        self.logger.info("Initializing database connection")
        
        # Create async engine with connection pooling
        self.engine = create_async_engine(
            self.database_url,
            echo=self.settings.db_echo,
            poolclass=QueuePool if self.settings.environment != "testing" else NullPool,
            pool_size=self.settings.db_pool_size,
            max_overflow=self.settings.db_max_overflow,
            pool_timeout=self.settings.db_pool_timeout,
            pool_recycle=self.settings.db_pool_recycle,
            pool_pre_ping=True,  # Validate connections before use
        )
        
        # Create session factory
        self.session_factory = async_sessionmaker(
            bind=self.engine,
            class_=AsyncSession,
            expire_on_commit=False,
            autoflush=True,
            autocommit=False,
        )
        
        self.logger.info("Database connection initialized successfully")
    
    async def close(self) -> None:
        """Close database connections."""
        if self.engine:
            self.logger.info("Closing database connections")
            await self.engine.dispose()
            self.logger.info("Database connections closed")
    
    async def get_session(self) -> AsyncGenerator[AsyncSession, None]:
        """
        Get database session with automatic cleanup.
        
        Yields:
            AsyncSession: Database session
            
        Raises:
            RuntimeError: If database is not initialized
        """
        if not self.session_factory:
            raise RuntimeError("Database not initialized. Call initialize() first.")
        
        async with self.session_factory() as session:
            try:
                yield session
            except Exception as e:
                self.logger.error(f"Database session error: {e}")
                await session.rollback()
                raise
            finally:
                await session.close()
    
    async def health_check(self) -> bool:
        """
        Check database connectivity.
        
        Returns:
            bool: True if database is accessible, False otherwise
        """
        try:
            if not self.engine:
                return False
            
            async with self.engine.begin() as conn:
                await conn.execute("SELECT 1")
            
            self.logger.debug("Database health check passed")
            return True
        
        except Exception as e:
            self.logger.error(f"Database health check failed: {e}")
            return False
    
    @property
    def is_initialized(self) -> bool:
        """Check if database manager is initialized."""
        return self.engine is not None and self.session_factory is not None


# Global database manager instance
_db_manager: Optional[DatabaseManager] = None


def get_database_manager() -> DatabaseManager:
    """
    Get global database manager instance.
    
    Returns:
        DatabaseManager: Global database manager
        
    Raises:
        RuntimeError: If database manager is not initialized
    """
    global _db_manager
    
    if _db_manager is None:
        settings = get_settings()
        _db_manager = DatabaseManager(settings.database_url)
    
    return _db_manager


async def get_db_session() -> AsyncGenerator[AsyncSession, None]:
    """
    Dependency for getting database session in FastAPI endpoints.
    
    Yields:
        AsyncSession: Database session
    """
    db_manager = get_database_manager()
    
    if not db_manager.is_initialized:
        await db_manager.initialize()
    
    async for session in db_manager.get_session():
        yield session


async def init_database() -> None:
    """Initialize global database manager."""
    db_manager = get_database_manager()
    await db_manager.initialize()


async def close_database() -> None:
    """Close global database manager."""
    global _db_manager
    
    if _db_manager:
        await _db_manager.close()
        _db_manager = None
