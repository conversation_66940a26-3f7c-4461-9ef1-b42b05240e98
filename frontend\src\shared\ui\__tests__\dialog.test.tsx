import { fireEvent, render, screen } from '@testing-library/react';
import { describe, expect, it, vi } from 'vitest';
import {
    Dialog,
    DialogClose,
    DialogContent,
    DialogDescription,
    DialogFooter,
    DialogHeader,
    DialogOverlay,
    DialogPortal,
    DialogTitle,
    DialogTrigger,
} from '../dialog';

// Mock Lucide React icons
vi.mock('lucide-react', () => ({
  X: () => <div data-testid="x-icon">X</div>,
}));

// Mock Radix UI Dialog
vi.mock('@radix-ui/react-dialog', () => ({
  Root: ({ children, ...props }: any) => <div data-testid="dialog-root" {...props}>{children}</div>,
  Trigger: ({ children, ...props }: any) => <button data-testid="dialog-trigger" {...props}>{children}</button>,
  Portal: ({ children, ...props }: any) => <div data-testid="dialog-portal" {...props}>{children}</div>,
  Close: ({ children, ...props }: any) => <button data-testid="dialog-close" {...props}>{children}</button>,
  Overlay: ({ children, className, ...props }: any) => (
    <div data-testid="dialog-overlay" className={className} {...props}>{children}</div>
  ),
  Content: ({ children, className, ...props }: any) => (
    <div data-testid="dialog-content" className={className} {...props}>{children}</div>
  ),
  Title: ({ children, className, ...props }: any) => (
    <h2 data-testid="dialog-title" className={className} {...props}>{children}</h2>
  ),
  Description: ({ children, className, ...props }: any) => (
    <p data-testid="dialog-description" className={className} {...props}>{children}</p>
  ),
}));

describe('Dialog Components', () => {
  describe('Dialog', () => {
    it('renders dialog root', () => {
      render(
        <Dialog>
          <div>Dialog content</div>
        </Dialog>
      );

      expect(screen.getByTestId('dialog-root')).toBeInTheDocument();
    });
  });

  describe('DialogTrigger', () => {
    it('renders trigger button', () => {
      render(
        <DialogTrigger>
          Open Dialog
        </DialogTrigger>
      );

      const trigger = screen.getByTestId('dialog-trigger');
      expect(trigger).toBeInTheDocument();
      expect(trigger).toHaveTextContent('Open Dialog');
    });

    it('handles click events', () => {
      const handleClick = vi.fn();

      render(
        <DialogTrigger onClick={handleClick}>
          Open Dialog
        </DialogTrigger>
      );

      fireEvent.click(screen.getByTestId('dialog-trigger'));
      expect(handleClick).toHaveBeenCalled();
    });
  });

  describe('DialogOverlay', () => {
    it('renders overlay with correct classes', () => {
      render(<DialogOverlay />);

      const overlay = screen.getByTestId('dialog-overlay');
      expect(overlay).toBeInTheDocument();
      expect(overlay).toHaveClass('fixed', 'inset-0', 'z-50', 'bg-black/80');
    });

    it('accepts custom className', () => {
      render(<DialogOverlay className="custom-overlay" />);

      const overlay = screen.getByTestId('dialog-overlay');
      expect(overlay).toHaveClass('custom-overlay');
    });

    it('forwards ref correctly', () => {
      const ref = vi.fn();
      render(<DialogOverlay ref={ref} />);

      expect(ref).toHaveBeenCalled();
    });
  });

  describe('DialogContent', () => {
    it('renders content with portal and overlay', () => {
      render(
        <DialogContent>
          <div>Dialog content</div>
        </DialogContent>
      );

      expect(screen.getByTestId('dialog-portal')).toBeInTheDocument();
      expect(screen.getByTestId('dialog-overlay')).toBeInTheDocument();
      expect(screen.getByTestId('dialog-content')).toBeInTheDocument();
      expect(screen.getByText('Dialog content')).toBeInTheDocument();
    });

    it('includes close button with X icon', () => {
      render(
        <DialogContent>
          Content
        </DialogContent>
      );

      expect(screen.getByTestId('dialog-close')).toBeInTheDocument();
      expect(screen.getByTestId('x-icon')).toBeInTheDocument();
      expect(screen.getByText('Close')).toBeInTheDocument();
    });

    it('applies correct styling classes', () => {
      render(<DialogContent />);

      const content = screen.getByTestId('dialog-content');
      expect(content).toHaveClass('fixed', 'left-[50%]', 'top-[50%]', 'z-50');
    });

    it('accepts custom className', () => {
      render(<DialogContent className="custom-content" />);

      const content = screen.getByTestId('dialog-content');
      expect(content).toHaveClass('custom-content');
    });

    it('forwards ref correctly', () => {
      const ref = vi.fn();
      render(<DialogContent ref={ref} />);

      expect(ref).toHaveBeenCalled();
    });
  });

  describe('DialogHeader', () => {
    it('renders header with correct structure', () => {
      render(
        <DialogHeader>
          <div>Header content</div>
        </DialogHeader>
      );

      const header = screen.getByText('Header content').parentElement;
      expect(header).toHaveClass('flex', 'flex-col', 'space-y-1.5');
    });

    it('accepts custom className', () => {
      render(
        <DialogHeader className="custom-header" data-testid="dialog-header">
          Header
        </DialogHeader>
      );

      const header = screen.getByTestId('dialog-header');
      expect(header).toHaveClass('custom-header');
    });

    it('spreads additional props', () => {
      render(
        <DialogHeader data-testid="dialog-header" role="banner">
          Header
        </DialogHeader>
      );

      const header = screen.getByTestId('dialog-header');
      expect(header).toHaveAttribute('role', 'banner');
    });
  });

  describe('DialogFooter', () => {
    it('renders footer with correct structure', () => {
      render(
        <DialogFooter>
          <div>Footer content</div>
        </DialogFooter>
      );

      const footer = screen.getByText('Footer content').parentElement;
      expect(footer).toHaveClass('flex', 'flex-col-reverse', 'sm:flex-row');
    });

    it('accepts custom className', () => {
      render(
        <DialogFooter className="custom-footer" data-testid="dialog-footer">
          Footer
        </DialogFooter>
      );

      const footer = screen.getByTestId('dialog-footer');
      expect(footer).toHaveClass('custom-footer');
    });

    it('spreads additional props', () => {
      render(
        <DialogFooter data-testid="dialog-footer" role="contentinfo">
          Footer
        </DialogFooter>
      );

      const footer = screen.getByTestId('dialog-footer');
      expect(footer).toHaveAttribute('role', 'contentinfo');
    });
  });

  describe('DialogTitle', () => {
    it('renders title with correct styling', () => {
      render(
        <DialogTitle>
          Dialog Title
        </DialogTitle>
      );

      const title = screen.getByTestId('dialog-title');
      expect(title).toBeInTheDocument();
      expect(title).toHaveTextContent('Dialog Title');
      expect(title).toHaveClass('text-lg', 'font-semibold');
    });

    it('accepts custom className', () => {
      render(
        <DialogTitle className="custom-title">
          Title
        </DialogTitle>
      );

      const title = screen.getByTestId('dialog-title');
      expect(title).toHaveClass('custom-title');
    });

    it('forwards ref correctly', () => {
      const ref = vi.fn();
      render(<DialogTitle ref={ref}>Title</DialogTitle>);

      expect(ref).toHaveBeenCalled();
    });
  });

  describe('DialogDescription', () => {
    it('renders description with correct styling', () => {
      render(
        <DialogDescription>
          Dialog description text
        </DialogDescription>
      );

      const description = screen.getByTestId('dialog-description');
      expect(description).toBeInTheDocument();
      expect(description).toHaveTextContent('Dialog description text');
      expect(description).toHaveClass('text-sm', 'text-muted-foreground');
    });

    it('accepts custom className', () => {
      render(
        <DialogDescription className="custom-description">
          Description
        </DialogDescription>
      );

      const description = screen.getByTestId('dialog-description');
      expect(description).toHaveClass('custom-description');
    });

    it('forwards ref correctly', () => {
      const ref = vi.fn();
      render(<DialogDescription ref={ref}>Description</DialogDescription>);

      expect(ref).toHaveBeenCalled();
    });
  });

  describe('DialogClose', () => {
    it('renders close button', () => {
      render(
        <DialogClose>
          Close
        </DialogClose>
      );

      const closeButton = screen.getByTestId('dialog-close');
      expect(closeButton).toBeInTheDocument();
      expect(closeButton).toHaveTextContent('Close');
    });

    it('handles click events', () => {
      const handleClick = vi.fn();

      render(
        <DialogClose onClick={handleClick}>
          Close
        </DialogClose>
      );

      fireEvent.click(screen.getByTestId('dialog-close'));
      expect(handleClick).toHaveBeenCalled();
    });
  });

  describe('DialogPortal', () => {
    it('renders portal container', () => {
      render(
        <DialogPortal>
          <div>Portal content</div>
        </DialogPortal>
      );

      expect(screen.getByTestId('dialog-portal')).toBeInTheDocument();
      expect(screen.getByText('Portal content')).toBeInTheDocument();
    });
  });

  describe('Complete Dialog Example', () => {
    it('renders complete dialog structure', () => {
      render(
        <Dialog>
          <DialogTrigger>Open</DialogTrigger>
          <DialogContent>
            <DialogHeader>
              <DialogTitle>Test Dialog</DialogTitle>
              <DialogDescription>This is a test dialog</DialogDescription>
            </DialogHeader>
            <div>Dialog body content</div>
            <DialogFooter>
              <DialogClose>Cancel</DialogClose>
            </DialogFooter>
          </DialogContent>
        </Dialog>
      );

      expect(screen.getByTestId('dialog-root')).toBeInTheDocument();
      expect(screen.getByText('Open')).toBeInTheDocument();
      expect(screen.getByText('Test Dialog')).toBeInTheDocument();
      expect(screen.getByText('This is a test dialog')).toBeInTheDocument();
      expect(screen.getByText('Dialog body content')).toBeInTheDocument();
      expect(screen.getByText('Cancel')).toBeInTheDocument();
    });
  });
});
