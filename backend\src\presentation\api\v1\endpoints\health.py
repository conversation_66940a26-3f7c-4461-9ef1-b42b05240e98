"""
Health check endpoints.

This module provides health check endpoints for monitoring
and load balancer health checks.
"""

import time
from datetime import datetime, timezone
from typing import Dict, Any

from fastapi import APIRouter, Depends, status
from sqlalchemy.ext.asyncio import AsyncSession

from src.infrastructure.config.settings import get_settings
from src.infrastructure.database.connection import get_db_session
from src.infrastructure.logging.setup import get_logger

logger = get_logger(__name__)
settings = get_settings()

router = APIRouter()


@router.get("/", status_code=status.HTTP_200_OK)
async def basic_health_check() -> Dict[str, Any]:
    """
    Basic health check endpoint.
    
    Returns basic application status without external dependencies.
    Suitable for load balancer health checks.
    
    Returns:
        dict: Basic health status
    """
    return {
        "status": "healthy",
        "timestamp": datetime.now(timezone.utc).isoformat(),
        "version": settings.app_version,
        "environment": settings.environment,
    }


@router.get("/detailed", status_code=status.HTTP_200_OK)
async def detailed_health_check(
    db: AsyncSession = Depends(get_db_session)
) -> Dict[str, Any]:
    """
    Detailed health check endpoint.
    
    Checks the health of all external dependencies including
    database and cache connections.
    
    Args:
        db: Database session dependency
        
    Returns:
        dict: Detailed health status with dependency checks
    """
    start_time = time.time()
    
    # Check database connectivity
    db_status = "healthy"
    db_response_time = None
    
    try:
        db_start = time.time()
        result = await db.execute("SELECT 1")
        db_response_time = round((time.time() - db_start) * 1000, 2)  # ms
        
        if not result:
            db_status = "unhealthy"
            
    except Exception as e:
        logger.error(f"Database health check failed: {e}")
        db_status = "unhealthy"
        db_response_time = None
    
    # TODO: Add Redis health check when Redis client is implemented
    redis_status = "not_implemented"
    redis_response_time = None
    
    # Calculate total response time
    total_response_time = round((time.time() - start_time) * 1000, 2)  # ms
    
    # Determine overall status
    overall_status = "healthy"
    if db_status != "healthy":
        overall_status = "unhealthy"
    
    return {
        "status": overall_status,
        "timestamp": datetime.now(timezone.utc).isoformat(),
        "version": settings.app_version,
        "environment": settings.environment,
        "response_time_ms": total_response_time,
        "dependencies": {
            "database": {
                "status": db_status,
                "response_time_ms": db_response_time,
            },
            "redis": {
                "status": redis_status,
                "response_time_ms": redis_response_time,
            },
        },
        "system": {
            "uptime_seconds": time.time(),  # Will be replaced with actual uptime
        },
    }


@router.get("/ready", status_code=status.HTTP_200_OK)
async def readiness_check(
    db: AsyncSession = Depends(get_db_session)
) -> Dict[str, Any]:
    """
    Readiness check endpoint.
    
    Checks if the application is ready to serve traffic.
    Used by Kubernetes readiness probes.
    
    Args:
        db: Database session dependency
        
    Returns:
        dict: Readiness status
    """
    try:
        # Check database connectivity
        await db.execute("SELECT 1")
        
        return {
            "status": "ready",
            "timestamp": datetime.now(timezone.utc).isoformat(),
        }
        
    except Exception as e:
        logger.error(f"Readiness check failed: {e}")
        return {
            "status": "not_ready",
            "timestamp": datetime.now(timezone.utc).isoformat(),
            "error": "Database connection failed",
        }


@router.get("/live", status_code=status.HTTP_200_OK)
async def liveness_check() -> Dict[str, Any]:
    """
    Liveness check endpoint.
    
    Simple check to verify the application is running.
    Used by Kubernetes liveness probes.
    
    Returns:
        dict: Liveness status
    """
    return {
        "status": "alive",
        "timestamp": datetime.now(timezone.utc).isoformat(),
    }
