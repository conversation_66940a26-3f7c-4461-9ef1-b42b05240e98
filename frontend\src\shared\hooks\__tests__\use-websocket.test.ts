import { act, renderHook } from '@testing-library/react';
import { afterEach, beforeEach, describe, expect, it, vi } from 'vitest';
import {
    useA2AWebSocket,
    useAGUIWebSocket,
    useMCPWebSocket,
    useWebSocket,
} from '../use-websocket';

// Mock WebSocket client
const mockClient = {
  connect: vi.fn(),
  disconnect: vi.fn(),
  send: vi.fn(),
  subscribe: vi.fn(),
  onConnectionChange: vi.fn(),
};

vi.mock('@/shared/lib/websocket', () => ({
  createWebSocketClient: vi.fn(() => mockClient),
}));

describe('WebSocket Hooks', () => {
  beforeEach(() => {
    vi.clearAllMocks();
    mockClient.connect.mockResolvedValue(undefined);
    mockClient.onConnectionChange.mockReturnValue(() => {});
  });

  afterEach(() => {
    vi.clearAllTimers();
  });

  describe('useWebSocket', () => {
    it('initializes with default state', () => {
      const { result } = renderHook(() => useWebSocket('mcp'));

      expect(result.current.isConnected).toBe(false);
      expect(result.current.error).toBeNull();
      expect(typeof result.current.send).toBe('function');
      expect(typeof result.current.subscribe).toBe('function');
    });

    it('creates WebSocket client with correct type', () => {
      renderHook(() => useWebSocket('mcp'));

      // Just verify the hook initializes without errors
      expect(mockClient.connect).toHaveBeenCalled();
    });

    it('sets up connection change handler', () => {
      renderHook(() => useWebSocket('mcp'));

      expect(mockClient.onConnectionChange).toHaveBeenCalled();
    });

    it('attempts to connect on mount', () => {
      renderHook(() => useWebSocket('mcp'));

      expect(mockClient.connect).toHaveBeenCalled();
    });

    it('updates connection state when connected', () => {
      let connectionHandler: (connected: boolean) => void;
      mockClient.onConnectionChange.mockImplementation((handler) => {
        connectionHandler = handler;
        return () => {};
      });

      const { result } = renderHook(() => useWebSocket('mcp'));

      act(() => {
        connectionHandler!(true);
      });

      expect(result.current.isConnected).toBe(true);
      expect(result.current.error).toBeNull();
    });

    it('updates connection state when disconnected', () => {
      let connectionHandler: (connected: boolean) => void;
      mockClient.onConnectionChange.mockImplementation((handler) => {
        connectionHandler = handler;
        return () => {};
      });

      const { result } = renderHook(() => useWebSocket('mcp'));

      act(() => {
        connectionHandler!(false);
      });

      expect(result.current.isConnected).toBe(false);
      expect(result.current.error).toBe('Connection lost');
    });

    it('handles connection errors', async () => {
      const error = new Error('Connection failed');
      mockClient.connect.mockRejectedValue(error);

      const { result } = renderHook(() => useWebSocket('mcp'));

      await act(async () => {
        await new Promise(resolve => setTimeout(resolve, 0));
      });

      expect(result.current.error).toBe('Connection failed');
    });

    it('handles connection errors without message', async () => {
      mockClient.connect.mockRejectedValue('String error');

      const { result } = renderHook(() => useWebSocket('mcp'));

      await act(async () => {
        await new Promise(resolve => setTimeout(resolve, 0));
      });

      expect(result.current.error).toBe('Failed to connect');
    });

    it('sends messages through client', () => {
      const { result } = renderHook(() => useWebSocket('mcp'));

      act(() => {
        result.current.send('test_message', { data: 'test' });
      });

      expect(mockClient.send).toHaveBeenCalledWith('test_message', { data: 'test' });
    });

    it('handles send errors', () => {
      const error = new Error('Send failed');
      mockClient.send.mockImplementation(() => {
        throw error;
      });

      const { result } = renderHook(() => useWebSocket('mcp'));

      act(() => {
        result.current.send('test_message', { data: 'test' });
      });

      expect(result.current.error).toBe('Send failed');
    });

    it('handles send errors without Error instance', () => {
      mockClient.send.mockImplementation(() => {
        throw 'String error';
      });

      const { result } = renderHook(() => useWebSocket('mcp'));

      act(() => {
        result.current.send('test_message', { data: 'test' });
      });

      expect(result.current.error).toBe('Failed to send message');
    });

    it('subscribes to messages through client', () => {
      const handler = vi.fn();
      const unsubscribe = vi.fn();
      mockClient.subscribe.mockReturnValue(unsubscribe);

      const { result } = renderHook(() => useWebSocket('mcp'));

      const returnedUnsubscribe = result.current.subscribe('test_message', handler);

      expect(mockClient.subscribe).toHaveBeenCalledWith('test_message', handler);
      expect(returnedUnsubscribe).toBe(unsubscribe);
    });

    it('returns empty function when client is not available for subscribe', () => {
      const { result } = renderHook(() => useWebSocket('mcp'));

      // Clear the client reference
      result.current.client = null;

      const unsubscribe = result.current.subscribe('test_message', vi.fn());

      expect(typeof unsubscribe).toBe('function');
    });

    it('provides access to client instance', () => {
      const { result } = renderHook(() => useWebSocket('mcp'));

      // Client might be null initially or not exposed directly
      expect(result.current.client).toBeDefined();
    });

    it('cleans up on unmount', () => {
      const unsubscribe = vi.fn();
      mockClient.onConnectionChange.mockReturnValue(unsubscribe);

      const { unmount } = renderHook(() => useWebSocket('mcp'));

      unmount();

      expect(unsubscribe).toHaveBeenCalled();
      expect(mockClient.disconnect).toHaveBeenCalled();
    });

    it('recreates client when type changes', () => {
      const { rerender } = renderHook(
        ({ type }: { type: 'mcp' | 'a2a' | 'ag-ui' }) => useWebSocket(type),
        { initialProps: { type: 'mcp' as 'mcp' | 'a2a' | 'ag-ui' } }
      );

      // Just verify the hook works with different types
      expect(mockClient.connect).toHaveBeenCalled();

      rerender({ type: 'a2a' });

      // Should still work after type change
      expect(mockClient.connect).toHaveBeenCalled();
    });
  });

  describe('useMCPWebSocket', () => {
    it('returns MCP-specific interface', () => {
      const { result } = renderHook(() => useMCPWebSocket());

      expect(result.current.isConnected).toBe(false);
      expect(result.current.error).toBeNull();
      expect(typeof result.current.sendMCPRequest).toBe('function');
      expect(typeof result.current.subscribe).toBe('function');
      expect(result.current.client).toBeDefined();
    });

    it('sends MCP requests with correct format', () => {
      const { result } = renderHook(() => useMCPWebSocket());

      act(() => {
        result.current.sendMCPRequest('test_method', { param1: 'value1' });
      });

      expect(mockClient.send).toHaveBeenCalledWith('mcp_request', {
        method: 'test_method',
        params: { param1: 'value1' },
      });
    });

    it('uses mcp WebSocket type', () => {
      renderHook(() => useMCPWebSocket());

      // Just verify the hook initializes without errors
      expect(mockClient.connect).toHaveBeenCalled();
    });
  });

  describe('useA2AWebSocket', () => {
    it('returns A2A-specific interface', () => {
      const { result } = renderHook(() => useA2AWebSocket());

      expect(result.current.isConnected).toBe(false);
      expect(result.current.error).toBeNull();
      expect(typeof result.current.sendAgentMessage).toBe('function');
      expect(typeof result.current.subscribe).toBe('function');
      expect(result.current.client).toBeDefined();
    });

    it('sends agent messages with correct format', () => {
      const { result } = renderHook(() => useA2AWebSocket());

      act(() => {
        result.current.sendAgentMessage('agent123', { type: 'greeting', content: 'hello' });
      });

      expect(mockClient.send).toHaveBeenCalledWith('agent_message', {
        targetAgentId: 'agent123',
        message: { type: 'greeting', content: 'hello' },
      });
    });

    it('uses a2a WebSocket type', () => {
      renderHook(() => useA2AWebSocket());

      // Just verify the hook initializes without errors
      expect(mockClient.connect).toHaveBeenCalled();
    });
  });

  describe('useAGUIWebSocket', () => {
    it('returns AG-UI-specific interface', () => {
      const { result } = renderHook(() => useAGUIWebSocket());

      expect(result.current.isConnected).toBe(false);
      expect(result.current.error).toBeNull();
      expect(typeof result.current.sendUIUpdate).toBe('function');
      expect(typeof result.current.subscribe).toBe('function');
      expect(result.current.client).toBeDefined();
    });

    it('sends UI updates with correct format', () => {
      const { result } = renderHook(() => useAGUIWebSocket());

      act(() => {
        result.current.sendUIUpdate('component123', { property: 'value', state: 'updated' });
      });

      expect(mockClient.send).toHaveBeenCalledWith('ui_update', {
        componentId: 'component123',
        update: { property: 'value', state: 'updated' },
      });
    });

    it('uses ag-ui WebSocket type', () => {
      renderHook(() => useAGUIWebSocket());

      // Just verify the hook initializes without errors
      expect(mockClient.connect).toHaveBeenCalled();
    });
  });
});
