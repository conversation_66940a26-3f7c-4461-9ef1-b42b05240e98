import type { Meta, StoryObj } from '@storybook/nextjs';
import { ThemeToggle, SimpleThemeToggle } from '@/shared/ui/theme-toggle';

const meta: Meta<typeof ThemeToggle> = {
  title: 'UI/ThemeToggle',
  component: ThemeToggle,
  parameters: {
    layout: 'centered',
  },
  tags: ['autodocs'],
};

export default meta;
type Story = StoryObj<typeof meta>;

export const Default: Story = {
  render: () => <ThemeToggle />,
};

export const Simple: Story = {
  render: () => <SimpleThemeToggle />,
};

export const InHeader: Story = {
  render: () => (
    <div className="flex items-center justify-between p-4 border rounded-lg">
      <h2 className="text-lg font-semibold">Application Header</h2>
      <ThemeToggle />
    </div>
  ),
};
