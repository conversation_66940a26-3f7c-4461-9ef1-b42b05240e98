"""
AG-UI service use cases.

This module contains use cases for AG-UI operations including
layout management, component updates, and session handling.
"""

import uuid
from datetime import datetime, timezone
from typing import Dict, List, Optional

from src.domain.entities.ag_ui import (
    AG<PERSON>Component,
    AGUIComponentCreate,
    AGUIComponentResponse,
    AGUICompo<PERSON><PERSON><PERSON><PERSON>,
    AGUIEvent,
    AGUILayout,
    AGUILayoutCreate,
    AGUILayoutResponse,
    AGUILayoutUpdate,
    AGUISession,
    AGUISessionResponse,
    AGUIStateUpdate,
    ComponentState,
    ComponentType,
    EventType,
)
from src.infrastructure.logging.setup import LoggerMixin


class AGUIService(LoggerMixin):
    """
    AG-UI service containing business logic for dynamic UI operations.
    
    Orchestrates AG-UI related use cases including layout management,
    component updates, and real-time session synchronization.
    """
    
    def __init__(self) -> None:
        """Initialize AG-UI service."""
        # In a real implementation, these would be injected dependencies
        self._layouts: Dict[uuid.UUID, AGUILayout] = {}
        self._sessions: Dict[uuid.UUID, AGUISession] = {}
        self._connection_sessions: Dict[str, uuid.UUID] = {}  # connection_id -> session_id
        self._events: Dict[uuid.UUID, List[AGUIEvent]] = {}
    
    async def create_layout(
        self,
        user_id: uuid.UUID,
        layout_data: AGUILayoutCreate
    ) -> AGUILayoutResponse:
        """
        Create new AG-UI layout.
        
        Args:
            user_id: User ID creating the layout
            layout_data: Layout creation data
            
        Returns:
            AGUILayoutResponse: Created layout information
        """
        # Create components
        components = []
        for comp_data in layout_data.components:
            component = AGUIComponent(
                id=f"comp_{uuid.uuid4().hex[:8]}",
                type=comp_data.type,
                parent_id=comp_data.parent_id,
                name=comp_data.name,
                label=comp_data.label,
                value=comp_data.value,
                properties=comp_data.properties,
                styles=comp_data.styles,
                events=comp_data.events,
                validation=comp_data.validation,
                metadata=comp_data.metadata,
            )
            components.append(component)
        
        # Create layout
        layout = AGUILayout(
            user_id=user_id,
            name=layout_data.name,
            description=layout_data.description,
            components=components,
            layout_config=layout_data.layout_config,
            metadata=layout_data.metadata,
        )
        
        # Store layout
        self._layouts[layout.id] = layout
        
        self.logger.info(f"Created AG-UI layout: {layout.id} for user: {user_id}")
        
        return AGUILayoutResponse(
            **layout.dict(),
            components=[
                AGUIComponentResponse(**comp.dict()) for comp in layout.components
            ]
        )
    
    async def get_layout(
        self,
        layout_id: uuid.UUID,
        user_id: uuid.UUID
    ) -> Optional[AGUILayoutResponse]:
        """
        Get AG-UI layout by ID.
        
        Args:
            layout_id: Layout ID
            user_id: User ID (for authorization)
            
        Returns:
            AGUILayoutResponse: Layout information or None if not found
        """
        layout = self._layouts.get(layout_id)
        
        if not layout:
            self.logger.warning(f"Layout not found: {layout_id}")
            return None
        
        # Check ownership
        if layout.user_id != user_id:
            self.logger.warning(f"Unauthorized layout access: {layout_id} by user: {user_id}")
            return None
        
        self.logger.debug(f"Retrieved layout: {layout_id}")
        
        return AGUILayoutResponse(
            **layout.dict(),
            components=[
                AGUIComponentResponse(**comp.dict()) for comp in layout.components
            ]
        )
    
    async def update_layout(
        self,
        layout_id: uuid.UUID,
        user_id: uuid.UUID,
        update_data: AGUILayoutUpdate
    ) -> Optional[AGUILayoutResponse]:
        """
        Update AG-UI layout.
        
        Args:
            layout_id: Layout ID
            user_id: User ID (for authorization)
            update_data: Update data
            
        Returns:
            AGUILayoutResponse: Updated layout information or None if not found
        """
        layout = self._layouts.get(layout_id)
        
        if not layout or layout.user_id != user_id:
            return None
        
        # Update fields
        if update_data.name:
            layout.name = update_data.name
        
        if update_data.description is not None:
            layout.description = update_data.description
        
        if update_data.layout_config:
            layout.layout_config.update(update_data.layout_config)
        
        if update_data.metadata:
            layout.metadata.update(update_data.metadata)
        
        layout.updated_at = datetime.now(timezone.utc)
        
        self.logger.info(f"Updated layout: {layout_id}")
        
        return AGUILayoutResponse(
            **layout.dict(),
            components=[
                AGUIComponentResponse(**comp.dict()) for comp in layout.components
            ]
        )
    
    async def delete_layout(
        self,
        layout_id: uuid.UUID,
        user_id: uuid.UUID
    ) -> bool:
        """
        Delete AG-UI layout.
        
        Args:
            layout_id: Layout ID
            user_id: User ID (for authorization)
            
        Returns:
            bool: True if deleted, False if not found
        """
        layout = self._layouts.get(layout_id)
        
        if not layout or layout.user_id != user_id:
            return False
        
        # Delete layout
        del self._layouts[layout_id]
        
        self.logger.info(f"Deleted layout: {layout_id}")
        return True
    
    async def list_user_layouts(
        self,
        user_id: uuid.UUID,
        skip: int = 0,
        limit: int = 100
    ) -> List[AGUILayoutResponse]:
        """
        List user's AG-UI layouts.
        
        Args:
            user_id: User ID
            skip: Number of layouts to skip
            limit: Maximum number of layouts to return
            
        Returns:
            List[AGUILayoutResponse]: List of user's layouts
        """
        user_layouts = [
            layout for layout in self._layouts.values()
            if layout.user_id == user_id
        ]
        
        # Sort by creation date (newest first)
        user_layouts.sort(key=lambda x: x.created_at, reverse=True)
        
        # Apply pagination
        paginated_layouts = user_layouts[skip:skip + limit]
        
        layouts = [
            AGUILayoutResponse(
                **layout.dict(),
                components=[
                    AGUIComponentResponse(**comp.dict()) for comp in layout.components
                ]
            )
            for layout in paginated_layouts
        ]
        
        self.logger.debug(f"Listed {len(layouts)} layouts for user: {user_id}")
        return layouts
    
    async def create_session(
        self,
        user_id: uuid.UUID,
        connection_id: str,
        layout_id: Optional[uuid.UUID] = None
    ) -> AGUISessionResponse:
        """
        Create new AG-UI session.
        
        Args:
            user_id: User ID
            connection_id: WebSocket connection ID
            layout_id: Optional layout ID to load
            
        Returns:
            AGUISessionResponse: Created session information
        """
        # Validate layout if provided
        if layout_id:
            layout = self._layouts.get(layout_id)
            if not layout or layout.user_id != user_id:
                raise ValueError("Invalid layout ID")
        
        # Create session
        session = AGUISession(
            user_id=user_id,
            layout_id=layout_id,
            connection_id=connection_id,
        )
        
        # Store session
        self._sessions[session.id] = session
        self._connection_sessions[connection_id] = session.id
        self._events[session.id] = []
        
        self.logger.info(f"Created AG-UI session: {session.id} for user: {user_id}")
        
        return AGUISessionResponse(**session.dict())
    
    async def get_session_by_connection(
        self,
        connection_id: str
    ) -> Optional[AGUISessionResponse]:
        """
        Get session by connection ID.
        
        Args:
            connection_id: WebSocket connection ID
            
        Returns:
            AGUISessionResponse: Session information or None if not found
        """
        session_id = self._connection_sessions.get(connection_id)
        if not session_id:
            return None
        
        session = self._sessions.get(session_id)
        if not session:
            return None
        
        return AGUISessionResponse(**session.dict())
    
    async def close_session(self, connection_id: str) -> bool:
        """
        Close AG-UI session.
        
        Args:
            connection_id: WebSocket connection ID
            
        Returns:
            bool: True if closed, False if not found
        """
        session_id = self._connection_sessions.get(connection_id)
        if not session_id:
            return False
        
        # Clean up session data
        if session_id in self._sessions:
            del self._sessions[session_id]
        if session_id in self._events:
            del self._events[session_id]
        del self._connection_sessions[connection_id]
        
        self.logger.info(f"Closed AG-UI session: {session_id}")
        return True
    
    async def handle_event(
        self,
        connection_id: str,
        event_data: dict
    ) -> Optional[AGUIStateUpdate]:
        """
        Handle AG-UI event from client.
        
        Args:
            connection_id: WebSocket connection ID
            event_data: Event data from client
            
        Returns:
            AGUIStateUpdate: State updates to send back or None
        """
        session_id = self._connection_sessions.get(connection_id)
        if not session_id:
            return None
        
        session = self._sessions.get(session_id)
        if not session:
            return None
        
        # Create event
        event = AGUIEvent(
            session_id=session_id,
            component_id=event_data.get("component_id", ""),
            event_type=EventType(event_data.get("event_type", "custom")),
            data=event_data.get("data", {}),
        )
        
        # Store event
        self._events[session_id].append(event)
        
        # Update session activity
        session.update_activity()
        
        # Process event and generate state updates
        state_update = await self._process_event(session, event)
        
        self.logger.debug(f"Handled event: {event.event_type} for session: {session_id}")
        
        return state_update
    
    async def _process_event(
        self,
        session: AGUISession,
        event: AGUIEvent
    ) -> Optional[AGUIStateUpdate]:
        """
        Process event and generate state updates.
        
        Args:
            session: AG-UI session
            event: Event to process
            
        Returns:
            AGUIStateUpdate: State updates or None
        """
        # Get current layout
        if not session.layout_id:
            return None
        
        layout = self._layouts.get(session.layout_id)
        if not layout:
            return None
        
        # Find component
        component = layout.get_component(event.component_id)
        if not component:
            return None
        
        # Process different event types
        component_updates = {}
        
        if event.event_type == EventType.CLICK:
            # Handle click events
            if component.type == ComponentType.BUTTON:
                # Button click - could trigger form submission, navigation, etc.
                component_updates[event.component_id] = AGUIComponentUpdate(
                    state=ComponentState.ACTIVE
                )
        
        elif event.event_type == EventType.CHANGE:
            # Handle value changes
            new_value = event.data.get("value")
            if new_value is not None:
                component.update_value(new_value)
                component_updates[event.component_id] = AGUIComponentUpdate(
                    value=new_value
                )
        
        elif event.event_type == EventType.INPUT:
            # Handle input events (real-time typing)
            new_value = event.data.get("value")
            if new_value is not None:
                component.update_value(new_value)
                component_updates[event.component_id] = AGUIComponentUpdate(
                    value=new_value
                )
        
        # Update session state
        session_state = {
            f"component_{event.component_id}": event.data
        }
        session.update_state(session_state)
        
        # Return state update if there are changes
        if component_updates:
            return AGUIStateUpdate(
                component_updates=component_updates,
                session_state=session_state
            )
        
        return None
    
    async def add_component_to_layout(
        self,
        layout_id: uuid.UUID,
        user_id: uuid.UUID,
        component_data: AGUIComponentCreate
    ) -> Optional[AGUIComponentResponse]:
        """
        Add component to existing layout.
        
        Args:
            layout_id: Layout ID
            user_id: User ID (for authorization)
            component_data: Component creation data
            
        Returns:
            AGUIComponentResponse: Created component or None if failed
        """
        layout = self._layouts.get(layout_id)
        
        if not layout or layout.user_id != user_id:
            return None
        
        # Create component
        component = AGUIComponent(
            id=f"comp_{uuid.uuid4().hex[:8]}",
            type=component_data.type,
            parent_id=component_data.parent_id,
            name=component_data.name,
            label=component_data.label,
            value=component_data.value,
            properties=component_data.properties,
            styles=component_data.styles,
            events=component_data.events,
            validation=component_data.validation,
            metadata=component_data.metadata,
        )
        
        # Add to layout
        layout.add_component(component)
        
        self.logger.info(f"Added component {component.id} to layout {layout_id}")
        
        return AGUIComponentResponse(**component.dict())
    
    async def update_component(
        self,
        layout_id: uuid.UUID,
        component_id: str,
        user_id: uuid.UUID,
        update_data: AGUIComponentUpdate
    ) -> Optional[AGUIComponentResponse]:
        """
        Update component in layout.
        
        Args:
            layout_id: Layout ID
            component_id: Component ID
            user_id: User ID (for authorization)
            update_data: Component update data
            
        Returns:
            AGUIComponentResponse: Updated component or None if failed
        """
        layout = self._layouts.get(layout_id)
        
        if not layout or layout.user_id != user_id:
            return None
        
        # Update component
        updates = {}
        if update_data.value is not None:
            updates["value"] = update_data.value
        if update_data.state is not None:
            updates["state"] = update_data.state
        if update_data.properties is not None:
            updates["properties"] = update_data.properties
        if update_data.styles is not None:
            updates["styles"] = update_data.styles
        if update_data.metadata is not None:
            updates["metadata"] = update_data.metadata
        
        success = layout.update_component(component_id, updates)
        
        if success:
            component = layout.get_component(component_id)
            if component:
                self.logger.info(f"Updated component {component_id} in layout {layout_id}")
                return AGUIComponentResponse(**component.dict())
        
        return None
    
    async def remove_component_from_layout(
        self,
        layout_id: uuid.UUID,
        component_id: str,
        user_id: uuid.UUID
    ) -> bool:
        """
        Remove component from layout.
        
        Args:
            layout_id: Layout ID
            component_id: Component ID
            user_id: User ID (for authorization)
            
        Returns:
            bool: True if removed, False if failed
        """
        layout = self._layouts.get(layout_id)
        
        if not layout or layout.user_id != user_id:
            return False
        
        success = layout.remove_component(component_id)
        
        if success:
            self.logger.info(f"Removed component {component_id} from layout {layout_id}")
        
        return success
