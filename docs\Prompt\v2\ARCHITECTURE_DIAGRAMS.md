# Lonors Architecture Diagrams

This document contains visual representations of the Lonors system architecture using Mermaid diagrams.

## System Architecture Overview

```mermaid
graph TD
    Client[Client Browser] --> Nginx[Nginx Reverse Proxy]

    subgraph "Frontend (React + TypeScript)"
        Nginx --> FE[React Application]
        FE --> Router[React Router]
        Router --> Pages[Pages Layer]
        Pages --> Widgets[Widgets Layer]
        Widgets --> Features[Features Layer]
        Features --> Entities[Entities Layer]
        Entities --> Shared[Shared Layer]

        Shared --> UI[UI Components]
        Shared --> Lib[Utility Functions]
        Shared --> API[API Client]

        API --> Axios[Axios HTTP Client]
    end

    subgraph "Backend (FastAPI + Python)"
        Nginx --> BE[FastAPI Application]

        BE --> Presentation[Presentation Layer]
        Presentation --> Application[Application Layer]
        Application --> Domain[Domain Layer]
        Domain --> Infrastructure[Infrastructure Layer]

        Presentation --> Routes[API Routes]
        Presentation --> Middleware[Middleware]

        Application --> UseCases[Use Cases]
        Application --> DTOs[Data Transfer Objects]

        Domain --> Entities[Domain Entities]
        Domain --> Repositories[Repository Interfaces]
        Domain --> Services[Domain Services]

        Infrastructure --> DBImpl[Database Implementation]
        Infrastructure --> CacheImpl[Cache Implementation]
        Infrastructure --> Security[Security Services]
    end

    subgraph "Infrastructure"
        DBImpl --> PostgreSQL[(PostgreSQL)]
        CacheImpl --> Redis[(Redis)]
        Security --> JWT[JWT Service]
    end

    style Client fill:#f9f9f9,stroke:#333,stroke-width:1px
    style Nginx fill:#00CED1,stroke:#333,stroke-width:1px
    style FE fill:#61DAFB,stroke:#333,stroke-width:1px
    style BE fill:#009688,stroke:#333,stroke-width:1px
    style PostgreSQL fill:#336791,stroke:#333,stroke-width:1px,color:white
    style Redis fill:#DC382D,stroke:#333,stroke-width:1px,color:white
```

## Data Flow Diagram

```mermaid
sequenceDiagram
    participant Client as Client Browser
    participant FE as Frontend (React)
    participant API as Backend API (FastAPI)
    participant Cache as Redis Cache
    participant DB as PostgreSQL Database

    Client->>FE: User Interaction

    alt Authentication Flow
        FE->>API: Login Request
        API->>DB: Validate Credentials
        DB->>API: User Data
        API->>Cache: Store Session
        API->>FE: JWT Token
        FE->>Client: Update UI (Authenticated)
    end

    alt Data Retrieval Flow
        FE->>API: Request Data
        API->>Cache: Check Cache

        alt Cache Hit
            Cache->>API: Return Cached Data
        else Cache Miss
            API->>DB: Query Database
            DB->>API: Return Data
            API->>Cache: Store in Cache
        end

        API->>FE: Return Data
        FE->>Client: Update UI
    end

    alt Real-time Updates
        Client->>FE: Subscribe to Updates
        FE->>API: WebSocket Connection
        API->>Cache: Subscribe to Channel

        loop Real-time Events
            DB->>Cache: Publish Event
            Cache->>API: Notify Event
            API->>FE: Push Update
            FE->>Client: Update UI
        end
    end

    alt Protocol Interaction (MCP)
        FE->>API: MCP Request
        API->>API: Process Context
        API->>DB: Store Context
        API->>FE: MCP Response
        FE->>Client: Update UI
    end
```

## Component Dependencies

```mermaid
graph TD
    subgraph "Frontend Dependencies"
        React[React 18+] --> ReactDOM[ReactDOM]
        React --> ReactRouter[React Router]
        React --> Zustand[Zustand State]
        React --> ReactQuery[TanStack Query]
        React --> ReactHookForm[React Hook Form]

        ReactHookForm --> Zod[Zod Validation]
        ReactQuery --> Axios[Axios HTTP Client]

        UI[UI Layer] --> TailwindCSS[Tailwind CSS]
        UI --> ShadCN[ShadCN UI]
        UI --> RadixUI[Radix UI Primitives]
        UI --> AnimeJS[Anime.js]

        ShadCN --> RadixUI

        Features[Features Layer] --> Entities[Entities Layer]
        Features --> Shared[Shared Layer]
        Shared --> UI
        Shared --> Lib[Utility Functions]
        Shared --> APIClient[API Client]

        APIClient --> Axios
        APIClient --> ReactQuery
    end

    subgraph "Backend Dependencies"
        FastAPI[FastAPI] --> Pydantic[Pydantic]
        FastAPI --> Uvicorn[Uvicorn ASGI Server]
        FastAPI --> SQLAlchemy[SQLAlchemy ORM]
        FastAPI --> Redis[Redis Client]

        SQLAlchemy --> AsyncPG[AsyncPG Driver]
        SQLAlchemy --> Alembic[Alembic Migrations]

        DI[Dependency Injection] --> Repositories[Repositories]
        DI --> Services[Services]

        Services --> Repositories
        Repositories --> SQLAlchemy

        Cache[Cache Service] --> Redis
        Auth[Auth Service] --> JWT[PyJWT]
        Auth --> Redis
    end

    style React fill:#61DAFB,stroke:#333,stroke-width:1px
    style FastAPI fill:#009688,stroke:#333,stroke-width:1px
    style TailwindCSS fill:#38B2AC,stroke:#333,stroke-width:1px
    style SQLAlchemy fill:#D71F00,stroke:#333,stroke-width:1px
```

## CI/CD Pipeline Visualization

```mermaid
graph TD
    Commit[Git Commit] --> GithubActions[GitHub Actions]

    subgraph "Continuous Integration"
        GithubActions --> Lint[Lint Code]
        Lint --> TypeCheck[Type Check]
        TypeCheck --> UnitTests[Unit Tests]
        UnitTests --> IntegrationTests[Integration Tests]
        IntegrationTests --> Coverage[Coverage Report]
        Coverage --> SecurityScan[Security Scan]
    end

    subgraph "Build Phase"
        SecurityScan --> BuildFrontend[Build Frontend]
        SecurityScan --> BuildBackend[Build Backend]
        BuildFrontend --> DockerFrontend[Docker Frontend Image]
        BuildBackend --> DockerBackend[Docker Backend Image]
        DockerFrontend --> ImageScan[Image Security Scan]
        DockerBackend --> ImageScan
    end

    subgraph "Test Environment"
        ImageScan --> DeployTest[Deploy to Test]
        DeployTest --> E2ETests[E2E Tests]
        E2ETests --> PerformanceTests[Performance Tests]
    end

    subgraph "Staging Deployment"
        PerformanceTests --> DeployStaging{Deploy to Staging?}
        DeployStaging -->|Yes| Staging[Deploy to Staging]
        DeployStaging -->|No| Skip[Skip Deployment]
        Staging --> StagingTests[Smoke Tests]
        StagingTests --> ApproveProduction{Approve Production?}
    end

    subgraph "Production Deployment"
        ApproveProduction -->|Yes| Production[Deploy to Production]
        ApproveProduction -->|No| Reject[Reject Deployment]
        Production --> BlueGreen[Blue-Green Deployment]
        BlueGreen --> HealthCheck[Health Checks]
        HealthCheck -->|Fail| Rollback[Automatic Rollback]
        HealthCheck -->|Pass| Complete[Deployment Complete]
        Complete --> Monitoring[Monitoring & Alerting]
    end

    style Commit fill:#f9f9f9,stroke:#333,stroke-width:1px
    style GithubActions fill:#2088FF,stroke:#333,stroke-width:1px,color:white
    style DeployStaging fill:#FFA500,stroke:#333,stroke-width:1px
    style ApproveProduction fill:#FFA500,stroke:#333,stroke-width:1px
    style Production fill:#00C853,stroke:#333,stroke-width:1px
    style Rollback fill:#FF5252,stroke:#333,stroke-width:1px
```

## Database Schema

```mermaid
erDiagram
    USERS {
        uuid id PK
        string email UK
        string username UK
        string full_name
        string hashed_password
        enum role
        enum status
        boolean is_verified
        datetime last_login
        datetime created_at
        datetime updated_at
    }

    MCP_MODELS {
        string id PK
        string name
        enum type
        string provider
        string version
        enum status
        int max_context_length
        json capabilities
        json metadata
    }

    MCP_CONTEXTS {
        uuid id PK
        uuid user_id FK
        enum type
        string title
        string description
        string model_id FK
        int max_length
        int current_length
        json metadata
        datetime created_at
        datetime updated_at
        datetime expires_at
    }

    MCP_MESSAGES {
        uuid id PK
        uuid context_id FK
        string role
        string content
        int content_length
        json metadata
        datetime created_at
    }

    AGUI_COMPONENTS {
        string id PK
        enum type
        string parent_id
        string name
        string label
        json value
        enum state
        json properties
        json styles
        json events
        json validation
        json metadata
        datetime created_at
        datetime updated_at
    }

    AGUI_LAYOUTS {
        uuid id PK
        uuid user_id FK
        string name
        string description
        json components
        json layout_config
        json metadata
        datetime created_at
        datetime updated_at
    }

    AGUI_SESSIONS {
        uuid id PK
        uuid user_id FK
        uuid layout_id FK
        string connection_id
        json state
        json metadata
        datetime created_at
        datetime last_activity
    }

    A2A_SERVICES {
        string id PK
        string name
        string version
        string description
        string host
        int port
        json endpoints
        json events
        enum status
        string health_check_url
        json metadata
        datetime registered_at
        datetime last_heartbeat
    }

    A2A_MESSAGES {
        string id PK
        string correlation_id
        enum type
        string source_service FK
        string target_service FK
        string method
        string event
        json payload
        json headers
        enum priority
        int timeout
        int retry_count
        int max_retries
        datetime created_at
        datetime expires_at
    }

    A2A_ROUTES {
        string id PK
        string source_pattern
        string target_pattern
        string method_pattern
        enum strategy
        int weight
        boolean enabled
        json metadata
        datetime created_at
    }

    USERS ||--o{ MCP_CONTEXTS : "creates"
    MCP_MODELS ||--o{ MCP_CONTEXTS : "used in"
    MCP_CONTEXTS ||--o{ MCP_MESSAGES : "contains"
    USERS ||--o{ AGUI_LAYOUTS : "creates"
    USERS ||--o{ AGUI_SESSIONS : "has"
    AGUI_LAYOUTS ||--o{ AGUI_SESSIONS : "used in"
    A2A_SERVICES ||--o{ A2A_MESSAGES : "sends/receives"
    A2A_ROUTES ||--o{ A2A_MESSAGES : "routes"
```

## Protocol Integration Architecture

```mermaid
graph TD
    subgraph "Protocol Integration"
        MCP[Model Context Protocol] --> MCPModels[MCP Models]
        MCP --> MCPContexts[MCP Contexts]
        MCP --> MCPMessages[MCP Messages]

        AGUI[AG-UI Protocol] --> AGUIComponents[AGUI Components]
        AGUI --> AGUILayouts[AGUI Layouts]
        AGUI --> AGUIEvents[AGUI Events]
        AGUI --> AGUISessions[AGUI Sessions]

        A2A[A2A Protocol] --> A2AServices[A2A Services]
        A2A --> A2AMessages[A2A Messages]
        A2A --> A2ARoutes[A2A Routes]
        A2A --> A2AConnections[A2A Connections]
    end

    subgraph "Frontend Integration"
        FE[Frontend Application] --> FEMCP[MCP Client]
        FE --> FEAGUI[AGUI Client]
        FE --> FEA2A[A2A Client]

        FEMCP --> MCPHooks[MCP React Hooks]
        FEAGUI --> AGUIComponents[AGUI React Components]
        FEA2A --> A2AHooks[A2A React Hooks]
    end

    subgraph "Backend Integration"
        BE[Backend Application] --> BEMCP[MCP Service]
        BE --> BEAGUI[AGUI Service]
        BE --> BEA2A[A2A Service]

        BEMCP --> MCPHandlers[MCP Request Handlers]
        BEAGUI --> AGUIHandlers[AGUI Request Handlers]
        BEA2A --> A2AHandlers[A2A Request Handlers]
    end

    FEMCP -.-> BEMCP
    FEAGUI -.-> BEAGUI
    FEA2A -.-> BEA2A

    style MCP fill:#FF9800,stroke:#333,stroke-width:1px
    style AGUI fill:#2196F3,stroke:#333,stroke-width:1px
    style A2A fill:#4CAF50,stroke:#333,stroke-width:1px
    style FE fill:#61DAFB,stroke:#333,stroke-width:1px
    style BE fill:#009688,stroke:#333,stroke-width:1px
```

## Frontend Feature Slice Design Architecture

```mermaid
graph TD
    subgraph "Feature Slice Design Layers"
        App[App Layer] --> Pages[Pages Layer]
        Pages --> Widgets[Widgets Layer]
        Widgets --> Features[Features Layer]
        Features --> Entities[Entities Layer]
        Entities --> Shared[Shared Layer]
    end

    subgraph "App Layer"
        App --> Providers[Global Providers]
        App --> Router[Routing Configuration]
        App --> Store[Global Store]
        App --> ErrorBoundary[Error Boundaries]
    end

    subgraph "Pages Layer"
        Pages --> HomePage[Home Page]
        Pages --> AuthPages[Auth Pages]
        Pages --> DashboardPage[Dashboard Page]
        Pages --> ProtocolPages[Protocol Pages]
        Pages --> ProfilePage[Profile Page]
    end

    subgraph "Widgets Layer"
        Widgets --> Header[Header Widget]
        Widgets --> Sidebar[Sidebar Widget]
        Widgets --> Footer[Footer Widget]
        Widgets --> DashboardWidgets[Dashboard Widgets]
    end

    subgraph "Features Layer"
        Features --> Auth[Authentication Feature]
        Features --> UserManagement[User Management Feature]
        Features --> AgentManagement[Agent Management Feature]
        Features --> ProtocolIntegration[Protocol Integration Feature]
    end

    subgraph "Entities Layer"
        Entities --> User[User Entity]
        Entities --> Agent[Agent Entity]
        Entities --> Protocol[Protocol Entity]
        Entities --> Session[Session Entity]
    end

    subgraph "Shared Layer"
        Shared --> UI[UI Components]
        Shared --> Lib[Utility Functions]
        Shared --> API[API Client]
        Shared --> Hooks[Custom Hooks]
        Shared --> Types[Type Definitions]
    end

    style App fill:#FF9800,stroke:#333,stroke-width:1px
    style Pages fill:#2196F3,stroke:#333,stroke-width:1px
    style Widgets fill:#4CAF50,stroke:#333,stroke-width:1px
    style Features fill:#9C27B0,stroke:#333,stroke-width:1px
    style Entities fill:#E91E63,stroke:#333,stroke-width:1px
    style Shared fill:#607D8B,stroke:#333,stroke-width:1px
```

## Backend Clean Architecture

```mermaid
graph TD
    subgraph "Clean Architecture Layers"
        Presentation[Presentation Layer] --> Application[Application Layer]
        Application --> Domain[Domain Layer]
        Presentation --> Domain
        Application --> Infrastructure[Infrastructure Layer]
        Presentation --> Infrastructure
    end

    subgraph "Domain Layer"
        Domain --> Entities[Domain Entities]
        Domain --> Repositories[Repository Interfaces]
        Domain --> Services[Domain Services]
        Domain --> ValueObjects[Value Objects]
        Domain --> Exceptions[Domain Exceptions]
    end

    subgraph "Application Layer"
        Application --> UseCases[Use Cases]
        Application --> DTOs[Data Transfer Objects]
        Application --> Interfaces[Application Interfaces]
        Application --> Events[Application Events]
    end

    subgraph "Infrastructure Layer"
        Infrastructure --> Database[Database Implementation]
        Infrastructure --> Cache[Cache Implementation]
        Infrastructure --> External[External Services]
        Infrastructure --> Security[Security Services]
        Infrastructure --> Logging[Logging Services]
    end

    subgraph "Presentation Layer"
        Presentation --> Controllers[API Controllers]
        Presentation --> Middleware[Middleware]
        Presentation --> Schemas[API Schemas]
        Presentation --> Validation[Request Validation]
    end

    style Domain fill:#FF5722,stroke:#333,stroke-width:1px
    style Application fill:#03A9F4,stroke:#333,stroke-width:1px
    style Infrastructure fill:#8BC34A,stroke:#333,stroke-width:1px
    style Presentation fill:#9C27B0,stroke:#333,stroke-width:1px
```

## Deployment Architecture

```mermaid
graph TD
    subgraph "Development Environment"
        DevDocker[Docker Compose] --> DevFrontend[Frontend Container]
        DevDocker --> DevBackend[Backend Container]
        DevDocker --> DevDB[PostgreSQL Container]
        DevDocker --> DevRedis[Redis Container]

        DevFrontend --> DevVolume[Source Code Volume]
        DevBackend --> DevVolume
    end

    subgraph "Staging Environment"
        StagingK8s[Kubernetes Cluster] --> StagingNS[Staging Namespace]
        StagingNS --> StagingFrontend[Frontend Deployment]
        StagingNS --> StagingBackend[Backend Deployment]
        StagingNS --> StagingDB[PostgreSQL StatefulSet]
        StagingNS --> StagingRedis[Redis StatefulSet]
        StagingNS --> StagingIngress[Ingress Controller]

        StagingFrontend --> StagingFrontendSvc[Frontend Service]
        StagingBackend --> StagingBackendSvc[Backend Service]
        StagingDB --> StagingDBSvc[DB Service]
        StagingRedis --> StagingRedisSvc[Redis Service]

        StagingIngress --> StagingFrontendSvc
        StagingIngress --> StagingBackendSvc
    end

    subgraph "Production Environment"
        ProdK8s[Kubernetes Cluster] --> ProdNS[Production Namespace]
        ProdNS --> ProdFrontend[Frontend Deployment]
        ProdNS --> ProdBackend[Backend Deployment]
        ProdNS --> ProdDB[PostgreSQL StatefulSet]
        ProdNS --> ProdRedis[Redis StatefulSet]
        ProdNS --> ProdIngress[Ingress Controller]

        ProdFrontend --> BlueGreen[Blue-Green Deployment]
        ProdBackend --> BlueGreen

        BlueGreen --> ProdFrontendSvc[Frontend Service]
        BlueGreen --> ProdBackendSvc[Backend Service]
        ProdDB --> ProdDBSvc[DB Service]
        ProdRedis --> ProdRedisSvc[Redis Service]

        ProdIngress --> ProdFrontendSvc
        ProdIngress --> ProdBackendSvc

        ProdDB --> DBReplica[DB Read Replicas]
        ProdRedis --> RedisCluster[Redis Cluster]
    end

    style DevDocker fill:#2196F3,stroke:#333,stroke-width:1px
    style StagingK8s fill:#FF9800,stroke:#333,stroke-width:1px
    style ProdK8s fill:#4CAF50,stroke:#333,stroke-width:1px
    style BlueGreen fill:#9C27B0,stroke:#333,stroke-width:1px
```
