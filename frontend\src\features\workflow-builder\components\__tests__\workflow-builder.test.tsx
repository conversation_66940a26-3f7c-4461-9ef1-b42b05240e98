import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import { describe, expect, it, vi, beforeEach } from 'vitest';
import { WorkflowBuilder } from '../workflow-builder';
import { WorkflowNode, WorkflowEdge, NodeType } from '../../types';
import { AgentType, AgentStatus } from '@/shared/types';

// Mock ReactFlow
vi.mock('reactflow', () => ({
  ReactFlowProvider: ({ children }: { children: React.ReactNode }) => <div>{children}</div>,
  useReactFlow: () => ({
    screenToFlowPosition: vi.fn(() => ({ x: 100, y: 100 })),
  }),
}));

// Mock the canvas and node library components
vi.mock('../canvas/workflow-canvas', () => ({
  default: ({ onDrop, onNodesChange, onEdgesChange }: any) => (
    <div data-testid="workflow-canvas">
      <button 
        onClick={() => onNodesChange([{ type: 'add', id: 'test-node' }])}
        data-testid="add-node"
      >
        Add Node
      </button>
      <button 
        onClick={() => onEdgesChange([{ type: 'add', id: 'test-edge' }])}
        data-testid="add-edge"
      >
        Add Edge
      </button>
      <div 
        data-testid="drop-zone"
        onDrop={(e) => onDrop?.(e)}
        onDragOver={(e) => e.preventDefault()}
      >
        Drop Zone
      </div>
    </div>
  ),
}));

vi.mock('../node-library/node-library', () => ({
  default: ({ onNodeDragStart }: any) => (
    <div data-testid="node-library">
      <div 
        draggable
        onDragStart={(e) => {
          e.dataTransfer.setData('application/reactflow', JSON.stringify({
            template: {
              id: 'test-template',
              type: NodeType.AGENT,
              name: 'Test Agent',
              description: 'Test agent description',
              defaultData: {
                agent: {
                  agent_type: AgentType.CHAT,
                  name: 'Test Agent',
                  description: 'Test agent',
                }
              }
            }
          }));
          onNodeDragStart(e, {});
        }}
        data-testid="draggable-node"
      >
        Draggable Node
      </div>
    </div>
  ),
}));

// Mock UI components
vi.mock('@/shared/ui/resizable', () => ({
  ResizablePanelGroup: ({ children }: { children: React.ReactNode }) => <div>{children}</div>,
  ResizablePanel: ({ children }: { children: React.ReactNode }) => <div>{children}</div>,
  ResizableHandle: () => <div data-testid="resize-handle" />,
}));

vi.mock('@/shared/ui/use-toast', () => ({
  useToast: () => ({
    toast: vi.fn(),
  }),
}));

describe('WorkflowBuilder', () => {
  const mockProps = {
    workflowId: 'test-workflow',
    onSave: vi.fn(),
    onExecute: vi.fn(),
    onNodesChange: vi.fn(),
    onEdgesChange: vi.fn(),
  };

  beforeEach(() => {
    vi.clearAllMocks();
  });

  it('renders workflow builder with canvas and node library', () => {
    render(<WorkflowBuilder {...mockProps} />);
    
    expect(screen.getByTestId('workflow-canvas')).toBeInTheDocument();
    expect(screen.getByTestId('node-library')).toBeInTheDocument();
  });

  it('handles node changes correctly', async () => {
    render(<WorkflowBuilder {...mockProps} />);
    
    const addNodeButton = screen.getByTestId('add-node');
    fireEvent.click(addNodeButton);
    
    await waitFor(() => {
      expect(mockProps.onNodesChange).toHaveBeenCalled();
    });
  });

  it('handles edge changes correctly', async () => {
    render(<WorkflowBuilder {...mockProps} />);
    
    const addEdgeButton = screen.getByTestId('add-edge');
    fireEvent.click(addEdgeButton);
    
    await waitFor(() => {
      expect(mockProps.onEdgesChange).toHaveBeenCalled();
    });
  });

  it('handles drag and drop from node library', async () => {
    render(<WorkflowBuilder {...mockProps} />);
    
    const draggableNode = screen.getByTestId('draggable-node');
    const dropZone = screen.getByTestId('drop-zone');
    
    // Simulate drag start
    fireEvent.dragStart(draggableNode);
    
    // Simulate drop
    const dropEvent = new DragEvent('drop', {
      bubbles: true,
      cancelable: true,
      dataTransfer: new DataTransfer(),
    });
    
    dropEvent.dataTransfer?.setData('application/reactflow', JSON.stringify({
      template: {
        id: 'test-template',
        type: NodeType.AGENT,
        name: 'Test Agent',
        description: 'Test agent description',
        defaultData: {
          agent: {
            agent_type: AgentType.CHAT,
            name: 'Test Agent',
            description: 'Test agent',
          }
        }
      }
    }));
    
    fireEvent(dropZone, dropEvent);
    
    await waitFor(() => {
      expect(mockProps.onNodesChange).toHaveBeenCalled();
    });
  });

  it('handles save workflow', () => {
    const { rerender } = render(<WorkflowBuilder {...mockProps} />);
    
    // Add some nodes first
    const initialNodes: WorkflowNode[] = [{
      id: 'node-1',
      type: NodeType.AGENT,
      position: { x: 100, y: 100 },
      data: {
        agent: {
          id: 'agent-1',
          name: 'Test Agent',
          agent_type: AgentType.CHAT,
          status: AgentStatus.IDLE,
        }
      }
    }];
    
    rerender(<WorkflowBuilder {...mockProps} initialNodes={initialNodes} />);
    
    // The save functionality would be triggered from the canvas component
    // This test verifies the prop is passed correctly
    expect(mockProps.onSave).toBeDefined();
  });

  it('handles workflow execution', () => {
    render(<WorkflowBuilder {...mockProps} />);
    
    // The execute functionality would be triggered from the canvas component
    // This test verifies the prop is passed correctly
    expect(mockProps.onExecute).toBeDefined();
  });

  it('handles readonly mode', () => {
    render(<WorkflowBuilder {...mockProps} readonly />);
    
    expect(screen.getByTestId('workflow-canvas')).toBeInTheDocument();
    expect(screen.getByTestId('node-library')).toBeInTheDocument();
  });

  it('initializes with provided nodes and edges', () => {
    const initialNodes: WorkflowNode[] = [{
      id: 'node-1',
      type: NodeType.AGENT,
      position: { x: 100, y: 100 },
      data: {
        agent: {
          id: 'agent-1',
          name: 'Test Agent',
          agent_type: AgentType.CHAT,
          status: AgentStatus.IDLE,
        }
      }
    }];
    
    const initialEdges: WorkflowEdge[] = [{
      id: 'edge-1',
      source: 'node-1',
      target: 'node-2',
    }];
    
    render(
      <WorkflowBuilder 
        {...mockProps} 
        initialNodes={initialNodes}
        initialEdges={initialEdges}
      />
    );
    
    expect(screen.getByTestId('workflow-canvas')).toBeInTheDocument();
  });

  it('handles invalid drop data gracefully', async () => {
    render(<WorkflowBuilder {...mockProps} />);
    
    const dropZone = screen.getByTestId('drop-zone');
    
    // Simulate drop with invalid data
    const dropEvent = new DragEvent('drop', {
      bubbles: true,
      cancelable: true,
      dataTransfer: new DataTransfer(),
    });
    
    dropEvent.dataTransfer?.setData('application/reactflow', 'invalid-json');
    
    fireEvent(dropZone, dropEvent);
    
    // Should not crash and should not call onNodesChange
    await waitFor(() => {
      expect(mockProps.onNodesChange).not.toHaveBeenCalled();
    });
  });
});
