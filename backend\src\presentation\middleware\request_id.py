"""
Request ID middleware for request tracing.

This middleware adds a unique request ID to each request for better
logging and debugging capabilities.
"""

import uuid
from typing import Callable

from fastapi import Request, Response
from starlette.middleware.base import BaseHTTPMiddleware

import structlog


class RequestIDMiddleware(BaseHTTPMiddleware):
    """
    Middleware to add unique request ID to each request.
    
    Adds a unique request ID header and sets it in the logging context
    for better request tracing and debugging.
    """
    
    def __init__(self, app, header_name: str = "X-Request-ID"):
        """
        Initialize request ID middleware.
        
        Args:
            app: FastAPI application instance
            header_name: Header name for request ID
        """
        super().__init__(app)
        self.header_name = header_name
    
    async def dispatch(self, request: Request, call_next: Callable) -> Response:
        """
        Process request and add request ID.
        
        Args:
            request: Incoming request
            call_next: Next middleware/endpoint
            
        Returns:
            Response: Response with request ID header
        """
        # Generate or extract request ID
        request_id = request.headers.get(self.header_name) or str(uuid.uuid4())
        
        # Set request ID in logging context
        structlog.contextvars.bind_contextvars(request_id=request_id)
        
        # Add request ID to request state
        request.state.request_id = request_id
        
        # Process request
        response = await call_next(request)
        
        # Add request ID to response headers
        response.headers[self.header_name] = request_id
        
        # Clear logging context
        structlog.contextvars.clear_contextvars()
        
        return response
