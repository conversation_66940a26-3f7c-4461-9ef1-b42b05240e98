version: '3.8'

services:
  # PostgreSQL Database with optimizations
  postgres:
    image: postgres:15-alpine
    container_name: lonors-postgres-dev
    environment:
      POSTGRES_DB: lonors_db
      POSTGRES_USER: lonors_user
      POSTGRES_PASSWORD: lonors_password
      POSTGRES_INITDB_ARGS: "--encoding=UTF8 --locale=C"
      # Performance optimizations for development
      POSTGRES_SHARED_PRELOAD_LIBRARIES: "pg_stat_statements"
    ports:
      - "5432:5432"
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./infra/database/init:/docker-entrypoint-initdb.d:ro
    networks:
      - lonors-network
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U lonors_user -d lonors_db"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s
    restart: unless-stopped
    deploy:
      resources:
        limits:
          cpus: '1'
          memory: 1G
        reservations:
          cpus: '0.25'
          memory: 256M
    command: >
      postgres
      -c shared_preload_libraries=pg_stat_statements
      -c pg_stat_statements.track=all
      -c max_connections=100
      -c shared_buffers=256MB
      -c effective_cache_size=1GB
      -c maintenance_work_mem=64MB
      -c checkpoint_completion_target=0.9
      -c wal_buffers=16MB
      -c default_statistics_target=100

  # DragonflyDB - High-performance Redis alternative
  dragonflydb:
    image: docker.dragonflydb.io/dragonflydb/dragonfly:v1.15.1
    container_name: lonors-dragonfly-dev
    ports:
      - "6379:6379"
    volumes:
      - dragonfly_data:/data
    networks:
      - lonors-network
    healthcheck:
      test: ["CMD", "redis-cli", "-p", "6379", "ping"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 30s
    restart: unless-stopped
    deploy:
      resources:
        limits:
          cpus: '0.5'
          memory: 512M
        reservations:
          cpus: '0.1'
          memory: 128M
    command: >
      dragonfly
      --logtostderr
      --alsologtostderr=false
      --port=6379
      --maxmemory=256mb
      --cache_mode=true

  # Backend API (Python FastAPI) with enhanced configuration
  backend:
    build:
      context: ./backend
      dockerfile: Dockerfile
      target: development
      args:
        BUILDKIT_INLINE_CACHE: 1
    container_name: lonors-backend-dev
    environment:
      ENVIRONMENT: development
      DATABASE_URL: postgresql+asyncpg://lonors_user:lonors_password@postgres:5432/lonors_db
      REDIS_URL: redis://dragonflydb:6379
      JWT_SECRET: your-jwt-secret-change-in-production
      JWT_ALGORITHM: HS256
      JWT_EXPIRES_IN: 7d
      PORT: 3001
      HOST: 0.0.0.0
      CORS_ORIGINS: "http://localhost:5500,http://127.0.0.1:5500,http://frontend:5500"
      LOG_LEVEL: debug
      PYTHONPATH: /app
      # Development optimizations
      WATCHFILES_FORCE_POLLING: "false"
      RELOAD_DIRS: "/app/src"
    ports:
      - "3001:3001"
    volumes:
      - ./backend:/app:cached
      - backend_cache:/app/.cache
      - backend_logs:/app/logs
      # Bind mount for hot reload optimization
      - ./backend/src:/app/src:cached
      - ./backend/tests:/app/tests:cached
    depends_on:
      postgres:
        condition: service_healthy
      dragonflydb:
        condition: service_healthy
    networks:
      - lonors-network
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:3001/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 60s
    restart: unless-stopped
    deploy:
      resources:
        limits:
          cpus: '1'
          memory: 1G
        reservations:
          cpus: '0.25'
          memory: 256M
    command: uv run fastapi dev src/main.py --host 0.0.0.0 --port 3001 --reload

  # Frontend (Next.js) with enhanced configuration
  frontend:
    build:
      context: ./frontend
      dockerfile: Dockerfile
      target: development
      args:
        BUILDKIT_INLINE_CACHE: 1
        NEXT_PUBLIC_API_URL: http://localhost:3001
        NEXT_PUBLIC_APP_NAME: Lonors
        NEXT_PUBLIC_APP_VERSION: 1.0.0
        NEXT_PUBLIC_ENVIRONMENT: development
    container_name: lonors-frontend-dev
    environment:
      NEXT_PUBLIC_API_URL: http://localhost:3001
      NEXT_PUBLIC_APP_NAME: Lonors
      NEXT_PUBLIC_APP_VERSION: 1.0.0
      NEXT_PUBLIC_ENVIRONMENT: development
      NODE_ENV: development
      # Development optimizations
      NEXT_DEV_SERVER_HOST: 0.0.0.0
      NEXT_DEV_SERVER_PORT: 5500
      CHOKIDAR_USEPOLLING: "false"
    ports:
      - "5500:5500"
    volumes:
      - ./frontend:/app:cached
      - frontend_node_modules:/app/node_modules
      - frontend_cache:/app/.next
      # Bind mount for hot reload optimization
      - ./frontend/src:/app/src:cached
      - ./frontend/public:/app/public:cached
      - ./frontend/package.json:/app/package.json:ro
      - ./frontend/pnpm-lock.yaml:/app/pnpm-lock.yaml:ro
      - ./frontend/next.config.js:/app/next.config.js:ro
      - ./frontend/tailwind.config.js:/app/tailwind.config.js:ro
      - ./frontend/tsconfig.json:/app/tsconfig.json:ro
    depends_on:
      backend:
        condition: service_healthy
    networks:
      - lonors-network
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:5500"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 60s
    restart: unless-stopped
    deploy:
      resources:
        limits:
          cpus: '1'
          memory: 1G
        reservations:
          cpus: '0.25'
          memory: 256M
    command: pnpm dev


  # Monitoring and observability (optional for development)
  prometheus:
    image: prom/prometheus:v2.45.0
    container_name: lonors-prometheus-dev
    ports:
      - "9090:9090"
    volumes:
      - ./infra/monitoring/prometheus.yml:/etc/prometheus/prometheus.yml:ro
      - prometheus_data:/prometheus
    networks:
      - lonors-network
    command:
      - '--config.file=/etc/prometheus/prometheus.yml'
      - '--storage.tsdb.path=/prometheus'
      - '--web.console.libraries=/etc/prometheus/console_libraries'
      - '--web.console.templates=/etc/prometheus/consoles'
      - '--storage.tsdb.retention.time=200h'
      - '--web.enable-lifecycle'
    restart: unless-stopped
    profiles:
      - monitoring

volumes:
  # Database volumes
  postgres_data:
    driver: local
  dragonfly_data:
    driver: local

  # Application cache volumes
  backend_cache:
    driver: local
  backend_logs:
    driver: local
  frontend_node_modules:
    driver: local
  frontend_cache:
    driver: local

  # Monitoring volumes
  prometheus_data:
    driver: local

networks:
  lonors-network:
    driver: bridge
    ipam:
      config:
        - subnet: **********/16
          gateway: **********
    driver_opts:
      com.docker.network.bridge.name: lonors-dev-bridge
