"""
Authentication dependencies for FastAPI.

This module provides dependency functions for authentication
and authorization in FastAPI endpoints.
"""

from typing import Optional

from fastapi import Depends, HTTPException, status
from fastapi.security import HTTPAuthorizationCredentials, HTTPBearer
from jose import JW<PERSON>rror

from src.infrastructure.security.jwt import jwt_manager
from src.infrastructure.logging.setup import get_logger

logger = get_logger(__name__)

# HTTP Bearer token scheme
security = HTTPBearer(auto_error=False)


class AuthenticationError(HTTPException):
    """Authentication error exception."""
    
    def __init__(self, detail: str = "Authentication failed"):
        super().__init__(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail=detail,
            headers={"WWW-Authenticate": "Bearer"},
        )


class AuthorizationError(HTTPException):
    """Authorization error exception."""
    
    def __init__(self, detail: str = "Insufficient permissions"):
        super().__init__(
            status_code=status.HTTP_403_FORBIDDEN,
            detail=detail,
        )


async def get_current_user_optional(
    credentials: Optional[HTTPAuthorizationCredentials] = Depends(security)
) -> Optional[dict]:
    """
    Get current user from JWT token (optional).
    
    Args:
        credentials: HTTP authorization credentials
        
    Returns:
        dict: User information or None if not authenticated
    """
    if not credentials:
        return None
    
    try:
        payload = jwt_manager.verify_token(credentials.credentials, "access")
        return {
            "user_id": payload["user_id"],
            "subject": payload["sub"],
            "token_id": payload.get("jti"),
        }
    except JWTError as e:
        logger.warning(f"Token verification failed: {e}")
        return None


async def get_current_user(
    credentials: Optional[HTTPAuthorizationCredentials] = Depends(security)
) -> dict:
    """
    Get current user from JWT token (required).
    
    Args:
        credentials: HTTP authorization credentials
        
    Returns:
        dict: User information
        
    Raises:
        AuthenticationError: If authentication fails
    """
    if not credentials:
        raise AuthenticationError("Missing authentication token")
    
    try:
        payload = jwt_manager.verify_token(credentials.credentials, "access")
        return {
            "user_id": payload["user_id"],
            "subject": payload["sub"],
            "token_id": payload.get("jti"),
        }
    except JWTError as e:
        logger.warning(f"Authentication failed: {e}")
        raise AuthenticationError("Invalid or expired token")


async def get_refresh_token_payload(
    credentials: Optional[HTTPAuthorizationCredentials] = Depends(security)
) -> dict:
    """
    Get refresh token payload.
    
    Args:
        credentials: HTTP authorization credentials
        
    Returns:
        dict: Refresh token payload
        
    Raises:
        AuthenticationError: If token is invalid
    """
    if not credentials:
        raise AuthenticationError("Missing refresh token")
    
    try:
        payload = jwt_manager.verify_token(credentials.credentials, "refresh")
        return {
            "user_id": payload["user_id"],
            "subject": payload["sub"],
            "token_id": payload.get("jti"),
        }
    except JWTError as e:
        logger.warning(f"Refresh token verification failed: {e}")
        raise AuthenticationError("Invalid or expired refresh token")


def require_permissions(*required_permissions: str):
    """
    Decorator to require specific permissions.
    
    Args:
        required_permissions: Required permission names
        
    Returns:
        Dependency function
    """
    async def permission_dependency(
        current_user: dict = Depends(get_current_user)
    ) -> dict:
        """Check if user has required permissions."""
        # TODO: Implement permission checking logic
        # This would typically check user roles/permissions from database
        
        # For now, just return the user
        # In a real implementation, you would:
        # 1. Get user from database with their roles/permissions
        # 2. Check if user has all required permissions
        # 3. Raise AuthorizationError if not
        
        logger.debug(f"Permission check for user {current_user['user_id']}: {required_permissions}")
        return current_user
    
    return permission_dependency


def require_roles(*required_roles: str):
    """
    Decorator to require specific roles.
    
    Args:
        required_roles: Required role names
        
    Returns:
        Dependency function
    """
    async def role_dependency(
        current_user: dict = Depends(get_current_user)
    ) -> dict:
        """Check if user has required roles."""
        # TODO: Implement role checking logic
        # This would typically check user roles from database
        
        logger.debug(f"Role check for user {current_user['user_id']}: {required_roles}")
        return current_user
    
    return role_dependency


# Common permission dependencies
require_admin = require_roles("admin")
require_user = require_roles("user", "admin")
require_moderator = require_roles("moderator", "admin")
