"""
A2A Protocol domain entities.

This module defines the domain entities for A2A (Application-to-Application)
protocol implementation including service discovery and message routing.
"""

import uuid
from datetime import datetime, timezone
from enum import Enum
from typing import Any, Dict, List, Optional

from pydantic import BaseModel, Field


class MessageType(str, Enum):
    """A2A message types."""
    REQUEST = "request"
    RESPONSE = "response"
    EVENT = "event"
    COMMAND = "command"
    ERROR = "error"


class MessagePriority(str, Enum):
    """Message priority levels."""
    LOW = "low"
    NORMAL = "normal"
    HIGH = "high"
    CRITICAL = "critical"


class ServiceStatus(str, Enum):
    """Service status enumeration."""
    ONLINE = "online"
    OFFLINE = "offline"
    BUSY = "busy"
    MAINTENANCE = "maintenance"
    ERROR = "error"


class RoutingStrategy(str, Enum):
    """Message routing strategies."""
    ROUND_ROBIN = "round_robin"
    LEAST_CONNECTIONS = "least_connections"
    WEIGHTED = "weighted"
    RANDOM = "random"
    STICKY_SESSION = "sticky_session"


class A2AService(BaseModel):
    """
    A2A Service entity.
    
    Represents a service registered in the A2A protocol system.
    """
    
    id: str = Field(..., description="Unique service identifier")
    name: str = Field(..., description="Human-readable service name")
    version: str = Field(..., description="Service version")
    description: Optional[str] = Field(None, description="Service description")
    host: str = Field(..., description="Service host address")
    port: int = Field(..., description="Service port")
    endpoints: List[str] = Field(default_factory=list, description="Available endpoints")
    events: List[str] = Field(default_factory=list, description="Published events")
    status: ServiceStatus = Field(default=ServiceStatus.ONLINE, description="Service status")
    health_check_url: Optional[str] = Field(None, description="Health check endpoint")
    metadata: Dict[str, Any] = Field(default_factory=dict, description="Service metadata")
    registered_at: datetime = Field(default_factory=lambda: datetime.now(timezone.utc))
    last_heartbeat: datetime = Field(default_factory=lambda: datetime.now(timezone.utc))
    
    class Config:
        """Pydantic configuration."""
        use_enum_values = True
        json_encoders = {
            datetime: lambda v: v.isoformat(),
        }
    
    def update_heartbeat(self) -> None:
        """Update last heartbeat timestamp."""
        self.last_heartbeat = datetime.now(timezone.utc)
    
    def is_healthy(self, timeout_seconds: int = 300) -> bool:
        """
        Check if service is healthy based on heartbeat.
        
        Args:
            timeout_seconds: Heartbeat timeout in seconds
            
        Returns:
            bool: True if service is healthy
        """
        if self.status == ServiceStatus.OFFLINE:
            return False
        
        time_since_heartbeat = datetime.now(timezone.utc) - self.last_heartbeat
        return time_since_heartbeat.total_seconds() < timeout_seconds


class A2AMessage(BaseModel):
    """
    A2A Message entity.
    
    Represents a message in the A2A protocol system.
    """
    
    id: str = Field(default_factory=lambda: f"msg_{uuid.uuid4().hex[:12]}", description="Unique message identifier")
    correlation_id: Optional[str] = Field(None, description="Correlation ID for request/response")
    type: MessageType = Field(..., description="Message type")
    source_service: str = Field(..., description="Source service ID")
    target_service: Optional[str] = Field(None, description="Target service ID")
    method: Optional[str] = Field(None, description="Method/endpoint to call")
    event: Optional[str] = Field(None, description="Event name for event messages")
    payload: Dict[str, Any] = Field(default_factory=dict, description="Message payload")
    headers: Dict[str, str] = Field(default_factory=dict, description="Message headers")
    priority: MessagePriority = Field(default=MessagePriority.NORMAL, description="Message priority")
    timeout: Optional[int] = Field(None, description="Timeout in seconds")
    retry_count: int = Field(default=0, description="Current retry count")
    max_retries: int = Field(default=3, description="Maximum retry attempts")
    created_at: datetime = Field(default_factory=lambda: datetime.now(timezone.utc))
    expires_at: Optional[datetime] = Field(None, description="Message expiration time")
    
    class Config:
        """Pydantic configuration."""
        use_enum_values = True
        json_encoders = {
            datetime: lambda v: v.isoformat(),
        }
    
    def is_expired(self) -> bool:
        """Check if message is expired."""
        if self.expires_at is None:
            return False
        return datetime.now(timezone.utc) > self.expires_at
    
    def can_retry(self) -> bool:
        """Check if message can be retried."""
        return self.retry_count < self.max_retries
    
    def increment_retry(self) -> None:
        """Increment retry count."""
        self.retry_count += 1


class A2ARoute(BaseModel):
    """
    A2A Route entity.
    
    Represents a routing rule in the A2A system.
    """
    
    id: str = Field(default_factory=lambda: f"route_{uuid.uuid4().hex[:8]}", description="Route identifier")
    source_pattern: str = Field(..., description="Source service pattern")
    target_pattern: str = Field(..., description="Target service pattern")
    method_pattern: Optional[str] = Field(None, description="Method pattern")
    strategy: RoutingStrategy = Field(default=RoutingStrategy.ROUND_ROBIN, description="Routing strategy")
    weight: int = Field(default=1, description="Route weight")
    enabled: bool = Field(default=True, description="Whether route is enabled")
    metadata: Dict[str, Any] = Field(default_factory=dict, description="Route metadata")
    created_at: datetime = Field(default_factory=lambda: datetime.now(timezone.utc))
    
    class Config:
        """Pydantic configuration."""
        use_enum_values = True
        json_encoders = {
            datetime: lambda v: v.isoformat(),
        }


class A2AConnection(BaseModel):
    """
    A2A Connection entity.
    
    Represents an active connection between services.
    """
    
    id: str = Field(default_factory=lambda: f"conn_{uuid.uuid4().hex[:8]}", description="Connection identifier")
    source_service: str = Field(..., description="Source service ID")
    target_service: str = Field(..., description="Target service ID")
    connection_type: str = Field(..., description="Connection type (http, websocket, etc.)")
    status: str = Field(default="active", description="Connection status")
    established_at: datetime = Field(default_factory=lambda: datetime.now(timezone.utc))
    last_activity: datetime = Field(default_factory=lambda: datetime.now(timezone.utc))
    message_count: int = Field(default=0, description="Number of messages sent")
    error_count: int = Field(default=0, description="Number of errors")
    metadata: Dict[str, Any] = Field(default_factory=dict, description="Connection metadata")
    
    class Config:
        """Pydantic configuration."""
        json_encoders = {
            datetime: lambda v: v.isoformat(),
        }
    
    def update_activity(self) -> None:
        """Update last activity timestamp."""
        self.last_activity = datetime.now(timezone.utc)
    
    def increment_message_count(self) -> None:
        """Increment message count."""
        self.message_count += 1
        self.update_activity()
    
    def increment_error_count(self) -> None:
        """Increment error count."""
        self.error_count += 1
        self.update_activity()


# Request/Response schemas
class A2AServiceRegister(BaseModel):
    """Schema for service registration."""
    
    id: str = Field(..., description="Service identifier")
    name: str = Field(..., description="Service name")
    version: str = Field(..., description="Service version")
    description: Optional[str] = Field(None, description="Service description")
    host: str = Field(..., description="Service host")
    port: int = Field(..., description="Service port")
    endpoints: List[str] = Field(default_factory=list, description="Available endpoints")
    events: List[str] = Field(default_factory=list, description="Published events")
    health_check_url: Optional[str] = Field(None, description="Health check URL")
    metadata: Dict[str, Any] = Field(default_factory=dict, description="Service metadata")


class A2AServiceUpdate(BaseModel):
    """Schema for service updates."""
    
    name: Optional[str] = Field(None, description="Service name")
    version: Optional[str] = Field(None, description="Service version")
    description: Optional[str] = Field(None, description="Service description")
    endpoints: Optional[List[str]] = Field(None, description="Available endpoints")
    events: Optional[List[str]] = Field(None, description="Published events")
    status: Optional[ServiceStatus] = Field(None, description="Service status")
    metadata: Optional[Dict[str, Any]] = Field(None, description="Service metadata")


class A2AMessageSend(BaseModel):
    """Schema for sending messages."""
    
    type: MessageType = Field(..., description="Message type")
    target_service: Optional[str] = Field(None, description="Target service ID")
    method: Optional[str] = Field(None, description="Method to call")
    event: Optional[str] = Field(None, description="Event name")
    payload: Dict[str, Any] = Field(default_factory=dict, description="Message payload")
    headers: Dict[str, str] = Field(default_factory=dict, description="Message headers")
    priority: MessagePriority = Field(default=MessagePriority.NORMAL, description="Message priority")
    timeout: Optional[int] = Field(None, description="Timeout in seconds")
    correlation_id: Optional[str] = Field(None, description="Correlation ID")


class A2ARouteCreate(BaseModel):
    """Schema for creating routes."""
    
    source_pattern: str = Field(..., description="Source service pattern")
    target_pattern: str = Field(..., description="Target service pattern")
    method_pattern: Optional[str] = Field(None, description="Method pattern")
    strategy: RoutingStrategy = Field(default=RoutingStrategy.ROUND_ROBIN, description="Routing strategy")
    weight: int = Field(default=1, description="Route weight")
    metadata: Dict[str, Any] = Field(default_factory=dict, description="Route metadata")


# Response schemas
class A2AServiceResponse(BaseModel):
    """Schema for service response."""
    
    id: str
    name: str
    version: str
    description: Optional[str]
    host: str
    port: int
    endpoints: List[str]
    events: List[str]
    status: ServiceStatus
    health_check_url: Optional[str]
    metadata: Dict[str, Any]
    registered_at: datetime
    last_heartbeat: datetime
    is_healthy: bool
    
    class Config:
        """Pydantic configuration."""
        from_attributes = True
        use_enum_values = True
        json_encoders = {
            datetime: lambda v: v.isoformat(),
        }


class A2AMessageResponse(BaseModel):
    """Schema for message response."""
    
    id: str
    correlation_id: Optional[str]
    type: MessageType
    source_service: str
    target_service: Optional[str]
    method: Optional[str]
    event: Optional[str]
    payload: Dict[str, Any]
    headers: Dict[str, str]
    priority: MessagePriority
    timeout: Optional[int]
    retry_count: int
    max_retries: int
    created_at: datetime
    expires_at: Optional[datetime]
    is_expired: bool
    can_retry: bool
    
    class Config:
        """Pydantic configuration."""
        from_attributes = True
        use_enum_values = True
        json_encoders = {
            datetime: lambda v: v.isoformat(),
        }


class A2ARouteResponse(BaseModel):
    """Schema for route response."""
    
    id: str
    source_pattern: str
    target_pattern: str
    method_pattern: Optional[str]
    strategy: RoutingStrategy
    weight: int
    enabled: bool
    metadata: Dict[str, Any]
    created_at: datetime
    
    class Config:
        """Pydantic configuration."""
        from_attributes = True
        use_enum_values = True
        json_encoders = {
            datetime: lambda v: v.isoformat(),
        }


class A2AConnectionResponse(BaseModel):
    """Schema for connection response."""
    
    id: str
    source_service: str
    target_service: str
    connection_type: str
    status: str
    established_at: datetime
    last_activity: datetime
    message_count: int
    error_count: int
    metadata: Dict[str, Any]
    
    class Config:
        """Pydantic configuration."""
        from_attributes = True
        json_encoders = {
            datetime: lambda v: v.isoformat(),
        }
