"""
Simple test server to verify basic FastAPI functionality.
"""

from fastapi import Fast<PERSON><PERSON>
from fastapi.middleware.cors import CORSMiddleware
import uvicorn

# Create FastAPI app
app = FastAPI(
    title="Lonors Test Server",
    description="Simple test server for development environment validation",
    version="1.0.0"
)

# Add CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=["http://localhost:5500", "http://127.0.0.1:5500"],
    allow_credentials=True,
    allow_methods=["GET", "POST", "PUT", "DELETE", "PATCH", "OPTIONS"],
    allow_headers=["*"],
)

@app.get("/")
async def root():
    """Root endpoint."""
    return {
        "message": "Lonors Test Server",
        "status": "running",
        "version": "1.0.0"
    }

@app.get("/health")
async def health_check():
    """Health check endpoint."""
    return {
        "status": "healthy",
        "service": "backend",
        "port": 3001
    }

@app.get("/api/v1/test")
async def test_endpoint():
    """Test API endpoint."""
    return {
        "message": "API is working",
        "endpoint": "/api/v1/test"
    }

if __name__ == "__main__":
    uvicorn.run(
        "test_server:app",
        host="0.0.0.0",
        port=3001,
        reload=True,
        log_level="info"
    )
