"""
User repository interface.

This module defines the repository interface for user data access
following the repository pattern and dependency inversion principle.
"""

import uuid
from abc import ABC, abstractmethod

from src.domain.entities.user import User


class UserRepositoryInterface(ABC):
    """
    User repository interface.

    Defines the contract for user data access operations
    without coupling to specific database implementations.
    """

    @abstractmethod
    async def create(self, user: User) -> User:
        """
        Create a new user.

        Args:
            user: User entity to create

        Returns:
            User: Created user entity

        Raises:
            ValueError: If user already exists
        """
        pass

    @abstractmethod
    async def get_by_id(self, user_id: str) -> User | None:
        """
        Get user by ID.

        Args:
            user_id: User ID

        Returns:
            User: User entity or None if not found
        """
        pass

    @abstractmethod
    async def get_by_email(self, email: str) -> User | None:
        """
        Get user by email address.

        Args:
            email: Email address

        Returns:
            User: User entity or None if not found
        """
        pass

    @abstractmethod
    async def get_by_username(self, username: str) -> User | None:
        """
        Get user by username.

        Args:
            username: Username

        Returns:
            User: User entity or None if not found
        """
        pass

    @abstractmethod
    async def update(self, user: User) -> User:
        """
        Update existing user.

        Args:
            user: User entity with updated data

        Returns:
            User: Updated user entity

        Raises:
            ValueError: If user not found
        """
        pass

    @abstractmethod
    async def delete(self, user_id: uuid.UUID) -> bool:
        """
        Delete user by ID.

        Args:
            user_id: User ID

        Returns:
            bool: True if deleted, False if not found
        """
        pass

    @abstractmethod
    async def list_users(
        self, skip: int = 0, limit: int = 100, active_only: bool = False
    ) -> list[User]:
        """
        List users with pagination.

        Args:
            skip: Number of users to skip
            limit: Maximum number of users to return
            active_only: Whether to return only active users

        Returns:
            List[User]: List of user entities
        """
        pass

    @abstractmethod
    async def count_users(self, active_only: bool = False) -> int:
        """
        Count total number of users.

        Args:
            active_only: Whether to count only active users

        Returns:
            int: Total number of users
        """
        pass

    @abstractmethod
    async def email_exists(self, email: str) -> bool:
        """
        Check if email already exists.

        Args:
            email: Email address to check

        Returns:
            bool: True if email exists
        """
        pass

    @abstractmethod
    async def username_exists(self, username: str) -> bool:
        """
        Check if username already exists.

        Args:
            username: Username to check

        Returns:
            bool: True if username exists
        """
        pass
