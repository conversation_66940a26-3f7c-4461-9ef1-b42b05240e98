import { render, screen } from '@testing-library/react';
import { beforeEach, describe, expect, it, vi } from 'vitest';
import RootLayout from '../layout';

// Mock Next.js font imports
vi.mock('next/font/google', () => ({
  Inter: () => ({
    className: 'inter-font',
    style: { fontFamily: 'Inter, sans-serif' },
    variable: '--font-inter',
  }),
  JetBrains_Mono: () => ({
    className: 'jetbrains-mono-font',
    style: { fontFamily: 'JetBrains Mono, monospace' },
    variable: '--font-jetbrains-mono',
  }),
}));

vi.mock('next/font/local', () => ({
  default: () => ({
    className: 'geist-font',
    style: { fontFamily: 'Geist, sans-serif' },
    variable: '--font-geist',
  }),
}));

// Mock the providers
vi.mock('../providers', () => ({
  Providers: ({ children }: { children: React.ReactNode }) => (
    <div data-testid="providers-wrapper">{children}</div>
  ),
}));

// Mock the metadata
vi.mock('../layout', async (importOriginal) => {
  const actual = await importOriginal() as any;
  return {
    ...actual,
    metadata: {
      title: 'Lonors AI Agent Platform',
      description: 'Build, orchestrate, and deploy AI agents with no-code visual workflows',
    },
  };
});

describe('RootLayout', () => {
  beforeEach(() => {
    vi.clearAllMocks();
  });

  it('renders children within providers wrapper', () => {
    const testContent = 'Test Content';

    render(
      <RootLayout>
        <div data-testid="test-child">{testContent}</div>
      </RootLayout>
    );

    expect(screen.getByTestId('providers-wrapper')).toBeInTheDocument();
    expect(screen.getByTestId('test-child')).toBeInTheDocument();
    expect(screen.getByText(testContent)).toBeInTheDocument();
  });

  it('applies correct HTML structure and attributes', () => {
    render(
      <RootLayout>
        <div>Test</div>
      </RootLayout>
    );

    // Check HTML lang attribute
    const htmlElement = document.documentElement;
    expect(htmlElement).toHaveAttribute('lang', 'en');
  });

  it('includes proper font class structure', () => {
    const { container } = render(
      <RootLayout>
        <div>Test</div>
      </RootLayout>
    );

    // Check that the container has proper structure
    expect(container.firstChild).toBeInTheDocument();
  });

  it('renders with proper accessibility attributes', () => {
    render(
      <RootLayout>
        <main role="main">Main Content</main>
      </RootLayout>
    );

    const mainElement = screen.getByRole('main');
    expect(mainElement).toBeInTheDocument();
    expect(mainElement).toHaveTextContent('Main Content');
  });

  it('handles empty children gracefully', () => {
    const { container } = render(
      <RootLayout>
        {null}
      </RootLayout>
    );

    expect(container).toBeInTheDocument();
    expect(screen.getByTestId('providers-wrapper')).toBeInTheDocument();
  });

  it('supports multiple children', () => {
    render(
      <RootLayout>
        <div data-testid="child-1">First Child</div>
        <div data-testid="child-2">Second Child</div>
        <span data-testid="child-3">Third Child</span>
      </RootLayout>
    );

    expect(screen.getByTestId('child-1')).toBeInTheDocument();
    expect(screen.getByTestId('child-2')).toBeInTheDocument();
    expect(screen.getByTestId('child-3')).toBeInTheDocument();
    expect(screen.getByText('First Child')).toBeInTheDocument();
    expect(screen.getByText('Second Child')).toBeInTheDocument();
    expect(screen.getByText('Third Child')).toBeInTheDocument();
  });

  it('maintains proper component hierarchy', () => {
    render(
      <RootLayout>
        <div data-testid="test-content">Test Content</div>
      </RootLayout>
    );

    const providersWrapper = screen.getByTestId('providers-wrapper');
    const testContent = screen.getByTestId('test-content');

    // Verify hierarchy: Providers > children
    expect(providersWrapper).toContainElement(testContent);
  });

  it('handles complex nested children', () => {
    render(
      <RootLayout>
        <div data-testid="parent">
          <div data-testid="nested-child">
            <span data-testid="deeply-nested">Deep Content</span>
          </div>
        </div>
      </RootLayout>
    );

    expect(screen.getByTestId('parent')).toBeInTheDocument();
    expect(screen.getByTestId('nested-child')).toBeInTheDocument();
    expect(screen.getByTestId('deeply-nested')).toBeInTheDocument();
    expect(screen.getByText('Deep Content')).toBeInTheDocument();
  });

  it('preserves component props and attributes', () => {
    render(
      <RootLayout>
        <div
          data-testid="component-with-props"
          className="custom-class"
          id="custom-id"
          aria-label="Custom Label"
        >
          Content with props
        </div>
      </RootLayout>
    );

    const component = screen.getByTestId('component-with-props');
    expect(component).toHaveClass('custom-class');
    expect(component).toHaveAttribute('id', 'custom-id');
    expect(component).toHaveAttribute('aria-label', 'Custom Label');
  });

  it('renders without errors when providers are available', () => {
    expect(() => {
      render(
        <RootLayout>
          <div>Test</div>
        </RootLayout>
      );
    }).not.toThrow();
  });

  it('maintains document structure integrity', () => {
    const { container } = render(
      <RootLayout>
        <div data-testid="content">Content</div>
      </RootLayout>
    );

    // Verify the basic DOM structure is maintained
    expect(container.firstChild).toBeInTheDocument();
    expect(screen.getByTestId('providers-wrapper')).toBeInTheDocument();
    expect(screen.getByTestId('content')).toBeInTheDocument();
  });

  it('handles React fragments as children', () => {
    render(
      <RootLayout>
        <>
          <div data-testid="fragment-child-1">Fragment Child 1</div>
          <div data-testid="fragment-child-2">Fragment Child 2</div>
        </>
      </RootLayout>
    );

    expect(screen.getByTestId('fragment-child-1')).toBeInTheDocument();
    expect(screen.getByTestId('fragment-child-2')).toBeInTheDocument();
  });
});
