"""
Simple FastAPI server for testing frontend-backend connectivity.
"""

import uvicorn
from fastapi import FastAPI, WebSocket
from fastapi.middleware.cors import CORSMiddleware

app = FastAPI(
    title="Lonors Backend Test Server",
    description="Simple test server for frontend-backend connectivity",
    version="1.0.0",
)

# Configure CORS
app.add_middleware(
    CORSMiddleware,
    allow_origins=[
        "http://localhost:5500",
        "http://127.0.0.1:5500",
        "http://************:5500",
        "ws://localhost:5500",
        "ws://127.0.0.1:5500",
        "ws://************:5500",
    ],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)


@app.get("/")
async def root():
    """Root endpoint."""
    return {"message": "Lonors Backend Test Server", "status": "running"}


@app.get("/health")
async def health_check():
    """Health check endpoint."""
    return {
        "status": "healthy",
        "service": "lonors-backend",
        "version": "1.0.0",
        "timestamp": "2024-01-01T00:00:00Z",
    }


@app.get("/api/health")
async def api_health_check():
    """API health check endpoint."""
    return {
        "status": "healthy",
        "service": "lonors-backend-api",
        "version": "1.0.0",
        "endpoints": {
            "health": "/health",
            "api_health": "/api/health",
            "websocket_mcp": "/ws/mcp",
            "websocket_a2a": "/ws/a2a",
            "websocket_ag_ui": "/ws/ag-ui",
        },
    }


@app.websocket("/ws/mcp")
async def websocket_mcp(websocket: WebSocket):
    """MCP WebSocket endpoint."""
    await websocket.accept()
    await websocket.send_json({
        "type": "connection",
        "protocol": "mcp",
        "status": "connected",
    })
    try:
        while True:
            data = await websocket.receive_json()
            await websocket.send_json({"type": "echo", "data": data})
    except Exception:
        pass


@app.websocket("/ws/a2a")
async def websocket_a2a(websocket: WebSocket):
    """A2A WebSocket endpoint."""
    await websocket.accept()
    await websocket.send_json({
        "type": "connection",
        "protocol": "a2a",
        "status": "connected",
    })
    try:
        while True:
            data = await websocket.receive_json()
            await websocket.send_json({"type": "echo", "data": data})
    except Exception:
        pass


@app.websocket("/ws/ag-ui")
async def websocket_ag_ui(websocket: WebSocket):
    """AG-UI WebSocket endpoint."""
    await websocket.accept()
    await websocket.send_json({
        "type": "connection",
        "protocol": "ag-ui",
        "status": "connected",
    })
    try:
        while True:
            data = await websocket.receive_json()
            await websocket.send_json({"type": "echo", "data": data})
    except Exception:
        pass


if __name__ == "__main__":
    uvicorn.run(app, host="0.0.0.0", port=3002)
