"""
AG-UI REST endpoints.

This module provides REST API endpoints for AG-UI layout management
and component operations.
"""

import uuid
from typing import List

from fastapi import APIRouter, Depends, HTTPException, Query, status

from src.application.use_cases.ag_ui_service import AGUIService
from src.domain.entities.ag_ui import (
    AGUIComponentCreate,
    AGUIComponentResponse,
    AGUIComponentUpdate,
    AGUILayoutCreate,
    AGUILayoutResponse,
    AGUILayoutUpdate,
    AGUISessionResponse,
)
from src.infrastructure.logging.setup import get_logger
from src.presentation.dependencies.auth import get_current_user

logger = get_logger(__name__)

router = APIRouter()


def get_ag_ui_service() -> AGUIService:
    """Get AG-UI service dependency."""
    return AGUIService()


@router.post("/layouts", response_model=AGUILayoutResponse, status_code=status.HTTP_201_CREATED)
async def create_layout(
    layout_data: AG<PERSON>LayoutCreate,
    current_user: dict = Depends(get_current_user),
    ag_ui_service: AGUIService = Depends(get_ag_ui_service)
) -> AGUILayoutResponse:
    """
    Create new AG-UI layout.
    
    Args:
        layout_data: Layout creation data
        current_user: Current authenticated user
        ag_ui_service: AG-UI service dependency
        
    Returns:
        AGUILayoutResponse: Created layout information
        
    Raises:
        HTTPException: If creation fails
    """
    try:
        user_id = uuid.UUID(current_user["user_id"])
        layout = await ag_ui_service.create_layout(user_id, layout_data)
        
        logger.info(f"Created AG-UI layout: {layout.id} for user: {user_id}")
        return layout
        
    except ValueError as e:
        logger.warning(f"Layout creation failed: {e}")
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=str(e)
        )
    except Exception as e:
        logger.error(f"Layout creation error: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to create layout"
        )


@router.get("/layouts", response_model=List[AGUILayoutResponse])
async def list_layouts(
    skip: int = Query(0, ge=0, description="Number of layouts to skip"),
    limit: int = Query(100, ge=1, le=1000, description="Maximum number of layouts to return"),
    current_user: dict = Depends(get_current_user),
    ag_ui_service: AGUIService = Depends(get_ag_ui_service)
) -> List[AGUILayoutResponse]:
    """
    List user's AG-UI layouts.
    
    Args:
        skip: Number of layouts to skip
        limit: Maximum number of layouts to return
        current_user: Current authenticated user
        ag_ui_service: AG-UI service dependency
        
    Returns:
        List[AGUILayoutResponse]: List of user's layouts
    """
    try:
        user_id = uuid.UUID(current_user["user_id"])
        layouts = await ag_ui_service.list_user_layouts(user_id, skip, limit)
        
        logger.debug(f"Listed {len(layouts)} layouts for user: {user_id}")
        return layouts
        
    except Exception as e:
        logger.error(f"List layouts error: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to list layouts"
        )


@router.get("/layouts/{layout_id}", response_model=AGUILayoutResponse)
async def get_layout(
    layout_id: uuid.UUID,
    current_user: dict = Depends(get_current_user),
    ag_ui_service: AGUIService = Depends(get_ag_ui_service)
) -> AGUILayoutResponse:
    """
    Get AG-UI layout by ID.
    
    Args:
        layout_id: Layout ID
        current_user: Current authenticated user
        ag_ui_service: AG-UI service dependency
        
    Returns:
        AGUILayoutResponse: Layout information
        
    Raises:
        HTTPException: If layout not found
    """
    try:
        user_id = uuid.UUID(current_user["user_id"])
        layout = await ag_ui_service.get_layout(layout_id, user_id)
        
        if not layout:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Layout not found"
            )
        
        logger.debug(f"Retrieved layout: {layout_id} for user: {user_id}")
        return layout
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Get layout error: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to retrieve layout"
        )


@router.put("/layouts/{layout_id}", response_model=AGUILayoutResponse)
async def update_layout(
    layout_id: uuid.UUID,
    update_data: AGUILayoutUpdate,
    current_user: dict = Depends(get_current_user),
    ag_ui_service: AGUIService = Depends(get_ag_ui_service)
) -> AGUILayoutResponse:
    """
    Update AG-UI layout.
    
    Args:
        layout_id: Layout ID
        update_data: Update data
        current_user: Current authenticated user
        ag_ui_service: AG-UI service dependency
        
    Returns:
        AGUILayoutResponse: Updated layout information
        
    Raises:
        HTTPException: If update fails
    """
    try:
        user_id = uuid.UUID(current_user["user_id"])
        layout = await ag_ui_service.update_layout(layout_id, user_id, update_data)
        
        if not layout:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Layout not found"
            )
        
        logger.info(f"Updated layout: {layout_id} for user: {user_id}")
        return layout
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Update layout error: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to update layout"
        )


@router.delete("/layouts/{layout_id}")
async def delete_layout(
    layout_id: uuid.UUID,
    current_user: dict = Depends(get_current_user),
    ag_ui_service: AGUIService = Depends(get_ag_ui_service)
) -> dict:
    """
    Delete AG-UI layout.
    
    Args:
        layout_id: Layout ID
        current_user: Current authenticated user
        ag_ui_service: AG-UI service dependency
        
    Returns:
        dict: Success message
        
    Raises:
        HTTPException: If deletion fails
    """
    try:
        user_id = uuid.UUID(current_user["user_id"])
        deleted = await ag_ui_service.delete_layout(layout_id, user_id)
        
        if not deleted:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Layout not found"
            )
        
        logger.info(f"Deleted layout: {layout_id} for user: {user_id}")
        return {"message": "Layout deleted successfully"}
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Delete layout error: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to delete layout"
        )


@router.post("/layouts/{layout_id}/components", response_model=AGUIComponentResponse, status_code=status.HTTP_201_CREATED)
async def add_component_to_layout(
    layout_id: uuid.UUID,
    component_data: AGUIComponentCreate,
    current_user: dict = Depends(get_current_user),
    ag_ui_service: AGUIService = Depends(get_ag_ui_service)
) -> AGUIComponentResponse:
    """
    Add component to layout.
    
    Args:
        layout_id: Layout ID
        component_data: Component creation data
        current_user: Current authenticated user
        ag_ui_service: AG-UI service dependency
        
    Returns:
        AGUIComponentResponse: Created component information
        
    Raises:
        HTTPException: If creation fails
    """
    try:
        user_id = uuid.UUID(current_user["user_id"])
        component = await ag_ui_service.add_component_to_layout(
            layout_id, user_id, component_data
        )
        
        if not component:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Layout not found"
            )
        
        logger.info(f"Added component {component.id} to layout {layout_id}")
        return component
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Add component error: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to add component"
        )


@router.put("/layouts/{layout_id}/components/{component_id}", response_model=AGUIComponentResponse)
async def update_component(
    layout_id: uuid.UUID,
    component_id: str,
    update_data: AGUIComponentUpdate,
    current_user: dict = Depends(get_current_user),
    ag_ui_service: AGUIService = Depends(get_ag_ui_service)
) -> AGUIComponentResponse:
    """
    Update component in layout.
    
    Args:
        layout_id: Layout ID
        component_id: Component ID
        update_data: Component update data
        current_user: Current authenticated user
        ag_ui_service: AG-UI service dependency
        
    Returns:
        AGUIComponentResponse: Updated component information
        
    Raises:
        HTTPException: If update fails
    """
    try:
        user_id = uuid.UUID(current_user["user_id"])
        component = await ag_ui_service.update_component(
            layout_id, component_id, user_id, update_data
        )
        
        if not component:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Layout or component not found"
            )
        
        logger.info(f"Updated component {component_id} in layout {layout_id}")
        return component
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Update component error: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to update component"
        )


@router.delete("/layouts/{layout_id}/components/{component_id}")
async def remove_component_from_layout(
    layout_id: uuid.UUID,
    component_id: str,
    current_user: dict = Depends(get_current_user),
    ag_ui_service: AGUIService = Depends(get_ag_ui_service)
) -> dict:
    """
    Remove component from layout.
    
    Args:
        layout_id: Layout ID
        component_id: Component ID
        current_user: Current authenticated user
        ag_ui_service: AG-UI service dependency
        
    Returns:
        dict: Success message
        
    Raises:
        HTTPException: If removal fails
    """
    try:
        user_id = uuid.UUID(current_user["user_id"])
        removed = await ag_ui_service.remove_component_from_layout(
            layout_id, component_id, user_id
        )
        
        if not removed:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Layout or component not found"
            )
        
        logger.info(f"Removed component {component_id} from layout {layout_id}")
        return {"message": "Component removed successfully"}
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Remove component error: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to remove component"
        )


@router.get("/sessions/current", response_model=AGUISessionResponse)
async def get_current_session(
    connection_id: str = Query(..., description="WebSocket connection ID"),
    ag_ui_service: AGUIService = Depends(get_ag_ui_service)
) -> AGUISessionResponse:
    """
    Get current AG-UI session by connection ID.
    
    Args:
        connection_id: WebSocket connection ID
        ag_ui_service: AG-UI service dependency
        
    Returns:
        AGUISessionResponse: Session information
        
    Raises:
        HTTPException: If session not found
    """
    try:
        session = await ag_ui_service.get_session_by_connection(connection_id)
        
        if not session:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Session not found"
            )
        
        logger.debug(f"Retrieved session for connection: {connection_id}")
        return session
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Get session error: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to retrieve session"
        )


@router.post("/templates/create-from-layout/{layout_id}")
async def create_template_from_layout(
    layout_id: uuid.UUID,
    template_name: str = Query(..., description="Template name"),
    current_user: dict = Depends(get_current_user),
    ag_ui_service: AGUIService = Depends(get_ag_ui_service)
) -> dict:
    """
    Create reusable template from existing layout.
    
    Args:
        layout_id: Source layout ID
        template_name: Name for the template
        current_user: Current authenticated user
        ag_ui_service: AG-UI service dependency
        
    Returns:
        dict: Template creation result
        
    Raises:
        HTTPException: If creation fails
    """
    try:
        user_id = uuid.UUID(current_user["user_id"])
        
        # Get source layout
        layout = await ag_ui_service.get_layout(layout_id, user_id)
        if not layout:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Source layout not found"
            )
        
        # Create template layout
        from src.domain.entities.ag_ui import AGUILayoutCreate
        
        template_data = AGUILayoutCreate(
            name=f"Template: {template_name}",
            description=f"Template created from layout: {layout.name}",
            components=[
                AGUIComponentCreate(
                    type=comp.type,
                    parent_id=comp.parent_id,
                    name=comp.name,
                    label=comp.label,
                    value=comp.value,
                    properties=comp.properties,
                    styles=comp.styles,
                    events=comp.events,
                    validation=comp.validation,
                    metadata={**comp.metadata, "is_template": True}
                )
                for comp in layout.components
            ],
            layout_config=layout.layout_config,
            metadata={
                **layout.metadata,
                "is_template": True,
                "source_layout_id": str(layout_id),
                "template_name": template_name
            }
        )
        
        template = await ag_ui_service.create_layout(user_id, template_data)
        
        logger.info(f"Created template {template.id} from layout {layout_id}")
        
        return {
            "message": "Template created successfully",
            "template_id": str(template.id),
            "template_name": template_name
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Create template error: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to create template"
        )
