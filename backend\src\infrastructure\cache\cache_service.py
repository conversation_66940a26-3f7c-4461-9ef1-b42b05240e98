"""High-level cache service for application caching needs."""

from typing import Any, Callable, Optional, TypeVar, Union
from datetime import timedelta
from functools import wraps
import hashlib

from src.infrastructure.cache.redis_client import RedisClient
from src.infrastructure.logging import Logger


T = TypeVar('T')


class CacheService:
    """Service for application-level caching operations."""
    
    # Cache key prefixes
    USER_PREFIX = "user"
    SESSION_PREFIX = "session"
    API_PREFIX = "api"
    RATE_LIMIT_PREFIX = "rate_limit"
    
    # Default expiration times
    DEFAULT_TTL = timedelta(hours=1)
    SESSION_TTL = timedelta(hours=24)
    API_TTL = timedelta(minutes=5)
    RATE_LIMIT_TTL = timedelta(minutes=1)
    
    def __init__(self, redis_client: RedisClient, logger: Logger) -> None:
        """Initialize cache service.
        
        Args:
            redis_client: Redis client instance
            logger: Logger instance
        """
        self._redis = redis_client
        self._logger = logger
        
    def _build_key(self, prefix: str, *parts: str) -> str:
        """Build a cache key from prefix and parts."""
        key_parts = [prefix] + [str(part) for part in parts]
        return ":".join(key_parts)
        
    async def get_user(self, user_id: str) -> Optional[dict]:
        """Get user from cache."""
        key = self._build_key(self.USER_PREFIX, user_id)
        return await self._redis.get(key)
        
    async def set_user(
        self,
        user_id: str,
        user_data: dict,
        ttl: Optional[timedelta] = None
    ) -> bool:
        """Cache user data."""
        key = self._build_key(self.USER_PREFIX, user_id)
        return await self._redis.set(key, user_data, ttl or self.DEFAULT_TTL)
        
    async def invalidate_user(self, user_id: str) -> bool:
        """Invalidate user cache."""
        key = self._build_key(self.USER_PREFIX, user_id)
        return await self._redis.delete(key)
        
    async def get_session(self, session_id: str) -> Optional[dict]:
        """Get session from cache."""
        key = self._build_key(self.SESSION_PREFIX, session_id)
        return await self._redis.get(key)
        
    async def set_session(
        self,
        session_id: str,
        session_data: dict,
        ttl: Optional[timedelta] = None
    ) -> bool:
        """Cache session data."""
        key = self._build_key(self.SESSION_PREFIX, session_id)
        return await self._redis.set(key, session_data, ttl or self.SESSION_TTL)
        
    async def extend_session(
        self,
        session_id: str,
        ttl: Optional[timedelta] = None
    ) -> bool:
        """Extend session expiration."""
        key = self._build_key(self.SESSION_PREFIX, session_id)
        return await self._redis.expire(key, ttl or self.SESSION_TTL)
        
    async def invalidate_session(self, session_id: str) -> bool:
        """Invalidate session."""
        key = self._build_key(self.SESSION_PREFIX, session_id)
        return await self._redis.delete(key)
        
    async def check_rate_limit(
        self,
        identifier: str,
        limit: int,
        window: Optional[timedelta] = None
    ) -> tuple[bool, int]:
        """Check if rate limit is exceeded."""
        key = self._build_key(self.RATE_LIMIT_PREFIX, identifier)
        window = window or self.RATE_LIMIT_TTL
        
        current = await self._redis.get(key)
        
        if current is None:
            await self._redis.set(key, 1, window)
            return True, 1
            
        current_count = int(current)
        
        if current_count >= limit:
            return False, current_count
            
        new_count = await self._redis.increment(key)
        
        ttl = await self._redis.get_ttl(key)
        if ttl == -1:
            await self._redis.expire(key, window)
            
        return True, new_count or current_count + 1 