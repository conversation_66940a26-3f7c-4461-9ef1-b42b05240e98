# Lonors Design System

This document outlines the comprehensive design system for the Lonors platform, including brand identity, color palette, typography, components, and usage guidelines.

## Table of Contents

- [Brand Identity](#brand-identity)
- [Color System](#color-system)
- [Typography](#typography)
- [Spacing & Layout](#spacing--layout)
- [Components](#components)
- [Animation Guidelines](#animation-guidelines)
- [Accessibility](#accessibility)
- [Usage Guidelines](#usage-guidelines)

## Brand Identity

### Brand Values

- **Innovation**: Cutting-edge AI technology and forward-thinking solutions
- **Reliability**: Stable, secure, and trustworthy platform
- **Simplicity**: Clean, intuitive interfaces that reduce complexity
- **Empowerment**: Tools that enhance human capabilities

### Visual Identity

- **Logo**: Sparkles icon representing AI magic and innovation
- **Brand Name**: "Lonors" - suggesting "honors" and "long-term" reliability
- **Tagline**: "The Future of AI-Powered Development"

## Color System

### Primary Colors

The Lonors brand uses a sophisticated blue palette that conveys trust, innovation, and professionalism.

```css
/* <PERSON>nors Primary Palette */
--lonors-50: #f0f9ff;   /* Lightest blue */
--lonors-100: #e0f2fe;  /* Very light blue */
--lonors-200: #bae6fd;  /* Light blue */
--lonors-300: #7dd3fc;  /* Medium light blue */
--lonors-400: #38bdf8;  /* Medium blue */
--lonors-500: #0ea5e9;  /* Primary blue */
--lonors-600: #0284c7;  /* Dark blue */
--lonors-700: #0369a1;  /* Darker blue */
--lonors-800: #075985;  /* Very dark blue */
--lonors-900: #0c4a6e;  /* Darkest blue */
--lonors-950: #082f49;  /* Ultra dark blue */
```

### Agent Interaction Colors

Special colors for AI agent interactions and states.

```css
/* Agent Colors */
--agent-primary: #6366f1;    /* Indigo for primary agent actions */
--agent-secondary: #8b5cf6;  /* Purple for secondary agent actions */
--agent-success: #10b981;    /* Green for successful operations */
--agent-warning: #f59e0b;    /* Amber for warnings */
--agent-error: #ef4444;      /* Red for errors */
```

### Semantic Colors

```css
/* Semantic Color System */
--color-background: hsl(0 0% 100%);           /* Light mode background */
--color-foreground: hsl(222.2 84% 4.9%);     /* Light mode text */
--color-primary: hsl(221.2 83.2% 53.3%);     /* Primary brand color */
--color-secondary: hsl(210 40% 96%);          /* Secondary background */
--color-muted: hsl(210 40% 96%);              /* Muted background */
--color-accent: hsl(210 40% 96%);             /* Accent background */
--color-destructive: hsl(0 84.2% 60.2%);     /* Destructive actions */
--color-border: hsl(214.3 31.8% 91.4%);      /* Border color */
--color-input: hsl(214.3 31.8% 91.4%);       /* Input border */
--color-ring: hsl(221.2 83.2% 53.3%);        /* Focus ring */
```

### Dark Mode Colors

```css
/* Dark Mode Overrides */
.dark {
  --color-background: hsl(222.2 84% 4.9%);
  --color-foreground: hsl(210 40% 98%);
  --color-primary: hsl(217.2 91.2% 59.8%);
  --color-secondary: hsl(217.2 32.6% 17.5%);
  --color-muted: hsl(217.2 32.6% 17.5%);
  --color-accent: hsl(217.2 32.6% 17.5%);
  --color-destructive: hsl(0 62.8% 30.6%);
  --color-border: hsl(217.2 32.6% 17.5%);
  --color-input: hsl(217.2 32.6% 17.5%);
  --color-ring: hsl(224.3 76.3% 94.1%);
}
```

## Typography

### Font Families

```css
/* Primary Font Stack */
font-family: 'Inter', system-ui, -apple-system, sans-serif;

/* Monospace Font Stack */
font-family: 'JetBrains Mono', 'Fira Code', Consolas, monospace;
```

### Type Scale

```css
/* Font Sizes */
--text-2xs: 0.625rem;    /* 10px */
--text-xs: 0.75rem;      /* 12px */
--text-sm: 0.875rem;     /* 14px */
--text-base: 1rem;       /* 16px */
--text-lg: 1.125rem;     /* 18px */
--text-xl: 1.25rem;      /* 20px */
--text-2xl: 1.5rem;      /* 24px */
--text-3xl: 1.875rem;    /* 30px */
--text-4xl: 2.25rem;     /* 36px */
--text-5xl: 3rem;        /* 48px */
--text-6xl: 3.75rem;     /* 60px */
--text-7xl: 4.5rem;      /* 72px */
```

### Font Weights

```css
--font-thin: 100;
--font-light: 300;
--font-normal: 400;
--font-medium: 500;
--font-semibold: 600;
--font-bold: 700;
--font-extrabold: 800;
--font-black: 900;
```

### Line Heights

```css
--leading-none: 1;
--leading-tight: 1.25;
--leading-snug: 1.375;
--leading-normal: 1.5;
--leading-relaxed: 1.625;
--leading-loose: 2;
```

## Spacing & Layout

### Spacing Scale

```css
/* Spacing Scale (rem units) */
--space-0: 0;
--space-1: 0.25rem;    /* 4px */
--space-2: 0.5rem;     /* 8px */
--space-3: 0.75rem;    /* 12px */
--space-4: 1rem;       /* 16px */
--space-5: 1.25rem;    /* 20px */
--space-6: 1.5rem;     /* 24px */
--space-8: 2rem;       /* 32px */
--space-10: 2.5rem;    /* 40px */
--space-12: 3rem;      /* 48px */
--space-16: 4rem;      /* 64px */
--space-20: 5rem;      /* 80px */
--space-24: 6rem;      /* 96px */
--space-32: 8rem;      /* 128px */
```

### Border Radius

```css
--radius-none: 0;
--radius-sm: 0.125rem;   /* 2px */
--radius-base: 0.25rem;  /* 4px */
--radius-md: 0.375rem;   /* 6px */
--radius-lg: 0.5rem;     /* 8px */
--radius-xl: 0.75rem;    /* 12px */
--radius-2xl: 1rem;      /* 16px */
--radius-full: 9999px;   /* Fully rounded */
```

### Shadows

```css
/* Shadow System */
--shadow-sm: 0 1px 2px 0 rgb(0 0 0 / 0.05);
--shadow-base: 0 1px 3px 0 rgb(0 0 0 / 0.1), 0 1px 2px -1px rgb(0 0 0 / 0.1);
--shadow-md: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1);
--shadow-lg: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);
--shadow-xl: 0 20px 25px -5px rgb(0 0 0 / 0.1), 0 8px 10px -6px rgb(0 0 0 / 0.1);
--shadow-2xl: 0 25px 50px -12px rgb(0 0 0 / 0.25);

/* Special Effects */
--shadow-glow: 0 0 20px rgba(99, 102, 241, 0.3);
--shadow-glow-lg: 0 0 40px rgba(99, 102, 241, 0.4);
```

## Components

### Button Variants

```typescript
// Button component variants
const buttonVariants = {
  variant: {
    default: 'bg-primary text-primary-foreground hover:bg-primary/90',
    destructive: 'bg-destructive text-destructive-foreground hover:bg-destructive/90',
    outline: 'border border-input bg-background hover:bg-accent hover:text-accent-foreground',
    secondary: 'bg-secondary text-secondary-foreground hover:bg-secondary/80',
    ghost: 'hover:bg-accent hover:text-accent-foreground',
    link: 'text-primary underline-offset-4 hover:underline',
    gradient: 'lonors-gradient text-white hover:opacity-90',
    agent: 'agent-gradient text-white hover:opacity-90',
  },
  size: {
    default: 'h-10 px-4 py-2',
    sm: 'h-9 rounded-md px-3',
    lg: 'h-11 rounded-md px-8',
    xl: 'h-12 rounded-lg px-10 text-base',
    icon: 'h-10 w-10',
  },
}
```

### Card Variants

```typescript
// Card component variants
const cardVariants = {
  variant: {
    default: 'rounded-lg border bg-card text-card-foreground shadow-sm',
    elevated: 'shadow-md hover:shadow-lg transition-shadow',
    glass: 'glass-effect',
    gradient: 'lonors-gradient text-white border-0',
    interactive: 'interactive cursor-pointer',
  },
}
```

### Input Variants

```typescript
// Input component variants
const inputVariants = {
  variant: {
    default: 'border border-input bg-background',
    ghost: 'border-transparent bg-transparent focus-visible:border-input',
    filled: 'bg-muted border-transparent focus-visible:bg-background',
  },
}
```

## Animation Guidelines

### Timing Functions

```css
/* Easing Functions */
--ease-linear: linear;
--ease-in: cubic-bezier(0.4, 0, 1, 1);
--ease-out: cubic-bezier(0, 0, 0.2, 1);
--ease-in-out: cubic-bezier(0.4, 0, 0.2, 1);
--ease-back: cubic-bezier(0.68, -0.55, 0.265, 1.55);
```

### Duration Scale

```css
/* Animation Durations */
--duration-75: 75ms;
--duration-100: 100ms;
--duration-150: 150ms;
--duration-200: 200ms;
--duration-300: 300ms;
--duration-500: 500ms;
--duration-700: 700ms;
--duration-1000: 1000ms;
```

### Animation Patterns

```css
/* Common Animations */
@keyframes fade-in {
  from { opacity: 0; transform: translateY(10px); }
  to { opacity: 1; transform: translateY(0); }
}

@keyframes slide-in {
  from { transform: translateX(-100%); }
  to { transform: translateX(0); }
}

@keyframes scale-in {
  from { transform: scale(0.95); opacity: 0; }
  to { transform: scale(1); opacity: 1; }
}

@keyframes shimmer {
  from { transform: translateX(-100%); }
  to { transform: translateX(100%); }
}
```

## Accessibility

### Focus Management

```css
/* Focus Ring System */
.focus-ring {
  @apply focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 focus:ring-offset-background;
}
```

### Color Contrast

- **AA Compliance**: Minimum 4.5:1 contrast ratio for normal text
- **AAA Compliance**: Minimum 7:1 contrast ratio for enhanced accessibility
- **Large Text**: Minimum 3:1 contrast ratio for text 18pt+ or 14pt+ bold

### Screen Reader Support

```html
<!-- Proper ARIA labels and roles -->
<button aria-label="Close dialog" role="button">
  <X className="h-4 w-4" />
</button>

<!-- Status announcements -->
<div role="status" aria-live="polite">
  Loading...
</div>
```

## Usage Guidelines

### Component Composition

```typescript
// Good: Composable components
<Card variant="elevated">
  <CardHeader>
    <CardTitle>Agent Status</CardTitle>
    <CardDescription>Monitor your AI agents</CardDescription>
  </CardHeader>
  <CardContent>
    <AgentStatusGrid />
  </CardContent>
</Card>

// Avoid: Monolithic components
<AgentStatusCard title="Agent Status" description="Monitor your AI agents" />
```

### Color Usage

```typescript
// Good: Semantic color usage
<Button variant="destructive">Delete Agent</Button>
<div className="text-agent-success">Agent is running</div>

// Avoid: Direct color values
<Button className="bg-red-500">Delete Agent</Button>
<div className="text-green-500">Agent is running</div>
```

### Spacing Consistency

```typescript
// Good: Consistent spacing scale
<div className="space-y-4">
  <div className="p-6">
    <h2 className="mb-2">Title</h2>
    <p className="mb-4">Description</p>
  </div>
</div>

// Avoid: Arbitrary spacing values
<div className="space-y-[13px]">
  <div className="p-[23px]">
    <h2 className="mb-[7px]">Title</h2>
    <p className="mb-[19px]">Description</p>
  </div>
</div>
```

### Responsive Design

```typescript
// Good: Mobile-first responsive design
<div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
  <Card />
  <Card />
  <Card />
</div>

// Good: Responsive typography
<h1 className="text-2xl md:text-3xl lg:text-4xl">
  Welcome to Lonors
</h1>
```

This design system ensures consistency, accessibility, and maintainability across the entire Lonors platform while providing flexibility for future enhancements and customizations.
