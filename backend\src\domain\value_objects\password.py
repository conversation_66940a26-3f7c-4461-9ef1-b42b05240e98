"""
Password value object for the Lonors application.

This module contains the Password value object that encapsulates password validation
and behavior following Domain-Driven Design principles.
"""

import math
import re
from typing import Any

import bcrypt

from ..exceptions import DomainValidationError


class Password:
    """Password value object with validation and security features."""

    MIN_LENGTH = 8
    MAX_LENGTH = 128

    def __init__(self, value: str) -> None:
        """
        Initialize password value object.

        Args:
            value: Password string to validate and store

        Raises:
            DomainValidationError: If password doesn't meet strength requirements
        """
        if not value or not value.strip():
            raise DomainValidationError("Password does not meet strength requirements")

        if not self._is_strong_password(value):
            raise DomainValidationError("Password does not meet strength requirements")

        self._value = value

    @property
    def value(self) -> str:
        """Get the password value."""
        return self._value

    @property
    def length(self) -> int:
        """Get the length of the password."""
        return len(self._value)

    def is_valid_length(self) -> bool:
        """Check if password length is valid."""
        return self.MIN_LENGTH <= len(self._value) <= self.MAX_LENGTH

    def has_uppercase(self) -> bool:
        """Check if password contains uppercase letters."""
        return bool(re.search(r"[A-Z]", self._value))

    def has_lowercase(self) -> bool:
        """Check if password contains lowercase letters."""
        return bool(re.search(r"[a-z]", self._value))

    def has_numbers(self) -> bool:
        """Check if password contains numbers."""
        return bool(re.search(r"\d", self._value))

    def has_symbols(self) -> bool:
        """Check if password contains symbols."""
        return bool(re.search(r"[!@#$%^&*()_+\-=\[\]{};':\"\\|,.<>\/?]", self._value))

    def is_strong(self) -> bool:
        """Check if password meets all strength requirements."""
        return (
            self.is_valid_length()
            and self.has_uppercase()
            and self.has_lowercase()
            and self.has_numbers()
            and self.has_symbols()
        )

    def strength_score(self) -> int:
        """
        Calculate password strength score.

        Returns:
            Score from 0-5 based on criteria met
        """
        score = 0
        if self.is_valid_length():
            score += 1
        if self.has_uppercase():
            score += 1
        if self.has_lowercase():
            score += 1
        if self.has_numbers():
            score += 1
        if self.has_symbols():
            score += 1
        return score

    def calculate_entropy(self) -> float:
        """
        Calculate password entropy.

        Returns:
            Entropy value in bits
        """
        charset_size = 0
        if self.has_lowercase():
            charset_size += 26
        if self.has_uppercase():
            charset_size += 26
        if self.has_numbers():
            charset_size += 10
        if self.has_symbols():
            charset_size += 32  # Common symbols

        if charset_size == 0:
            return 0.0

        return len(self._value) * math.log2(charset_size)

    def hash(self) -> str:
        """
        Hash the password using bcrypt.

        Returns:
            Hashed password string
        """
        salt = bcrypt.gensalt()
        hashed = bcrypt.hashpw(self._value.encode("utf-8"), salt)
        return hashed.decode("utf-8")

    def verify(self, hashed_password: str) -> bool:
        """
        Verify password against a hash.

        Args:
            hashed_password: Hashed password to verify against

        Returns:
            True if password matches hash, False otherwise
        """
        try:
            return bcrypt.checkpw(
                self._value.encode("utf-8"), hashed_password.encode("utf-8")
            )
        except (ValueError, TypeError):
            return False

    def _is_strong_password(self, password: str) -> bool:
        """
        Check if password meets strength requirements.

        Args:
            password: Password to validate

        Returns:
            True if password is strong, False otherwise
        """
        if not self.MIN_LENGTH <= len(password) <= self.MAX_LENGTH:
            return False

        # Must have at least 3 of the 4 character types for strong password
        # But for basic validation, require all 4 types
        criteria_met = sum([
            bool(re.search(r"[A-Z]", password)),  # Uppercase
            bool(re.search(r"[a-z]", password)),  # Lowercase
            bool(re.search(r"\d", password)),  # Numbers
            bool(
                re.search(r"[!@#$%^&*()_+\-=\[\]{};':\"\\|,.<>\/?]", password)
            ),  # Symbols
        ])

        return criteria_met >= 4  # Require all 4 types for strong password

    def __eq__(self, other: Any) -> bool:
        """Check equality with another Password object."""
        if not isinstance(other, Password):
            return False
        return self._value == other._value

    def __hash__(self) -> int:
        """Return hash of the password value."""
        return hash(self._value)

    def __str__(self) -> str:
        """Return masked string representation of the password."""
        return "Password(***)"

    def __repr__(self) -> str:
        """Return masked detailed string representation of the password."""
        return "Password(***)"
