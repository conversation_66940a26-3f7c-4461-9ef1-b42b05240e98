import { Agent, AgentType, AgentStatus } from '@/shared/types';

export interface AgentModel extends Agent {
  // Additional computed properties
  isOnline: boolean;
  lastActivity?: Date;
  executionCount: number;
}

export const createAgent = (data: Partial<Agent>): AgentModel => ({
  id: data.id || '',
  name: data.name || '',
  description: data.description || '',
  type: data.type || AgentType.CHAT,
  status: data.status || AgentStatus.IDLE,
  configuration: data.configuration || {},
  capabilities: data.capabilities || [],
  ownerId: data.ownerId || '',
  isPublic: data.isPublic || false,
  tags: data.tags || [],
  createdAt: data.createdAt || new Date().toISOString(),
  updatedAt: data.updatedAt || new Date().toISOString(),
  // Computed properties
  isOnline: data.status === AgentStatus.RUNNING,
  executionCount: 0,
});

export const agentSelectors = {
  isActive: (agent: AgentModel) => 
    agent.status === AgentStatus.RUNNING || agent.status === AgentStatus.PAUSED,
  
  canExecute: (agent: AgentModel) => 
    agent.status === AgentStatus.IDLE || agent.status === AgentStatus.PAUSED,
  
  getStatusColor: (status: AgentStatus) => {
    switch (status) {
      case AgentStatus.RUNNING:
        return 'green';
      case AgentStatus.PAUSED:
        return 'yellow';
      case AgentStatus.ERROR:
        return 'red';
      case AgentStatus.STOPPED:
        return 'gray';
      default:
        return 'blue';
    }
  },
};
