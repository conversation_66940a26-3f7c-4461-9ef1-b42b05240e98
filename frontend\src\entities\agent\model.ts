import { Agent, AgentStatus, AgentType } from '@/shared/types';

export interface AgentModel extends Agent {
  // Additional computed properties
  isOnline: boolean;
  lastActivity?: Date;
  executionCount: number;
}

export const createAgent = (data: Partial<Agent>): AgentModel => ({
  id: data.id || '',
  name: data.name || '',
  description: data.description || '',
  agent_type: data.agent_type || AgentType.CHAT,
  version: data.version || '1.0.0',
  status: data.status || AgentStatus.IDLE,
  configuration: data.configuration || {
    model_name: 'gpt-4',
    temperature: 0.7,
    max_tokens: 2048,
    timeout_seconds: 300,
    retry_attempts: 3,
    memory_enabled: true,
    tools_enabled: true,
    custom_settings: {},
  },
  capabilities: data.capabilities || [],
  current_task_id: data.current_task_id,
  created_by: data.created_by || '',
  metrics: data.metrics || {
    total_executions: 0,
    successful_executions: 0,
    failed_executions: 0,
    average_execution_time: 0,
    total_tokens_used: 0,
    total_cost: 0,
  },
  parent_agent_id: data.parent_agent_id,
  child_agent_ids: data.child_agent_ids || [],
  tags: data.tags || [],
  metadata: data.metadata || {},
  createdAt: data.createdAt || new Date().toISOString(),
  updatedAt: data.updatedAt || new Date().toISOString(),
  // Computed properties
  isOnline: data.status === AgentStatus.RUNNING,
  executionCount: data.metrics?.total_executions || 0,
  lastActivity: data.metrics?.last_execution_time ? new Date(data.metrics.last_execution_time) : undefined,
});

export const agentSelectors = {
  isActive: (agent: AgentModel) =>
    agent.status === AgentStatus.RUNNING || agent.status === AgentStatus.PAUSED,

  canExecute: (agent: AgentModel) =>
    agent.status === AgentStatus.IDLE || agent.status === AgentStatus.PAUSED,

  isCompleted: (agent: AgentModel) =>
    agent.status === AgentStatus.COMPLETED ||
    agent.status === AgentStatus.FAILED ||
    agent.status === AgentStatus.CANCELLED,

  getStatusColor: (status: AgentStatus) => {
    switch (status) {
      case AgentStatus.RUNNING:
        return 'green';
      case AgentStatus.PAUSED:
        return 'yellow';
      case AgentStatus.COMPLETED:
        return 'blue';
      case AgentStatus.FAILED:
        return 'red';
      case AgentStatus.CANCELLED:
        return 'gray';
      default:
        return 'slate';
    }
  },

  getSuccessRate: (agent: AgentModel): number => {
    const { total_executions, successful_executions } = agent.metrics;
    return total_executions > 0 ? (successful_executions / total_executions) * 100 : 0;
  },

  getAverageExecutionTime: (agent: AgentModel): string => {
    const time = agent.metrics.average_execution_time;
    if (time < 60) return `${time.toFixed(1)}s`;
    if (time < 3600) return `${(time / 60).toFixed(1)}m`;
    return `${(time / 3600).toFixed(1)}h`;
  },

  formatCost: (agent: AgentModel): string => {
    return `$${agent.metrics.total_cost.toFixed(4)}`;
  },
};
