# Contributing to <PERSON><PERSON><PERSON>

Thank you for your interest in contributing to <PERSON><PERSON><PERSON>! This document provides guidelines and information for contributors.

## Table of Contents

- [Code of Conduct](#code-of-conduct)
- [Getting Started](#getting-started)
- [Development Workflow](#development-workflow)
- [Coding Standards](#coding-standards)
- [Testing Requirements](#testing-requirements)
- [Pull Request Process](#pull-request-process)
- [Issue Reporting](#issue-reporting)

## Code of Conduct

This project adheres to a code of conduct that promotes a welcoming and inclusive environment. By participating, you agree to uphold these standards.

## Getting Started

### Prerequisites

- **Node.js 18+** (for frontend development)
- **Python 3.11+** (for backend development)
- **Docker and Docker Compose** (for containerized development)
- **pnpm** (for frontend package management)
- **uv** (for Python package management)
- **Git** (version control)

### Initial Setup

1. **Fork the repository**
   ```bash
   # Fork on GitHub, then clone your fork
   git clone https://github.com/YOUR_USERNAME/lonors.git
   cd lonors
   ```

2. **Set up development environment**
   ```bash
   # Install root dependencies
   pnpm install
   
   # Set up backend
   cd backend && uv sync --dev
   
   # Set up frontend (when available)
   cd ../frontend && pnpm install
   
   # Copy environment configuration
   cp .env.example .env
   ```

3. **Start development services**
   ```bash
   # Start with Docker (recommended)
   pnpm docker:up
   
   # Or start individually
   pnpm dev
   ```

## Development Workflow

### Branching Strategy

- **main**: Production-ready code
- **develop**: Integration branch for features
- **staging**: Pre-production testing
- **feature/[name]**: Individual feature development
- **hotfix/[name]**: Critical production fixes

### Feature Development Process

1. **Create a feature branch**
   ```bash
   git checkout develop
   git pull origin develop
   git checkout -b feature/your-feature-name
   ```

2. **Follow TDD approach**
   - Write tests first
   - Implement functionality
   - Refactor and optimize
   - Ensure all tests pass

3. **Commit your changes**
   ```bash
   git add .
   git commit -m "feat: add your feature description"
   ```

4. **Push and create PR**
   ```bash
   git push origin feature/your-feature-name
   # Create PR on GitHub targeting develop branch
   ```

## Coding Standards

### Python Backend Standards

#### Code Style
- **Black** for code formatting
- **Ruff** for linting and import sorting
- **MyPy** for type checking
- Line length: 88 characters
- Use type hints for all functions and methods

#### Architecture Principles
- Follow **Clean Architecture** patterns
- Implement **SOLID** principles
- Use **dependency injection**
- Separate concerns into layers (domain, application, infrastructure, presentation)

#### Example Code Structure
```python
# Good: Type hints, clear naming, single responsibility
async def create_user(
    user_data: UserCreateSchema,
    user_repository: UserRepository,
) -> UserResponse:
    """Create a new user with validation."""
    # Implementation here
    pass
```

### TypeScript Frontend Standards

#### Code Style
- **ESLint** with TypeScript rules
- **Prettier** for code formatting
- **Feature Slice Design (FSD)** architecture
- Use strict TypeScript configuration

#### Component Guidelines
```typescript
// Good: Typed props, clear naming, single responsibility
interface UserCardProps {
  user: User;
  onEdit: (userId: string) => void;
}

export const UserCard: React.FC<UserCardProps> = ({ user, onEdit }) => {
  // Implementation here
};
```

### Commit Message Convention

Use [Conventional Commits](https://www.conventionalcommits.org/) format:

```
type(scope): description

[optional body]

[optional footer]
```

**Types:**
- `feat`: New feature
- `fix`: Bug fix
- `docs`: Documentation changes
- `style`: Code style changes (formatting, etc.)
- `refactor`: Code refactoring
- `test`: Adding or updating tests
- `chore`: Maintenance tasks

**Examples:**
```
feat(auth): add JWT token refresh functionality
fix(api): resolve user creation validation error
docs(readme): update installation instructions
test(user): add unit tests for user service
```

## Testing Requirements

### Backend Testing
- **Unit tests**: 90%+ coverage required
- **Integration tests**: For all API endpoints
- **Test framework**: pytest with async support
- **Test database**: Use test containers

```bash
# Run backend tests
cd backend
uv run pytest --cov=src --cov-fail-under=90
```

### Frontend Testing
- **Unit tests**: 90%+ coverage required
- **Component tests**: React Testing Library
- **E2E tests**: Playwright for critical paths
- **Accessibility tests**: axe-core integration

```bash
# Run frontend tests
cd frontend
pnpm test:coverage
```

### Test Guidelines
- Write tests before implementation (TDD)
- Test behavior, not implementation
- Use descriptive test names
- Mock external dependencies
- Test error scenarios

## Pull Request Process

### Before Submitting

1. **Ensure all tests pass**
   ```bash
   pnpm test
   ```

2. **Run code quality checks**
   ```bash
   pnpm lint
   pnpm format
   ```

3. **Update documentation**
   - Update README if needed
   - Add/update API documentation
   - Update CHANGELOG.md

4. **Verify Docker build**
   ```bash
   pnpm docker:build
   ```

### PR Requirements

- **Title**: Clear, descriptive title following conventional commits
- **Description**: Explain what changes were made and why
- **Tests**: Include tests for new functionality
- **Documentation**: Update relevant documentation
- **Breaking Changes**: Clearly mark any breaking changes

### Review Process

1. **Automated checks**: All CI checks must pass
2. **Code review**: At least one maintainer approval required
3. **Testing**: Manual testing of new features
4. **Documentation**: Verify documentation is complete

## Issue Reporting

### Bug Reports

Include the following information:
- **Environment**: OS, Python/Node versions, browser
- **Steps to reproduce**: Clear, numbered steps
- **Expected behavior**: What should happen
- **Actual behavior**: What actually happens
- **Screenshots/logs**: If applicable

### Feature Requests

Include the following information:
- **Problem**: What problem does this solve?
- **Solution**: Proposed solution or approach
- **Alternatives**: Other solutions considered
- **Additional context**: Any other relevant information

## Development Guidelines

### Security Considerations
- Follow **OWASP** security guidelines
- Never commit secrets or credentials
- Use environment variables for configuration
- Validate all inputs
- Implement proper authentication and authorization

### Performance Guidelines
- Write efficient database queries
- Implement proper caching strategies
- Optimize frontend bundle size
- Use lazy loading where appropriate
- Monitor performance metrics

### Documentation Requirements
- Document all public APIs
- Include code examples
- Update architecture documentation
- Maintain up-to-date README
- Document deployment procedures

## Getting Help

- **GitHub Issues**: For bugs and feature requests
- **GitHub Discussions**: For questions and general discussion
- **Documentation**: Check the docs/ directory
- **Code Examples**: Look at existing implementations

## Recognition

Contributors will be recognized in:
- CONTRIBUTORS.md file
- Release notes for significant contributions
- GitHub contributor statistics

Thank you for contributing to Lonors! 🚀
