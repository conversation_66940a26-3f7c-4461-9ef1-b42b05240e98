"""
Folder database model.

This module defines the SQLAlchemy model for folders
with proper database constraints and relationships.
"""

import uuid
from typing import TYPE_CHECKING

from sqlalchemy import <PERSON><PERSON><PERSON>, ForeignKey, String
from sqlalchemy.dialects.postgresql import UUID
from sqlalchemy.orm import Mapped, mapped_column, relationship

from src.infrastructure.database.models.base import Base

if TYPE_CHECKING:
    from src.infrastructure.database.models.user import UserModel
    from src.infrastructure.database.models.note import NoteModel


class FolderModel(Base):
    """
    Folder SQLAlchemy model.
    
    Represents the folders table in the database with all
    necessary constraints and relationships.
    """
    
    # Folder identification
    name: Mapped[str] = mapped_column(
        String(255),
        nullable=False,
        index=True,
        doc="Folder name"
    )
    
    # Relationships
    parent_id: Mapped[uuid.UUID | None] = mapped_column(
        UUID(as_uuid=True),
        ForeignKey("folders.id", ondelete="SET NULL"),
        nullable=True,
        index=True,
        doc="Parent folder ID"
    )
    
    user_id: Mapped[uuid.UUID] = mapped_column(
        UUID(as_uuid=True),
        ForeignKey("users.id", ondelete="CASCADE"),
        nullable=False,
        index=True,
        doc="Owner user ID"
    )
    
    # Status fields
    is_archived: Mapped[bool] = mapped_column(
        Boolean,
        default=False,
        nullable=False,
        index=True,
        doc="Whether folder is archived"
    )
    
    # Relationships
    user: Mapped["UserModel"] = relationship(
        "UserModel",
        back_populates="folders"
    )
    
    notes: Mapped[list["NoteModel"]] = relationship(
        "NoteModel",
        back_populates="folder",
        cascade="all, delete-orphan"
    )
    
    parent: Mapped["FolderModel"] = relationship(
        "FolderModel",
        remote_side="FolderModel.id",
        back_populates="children"
    )
    
    children: Mapped[list["FolderModel"]] = relationship(
        "FolderModel",
        back_populates="parent",
        cascade="all, delete-orphan"
    )
    
    def __repr__(self) -> str:
        """String representation of the folder."""
        return f"<FolderModel(id={self.id}, name={self.name})>"
    
    def to_domain_entity(self) -> "Folder":
        """
        Convert database model to domain entity.
        
        Returns:
            Folder: Domain entity
        """
        from src.domain.entities.folder import Folder
        
        return Folder(
            id=self.id,
            name=self.name,
            parent_id=self.parent_id,
            user_id=self.user_id,
            is_archived=self.is_archived,
            created_at=self.created_at,
            updated_at=self.updated_at,
            metadata={}  # Add metadata field if needed
        )
    
    @classmethod
    def from_domain_entity(cls, folder: "Folder") -> "FolderModel":
        """
        Create database model from domain entity.
        
        Args:
            folder: Domain entity
            
        Returns:
            FolderModel: Database model
        """
        return cls(
            id=folder.id,
            name=folder.name,
            parent_id=folder.parent_id,
            user_id=folder.user_id,
            is_archived=folder.is_archived,
            created_at=folder.created_at,
            updated_at=folder.updated_at,
        )
