"""
User repository implementation.

This module provides the concrete implementation of the user repository
using SQLAlchemy for database operations.
"""

import uuid
from typing import List, Optional

from sqlalchemy import and_, func, select
from sqlalchemy.ext.asyncio import AsyncSession

from src.domain.entities.user import User, UserStatus
from src.domain.repositories.user_repository import UserRepositoryInterface
from src.infrastructure.database.models.user import UserModel
from src.infrastructure.logging.setup import LoggerMixin


class UserRepository(UserRepositoryInterface, LoggerMixin):
    """
    SQLAlchemy implementation of user repository.
    
    Provides async database operations for user management
    following the repository pattern.
    """
    
    def __init__(self, session: AsyncSession) -> None:
        """
        Initialize user repository.
        
        Args:
            session: Async database session
        """
        self.session = session
    
    async def create(self, user: User) -> User:
        """
        Create a new user.
        
        Args:
            user: User entity to create
            
        Returns:
            User: Created user entity
            
        Raises:
            ValueError: If user already exists
        """
        # Check if email or username already exists
        if await self.email_exists(user.email):
            raise ValueError(f"Email already exists: {user.email}")
        
        if await self.username_exists(user.username):
            raise ValueError(f"Username already exists: {user.username}")
        
        # Create database model from domain entity
        db_user = UserModel.from_domain_entity(user)
        
        # Add to session and commit
        self.session.add(db_user)
        await self.session.commit()
        await self.session.refresh(db_user)
        
        self.logger.info(f"Created user: {user.email}")
        return db_user.to_domain_entity()
    
    async def get_by_id(self, user_id: uuid.UUID) -> Optional[User]:
        """
        Get user by ID.
        
        Args:
            user_id: User ID
            
        Returns:
            User: User entity or None if not found
        """
        stmt = select(UserModel).where(UserModel.id == user_id)
        result = await self.session.execute(stmt)
        db_user = result.scalar_one_or_none()
        
        if db_user:
            self.logger.debug(f"Found user by ID: {user_id}")
            return db_user.to_domain_entity()
        
        self.logger.debug(f"User not found by ID: {user_id}")
        return None
    
    async def get_by_email(self, email: str) -> Optional[User]:
        """
        Get user by email address.
        
        Args:
            email: Email address
            
        Returns:
            User: User entity or None if not found
        """
        stmt = select(UserModel).where(UserModel.email == email.lower())
        result = await self.session.execute(stmt)
        db_user = result.scalar_one_or_none()
        
        if db_user:
            self.logger.debug(f"Found user by email: {email}")
            return db_user.to_domain_entity()
        
        self.logger.debug(f"User not found by email: {email}")
        return None
    
    async def get_by_username(self, username: str) -> Optional[User]:
        """
        Get user by username.
        
        Args:
            username: Username
            
        Returns:
            User: User entity or None if not found
        """
        stmt = select(UserModel).where(UserModel.username == username.lower())
        result = await self.session.execute(stmt)
        db_user = result.scalar_one_or_none()
        
        if db_user:
            self.logger.debug(f"Found user by username: {username}")
            return db_user.to_domain_entity()
        
        self.logger.debug(f"User not found by username: {username}")
        return None
    
    async def update(self, user: User) -> User:
        """
        Update existing user.
        
        Args:
            user: User entity with updated data
            
        Returns:
            User: Updated user entity
            
        Raises:
            ValueError: If user not found
        """
        stmt = select(UserModel).where(UserModel.id == user.id)
        result = await self.session.execute(stmt)
        db_user = result.scalar_one_or_none()
        
        if not db_user:
            raise ValueError(f"User not found: {user.id}")
        
        # Update fields from domain entity
        db_user.email = user.email
        db_user.username = user.username
        db_user.full_name = user.full_name
        db_user.hashed_password = user.hashed_password
        db_user.role = user.role
        db_user.status = user.status
        db_user.is_verified = user.is_verified
        db_user.last_login = user.last_login
        db_user.updated_at = user.updated_at
        
        await self.session.commit()
        await self.session.refresh(db_user)
        
        self.logger.info(f"Updated user: {user.email}")
        return db_user.to_domain_entity()
    
    async def delete(self, user_id: uuid.UUID) -> bool:
        """
        Delete user by ID.
        
        Args:
            user_id: User ID
            
        Returns:
            bool: True if deleted, False if not found
        """
        stmt = select(UserModel).where(UserModel.id == user_id)
        result = await self.session.execute(stmt)
        db_user = result.scalar_one_or_none()
        
        if not db_user:
            self.logger.debug(f"User not found for deletion: {user_id}")
            return False
        
        await self.session.delete(db_user)
        await self.session.commit()
        
        self.logger.info(f"Deleted user: {user_id}")
        return True
    
    async def list_users(
        self,
        skip: int = 0,
        limit: int = 100,
        active_only: bool = False
    ) -> List[User]:
        """
        List users with pagination.
        
        Args:
            skip: Number of users to skip
            limit: Maximum number of users to return
            active_only: Whether to return only active users
            
        Returns:
            List[User]: List of user entities
        """
        stmt = select(UserModel)
        
        if active_only:
            stmt = stmt.where(
                and_(
                    UserModel.status == UserStatus.ACTIVE,
                    UserModel.is_verified == True
                )
            )
        
        stmt = stmt.offset(skip).limit(limit).order_by(UserModel.created_at.desc())
        
        result = await self.session.execute(stmt)
        db_users = result.scalars().all()
        
        users = [db_user.to_domain_entity() for db_user in db_users]
        
        self.logger.debug(f"Listed {len(users)} users (skip={skip}, limit={limit}, active_only={active_only})")
        return users
    
    async def count_users(self, active_only: bool = False) -> int:
        """
        Count total number of users.
        
        Args:
            active_only: Whether to count only active users
            
        Returns:
            int: Total number of users
        """
        stmt = select(func.count(UserModel.id))
        
        if active_only:
            stmt = stmt.where(
                and_(
                    UserModel.status == UserStatus.ACTIVE,
                    UserModel.is_verified == True
                )
            )
        
        result = await self.session.execute(stmt)
        count = result.scalar()
        
        self.logger.debug(f"User count: {count} (active_only={active_only})")
        return count or 0
    
    async def email_exists(self, email: str) -> bool:
        """
        Check if email already exists.
        
        Args:
            email: Email address to check
            
        Returns:
            bool: True if email exists
        """
        stmt = select(func.count(UserModel.id)).where(UserModel.email == email.lower())
        result = await self.session.execute(stmt)
        count = result.scalar()
        
        exists = (count or 0) > 0
        self.logger.debug(f"Email exists check for {email}: {exists}")
        return exists
    
    async def username_exists(self, username: str) -> bool:
        """
        Check if username already exists.
        
        Args:
            username: Username to check
            
        Returns:
            bool: True if username exists
        """
        stmt = select(func.count(UserModel.id)).where(UserModel.username == username.lower())
        result = await self.session.execute(stmt)
        count = result.scalar()
        
        exists = (count or 0) > 0
        self.logger.debug(f"Username exists check for {username}: {exists}")
        return exists
