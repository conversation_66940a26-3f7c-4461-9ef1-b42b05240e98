"""
Agent domain entity for AI agent orchestration.

This module defines the core Agent entity with its properties and behaviors
following domain-driven design principles.
"""

from datetime import datetime
from enum import Enum
from typing import Any, Dict, List, Optional
from uuid import UUID, uuid4

from pydantic import BaseModel, Field


class AgentStatus(str, Enum):
    """Agent execution status."""
    IDLE = "idle"
    RUNNING = "running"
    PAUSED = "paused"
    COMPLETED = "completed"
    FAILED = "failed"
    CANCELLED = "cancelled"


class AgentType(str, Enum):
    """Agent type classification."""
    CHAT = "chat"
    WORKFLOW = "workflow"
    TOOL = "tool"
    REASONING = "reasoning"
    MULTIMODAL = "multimodal"
    CUSTOM = "custom"


class AgentCapability(BaseModel):
    """Agent capability definition."""
    name: str
    description: str
    parameters: Dict[str, Any] = Field(default_factory=dict)
    required: bool = False


class AgentConfiguration(BaseModel):
    """Agent configuration settings."""
    model_name: str = "gpt-4"
    temperature: float = 0.7
    max_tokens: int = 2048
    timeout_seconds: int = 300
    retry_attempts: int = 3
    memory_enabled: bool = True
    tools_enabled: bool = True
    custom_settings: Dict[str, Any] = Field(default_factory=dict)


class AgentMetrics(BaseModel):
    """Agent performance metrics."""
    total_executions: int = 0
    successful_executions: int = 0
    failed_executions: int = 0
    average_execution_time: float = 0.0
    last_execution_time: Optional[datetime] = None
    total_tokens_used: int = 0
    total_cost: float = 0.0


class Agent(BaseModel):
    """
    Agent domain entity representing an AI agent in the system.
    
    This entity encapsulates all agent-related data and business logic
    while maintaining domain integrity and consistency.
    """
    
    # Identity
    id: UUID = Field(default_factory=uuid4)
    name: str
    description: str
    
    # Classification
    agent_type: AgentType
    version: str = "1.0.0"
    
    # Configuration
    configuration: AgentConfiguration = Field(default_factory=AgentConfiguration)
    capabilities: List[AgentCapability] = Field(default_factory=list)
    
    # State
    status: AgentStatus = AgentStatus.IDLE
    current_task_id: Optional[UUID] = None
    
    # Metadata
    created_at: datetime = Field(default_factory=datetime.utcnow)
    updated_at: datetime = Field(default_factory=datetime.utcnow)
    created_by: UUID
    
    # Performance
    metrics: AgentMetrics = Field(default_factory=AgentMetrics)
    
    # Relationships
    parent_agent_id: Optional[UUID] = None
    child_agent_ids: List[UUID] = Field(default_factory=list)
    
    # Tags and metadata
    tags: List[str] = Field(default_factory=list)
    metadata: Dict[str, Any] = Field(default_factory=dict)
    
    class Config:
        """Pydantic configuration."""
        use_enum_values = True
        validate_assignment = True
        arbitrary_types_allowed = True
    
    def add_capability(self, capability: AgentCapability) -> None:
        """Add a new capability to the agent."""
        if capability.name not in [cap.name for cap in self.capabilities]:
            self.capabilities.append(capability)
            self.updated_at = datetime.utcnow()
    
    def remove_capability(self, capability_name: str) -> bool:
        """Remove a capability from the agent."""
        for i, cap in enumerate(self.capabilities):
            if cap.name == capability_name:
                self.capabilities.pop(i)
                self.updated_at = datetime.utcnow()
                return True
        return False
    
    def update_status(self, new_status: AgentStatus) -> None:
        """Update agent status with validation."""
        if self._is_valid_status_transition(self.status, new_status):
            self.status = new_status
            self.updated_at = datetime.utcnow()
        else:
            raise ValueError(f"Invalid status transition from {self.status} to {new_status}")
    
    def start_task(self, task_id: UUID) -> None:
        """Start executing a task."""
        if self.status != AgentStatus.IDLE:
            raise ValueError(f"Agent must be idle to start task, current status: {self.status}")
        
        self.current_task_id = task_id
        self.status = AgentStatus.RUNNING
        self.updated_at = datetime.utcnow()
    
    def complete_task(self, execution_time: float, tokens_used: int = 0, cost: float = 0.0) -> None:
        """Complete the current task and update metrics."""
        if self.status != AgentStatus.RUNNING:
            raise ValueError(f"Agent must be running to complete task, current status: {self.status}")
        
        self.current_task_id = None
        self.status = AgentStatus.IDLE
        self.updated_at = datetime.utcnow()
        
        # Update metrics
        self.metrics.total_executions += 1
        self.metrics.successful_executions += 1
        self.metrics.last_execution_time = datetime.utcnow()
        self.metrics.total_tokens_used += tokens_used
        self.metrics.total_cost += cost
        
        # Update average execution time
        total_time = self.metrics.average_execution_time * (self.metrics.total_executions - 1)
        self.metrics.average_execution_time = (total_time + execution_time) / self.metrics.total_executions
    
    def fail_task(self, error_message: str) -> None:
        """Fail the current task and update metrics."""
        if self.status != AgentStatus.RUNNING:
            raise ValueError(f"Agent must be running to fail task, current status: {self.status}")
        
        self.current_task_id = None
        self.status = AgentStatus.FAILED
        self.updated_at = datetime.utcnow()
        
        # Update metrics
        self.metrics.total_executions += 1
        self.metrics.failed_executions += 1
        self.metrics.last_execution_time = datetime.utcnow()
        
        # Store error in metadata
        self.metadata["last_error"] = {
            "message": error_message,
            "timestamp": datetime.utcnow().isoformat()
        }
    
    def add_child_agent(self, child_agent_id: UUID) -> None:
        """Add a child agent relationship."""
        if child_agent_id not in self.child_agent_ids:
            self.child_agent_ids.append(child_agent_id)
            self.updated_at = datetime.utcnow()
    
    def remove_child_agent(self, child_agent_id: UUID) -> bool:
        """Remove a child agent relationship."""
        if child_agent_id in self.child_agent_ids:
            self.child_agent_ids.remove(child_agent_id)
            self.updated_at = datetime.utcnow()
            return True
        return False
    
    def get_success_rate(self) -> float:
        """Calculate agent success rate."""
        if self.metrics.total_executions == 0:
            return 0.0
        return self.metrics.successful_executions / self.metrics.total_executions
    
    def is_available(self) -> bool:
        """Check if agent is available for new tasks."""
        return self.status == AgentStatus.IDLE
    
    def _is_valid_status_transition(self, from_status: AgentStatus, to_status: AgentStatus) -> bool:
        """Validate status transitions."""
        valid_transitions = {
            AgentStatus.IDLE: [AgentStatus.RUNNING, AgentStatus.CANCELLED],
            AgentStatus.RUNNING: [AgentStatus.PAUSED, AgentStatus.COMPLETED, AgentStatus.FAILED, AgentStatus.CANCELLED],
            AgentStatus.PAUSED: [AgentStatus.RUNNING, AgentStatus.CANCELLED],
            AgentStatus.COMPLETED: [AgentStatus.IDLE],
            AgentStatus.FAILED: [AgentStatus.IDLE],
            AgentStatus.CANCELLED: [AgentStatus.IDLE],
        }
        
        return to_status in valid_transitions.get(from_status, [])
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert agent to dictionary representation."""
        return {
            "id": str(self.id),
            "name": self.name,
            "description": self.description,
            "agent_type": self.agent_type.value,
            "version": self.version,
            "status": self.status.value,
            "configuration": self.configuration.dict(),
            "capabilities": [cap.dict() for cap in self.capabilities],
            "metrics": self.metrics.dict(),
            "created_at": self.created_at.isoformat(),
            "updated_at": self.updated_at.isoformat(),
            "tags": self.tags,
            "metadata": self.metadata,
        }
