import { fireEvent, render, screen } from '@testing-library/react';
import { describe, expect, it, vi } from 'vitest';
import { Input } from '../input';

describe('Input', () => {
  it('renders input element', () => {
    render(<Input data-testid="test-input" />);

    const input = screen.getByTestId('test-input');
    expect(input).toBeInTheDocument();
    expect(input.tagName).toBe('INPUT');
  });

  it('applies default styling classes', () => {
    render(<Input data-testid="test-input" />);

    const input = screen.getByTestId('test-input');
    expect(input).toHaveClass('flex', 'h-10', 'w-full', 'rounded-md', 'border');
  });

  it('accepts custom className', () => {
    render(<Input className="custom-input" data-testid="test-input" />);

    const input = screen.getByTestId('test-input');
    expect(input).toHaveClass('custom-input');
    expect(input).toHaveClass('flex'); // Should still have default classes
  });

  it('forwards ref correctly', () => {
    const ref = vi.fn();
    render(<Input ref={ref} />);

    expect(ref).toHaveBeenCalled();
  });

  it('handles different input types', () => {
    const { rerender } = render(<Input type="text" data-testid="test-input" />);
    expect(screen.getByTestId('test-input')).toHaveAttribute('type', 'text');

    rerender(<Input type="email" data-testid="test-input" />);
    expect(screen.getByTestId('test-input')).toHaveAttribute('type', 'email');

    rerender(<Input type="password" data-testid="test-input" />);
    expect(screen.getByTestId('test-input')).toHaveAttribute('type', 'password');

    rerender(<Input type="number" data-testid="test-input" />);
    expect(screen.getByTestId('test-input')).toHaveAttribute('type', 'number');
  });

  it('handles placeholder text', () => {
    render(<Input placeholder="Enter your name" data-testid="test-input" />);

    const input = screen.getByTestId('test-input');
    expect(input).toHaveAttribute('placeholder', 'Enter your name');
  });

  it('handles value prop', () => {
    render(<Input value="test value" data-testid="test-input" readOnly />);

    const input = screen.getByTestId('test-input') as HTMLInputElement;
    expect(input.value).toBe('test value');
  });

  it('handles onChange events', () => {
    const handleChange = vi.fn();
    render(<Input onChange={handleChange} data-testid="test-input" />);

    const input = screen.getByTestId('test-input');
    fireEvent.change(input, { target: { value: 'new value' } });

    expect(handleChange).toHaveBeenCalled();
  });

  it('handles disabled state', () => {
    render(<Input disabled data-testid="test-input" />);

    const input = screen.getByTestId('test-input');
    expect(input).toBeDisabled();
    expect(input).toHaveClass('disabled:cursor-not-allowed', 'disabled:opacity-50');
  });

  it('handles required attribute', () => {
    render(<Input required data-testid="test-input" />);

    const input = screen.getByTestId('test-input');
    expect(input).toBeRequired();
  });

  it('handles focus events', () => {
    const handleFocus = vi.fn();
    const handleBlur = vi.fn();

    render(
      <Input
        onFocus={handleFocus}
        onBlur={handleBlur}
        data-testid="test-input"
      />
    );

    const input = screen.getByTestId('test-input');

    fireEvent.focus(input);
    expect(handleFocus).toHaveBeenCalled();

    fireEvent.blur(input);
    expect(handleBlur).toHaveBeenCalled();
  });

  it('handles keyboard events', () => {
    const handleKeyDown = vi.fn();
    const handleKeyUp = vi.fn();

    render(
      <Input
        onKeyDown={handleKeyDown}
        onKeyUp={handleKeyUp}
        data-testid="test-input"
      />
    );

    const input = screen.getByTestId('test-input');

    fireEvent.keyDown(input, { key: 'Enter' });
    expect(handleKeyDown).toHaveBeenCalled();

    fireEvent.keyUp(input, { key: 'Enter' });
    expect(handleKeyUp).toHaveBeenCalled();
  });

  it('handles min and max attributes for number inputs', () => {
    render(
      <Input
        type="number"
        min={0}
        max={100}
        data-testid="test-input"
      />
    );

    const input = screen.getByTestId('test-input');
    expect(input).toHaveAttribute('min', '0');
    expect(input).toHaveAttribute('max', '100');
  });

  it('handles step attribute for number inputs', () => {
    render(
      <Input
        type="number"
        step={0.1}
        data-testid="test-input"
      />
    );

    const input = screen.getByTestId('test-input');
    expect(input).toHaveAttribute('step', '0.1');
  });

  it('handles maxLength attribute', () => {
    render(<Input maxLength={10} data-testid="test-input" />);

    const input = screen.getByTestId('test-input');
    expect(input).toHaveAttribute('maxLength', '10');
  });

  it('handles pattern attribute', () => {
    render(<Input pattern="[0-9]*" data-testid="test-input" />);

    const input = screen.getByTestId('test-input');
    expect(input).toHaveAttribute('pattern', '[0-9]*');
  });

  it('handles autoComplete attribute', () => {
    render(<Input autoComplete="email" data-testid="test-input" />);

    const input = screen.getByTestId('test-input');
    expect(input).toHaveAttribute('autoComplete', 'email');
  });

  it('handles autoFocus attribute', () => {
    render(<Input autoFocus data-testid="test-input" />);

    const input = screen.getByTestId('test-input');
    // Just verify the input renders without errors when autoFocus is provided
    expect(input).toBeInTheDocument();
  });

  it('handles readOnly attribute', () => {
    render(<Input readOnly data-testid="test-input" />);

    const input = screen.getByTestId('test-input');
    expect(input).toHaveAttribute('readOnly');
  });

  it('handles name attribute', () => {
    render(<Input name="username" data-testid="test-input" />);

    const input = screen.getByTestId('test-input');
    expect(input).toHaveAttribute('name', 'username');
  });

  it('handles id attribute', () => {
    render(<Input id="username-input" data-testid="test-input" />);

    const input = screen.getByTestId('test-input');
    expect(input).toHaveAttribute('id', 'username-input');
  });

  it('handles aria attributes', () => {
    render(
      <Input
        aria-label="Username input"
        aria-describedby="username-help"
        data-testid="test-input"
      />
    );

    const input = screen.getByTestId('test-input');
    expect(input).toHaveAttribute('aria-label', 'Username input');
    expect(input).toHaveAttribute('aria-describedby', 'username-help');
  });

  it('handles file input type', () => {
    render(<Input type="file" accept=".jpg,.png" data-testid="test-input" />);

    const input = screen.getByTestId('test-input');
    expect(input).toHaveAttribute('type', 'file');
    expect(input).toHaveAttribute('accept', '.jpg,.png');
    expect(input).toHaveClass('file:border-0', 'file:bg-transparent');
  });

  it('has correct display name', () => {
    expect(Input.displayName).toBe('Input');
  });

  it('spreads all additional props', () => {
    render(
      <Input
        data-custom="custom-value"
        role="textbox"
        tabIndex={0}
        data-testid="test-input"
      />
    );

    const input = screen.getByTestId('test-input');
    expect(input).toHaveAttribute('data-custom', 'custom-value');
    expect(input).toHaveAttribute('role', 'textbox');
    expect(input).toHaveAttribute('tabIndex', '0');
  });
});
