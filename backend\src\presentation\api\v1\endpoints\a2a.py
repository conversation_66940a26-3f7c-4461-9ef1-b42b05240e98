"""
A2A Protocol endpoints.

This module provides A2A API endpoints for service discovery,
message routing, and inter-service communication.
"""

from typing import List, Optional

from fastapi import APIRouter, Depends, HTTPException, Query, status

from src.application.use_cases.a2a_service import A2AService
from src.domain.entities.a2a import (
    A2AConnectionResponse,
    A2AMessageResponse,
    A2AMessageSend,
    A2ARouteCreate,
    A2ARouteResponse,
    A2AServiceRegister,
    A2AServiceResponse,
    A2AServiceUpdate,
    MessageType,
    ServiceStatus,
)
from src.infrastructure.logging.setup import get_logger
from src.presentation.dependencies.auth import get_current_user

logger = get_logger(__name__)

router = APIRouter()


def get_a2a_service() -> A2AService:
    """Get A2A service dependency."""
    return A2AService()


@router.post("/services/register", response_model=A2AServiceResponse, status_code=status.HTTP_201_CREATED)
async def register_service(
    service_data: A2AServiceRegister,
    current_user: dict = Depends(get_current_user),
    a2a_service: A2AService = Depends(get_a2a_service)
) -> A2AServiceResponse:
    """
    Register a new service.
    
    Args:
        service_data: Service registration data
        current_user: Current authenticated user
        a2a_service: A2A service dependency
        
    Returns:
        A2AServiceResponse: Registered service information
        
    Raises:
        HTTPException: If registration fails
    """
    try:
        service = await a2a_service.register_service(service_data)
        
        logger.info(f"Registered A2A service: {service.id} by user: {current_user['user_id']}")
        return service
        
    except ValueError as e:
        logger.warning(f"Service registration failed: {e}")
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=str(e)
        )
    except Exception as e:
        logger.error(f"Service registration error: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to register service"
        )


@router.delete("/services/{service_id}")
async def unregister_service(
    service_id: str,
    current_user: dict = Depends(get_current_user),
    a2a_service: A2AService = Depends(get_a2a_service)
) -> dict:
    """
    Unregister a service.
    
    Args:
        service_id: Service ID to unregister
        current_user: Current authenticated user
        a2a_service: A2A service dependency
        
    Returns:
        dict: Success message
        
    Raises:
        HTTPException: If unregistration fails
    """
    try:
        unregistered = await a2a_service.unregister_service(service_id)
        
        if not unregistered:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Service not found"
            )
        
        logger.info(f"Unregistered A2A service: {service_id} by user: {current_user['user_id']}")
        return {"message": "Service unregistered successfully"}
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Service unregistration error: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to unregister service"
        )


@router.put("/services/{service_id}", response_model=A2AServiceResponse)
async def update_service(
    service_id: str,
    update_data: A2AServiceUpdate,
    current_user: dict = Depends(get_current_user),
    a2a_service: A2AService = Depends(get_a2a_service)
) -> A2AServiceResponse:
    """
    Update service information.
    
    Args:
        service_id: Service ID
        update_data: Update data
        current_user: Current authenticated user
        a2a_service: A2A service dependency
        
    Returns:
        A2AServiceResponse: Updated service information
        
    Raises:
        HTTPException: If update fails
    """
    try:
        service = await a2a_service.update_service(service_id, update_data)
        
        if not service:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Service not found"
            )
        
        logger.info(f"Updated A2A service: {service_id} by user: {current_user['user_id']}")
        return service
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Service update error: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to update service"
        )


@router.get("/services", response_model=List[A2AServiceResponse])
async def list_services(
    status_filter: Optional[ServiceStatus] = Query(None, description="Filter by service status"),
    healthy_only: bool = Query(False, description="Return only healthy services"),
    current_user: dict = Depends(get_current_user),
    a2a_service: A2AService = Depends(get_a2a_service)
) -> List[A2AServiceResponse]:
    """
    List registered services.
    
    Args:
        status_filter: Filter by service status
        healthy_only: Return only healthy services
        current_user: Current authenticated user
        a2a_service: A2A service dependency
        
    Returns:
        List[A2AServiceResponse]: List of services
    """
    try:
        services = await a2a_service.list_services(status_filter, healthy_only)
        
        logger.debug(f"Listed {len(services)} A2A services for user: {current_user['user_id']}")
        return services
        
    except Exception as e:
        logger.error(f"List services error: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to list services"
        )


@router.get("/services/{service_id}", response_model=A2AServiceResponse)
async def get_service(
    service_id: str,
    current_user: dict = Depends(get_current_user),
    a2a_service: A2AService = Depends(get_a2a_service)
) -> A2AServiceResponse:
    """
    Get service by ID.
    
    Args:
        service_id: Service ID
        current_user: Current authenticated user
        a2a_service: A2A service dependency
        
    Returns:
        A2AServiceResponse: Service information
        
    Raises:
        HTTPException: If service not found
    """
    try:
        service = await a2a_service.get_service(service_id)
        
        if not service:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Service not found"
            )
        
        logger.debug(f"Retrieved A2A service: {service_id}")
        return service
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Get service error: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to retrieve service"
        )


@router.post("/messages/send", response_model=A2AMessageResponse)
async def send_message(
    message_data: A2AMessageSend,
    source_service: str = Query(..., description="Source service ID"),
    current_user: dict = Depends(get_current_user),
    a2a_service: A2AService = Depends(get_a2a_service)
) -> A2AMessageResponse:
    """
    Send message to target service.
    
    Args:
        message_data: Message data
        source_service: Source service ID
        current_user: Current authenticated user
        a2a_service: A2A service dependency
        
    Returns:
        A2AMessageResponse: Message information
        
    Raises:
        HTTPException: If sending fails
    """
    try:
        message = await a2a_service.send_message(source_service, message_data)
        
        logger.info(f"Sent A2A message: {message.id} from {source_service}")
        return message
        
    except ValueError as e:
        logger.warning(f"Message sending failed: {e}")
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=str(e)
        )
    except Exception as e:
        logger.error(f"Message sending error: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to send message"
        )


@router.get("/messages", response_model=List[A2AMessageResponse])
async def list_messages(
    source_service: Optional[str] = Query(None, description="Filter by source service"),
    target_service: Optional[str] = Query(None, description="Filter by target service"),
    message_type: Optional[MessageType] = Query(None, description="Filter by message type"),
    limit: int = Query(100, ge=1, le=1000, description="Maximum number of messages to return"),
    current_user: dict = Depends(get_current_user),
    a2a_service: A2AService = Depends(get_a2a_service)
) -> List[A2AMessageResponse]:
    """
    List messages with optional filtering.
    
    Args:
        source_service: Filter by source service
        target_service: Filter by target service
        message_type: Filter by message type
        limit: Maximum number of messages to return
        current_user: Current authenticated user
        a2a_service: A2A service dependency
        
    Returns:
        List[A2AMessageResponse]: List of messages
    """
    try:
        messages = await a2a_service.list_messages(
            source_service, target_service, message_type, limit
        )
        
        logger.debug(f"Listed {len(messages)} A2A messages")
        return messages
        
    except Exception as e:
        logger.error(f"List messages error: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to list messages"
        )


@router.get("/messages/{message_id}", response_model=A2AMessageResponse)
async def get_message(
    message_id: str,
    current_user: dict = Depends(get_current_user),
    a2a_service: A2AService = Depends(get_a2a_service)
) -> A2AMessageResponse:
    """
    Get message by ID.
    
    Args:
        message_id: Message ID
        current_user: Current authenticated user
        a2a_service: A2A service dependency
        
    Returns:
        A2AMessageResponse: Message information
        
    Raises:
        HTTPException: If message not found
    """
    try:
        message = await a2a_service.get_message(message_id)
        
        if not message:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Message not found"
            )
        
        logger.debug(f"Retrieved A2A message: {message_id}")
        return message
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Get message error: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to retrieve message"
        )


@router.get("/connections", response_model=List[A2AConnectionResponse])
async def list_connections(
    source_service: Optional[str] = Query(None, description="Filter by source service"),
    target_service: Optional[str] = Query(None, description="Filter by target service"),
    current_user: dict = Depends(get_current_user),
    a2a_service: A2AService = Depends(get_a2a_service)
) -> List[A2AConnectionResponse]:
    """
    List connections with optional filtering.
    
    Args:
        source_service: Filter by source service
        target_service: Filter by target service
        current_user: Current authenticated user
        a2a_service: A2A service dependency
        
    Returns:
        List[A2AConnectionResponse]: List of connections
    """
    try:
        connections = await a2a_service.list_connections(source_service, target_service)
        
        logger.debug(f"Listed {len(connections)} A2A connections")
        return connections
        
    except Exception as e:
        logger.error(f"List connections error: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to list connections"
        )


@router.post("/routes", response_model=A2ARouteResponse, status_code=status.HTTP_201_CREATED)
async def create_route(
    route_data: A2ARouteCreate,
    current_user: dict = Depends(get_current_user),
    a2a_service: A2AService = Depends(get_a2a_service)
) -> A2ARouteResponse:
    """
    Create routing rule.
    
    Args:
        route_data: Route creation data
        current_user: Current authenticated user
        a2a_service: A2A service dependency
        
    Returns:
        A2ARouteResponse: Created route information
        
    Raises:
        HTTPException: If creation fails
    """
    try:
        route = await a2a_service.create_route(route_data)
        
        logger.info(f"Created A2A route: {route.id} by user: {current_user['user_id']}")
        return route
        
    except Exception as e:
        logger.error(f"Route creation error: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to create route"
        )


@router.get("/routes", response_model=List[A2ARouteResponse])
async def list_routes(
    current_user: dict = Depends(get_current_user),
    a2a_service: A2AService = Depends(get_a2a_service)
) -> List[A2ARouteResponse]:
    """
    List routing rules.
    
    Args:
        current_user: Current authenticated user
        a2a_service: A2A service dependency
        
    Returns:
        List[A2ARouteResponse]: List of routes
    """
    try:
        routes = await a2a_service.list_routes()
        
        logger.debug(f"Listed {len(routes)} A2A routes")
        return routes
        
    except Exception as e:
        logger.error(f"List routes error: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to list routes"
        )


@router.delete("/routes/{route_id}")
async def delete_route(
    route_id: str,
    current_user: dict = Depends(get_current_user),
    a2a_service: A2AService = Depends(get_a2a_service)
) -> dict:
    """
    Delete routing rule.
    
    Args:
        route_id: Route ID
        current_user: Current authenticated user
        a2a_service: A2A service dependency
        
    Returns:
        dict: Success message
        
    Raises:
        HTTPException: If deletion fails
    """
    try:
        deleted = await a2a_service.delete_route(route_id)
        
        if not deleted:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Route not found"
            )
        
        logger.info(f"Deleted A2A route: {route_id} by user: {current_user['user_id']}")
        return {"message": "Route deleted successfully"}
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Route deletion error: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to delete route"
        )


@router.post("/services/{service_id}/heartbeat")
async def service_heartbeat(
    service_id: str,
    current_user: dict = Depends(get_current_user),
    a2a_service: A2AService = Depends(get_a2a_service)
) -> dict:
    """
    Update service heartbeat.
    
    Args:
        service_id: Service ID
        current_user: Current authenticated user
        a2a_service: A2A service dependency
        
    Returns:
        dict: Success message
        
    Raises:
        HTTPException: If heartbeat fails
    """
    try:
        updated = await a2a_service.heartbeat(service_id)
        
        if not updated:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Service not found"
            )
        
        logger.debug(f"Updated heartbeat for service: {service_id}")
        return {"message": "Heartbeat updated successfully"}
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Heartbeat error: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to update heartbeat"
        )


@router.get("/services/{service_id}/health")
async def get_service_health(
    service_id: str,
    current_user: dict = Depends(get_current_user),
    a2a_service: A2AService = Depends(get_a2a_service)
) -> dict:
    """
    Get service health information.
    
    Args:
        service_id: Service ID
        current_user: Current authenticated user
        a2a_service: A2A service dependency
        
    Returns:
        dict: Health information
        
    Raises:
        HTTPException: If service not found
    """
    try:
        health = await a2a_service.get_service_health(service_id)
        
        if not health:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Service not found"
            )
        
        logger.debug(f"Retrieved health for service: {service_id}")
        return health
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Get service health error: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to retrieve service health"
        )


@router.get("/statistics")
async def get_statistics(
    current_user: dict = Depends(get_current_user),
    a2a_service: A2AService = Depends(get_a2a_service)
) -> dict:
    """
    Get A2A system statistics.
    
    Args:
        current_user: Current authenticated user
        a2a_service: A2A service dependency
        
    Returns:
        dict: System statistics
    """
    try:
        stats = await a2a_service.get_statistics()
        
        logger.debug(f"Retrieved A2A statistics for user: {current_user['user_id']}")
        return stats
        
    except Exception as e:
        logger.error(f"Get statistics error: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to retrieve statistics"
        )
