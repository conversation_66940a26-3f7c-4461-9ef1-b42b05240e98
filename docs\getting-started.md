# Getting Started Guide

## Overview

This guide will help you set up the development environment and get started with the project. The system consists of multiple components that work together to provide a comprehensive knowledge management solution with AI capabilities.

## Prerequisites

- **Node.js** (v18 or later)
- **Docker** and **Docker Compose**
- **Git**
- **PostgreSQL** (v14 or later)
- **MongoDB** (v6 or later)
- **Neo4j** (v5 or later)
- **Qdrant** (latest version)
- **<PERSON><PERSON><PERSON>** (for local AI models)

## Project Structure

The project is organized into several main components:

```
/
├── frontend/               # React frontend application
├── backend/                # Node.js backend API
├── model-manager/          # Local AI model management service
├── docs/                   # Documentation
│   ├── architecture/       # Architecture documentation
│   ├── api/                # API documentation
│   └── guides/             # User and developer guides
├── scripts/                # Development and deployment scripts
└── docker/                 # Docker configuration
```

## Setting Up the Development Environment

### 1. Clone the Repository

```bash
git clone https://github.com/yourusername/knowledge-system.git
cd knowledge-system
```

### 2. Set Up Environment Variables

Create a `.env` file in the root directory:

```
# Database Configuration
POSTGRES_USER=postgres
POSTGRES_PASSWORD=postgres
POSTGRES_DB=knowledge_system
POSTGRES_HOST=localhost
POSTGRES_PORT=5432

MONGODB_URI=mongodb://localhost:27017/knowledge_system

NEO4J_URI=bolt://localhost:7687
NEO4J_USER=neo4j
NEO4J_PASSWORD=password

QDRANT_URL=http://localhost:6333

# API Configuration
API_PORT=3001
API_SECRET=your_secret_key

# Model Manager Configuration
MODEL_MANAGER_PORT=3002
OLLAMA_URL=http://localhost:11434
MODELS_DIR=./models
```

### 3. Start the Databases with Docker

```bash
docker-compose -f docker/docker-compose.dev.yml up -d
```

This will start PostgreSQL, MongoDB, Neo4j, and Qdrant containers.

### 4. Install Dependencies

```bash
# Install frontend dependencies
cd frontend
npm install

# Install backend dependencies
cd ../backend
npm install

# Install model manager dependencies
cd ../model-manager
npm install
```

### 5. Set Up Ollama

Follow the instructions at [Ollama's website](https://ollama.ai/) to install Ollama on your system.

Pull a basic model to get started:

```bash
ollama pull llama2
```

### 6. Run Database Migrations

```bash
cd backend
npm run migrate
```

### 7. Start the Development Servers

In separate terminal windows:

```bash
# Start the frontend
cd frontend
npm run dev

# Start the backend
cd backend
npm run dev

# Start the model manager
cd model-manager
npm run dev
```

## Component Development Guides

### Frontend Development

The frontend is built with React, TypeScript, and Vite. It uses a feature-based folder structure.

To create a new feature:

1. Create a new folder in `frontend/src/features/`
2. Add the necessary components, hooks, and API clients
3. Export the public API from an `index.ts` file

Example:

```typescript
// frontend/src/features/myfeature/index.ts
export { MyComponent } from './components/MyComponent';
export { useMyFeature } from './hooks/useMyFeature';
export type { MyFeatureData } from './types';
```

### Backend Development

The backend uses Node.js with Express and TypeScript. It follows a layered architecture with controllers, services, and repositories.

To create a new API endpoint:

1. Create a controller in `backend/src/api/controllers/`
2. Create a service in `backend/src/services/`
3. Create a repository in `backend/src/repositories/` if needed
4. Add routes in `backend/src/api/routes/`

Example:

```typescript
// backend/src/api/routes/myfeature.ts
import { Router } from 'express';
import { MyFeatureController } from '../controllers/myfeature-controller';

const router = Router();
const controller = new MyFeatureController();

router.get('/', controller.getAll);
router.get('/:id', controller.getById);
router.post('/', controller.create);
router.put('/:id', controller.update);
router.delete('/:id', controller.delete);

export default router;
```

### Model Manager Development

The model manager is responsible for downloading, managing, and running AI models locally. It provides a REST API for the backend to interact with.

To add support for a new model type:

1. Create an adapter in `model-manager/src/adapters/`
2. Register the adapter in `model-manager/src/models/model-registry.ts`
3. Add API endpoints in `model-manager/src/api/routes/`

Example:

```typescript
// model-manager/src/adapters/my-model-adapter.ts
import { ModelAdapter } from '../types';

export class MyModelAdapter implements ModelAdapter {
  // Implement the ModelAdapter interface
}
```

## Development Workflow

1. **Pick a Task**: Choose a task from the project board or create a new issue
2. **Create a Branch**: Create a new branch for your task (`feature/my-feature` or `fix/my-bug`)
3. **Implement**: Write code and tests for your feature or fix
4. **Test Locally**: Run tests and verify your changes work locally
5. **Create a Pull Request**: Push your branch and create a pull request
6. **Code Review**: Address feedback from code review
7. **Merge**: Once approved, merge your pull request

## Testing

### Frontend Testing

```bash
cd frontend
npm run test
```

### Backend Testing

```bash
cd backend
npm run test
```

### Model Manager Testing

```bash
cd model-manager
npm run test
```

## Documentation

- Architecture documentation is in `docs/architecture/`
- API documentation is in `docs/api/`
- User and developer guides are in `docs/guides/`

## Troubleshooting

### Database Connection Issues

If you have trouble connecting to the databases:

1. Check if the Docker containers are running: `docker ps`
2. Verify the environment variables match the Docker configuration
3. Try restarting the containers: `docker-compose -f docker/docker-compose.dev.yml restart`

### Ollama Issues

If Ollama is not working correctly:

1. Check if Ollama is running: `ollama ps`
2. Verify the Ollama URL in the environment variables
3. Try restarting Ollama: `ollama restart`

### Model Manager Issues

If the model manager is not working correctly:

1. Check the logs: `cd model-manager && npm run logs`
2. Verify the model directory exists and has write permissions
3. Check if Ollama is running and accessible

## Resources

- [Project Roadmap](./project-roadmap.md)
- [Architecture Documentation](./architecture/)
- [API Documentation](./api/)
- [User Guides](./guides/)

## Contributing

Please read the [Contributing Guide](./CONTRIBUTING.md) before submitting pull requests.

## License

This project is licensed under the MIT License - see the [LICENSE](./LICENSE) file for details.
