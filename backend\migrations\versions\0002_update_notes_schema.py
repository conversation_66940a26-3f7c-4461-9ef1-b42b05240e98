"""
Update notes schema to match new models.

Revision ID: 0002
Revises: 0001
Create Date: 2024-01-15 14:00:00.000000

"""
from typing import Sequence, Union

import sqlalchemy as sa
from alembic import op
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision: str = '0002'
down_revision: Union[str, None] = '0001'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    """Upgrade database schema."""
    
    # Create NoteFormat enum type
    noteformat_enum = postgresql.ENUM('MARKDOWN', 'RICHTEXT', name='noteformat')
    noteformat_enum.create(op.get_bind())
    
    # Create folders table
    op.create_table(
        'folder_model',
        sa.Column('id', postgresql.UUID(as_uuid=True), nullable=False),
        sa.Column('created_at', sa.DateTime(timezone=True), nullable=False),
        sa.Column('updated_at', sa.DateTime(timezone=True), nullable=False),
        sa.Column('name', sa.String(length=255), nullable=False),
        sa.Column('parent_id', postgresql.UUID(as_uuid=True), nullable=True),
        sa.Column('user_id', postgresql.UUID(as_uuid=True), nullable=False),
        sa.Column('is_archived', sa.Boolean(), nullable=False),
        sa.ForeignKeyConstraint(['parent_id'], ['folder_model.id'], ondelete='SET NULL'),
        sa.ForeignKeyConstraint(['user_id'], ['users.id'], ondelete='CASCADE'),
        sa.PrimaryKeyConstraint('id')
    )
    
    # Create indexes for folders
    op.create_index(op.f('ix_folder_model_id'), 'folder_model', ['id'], unique=False)
    op.create_index(op.f('ix_folder_model_name'), 'folder_model', ['name'], unique=False)
    op.create_index(op.f('ix_folder_model_parent_id'), 'folder_model', ['parent_id'], unique=False)
    op.create_index(op.f('ix_folder_model_user_id'), 'folder_model', ['user_id'], unique=False)
    op.create_index(op.f('ix_folder_model_is_archived'), 'folder_model', ['is_archived'], unique=False)
    
    # Create tags table
    op.create_table(
        'tag_model',
        sa.Column('id', postgresql.UUID(as_uuid=True), nullable=False),
        sa.Column('created_at', sa.DateTime(timezone=True), nullable=False),
        sa.Column('updated_at', sa.DateTime(timezone=True), nullable=False),
        sa.Column('name', sa.String(length=255), nullable=False),
        sa.Column('color', sa.String(length=50), nullable=True),
        sa.Column('user_id', postgresql.UUID(as_uuid=True), nullable=False),
        sa.ForeignKeyConstraint(['user_id'], ['users.id'], ondelete='CASCADE'),
        sa.PrimaryKeyConstraint('id'),
        sa.UniqueConstraint('name', 'user_id', name='uq_tag_name_user')
    )
    
    # Create indexes for tags
    op.create_index(op.f('ix_tag_model_id'), 'tag_model', ['id'], unique=False)
    op.create_index(op.f('ix_tag_model_name'), 'tag_model', ['name'], unique=False)
    op.create_index(op.f('ix_tag_model_user_id'), 'tag_model', ['user_id'], unique=False)
    
    # Create notes table
    op.create_table(
        'note_model',
        sa.Column('id', postgresql.UUID(as_uuid=True), nullable=False),
        sa.Column('created_at', sa.DateTime(timezone=True), nullable=False),
        sa.Column('updated_at', sa.DateTime(timezone=True), nullable=False),
        sa.Column('title', sa.String(length=255), nullable=False),
        sa.Column('content', sa.Text(), nullable=False),
        sa.Column('content_format', noteformat_enum, nullable=False),
        sa.Column('content_version', sa.Integer(), nullable=False),
        sa.Column('folder_id', postgresql.UUID(as_uuid=True), nullable=True),
        sa.Column('created_by', postgresql.UUID(as_uuid=True), nullable=False),
        sa.Column('last_edited_by', postgresql.UUID(as_uuid=True), nullable=False),
        sa.Column('is_archived', sa.Boolean(), nullable=False),
        sa.Column('is_starred', sa.Boolean(), nullable=False),
        sa.Column('tags', sa.JSON(), nullable=False),
        sa.ForeignKeyConstraint(['created_by'], ['users.id'], ondelete='CASCADE'),
        sa.ForeignKeyConstraint(['folder_id'], ['folder_model.id'], ondelete='SET NULL'),
        sa.ForeignKeyConstraint(['last_edited_by'], ['users.id'], ondelete='CASCADE'),
        sa.PrimaryKeyConstraint('id')
    )
    
    # Create indexes for notes
    op.create_index(op.f('ix_note_model_id'), 'note_model', ['id'], unique=False)
    op.create_index(op.f('ix_note_model_title'), 'note_model', ['title'], unique=False)
    op.create_index(op.f('ix_note_model_folder_id'), 'note_model', ['folder_id'], unique=False)
    op.create_index(op.f('ix_note_model_created_by'), 'note_model', ['created_by'], unique=False)
    op.create_index(op.f('ix_note_model_last_edited_by'), 'note_model', ['last_edited_by'], unique=False)
    op.create_index(op.f('ix_note_model_is_archived'), 'note_model', ['is_archived'], unique=False)
    op.create_index(op.f('ix_note_model_is_starred'), 'note_model', ['is_starred'], unique=False)


def downgrade() -> None:
    """Downgrade database schema."""
    
    # Drop indexes for notes
    op.drop_index(op.f('ix_note_model_is_starred'), table_name='note_model')
    op.drop_index(op.f('ix_note_model_is_archived'), table_name='note_model')
    op.drop_index(op.f('ix_note_model_last_edited_by'), table_name='note_model')
    op.drop_index(op.f('ix_note_model_created_by'), table_name='note_model')
    op.drop_index(op.f('ix_note_model_folder_id'), table_name='note_model')
    op.drop_index(op.f('ix_note_model_title'), table_name='note_model')
    op.drop_index(op.f('ix_note_model_id'), table_name='note_model')
    
    # Drop notes table
    op.drop_table('note_model')
    
    # Drop indexes for tags
    op.drop_index(op.f('ix_tag_model_user_id'), table_name='tag_model')
    op.drop_index(op.f('ix_tag_model_name'), table_name='tag_model')
    op.drop_index(op.f('ix_tag_model_id'), table_name='tag_model')
    
    # Drop tags table
    op.drop_table('tag_model')
    
    # Drop indexes for folders
    op.drop_index(op.f('ix_folder_model_is_archived'), table_name='folder_model')
    op.drop_index(op.f('ix_folder_model_user_id'), table_name='folder_model')
    op.drop_index(op.f('ix_folder_model_parent_id'), table_name='folder_model')
    op.drop_index(op.f('ix_folder_model_name'), table_name='folder_model')
    op.drop_index(op.f('ix_folder_model_id'), table_name='folder_model')
    
    # Drop folders table
    op.drop_table('folder_model')
    
    # Drop enum type
    noteformat_enum = postgresql.ENUM('MARKDOWN', 'RICHTEXT', name='noteformat')
    noteformat_enum.drop(op.get_bind())
