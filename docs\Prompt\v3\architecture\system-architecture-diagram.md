# System Architecture Diagram

This diagram illustrates the high-level architecture of the Lonors AI Platform, showing the relationships between frontend, backend, and database components.

```mermaid
graph TB
    subgraph "Frontend (React + TypeScript)"
        FE["React Application"]
        FSD["Feature Slice Design"]
        UI["ShadCN UI + Tailwind"]
        STATE["Zustand + TanStack Query"]
        ANIME["Anime.js Animations"]

        FE --> FSD
        FE --> UI
        FE --> STATE
        FE --> ANIME
    end

    subgraph "Backend (Python + FastAPI)"
        BE["FastAPI Application"]
        CA["Clean Architecture"]
        DI["Dependency Injection"]
        ASYNC["Async/Await"]

        BE --> CA
        BE --> DI
        BE --> ASYNC
    end

    subgraph "Protocols"
        MCP["Model Context Protocol"]
        AGUI["AG-UI Protocol"]
        A2A["A2A Protocol"]
        WS["WebSocket Communication"]

        MCP --> BE
        AGUI --> BE
        A2A --> BE
        WS --> BE
    end

    subgraph "Database Layer"
        PG["PostgreSQL"]
        REDIS["Redis Cache"]
        ORM["SQLAlchemy ORM"]
        MIGRATIONS["Alembic Migrations"]

        ORM --> PG
        MIGRATIONS --> PG
        BE --> ORM
        BE --> REDIS
    end

    subgraph "Infrastructure"
        DOCKER["Docker + Compose"]
        NGINX["Nginx Reverse Proxy"]
        CI["GitHub Actions CI/CD"]

        DOCKER --> FE
        DOCKER --> BE
        DOCKER --> PG
        DOCKER --> REDIS
        NGINX --> FE
        NGINX --> BE
        CI --> DOCKER
    end

    FE <--> BE
    BE <--> PG
    BE <--> REDIS
    FE <--> WS
```

## Component Descriptions

### Frontend
- **React Application**: Modern React 18 with TypeScript 5
- **Feature Slice Design**: Architecture pattern organizing code by business domains
- **ShadCN UI + Tailwind**: Component library with utility-first CSS
- **Zustand + TanStack Query**: State management and data fetching
- **Anime.js Animations**: Animation library for UI interactions

### Backend
- **FastAPI Application**: High-performance async API framework
- **Clean Architecture**: Domain-driven design with separation of concerns
- **Dependency Injection**: Loose coupling between components
- **Async/Await**: Non-blocking I/O operations

### Protocols
- **Model Context Protocol (MCP)**: AI model integration and context management
- **AG-UI Protocol**: Dynamic UI component communication
- **A2A Protocol**: Application-to-application communication
- **WebSocket Communication**: Real-time bidirectional communication

### Database Layer
- **PostgreSQL**: Primary relational database
- **Redis Cache**: In-memory data store for caching and sessions
- **SQLAlchemy ORM**: Object-relational mapping for database operations
- **Alembic Migrations**: Database schema version control

### Infrastructure
- **Docker + Compose**: Containerization and orchestration
- **Nginx Reverse Proxy**: Production-ready web server and load balancer
- **GitHub Actions CI/CD**: Continuous integration and deployment pipeline
