import { afterEach, beforeEach, describe, expect, it, vi } from 'vitest';
import {
    capitalize,
    copyToClipboard,
    isEmpty,
    sleep,
    toCamelCase,
    toKebabCase
} from '../utils';

describe('Extended Utils', () => {
  describe('isEmpty', () => {
    it('returns true for null and undefined', () => {
      expect(isEmpty(null)).toBe(true);
      expect(isEmpty(undefined)).toBe(true);
    });

    it('returns true for empty strings', () => {
      expect(isEmpty('')).toBe(true);
      expect(isEmpty('   ')).toBe(true);
      expect(isEmpty('\t\n')).toBe(true);
    });

    it('returns false for non-empty strings', () => {
      expect(isEmpty('hello')).toBe(false);
      expect(isEmpty(' hello ')).toBe(false);
    });

    it('returns true for empty arrays', () => {
      expect(isEmpty([])).toBe(true);
    });

    it('returns false for non-empty arrays', () => {
      expect(isEmpty([1, 2, 3])).toBe(false);
      expect(isEmpty([''])).toBe(false);
    });

    it('returns true for empty objects', () => {
      expect(isEmpty({})).toBe(true);
    });

    it('returns false for non-empty objects', () => {
      expect(isEmpty({ key: 'value' })).toBe(false);
      expect(isEmpty({ a: null })).toBe(false);
    });

    it('returns false for other types', () => {
      expect(isEmpty(0)).toBe(false);
      expect(isEmpty(false)).toBe(false);
      // Note: Date objects are considered empty by isEmpty since they have no enumerable keys
      expect(isEmpty(new Date())).toBe(true);
    });
  });

  describe('capitalize', () => {
    it('capitalizes the first letter', () => {
      expect(capitalize('hello')).toBe('Hello');
      expect(capitalize('world')).toBe('World');
    });

    it('handles single character strings', () => {
      expect(capitalize('a')).toBe('A');
      expect(capitalize('z')).toBe('Z');
    });

    it('handles empty strings', () => {
      expect(capitalize('')).toBe('');
    });

    it('preserves the rest of the string', () => {
      expect(capitalize('hELLO')).toBe('HELLO');
      expect(capitalize('camelCase')).toBe('CamelCase');
    });

    it('handles strings starting with numbers', () => {
      expect(capitalize('123abc')).toBe('123abc');
    });

    it('handles strings with spaces', () => {
      expect(capitalize('hello world')).toBe('Hello world');
    });
  });

  describe('toKebabCase', () => {
    it('converts camelCase to kebab-case', () => {
      expect(toKebabCase('camelCase')).toBe('camel-case');
      expect(toKebabCase('myVariableName')).toBe('my-variable-name');
    });

    it('converts PascalCase to kebab-case', () => {
      expect(toKebabCase('PascalCase')).toBe('pascal-case');
      expect(toKebabCase('MyComponent')).toBe('my-component');
    });

    it('handles spaces and underscores', () => {
      expect(toKebabCase('hello world')).toBe('hello-world');
      expect(toKebabCase('hello_world')).toBe('hello-world');
      expect(toKebabCase('hello   world')).toBe('hello-world');
    });

    it('handles mixed separators', () => {
      expect(toKebabCase('hello_world test')).toBe('hello-world-test');
      expect(toKebabCase('myVar_name test')).toBe('my-var-name-test');
    });

    it('handles already kebab-case strings', () => {
      expect(toKebabCase('kebab-case')).toBe('kebab-case');
      expect(toKebabCase('already-kebab')).toBe('already-kebab');
    });

    it('handles empty strings', () => {
      expect(toKebabCase('')).toBe('');
    });
  });

  describe('toCamelCase', () => {
    it('converts kebab-case to camelCase', () => {
      expect(toCamelCase('kebab-case')).toBe('kebabCase');
      expect(toCamelCase('my-variable-name')).toBe('myVariableName');
    });

    it('converts snake_case to camelCase', () => {
      expect(toCamelCase('snake_case')).toBe('snakeCase');
      expect(toCamelCase('my_variable_name')).toBe('myVariableName');
    });

    it('handles spaces', () => {
      expect(toCamelCase('hello world')).toBe('helloWorld');
      expect(toCamelCase('my variable name')).toBe('myVariableName');
    });

    it('handles mixed separators', () => {
      expect(toCamelCase('hello-world_test case')).toBe('helloWorldTestCase');
    });

    it('handles PascalCase input', () => {
      expect(toCamelCase('PascalCase')).toBe('pascalCase');
      expect(toCamelCase('MyComponent')).toBe('myComponent');
    });

    it('handles already camelCase strings', () => {
      expect(toCamelCase('camelCase')).toBe('camelCase');
      expect(toCamelCase('alreadyCamel')).toBe('alreadyCamel');
    });

    it('handles empty strings', () => {
      expect(toCamelCase('')).toBe('');
    });
  });

  describe('sleep', () => {
    beforeEach(() => {
      vi.useFakeTimers();
    });

    afterEach(() => {
      vi.useRealTimers();
    });

    it('returns a promise that resolves after specified time', async () => {
      const promise = sleep(1000);

      // Promise should not be resolved yet
      let resolved = false;
      promise.then(() => { resolved = true; });

      expect(resolved).toBe(false);

      // Fast-forward time
      vi.advanceTimersByTime(1000);
      await promise;

      expect(resolved).toBe(true);
    });

    it('handles zero delay', async () => {
      const promise = sleep(0);
      vi.advanceTimersByTime(0);
      await promise;

      // Should resolve without error
      expect(true).toBe(true);
    });

    it('can be used with async/await', async () => {
      const start = Date.now();
      const sleepPromise = sleep(500);

      vi.advanceTimersByTime(500);
      await sleepPromise;

      // Should complete without throwing
      expect(true).toBe(true);
    });
  });

  describe('copyToClipboard', () => {
    beforeEach(() => {
      // Mock navigator.clipboard
      Object.assign(navigator, {
        clipboard: {
          writeText: vi.fn(),
        },
      });

      // Mock document methods for fallback
      document.createElement = vi.fn();
      document.body.appendChild = vi.fn();
      document.body.removeChild = vi.fn();
      document.execCommand = vi.fn();
    });

    afterEach(() => {
      vi.restoreAllMocks();
    });

    it('uses navigator.clipboard when available', async () => {
      const mockWriteText = vi.fn().mockResolvedValue(undefined);
      navigator.clipboard.writeText = mockWriteText;

      const result = await copyToClipboard('test text');

      expect(mockWriteText).toHaveBeenCalledWith('test text');
      expect(result).toBe(true);
    });

    it('falls back to execCommand when clipboard API fails', async () => {
      const mockWriteText = vi.fn().mockRejectedValue(new Error('Not supported'));
      navigator.clipboard.writeText = mockWriteText;

      const mockTextArea = {
        value: '',
        focus: vi.fn(),
        select: vi.fn(),
      };

      (document.createElement as any).mockReturnValue(mockTextArea);
      (document.execCommand as any).mockReturnValue(true);

      const result = await copyToClipboard('test text');

      expect(document.createElement).toHaveBeenCalledWith('textarea');
      expect(mockTextArea.value).toBe('test text');
      expect(mockTextArea.focus).toHaveBeenCalled();
      expect(mockTextArea.select).toHaveBeenCalled();
      expect(document.execCommand).toHaveBeenCalledWith('copy');
      expect(result).toBe(true);
    });

    it('returns false when both methods fail', async () => {
      const mockWriteText = vi.fn().mockRejectedValue(new Error('Not supported'));
      navigator.clipboard.writeText = mockWriteText;

      const mockTextArea = {
        value: '',
        focus: vi.fn(),
        select: vi.fn(),
      };

      (document.createElement as any).mockReturnValue(mockTextArea);
      (document.execCommand as any).mockImplementation(() => {
        throw new Error('execCommand failed');
      });

      const result = await copyToClipboard('test text');

      expect(result).toBe(false);
    });

    it('handles execCommand throwing an error', async () => {
      const mockWriteText = vi.fn().mockRejectedValue(new Error('Not supported'));
      navigator.clipboard.writeText = mockWriteText;

      const mockTextArea = {
        value: '',
        focus: vi.fn(),
        select: vi.fn(),
      };

      (document.createElement as any).mockReturnValue(mockTextArea);
      (document.execCommand as any).mockImplementation(() => {
        throw new Error('execCommand failed');
      });

      const result = await copyToClipboard('test text');

      expect(result).toBe(false);
      expect(document.body.removeChild).toHaveBeenCalledWith(mockTextArea);
    });
  });
});
