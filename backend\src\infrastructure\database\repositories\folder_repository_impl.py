"""
Folder Repository Implementation

This module implements the folder repository interface using SQLAlchemy.
"""

from uuid import UUID

from sqlalchemy import select
from sqlalchemy.ext.asyncio import AsyncSession

from src.domain.entities.folder import Folder
from src.domain.repositories.folder_repository import FolderRepository
from src.infrastructure.database.models.folder import FolderModel


class SQLAlchemyFolderRepository(FolderRepository):
    """SQLAlchemy implementation of the folder repository."""

    def __init__(self, session: AsyncSession):
        """
        Initialize the repository.

        Args:
            session: SQLAlchemy async session
        """
        self._session = session

    async def get_by_id(self, folder_id: UUID) -> Folder | None:
        """
        Get a folder by its ID.

        Args:
            folder_id: The ID of the folder to retrieve

        Returns:
            The folder if found, None otherwise
        """
        query = select(FolderModel).where(FolderModel.id == folder_id)
        result = await self._session.execute(query)
        folder_model = result.scalar_one_or_none()

        if folder_model is None:
            return None

        return folder_model.to_domain_entity()

    async def get_all_by_user(
        self,
        user_id: UUID,
        parent_id: UUID | None = None,
        include_archived: bool = False,
    ) -> list[Folder]:
        """
        Get all folders for a user with optional filtering.

        Args:
            user_id: The ID of the user
            parent_id: Optional parent folder ID to filter by
            include_archived: Whether to include archived folders

        Returns:
            List of folders matching the criteria
        """
        query = select(FolderModel).where(FolderModel.user_id == user_id)

        # Parent filter
        if parent_id is not None:
            query = query.where(FolderModel.parent_id == parent_id)

        # Archive filter
        if not include_archived:
            query = query.where(FolderModel.is_archived == False)  # noqa: E712

        result = await self._session.execute(query)
        folder_models = result.scalars().all()

        return [folder_model.to_domain_entity() for folder_model in folder_models]

    async def create(self, folder: Folder) -> Folder:
        """
        Create a new folder.

        Args:
            folder: The folder to create

        Returns:
            The created folder with any generated fields
        """
        # Create folder model using the from_domain_entity method
        folder_model = FolderModel.from_domain_entity(folder)

        self._session.add(folder_model)
        await self._session.flush()
        await self._session.refresh(folder_model)

        return folder_model.to_domain_entity()

    async def update(self, folder: Folder) -> Folder:
        """
        Update an existing folder.

        Args:
            folder: The folder to update

        Returns:
            The updated folder
        """
        query = select(FolderModel).where(FolderModel.id == folder.id)
        result = await self._session.execute(query)
        folder_model = result.scalar_one_or_none()

        if folder_model is None:
            raise ValueError(f"Folder with ID {folder.id} not found")

        folder_model.name = folder.name
        folder_model.parent_id = folder.parent_id
        folder_model.is_archived = folder.is_archived
        folder_model.updated_at = folder.updated_at

        await self._session.flush()
        await self._session.refresh(folder_model)

        return folder_model.to_domain_entity()

    async def delete(self, folder_id: UUID) -> bool:
        """
        Delete a folder by its ID.

        Args:
            folder_id: The ID of the folder to delete

        Returns:
            True if the folder was deleted, False otherwise
        """
        query = select(FolderModel).where(FolderModel.id == folder_id)
        result = await self._session.execute(query)
        folder_model = result.scalar_one_or_none()

        if folder_model is None:
            return False

        await self._session.delete(folder_model)
        await self._session.flush()

        return True
