"""
Tests for user service.

This module tests the user service use cases
and business logic orchestration.
"""

import pytest
import uuid
from unittest.mock import AsyncMock, Mock

from src.application.use_cases.user_service import UserService
from src.domain.entities.user import (
    User,
    UserCreate,
    UserRole,
    UserStatus,
    LoginRequest,
    PasswordChangeRequest,
    UserUpdate,
)


class TestUserService:
    """Test cases for user service."""
    
    @pytest.fixture
    def mock_user_repository(self):
        """Create mock user repository."""
        return AsyncMock()
    
    @pytest.fixture
    def user_service(self, mock_user_repository):
        """Create user service with mock repository."""
        return UserService(mock_user_repository)
    
    @pytest.fixture
    def sample_user_create(self):
        """Create sample user creation data."""
        return UserCreate(
            email="<EMAIL>",
            username="testuser",
            full_name="Test User",
            password="password123",
        )
    
    @pytest.fixture
    def sample_user(self):
        """Create sample user entity."""
        return User(
            id=uuid.uuid4(),
            email="<EMAIL>",
            username="testuser",
            full_name="Test User",
            hashed_password="hashed_password",
            role=UserRole.USER,
            status=UserStatus.ACTIVE,
            is_verified=True,
        )
    
    async def test_register_user_success(self, user_service, mock_user_repository, sample_user_create):
        """Test successful user registration."""
        # Mock repository responses
        mock_user_repository.email_exists.return_value = False
        mock_user_repository.username_exists.return_value = False
        mock_user_repository.create.return_value = User(
            id=uuid.uuid4(),
            email=sample_user_create.email.lower(),
            username=sample_user_create.username.lower(),
            full_name=sample_user_create.full_name,
            hashed_password="hashed_password",
            status=UserStatus.PENDING_VERIFICATION,
            is_verified=False,
        )
        
        # Register user
        result = await user_service.register_user(sample_user_create)
        
        # Verify calls
        mock_user_repository.email_exists.assert_called_once_with(sample_user_create.email)
        mock_user_repository.username_exists.assert_called_once_with(sample_user_create.username)
        mock_user_repository.create.assert_called_once()
        
        # Verify result
        assert result.email == sample_user_create.email.lower()
        assert result.username == sample_user_create.username.lower()
        assert result.status == UserStatus.PENDING_VERIFICATION
        assert result.is_verified is False
    
    async def test_register_user_email_exists(self, user_service, mock_user_repository, sample_user_create):
        """Test user registration with existing email."""
        # Mock email exists
        mock_user_repository.email_exists.return_value = True
        
        # Registration should fail
        with pytest.raises(ValueError, match="Email already registered"):
            await user_service.register_user(sample_user_create)
        
        # Verify only email check was called
        mock_user_repository.email_exists.assert_called_once()
        mock_user_repository.username_exists.assert_not_called()
        mock_user_repository.create.assert_not_called()
    
    async def test_register_user_username_exists(self, user_service, mock_user_repository, sample_user_create):
        """Test user registration with existing username."""
        # Mock responses
        mock_user_repository.email_exists.return_value = False
        mock_user_repository.username_exists.return_value = True
        
        # Registration should fail
        with pytest.raises(ValueError, match="Username already taken"):
            await user_service.register_user(sample_user_create)
        
        # Verify calls
        mock_user_repository.email_exists.assert_called_once()
        mock_user_repository.username_exists.assert_called_once()
        mock_user_repository.create.assert_not_called()
    
    async def test_authenticate_user_success(self, user_service, mock_user_repository, sample_user):
        """Test successful user authentication."""
        # Mock repository response
        mock_user_repository.get_by_email.return_value = sample_user
        mock_user_repository.update.return_value = sample_user
        
        # Mock password verification
        with pytest.mock.patch('src.application.use_cases.user_service.password_manager') as mock_password_manager:
            mock_password_manager.verify_password.return_value = True
            
            # Mock JWT manager
            with pytest.mock.patch('src.application.use_cases.user_service.jwt_manager') as mock_jwt_manager:
                mock_jwt_manager.create_access_token.return_value = "access_token"
                mock_jwt_manager.create_refresh_token.return_value = "refresh_token"
                mock_jwt_manager.access_token_expire.total_seconds.return_value = 3600
                
                # Authenticate user
                login_request = LoginRequest(email=sample_user.email, password="password123")
                result = await user_service.authenticate_user(login_request)
                
                # Verify calls
                mock_user_repository.get_by_email.assert_called_once_with(sample_user.email)
                mock_password_manager.verify_password.assert_called_once()
                mock_user_repository.update.assert_called_once()
                
                # Verify result
                assert result.access_token == "access_token"
                assert result.refresh_token == "refresh_token"
                assert result.user.email == sample_user.email
    
    async def test_authenticate_user_not_found(self, user_service, mock_user_repository):
        """Test authentication with non-existent user."""
        # Mock repository response
        mock_user_repository.get_by_email.return_value = None
        
        # Authentication should fail
        login_request = LoginRequest(email="<EMAIL>", password="password")
        with pytest.raises(ValueError, match="Invalid email or password"):
            await user_service.authenticate_user(login_request)
    
    async def test_authenticate_user_wrong_password(self, user_service, mock_user_repository, sample_user):
        """Test authentication with wrong password."""
        # Mock repository response
        mock_user_repository.get_by_email.return_value = sample_user
        
        # Mock password verification failure
        with pytest.mock.patch('src.application.use_cases.user_service.password_manager') as mock_password_manager:
            mock_password_manager.verify_password.return_value = False
            
            # Authentication should fail
            login_request = LoginRequest(email=sample_user.email, password="wrong_password")
            with pytest.raises(ValueError, match="Invalid email or password"):
                await user_service.authenticate_user(login_request)
    
    async def test_authenticate_user_inactive(self, user_service, mock_user_repository):
        """Test authentication with inactive user."""
        # Create inactive user
        inactive_user = User(
            id=uuid.uuid4(),
            email="<EMAIL>",
            username="testuser",
            hashed_password="hashed_password",
            status=UserStatus.SUSPENDED,
            is_verified=True,
        )
        
        # Mock repository response
        mock_user_repository.get_by_email.return_value = inactive_user
        
        # Mock password verification success
        with pytest.mock.patch('src.application.use_cases.user_service.password_manager') as mock_password_manager:
            mock_password_manager.verify_password.return_value = True
            
            # Authentication should fail
            login_request = LoginRequest(email=inactive_user.email, password="password")
            with pytest.raises(ValueError, match="Account is not active or verified"):
                await user_service.authenticate_user(login_request)
    
    async def test_get_user_by_id(self, user_service, mock_user_repository, sample_user):
        """Test getting user by ID."""
        # Mock repository response
        mock_user_repository.get_by_id.return_value = sample_user
        
        # Get user
        result = await user_service.get_user_by_id(sample_user.id)
        
        # Verify call
        mock_user_repository.get_by_id.assert_called_once_with(sample_user.id)
        
        # Verify result
        assert result is not None
        assert result.id == sample_user.id
        assert result.email == sample_user.email
    
    async def test_get_user_by_id_not_found(self, user_service, mock_user_repository):
        """Test getting non-existent user by ID."""
        # Mock repository response
        mock_user_repository.get_by_id.return_value = None
        
        # Get user
        user_id = uuid.uuid4()
        result = await user_service.get_user_by_id(user_id)
        
        # Verify result
        assert result is None
    
    async def test_update_user_profile(self, user_service, mock_user_repository, sample_user):
        """Test updating user profile."""
        # Mock repository responses
        mock_user_repository.get_by_id.return_value = sample_user
        mock_user_repository.username_exists.return_value = False
        
        updated_user = User(**sample_user.dict())
        updated_user.full_name = "Updated Name"
        updated_user.username = "newusername"
        mock_user_repository.update.return_value = updated_user
        
        # Update profile
        update_data = UserUpdate(full_name="Updated Name", username="newusername")
        result = await user_service.update_user_profile(sample_user.id, update_data)
        
        # Verify calls
        mock_user_repository.get_by_id.assert_called_once_with(sample_user.id)
        mock_user_repository.username_exists.assert_called_once_with("newusername")
        mock_user_repository.update.assert_called_once()
        
        # Verify result
        assert result.full_name == "Updated Name"
        assert result.username == "newusername"
    
    async def test_update_user_profile_username_taken(self, user_service, mock_user_repository, sample_user):
        """Test updating profile with taken username."""
        # Mock repository responses
        mock_user_repository.get_by_id.return_value = sample_user
        mock_user_repository.username_exists.return_value = True
        
        # Update should fail
        update_data = UserUpdate(username="takenusername")
        with pytest.raises(ValueError, match="Username already taken"):
            await user_service.update_user_profile(sample_user.id, update_data)
    
    async def test_change_password(self, user_service, mock_user_repository, sample_user):
        """Test changing user password."""
        # Mock repository responses
        mock_user_repository.get_by_id.return_value = sample_user
        mock_user_repository.update.return_value = sample_user
        
        # Mock password verification and hashing
        with pytest.mock.patch('src.application.use_cases.user_service.password_manager') as mock_password_manager:
            mock_password_manager.verify_password.return_value = True
            mock_password_manager.hash_password.return_value = "new_hashed_password"
            
            # Change password
            password_data = PasswordChangeRequest(
                current_password="old_password",
                new_password="new_password"
            )
            await user_service.change_password(sample_user.id, password_data)
            
            # Verify calls
            mock_user_repository.get_by_id.assert_called_once_with(sample_user.id)
            mock_password_manager.verify_password.assert_called_once()
            mock_password_manager.hash_password.assert_called_once_with("new_password")
            mock_user_repository.update.assert_called_once()
    
    async def test_change_password_wrong_current(self, user_service, mock_user_repository, sample_user):
        """Test changing password with wrong current password."""
        # Mock repository response
        mock_user_repository.get_by_id.return_value = sample_user
        
        # Mock password verification failure
        with pytest.mock.patch('src.application.use_cases.user_service.password_manager') as mock_password_manager:
            mock_password_manager.verify_password.return_value = False
            
            # Change password should fail
            password_data = PasswordChangeRequest(
                current_password="wrong_password",
                new_password="new_password"
            )
            with pytest.raises(ValueError, match="Current password is incorrect"):
                await user_service.change_password(sample_user.id, password_data)
