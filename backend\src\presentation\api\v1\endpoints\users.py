"""
User management endpoints.

This module provides user management API endpoints
for profile operations and user administration.
"""

import uuid
from typing import List

from fastapi import APIRouter, Depends, HTTPException, Query, status

from src.application.use_cases.user_service import UserService
from src.domain.entities.user import UserResponse, UserUpdate
from src.infrastructure.logging.setup import get_logger
from src.presentation.dependencies.auth import get_current_user, require_admin
from src.presentation.dependencies.services import get_user_service

logger = get_logger(__name__)

router = APIRouter()


@router.get("/me", response_model=UserResponse)
async def get_my_profile(
    current_user: dict = Depends(get_current_user),
    user_service: UserService = Depends(get_user_service)
) -> UserResponse:
    """
    Get current user profile.
    
    Args:
        current_user: Current authenticated user
        user_service: User service dependency
        
    Returns:
        UserResponse: Current user profile
        
    Raises:
        HTTPException: If user not found
    """
    try:
        user_id = uuid.UUID(current_user["user_id"])
        user_response = await user_service.get_user_by_id(user_id)
        
        if not user_response:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="User not found"
            )
        
        logger.debug(f"Retrieved profile for user: {current_user['user_id']}")
        return user_response
        
    except ValueError as e:
        logger.error(f"Invalid user ID: {e}")
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Invalid user ID"
        )
    except Exception as e:
        logger.error(f"Get profile error: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to retrieve profile"
        )


@router.put("/me", response_model=UserResponse)
async def update_my_profile(
    update_data: UserUpdate,
    current_user: dict = Depends(get_current_user),
    user_service: UserService = Depends(get_user_service)
) -> UserResponse:
    """
    Update current user profile.
    
    Args:
        update_data: Profile update data
        current_user: Current authenticated user
        user_service: User service dependency
        
    Returns:
        UserResponse: Updated user profile
        
    Raises:
        HTTPException: If update fails
    """
    try:
        user_id = uuid.UUID(current_user["user_id"])
        updated_user = await user_service.update_user_profile(user_id, update_data)
        
        logger.info(f"Profile updated for user: {current_user['user_id']}")
        return updated_user
        
    except ValueError as e:
        logger.warning(f"Profile update failed: {e}")
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=str(e)
        )
    except Exception as e:
        logger.error(f"Profile update error: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Profile update failed"
        )


@router.get("/{user_id}", response_model=UserResponse)
async def get_user_by_id(
    user_id: uuid.UUID,
    current_user: dict = Depends(get_current_user),
    user_service: UserService = Depends(get_user_service)
) -> UserResponse:
    """
    Get user by ID.
    
    Args:
        user_id: User ID to retrieve
        current_user: Current authenticated user
        user_service: User service dependency
        
    Returns:
        UserResponse: User information
        
    Raises:
        HTTPException: If user not found or access denied
    """
    try:
        # Users can only view their own profile unless they're admin
        current_user_id = uuid.UUID(current_user["user_id"])
        if user_id != current_user_id:
            # TODO: Check if current user is admin
            # For now, allow access to any user profile
            pass
        
        user_response = await user_service.get_user_by_id(user_id)
        
        if not user_response:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="User not found"
            )
        
        logger.debug(f"Retrieved user: {user_id}")
        return user_response
        
    except ValueError as e:
        logger.error(f"Invalid user ID: {e}")
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Invalid user ID"
        )
    except Exception as e:
        logger.error(f"Get user error: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to retrieve user"
        )


@router.get("/", response_model=List[UserResponse])
async def list_users(
    skip: int = Query(0, ge=0, description="Number of users to skip"),
    limit: int = Query(100, ge=1, le=1000, description="Maximum number of users to return"),
    active_only: bool = Query(False, description="Return only active users"),
    current_user: dict = Depends(require_admin),
    user_service: UserService = Depends(get_user_service)
) -> List[UserResponse]:
    """
    List users with pagination (admin only).
    
    Args:
        skip: Number of users to skip
        limit: Maximum number of users to return
        active_only: Whether to return only active users
        current_user: Current authenticated admin user
        user_service: User service dependency
        
    Returns:
        List[UserResponse]: List of users
    """
    try:
        users = await user_service.list_users(skip, limit, active_only)
        
        logger.info(f"Listed {len(users)} users (admin: {current_user['user_id']})")
        return users
        
    except Exception as e:
        logger.error(f"List users error: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to list users"
        )


@router.get("/stats/count")
async def get_user_count(
    active_only: bool = Query(False, description="Count only active users"),
    current_user: dict = Depends(require_admin),
    user_service: UserService = Depends(get_user_service)
) -> dict:
    """
    Get user count statistics (admin only).
    
    Args:
        active_only: Whether to count only active users
        current_user: Current authenticated admin user
        user_service: User service dependency
        
    Returns:
        dict: User count statistics
    """
    try:
        total_count = await user_service.get_user_count(active_only=False)
        active_count = await user_service.get_user_count(active_only=True)
        
        result = {
            "total_users": total_count,
            "active_users": active_count,
            "inactive_users": total_count - active_count,
        }
        
        if active_only:
            result = {"active_users": active_count}
        
        logger.debug(f"User count retrieved (admin: {current_user['user_id']})")
        return result
        
    except Exception as e:
        logger.error(f"Get user count error: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to get user count"
        )


@router.post("/{user_id}/activate", response_model=UserResponse)
async def activate_user(
    user_id: uuid.UUID,
    current_user: dict = Depends(require_admin),
    user_service: UserService = Depends(get_user_service)
) -> UserResponse:
    """
    Activate user account (admin only).
    
    Args:
        user_id: User ID to activate
        current_user: Current authenticated admin user
        user_service: User service dependency
        
    Returns:
        UserResponse: Updated user information
        
    Raises:
        HTTPException: If activation fails
    """
    try:
        updated_user = await user_service.activate_user(user_id)
        
        logger.info(f"User activated: {user_id} (admin: {current_user['user_id']})")
        return updated_user
        
    except ValueError as e:
        logger.warning(f"User activation failed: {e}")
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=str(e)
        )
    except Exception as e:
        logger.error(f"User activation error: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="User activation failed"
        )


@router.post("/{user_id}/suspend", response_model=UserResponse)
async def suspend_user(
    user_id: uuid.UUID,
    current_user: dict = Depends(require_admin),
    user_service: UserService = Depends(get_user_service)
) -> UserResponse:
    """
    Suspend user account (admin only).
    
    Args:
        user_id: User ID to suspend
        current_user: Current authenticated admin user
        user_service: User service dependency
        
    Returns:
        UserResponse: Updated user information
        
    Raises:
        HTTPException: If suspension fails
    """
    try:
        # Prevent admin from suspending themselves
        current_user_id = uuid.UUID(current_user["user_id"])
        if user_id == current_user_id:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Cannot suspend your own account"
            )
        
        updated_user = await user_service.suspend_user(user_id)
        
        logger.info(f"User suspended: {user_id} (admin: {current_user['user_id']})")
        return updated_user
        
    except ValueError as e:
        logger.warning(f"User suspension failed: {e}")
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=str(e)
        )
    except Exception as e:
        logger.error(f"User suspension error: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="User suspension failed"
        )
