// We keep the namespace import for potential future use or if other types are correctly exported.
// However, for problematic types, we'll use 'any'.
import {
  ANIMATION_DEFAULTS,
  ANIMATION_PRESETS,
  createAccessibleAnimation,
} from '@/shared/lib/animations'; // Adjust path as necessary
import { useEffect, useRef } from 'react';

// Define a minimal local interface for Anime.js parameters.
// Use 'any' for types that are causing issues with the current animejs type definitions.
interface MinimalAnimeParams {
  targets?: any; // Replaced anime.AnimeTarget with any
  duration?: number | ((target: HTMLElement, index: number, total: number) => number);
  delay?: number | ((target: HTMLElement, index: number, total: number) => number);
  easing?: string | ((el: HTMLElement, i: number, l: number) => string) | ReadonlyArray<number>;
  opacity?: number | [number, number];
  translateY?: number | string | [number | string, number | string];
  translateX?: number | string | [number | string, number | string];
  scale?: number | [number, number];
  rotate?: number | string | [number | string, number | string];
  loop?: boolean | number;
  direction?: 'normal' | 'reverse' | 'alternate';
  autoplay?: boolean;
  begin?: (anim: any) => void; // Replaced anime.AnimeInstance with any
  complete?: (anim: any) => void; // Replaced anime.AnimeInstance with any
  // Add other properties as needed from anime.js documentation
  [key: string]: any; // Allow other animejs properties
}

export interface UseAnimeConfig extends Omit<MinimalAnimeParams, 'targets'> {
  preset?: keyof typeof ANIMATION_PRESETS;
  // Add any other custom options you might want to control via the hook
}

/**
 * A React hook to apply Anime.js animations declaratively to an element.
 * It automatically handles cleanup and respects 'prefers-reduced-motion'.
 * The actual anime runtime calls are made by functions from animations.ts.
 * This hook primarily uses animejs for type definitions (with workarounds for problematic types).
 *
 * @param config - The Anime.js animation configuration (or a preset name).
 * @param deps - Optional dependency array to re-trigger the animation.
 * @returns A React ref object to attach to the target DOM element.
 */
export function useAnime<T extends HTMLElement = HTMLElement>(
  config: UseAnimeConfig,
  deps: React.DependencyList = []
) {
  const targetRef = useRef<T>(null);

  useEffect(() => {
    let animationInstance: any | null = null; // Replaced anime.AnimeInstance with any

    if (targetRef.current) {
      const { preset, ...animeConfig } = config;
      let finalConfig: MinimalAnimeParams = {
        ...ANIMATION_DEFAULTS,
        ...animeConfig,
      };

      if (preset && ANIMATION_PRESETS[preset]) {
        finalConfig = {
          ...finalConfig,
          ...(ANIMATION_PRESETS[preset] as MinimalAnimeParams),
          ...animeConfig,
        };
      }

      animationInstance = createAccessibleAnimation({
        targets: targetRef.current,
        ...finalConfig,
      });
    }

    return () => {
      if (animationInstance && typeof animationInstance.pause === 'function') {
        animationInstance.pause();
      }
    };
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [targetRef, config, ...deps]);

  return targetRef;
}
