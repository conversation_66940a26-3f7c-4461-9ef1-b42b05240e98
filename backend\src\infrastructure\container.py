"""
Dependency injection container.

This module provides a simple dependency injection container
for managing application dependencies and their lifecycles.
"""

from collections.abc import Callable
from typing import Any, TypeVar

from src.domain.repositories.folder_repository import FolderRepository
from src.domain.repositories.note_repository import NoteRepository
from src.domain.repositories.tag_repository import TagRepository

# Repository interface imports
from src.domain.repositories.user_repository import UserRepositoryInterface

# AI services
# AI services
# AI services
from src.infrastructure.cache.cache_service import CacheService
from src.infrastructure.cache.redis_client import RedisClient
from src.infrastructure.config.settings import Settings, get_settings
from src.infrastructure.database.connection import DatabaseManager
from src.infrastructure.database.repositories.folder_repository_impl import (
    SQLAlchemyFolderRepository,
)
from src.infrastructure.database.repositories.note_repository_impl import (
    SQLAlchemyNoteRepository,
)
from src.infrastructure.database.repositories.tag_repository_impl import (
    SQLAlchemyTagRepository,
)

# Repository implementation imports
from src.infrastructure.database.repositories.user_repository import UserRepository
from src.infrastructure.logging import LoggerMixin

# AI services imports
try:
    from src.infrastructure.ai.models.local_model_service import LocalModelService

    AI_SERVICES_AVAILABLE = True
except ImportError:
    AI_SERVICES_AVAILABLE = False
    LocalModelService = None

T = TypeVar("T")


class Container(LoggerMixin):
    """
    Simple dependency injection container.

    Manages application dependencies and their lifecycles,
    supporting both singleton and transient dependencies.
    """

    def __init__(self) -> None:
        """Initialize the container."""
        self._services: dict[type, Any] = {}
        self._factories: dict[type, Callable[[], Any]] = {}
        self._singletons: dict[type, Any] = {}

        # Register core services
        self._register_core_services()

    def _register_core_services(self) -> None:
        """Register core application services."""
        # Register settings as singleton
        self.register_singleton(Settings, get_settings)

        # Register logger
        self.register_singleton(LoggerMixin, lambda: LoggerMixin())

        # Register database manager as singleton
        def create_db_manager() -> DatabaseManager:
            settings = self.get(Settings)
            return DatabaseManager(settings.database_url)

        self.register_singleton(DatabaseManager, create_db_manager)

        # Register Redis client as singleton
        def create_redis_client() -> RedisClient:
            settings = self.get(Settings)
            logger = self.get(LoggerMixin)
            return RedisClient(settings, logger)

        self.register_singleton(RedisClient, create_redis_client)

        # Register cache service as singleton
        def create_cache_service() -> CacheService:
            redis_client = self.get(RedisClient)
            logger = self.get(LoggerMixin)
            return CacheService(redis_client, logger)

        self.register_singleton(CacheService, create_cache_service)

        # Register AI services
        self._register_ai_services()

        # Register repositories
        self._register_repositories()

    def _register_repositories(self) -> None:
        """Register repository dependencies."""
        # Note: Repository instances should be created with a session context
        # For now, we register the repository classes and let the application
        # layer handle session management through dependency injection

        # Register repository classes for dependency injection
        self.register_transient(UserRepositoryInterface, lambda: UserRepository)
        self.register_transient(UserRepository, lambda: UserRepository)
        self.register_transient(NoteRepository, lambda: SQLAlchemyNoteRepository)
        self.register_transient(
            SQLAlchemyNoteRepository, lambda: SQLAlchemyNoteRepository
        )
        self.register_transient(FolderRepository, lambda: SQLAlchemyFolderRepository)
        self.register_transient(
            SQLAlchemyFolderRepository, lambda: SQLAlchemyFolderRepository
        )
        self.register_transient(TagRepository, lambda: SQLAlchemyTagRepository)
        self.register_transient(
            SQLAlchemyTagRepository, lambda: SQLAlchemyTagRepository
        )

    def _register_ai_services(self) -> None:
        """Register AI service dependencies."""
        if AI_SERVICES_AVAILABLE and LocalModelService:
            # Register LocalModelService as singleton
            def create_local_model_service():
                settings = self.get(Settings)
                models_dir = getattr(settings, "models_directory", "./models")
                return LocalModelService(models_dir)

            self.register_singleton(LocalModelService, create_local_model_service)
            self.logger.debug("Registered AI services")
        else:
            self.logger.warning("AI services not available - missing dependencies")

    def register_singleton(self, interface: type[T], factory: Callable[[], T]) -> None:
        """
        Register a singleton service.

        Args:
            interface: The interface/type to register
            factory: Factory function to create the service
        """
        self._factories[interface] = factory
        self.logger.debug(f"Registered singleton: {interface.__name__}")

    def register_transient(self, interface: type[T], factory: Callable[[], T]) -> None:
        """
        Register a transient service.

        Args:
            interface: The interface/type to register
            factory: Factory function to create the service
        """
        self._services[interface] = factory
        self.logger.debug(f"Registered transient: {interface.__name__}")

    def register_instance(self, interface: type[T], instance: T) -> None:
        """
        Register a specific instance.

        Args:
            interface: The interface/type to register
            instance: The instance to register
        """
        self._singletons[interface] = instance
        self.logger.debug(f"Registered instance: {interface.__name__}")

    def get(self, interface: type[T]) -> T:
        """
        Get a service instance.

        Args:
            interface: The interface/type to resolve

        Returns:
            T: Service instance

        Raises:
            ValueError: If service is not registered
        """
        # Check if already instantiated singleton
        if interface in self._singletons:
            return self._singletons[interface]

        # Check if singleton factory exists
        if interface in self._factories:
            instance = self._factories[interface]()
            self._singletons[interface] = instance
            self.logger.debug(f"Created singleton: {interface.__name__}")
            return instance

        # Check if transient factory exists
        if interface in self._services:
            instance = self._services[interface]()
            self.logger.debug(f"Created transient: {interface.__name__}")
            return instance

        raise ValueError(f"Service not registered: {interface.__name__}")

    def has(self, interface: type[T]) -> bool:
        """
        Check if a service is registered.

        Args:
            interface: The interface/type to check

        Returns:
            bool: True if service is registered
        """
        return (
            interface in self._singletons
            or interface in self._factories
            or interface in self._services
        )

    def clear(self) -> None:
        """Clear all registered services."""
        self._services.clear()
        self._factories.clear()
        self._singletons.clear()
        self.logger.debug("Container cleared")

    async def dispose(self) -> None:
        """Dispose of all services that support disposal."""
        for service in self._singletons.values():
            # Handle Redis client disconnect
            if isinstance(service, RedisClient):
                await service.disconnect()
            # Handle database disconnect
            elif hasattr(service, "close"):
                try:
                    if callable(service.close):
                        if hasattr(service, "close") and callable(service.close):
                            await service.close()
                except Exception as e:
                    self.logger.error(f"Error disposing service: {e}")

        self.clear()


# Global container instance
_container: Container | None = None


def get_container() -> Container:
    """
    Get the global container instance.

    Returns:
        Container: Global container instance
    """
    global _container

    if _container is None:
        _container = Container()

    return _container


def reset_container() -> None:
    """Reset the global container instance."""
    global _container
    _container = None
