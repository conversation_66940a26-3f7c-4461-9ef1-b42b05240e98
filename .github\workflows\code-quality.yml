name: Code Quality

on:
  push:
    branches: [ main, develop, staging ]
  pull_request:
    branches: [ main, develop ]

env:
  PYTHON_VERSION: "3.11"
  NODE_VERSION: "18"
  PNPM_VERSION: "8"

jobs:
  # Python Code Quality
  python-quality:
    name: Python Code Quality
    runs-on: ubuntu-latest

    steps:
    - name: Checkout code
      uses: actions/checkout@v4

    - name: Install uv
      uses: astral-sh/setup-uv@v3
      with:
        version: "latest"

    - name: Set up Python
      run: uv python install ${{ env.PYTHON_VERSION }}

    - name: Install dependencies
      working-directory: ./backend
      run: uv sync --dev

    - name: Run Ruff linting
      working-directory: ./backend
      run: |
        uv run ruff check src/ tests/ --output-format=github
        
    - name: Run Ruff formatting check
      working-directory: ./backend
      run: uv run ruff format --check src/ tests/

    - name: Run Black formatting check
      working-directory: ./backend
      run: uv run black --check --diff src/ tests/

    - name: Run isort import sorting check
      working-directory: ./backend
      run: uv run isort --check-only --diff src/ tests/

    - name: Run MyPy type checking
      working-directory: ./backend
      run: uv run mypy src/ --strict

    - name: Run Bandit security linting
      working-directory: ./backend
      run: uv run bandit -r src/ -f json -o bandit-report.json

    - name: Upload Bandit report
      uses: actions/upload-artifact@v3
      if: always()
      with:
        name: bandit-report
        path: ./backend/bandit-report.json

  # TypeScript/React Code Quality
  typescript-quality:
    name: TypeScript/React Code Quality
    runs-on: ubuntu-latest

    steps:
    - name: Checkout code
      uses: actions/checkout@v4

    - name: Setup Node.js
      uses: actions/setup-node@v4
      with:
        node-version: ${{ env.NODE_VERSION }}

    - name: Setup pnpm
      uses: pnpm/action-setup@v2
      with:
        version: ${{ env.PNPM_VERSION }}

    - name: Get pnpm store directory
      shell: bash
      run: |
        echo "STORE_PATH=$(pnpm store path --silent)" >> $GITHUB_ENV

    - name: Setup pnpm cache
      uses: actions/cache@v3
      with:
        path: ${{ env.STORE_PATH }}
        key: ${{ runner.os }}-pnpm-store-${{ hashFiles('**/pnpm-lock.yaml') }}
        restore-keys: |
          ${{ runner.os }}-pnpm-store-

    - name: Install dependencies
      run: pnpm install --frozen-lockfile

    - name: Install frontend dependencies
      working-directory: ./frontend
      run: pnpm install --frozen-lockfile

    - name: Run ESLint
      working-directory: ./frontend
      run: pnpm lint --format=@microsoft/eslint-formatter-sarif --output-file=eslint-results.sarif

    - name: Upload ESLint results to GitHub
      uses: github/codeql-action/upload-sarif@v2
      if: always()
      with:
        sarif_file: ./frontend/eslint-results.sarif
        wait-for-processing: true

    - name: Run Prettier formatting check
      working-directory: ./frontend
      run: pnpm prettier --check "src/**/*.{ts,tsx,js,jsx,json,css,md}"

    - name: Run TypeScript type checking
      working-directory: ./frontend
      run: pnpm type-check

    - name: Check for unused dependencies
      working-directory: ./frontend
      run: pnpm dlx depcheck

  # Documentation Quality
  docs-quality:
    name: Documentation Quality
    runs-on: ubuntu-latest

    steps:
    - name: Checkout code
      uses: actions/checkout@v4

    - name: Setup Node.js
      uses: actions/setup-node@v4
      with:
        node-version: ${{ env.NODE_VERSION }}

    - name: Install markdownlint
      run: npm install -g markdownlint-cli

    - name: Run markdownlint
      run: markdownlint "**/*.md" --ignore node_modules --ignore .git

    - name: Check for broken links
      uses: gaurav-nelson/github-action-markdown-link-check@v1
      with:
        use-quiet-mode: 'yes'
        use-verbose-mode: 'yes'
        config-file: '.github/markdown-link-check-config.json'

  # Dependency Security Audit
  dependency-audit:
    name: Dependency Security Audit
    runs-on: ubuntu-latest

    steps:
    - name: Checkout code
      uses: actions/checkout@v4

    - name: Setup Node.js
      uses: actions/setup-node@v4
      with:
        node-version: ${{ env.NODE_VERSION }}

    - name: Setup pnpm
      uses: pnpm/action-setup@v2
      with:
        version: ${{ env.PNPM_VERSION }}

    - name: Install dependencies
      run: pnpm install --frozen-lockfile

    - name: Run pnpm audit
      run: pnpm audit --audit-level moderate

    - name: Install uv
      uses: astral-sh/setup-uv@v3
      with:
        version: "latest"

    - name: Set up Python
      run: uv python install ${{ env.PYTHON_VERSION }}

    - name: Install Python dependencies
      working-directory: ./backend
      run: uv sync --dev

    - name: Run Python security audit (Safety)
      working-directory: ./backend
      run: |
        uv add --dev safety
        uv run safety check --json --output safety-report.json || true

    - name: Upload Safety report
      uses: actions/upload-artifact@v3
      if: always()
      with:
        name: safety-report
        path: ./backend/safety-report.json

  # Code Complexity Analysis
  complexity-analysis:
    name: Code Complexity Analysis
    runs-on: ubuntu-latest

    steps:
    - name: Checkout code
      uses: actions/checkout@v4

    - name: Install uv
      uses: astral-sh/setup-uv@v3
      with:
        version: "latest"

    - name: Set up Python
      run: uv python install ${{ env.PYTHON_VERSION }}

    - name: Install Python dependencies
      working-directory: ./backend
      run: uv sync --dev

    - name: Run Radon complexity analysis
      working-directory: ./backend
      run: |
        uv add --dev radon
        uv run radon cc src/ --json > radon-complexity.json
        uv run radon mi src/ --json > radon-maintainability.json

    - name: Upload complexity reports
      uses: actions/upload-artifact@v3
      if: always()
      with:
        name: complexity-reports
        path: |
          ./backend/radon-complexity.json
          ./backend/radon-maintainability.json

    - name: Setup Node.js for frontend analysis
      uses: actions/setup-node@v4
      with:
        node-version: ${{ env.NODE_VERSION }}

    - name: Setup pnpm
      uses: pnpm/action-setup@v2
      with:
        version: ${{ env.PNPM_VERSION }}

    - name: Install frontend dependencies
      working-directory: ./frontend
      run: pnpm install --frozen-lockfile

    - name: Run TypeScript complexity analysis
      working-directory: ./frontend
      run: |
        pnpm dlx ts-complexity-analyzer src/ --output complexity-report.json

    - name: Upload frontend complexity report
      uses: actions/upload-artifact@v3
      if: always()
      with:
        name: frontend-complexity-report
        path: ./frontend/complexity-report.json
