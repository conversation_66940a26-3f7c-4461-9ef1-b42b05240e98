"""
Notes API Endpoints

This module defines the API endpoints for notes.
"""

from uuid import UUID

from fastapi import APIRouter, Depends, HTTPException, Query, status

from src.application.dto.note_dto import (
    CreateNoteDTO,
    NoteDTO,
    NoteFilterDTO,
    NoteMetadataDTO,
    UpdateNoteDTO,
)
from src.application.use_cases.note_service import NoteService
from src.presentation.dependencies.auth import get_current_user
from src.presentation.dependencies.services import get_note_service

router = APIRouter(prefix="/notes", tags=["Notes"])


@router.get("/", response_model=list[NoteMetadataDTO])
async def get_notes(
    folder_id: UUID | None = Query(None, description="Filter by folder ID"),
    include_archived: bool = Query(False, description="Include archived notes"),
    only_starred: bool = Query(False, description="Only include starred notes"),
    tags: list[str] | None = Query(None, description="Filter by tags"),
    search_query: str | None = Query(None, description="Search in title and content"),
    sort_by: str = Query("updated_at", description="Field to sort by"),
    sort_direction: str = Query("desc", description="Sort direction (asc or desc)"),
    current_user=Depends(get_current_user),
    note_service: NoteService = Depends(get_note_service),
):
    """
    Get all notes for the current user with optional filtering.
    """
    filters = NoteFilterDTO(
        folder_id=folder_id,
        include_archived=include_archived,
        only_starred=only_starred,
        tags=tags,
        search_query=search_query,
        sort_by=sort_by,
        sort_direction=sort_direction,
    )

    return await note_service.get_notes(current_user.id, filters)


@router.get("/{note_id}", response_model=NoteDTO)
async def get_note(
    note_id: UUID,
    current_user=Depends(get_current_user),
    note_service: NoteService = Depends(get_note_service),
):
    """
    Get a specific note by ID.
    """
    note = await note_service.get_note(note_id, current_user.id)

    if note is None:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Note not found",
        )

    return note


@router.post("/", response_model=NoteDTO, status_code=status.HTTP_201_CREATED)
async def create_note(
    create_dto: CreateNoteDTO,
    current_user=Depends(get_current_user),
    note_service: NoteService = Depends(get_note_service),
):
    """
    Create a new note.
    """
    return await note_service.create_note(current_user.id, create_dto)


@router.put("/{note_id}", response_model=NoteDTO)
async def update_note(
    note_id: UUID,
    update_dto: UpdateNoteDTO,
    current_user=Depends(get_current_user),
    note_service: NoteService = Depends(get_note_service),
):
    """
    Update an existing note.
    """
    note = await note_service.update_note(note_id, current_user.id, update_dto)

    if note is None:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Note not found",
        )

    return note


@router.delete("/{note_id}", status_code=status.HTTP_204_NO_CONTENT)
async def delete_note(
    note_id: UUID,
    current_user=Depends(get_current_user),
    note_service: NoteService = Depends(get_note_service),
):
    """
    Delete a note.
    """
    success = await note_service.delete_note(note_id, current_user.id)

    if not success:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Note not found",
        )


@router.patch("/{note_id}/archive", response_model=NoteDTO)
async def archive_note(
    note_id: UUID,
    current_user=Depends(get_current_user),
    note_service: NoteService = Depends(get_note_service),
):
    """
    Archive a note.
    """
    note = await note_service.archive_note(note_id, current_user.id)

    if note is None:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Note not found",
        )

    return note


@router.patch("/{note_id}/unarchive", response_model=NoteDTO)
async def unarchive_note(
    note_id: UUID,
    current_user=Depends(get_current_user),
    note_service: NoteService = Depends(get_note_service),
):
    """
    Unarchive a note.
    """
    note = await note_service.unarchive_note(note_id, current_user.id)

    if note is None:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Note not found",
        )

    return note


@router.patch("/{note_id}/star", response_model=NoteDTO)
async def star_note(
    note_id: UUID,
    current_user=Depends(get_current_user),
    note_service: NoteService = Depends(get_note_service),
):
    """
    Star a note.
    """
    note = await note_service.star_note(note_id, current_user.id)

    if note is None:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Note not found",
        )

    return note


@router.patch("/{note_id}/unstar", response_model=NoteDTO)
async def unstar_note(
    note_id: UUID,
    current_user=Depends(get_current_user),
    note_service: NoteService = Depends(get_note_service),
):
    """
    Unstar a note.
    """
    note = await note_service.unstar_note(note_id, current_user.id)

    if note is None:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Note not found",
        )

    return note
