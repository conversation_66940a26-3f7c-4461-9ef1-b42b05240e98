# Data Flow Diagram

This diagram illustrates the data flow within the Lonors AI Platform, showing how data moves between different components during user interactions.

```mermaid
flowchart TD
    User([User]) <--> |Interacts with UI| Frontend

    subgraph Frontend[Frontend Layer]
        UI[UI Components] <--> |State Updates| StateManagement[State Management]
        StateManagement <--> |API Requests| APIClient[API Client]
        UI <--> |Real-time Events| WSClient[WebSocket Client]
    end

    subgraph Backend[Backend Layer]
        APIRoutes[API Routes] <--> |Process Requests| UseCases[Use Cases]
        WSServer[WebSocket Server] <--> |Real-time Events| UseCases
        UseCases <--> |Domain Logic| DomainServices[Domain Services]
        DomainServices <--> |Data Access| Repositories[Repositories]
    end

    subgraph Persistence[Persistence Layer]
        Repositories <--> |SQL Queries| Database[(PostgreSQL)]
        Repositories <--> |Cache Operations| Cache[(Redis)]
    end

    subgraph Protocols[Protocol Layer]
        MCPHandler[MC<PERSON> Handler] <--> |AI Model Integration| ExternalAI[External AI Services]
        AGUIHandler[AG-UI Handler] <--> |Dynamic UI| WSServer
        A2AHandler[A2A Handler] <--> |Inter-App Communication| ExternalServices[External Services]
    end

    APIClient <--> |HTTP Requests/Responses| APIRoutes
    WSClient <--> |WebSocket Connection| WSServer
    UseCases <--> |Protocol Requests| MCPHandler
    UseCases <--> |UI Updates| AGUIHandler
    UseCases <--> |Service Communication| A2AHandler

    %% Data flow for authentication
    User --> |Login Credentials| Frontend
    Frontend --> |Authentication Request| Backend
    Backend --> |Validate Credentials| Database
    Backend --> |Generate JWT| Frontend
    Frontend --> |Store Token| Browser[Browser Storage]

    %% Data flow for AI interaction
    User --> |Chat Message| Frontend
    Frontend --> |Send Message| Backend
    Backend --> |Process with AI| MCPHandler
    MCPHandler --> |AI Response| Backend
    Backend --> |Response Data| Frontend
    Frontend --> |Display to User| User

    %% Data flow for real-time updates
    ExternalServices --> |Event Notification| A2AHandler
    A2AHandler --> |Process Event| Backend
    Backend --> |Push Update| WSServer
    WSServer --> |Real-time Update| WSClient
    WSClient --> |Update UI| Frontend
    Frontend --> |Show Notification| User
```

## Data Flow Descriptions

### User Authentication Flow
1. User enters credentials in the login form
2. Frontend sends authentication request to backend API
3. Backend validates credentials against database
4. On successful validation, backend generates JWT tokens
5. Frontend stores tokens in secure storage
6. User is redirected to authenticated area

### AI Interaction Flow
1. User sends a message through the chat interface
2. Frontend sends message to backend API
3. Backend processes message through MCP (Model Context Protocol)
4. MCP handler communicates with external AI service
5. AI response is processed and returned to frontend
6. Frontend displays response to user

### Real-time Update Flow
1. External event occurs (e.g., new notification)
2. Event is communicated through A2A Protocol
3. Backend processes event and determines affected users
4. WebSocket server pushes update to connected clients
5. Frontend WebSocket client receives update
6. UI is updated to reflect new information
7. User is notified of the change

### Data Persistence Flow
1. User performs action requiring data change
2. Frontend sends request to backend API
3. Backend validates request and processes business logic
4. Repository layer handles data access operations
5. Data is stored in PostgreSQL database
6. Frequently accessed data is cached in Redis
7. Confirmation is returned through the API response

### Dynamic UI Generation Flow
1. Backend determines UI needs to be updated
2. AG-UI Protocol handler generates UI description
3. UI description is sent through WebSocket connection
4. Frontend receives UI description
5. Component rendering system processes description
6. UI is dynamically updated without page reload
