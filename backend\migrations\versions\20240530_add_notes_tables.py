"""
Add notes tables migration.

Revision ID: 20240530_add_notes_tables
Revises:
Create Date: 2024-05-30 12:00:00.000000

"""
from typing import Sequence, Union

import sqlalchemy as sa
from alembic import op
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision: str = '20240530_add_notes_tables'
down_revision: Union[str, None] = None
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # Create note_format enum type
    op.execute("CREATE TYPE note_format AS ENUM ('markdown', 'richtext')")

    # Create folders table
    op.create_table(
        'folders',
        sa.Column('id', postgresql.UUID(as_uuid=True), primary_key=True),
        sa.Column('name', sa.String(255), nullable=False),
        sa.Column('parent_id', postgresql.UUID(as_uuid=True), sa.<PERSON>ey('folders.id', ondelete='SET NULL'), nullable=True),
        sa.Column('user_id', postgresql.UUID(as_uuid=True), sa.ForeignKey('users.id', ondelete='CASCADE'), nullable=False),
        sa.Column('is_archived', sa.Boolean(), nullable=False, default=False),
        sa.Column('created_at', sa.DateTime(), nullable=False, server_default=sa.text('now()')),
        sa.Column('updated_at', sa.DateTime(), nullable=False, server_default=sa.text('now()')),
        sa.Column('metadata', postgresql.JSONB(astext_type=sa.Text()), nullable=False, server_default='{}'),
    )

    # Create tags table
    op.create_table(
        'tags',
        sa.Column('id', postgresql.UUID(as_uuid=True), primary_key=True),
        sa.Column('name', sa.String(255), nullable=False),
        sa.Column('color', sa.String(50), nullable=True),
        sa.Column('user_id', postgresql.UUID(as_uuid=True), sa.ForeignKey('users.id', ondelete='CASCADE'), nullable=False),
        sa.Column('created_at', sa.DateTime(), nullable=False, server_default=sa.text('now()')),
        sa.UniqueConstraint('name', 'user_id', name='uq_tag_name_user'),
    )

    # Create notes table
    op.create_table(
        'notes',
        sa.Column('id', postgresql.UUID(as_uuid=True), primary_key=True),
        sa.Column('title', sa.String(255), nullable=False),
        sa.Column('content', sa.Text(), nullable=False),
        sa.Column('format', sa.Enum('markdown', 'richtext', name='note_format'), nullable=False, server_default='markdown'),
        sa.Column('version', sa.Integer(), nullable=False, server_default='1'),
        sa.Column('folder_id', postgresql.UUID(as_uuid=True), sa.ForeignKey('folders.id', ondelete='SET NULL'), nullable=True),
        sa.Column('is_archived', sa.Boolean(), nullable=False, server_default='false'),
        sa.Column('is_starred', sa.Boolean(), nullable=False, server_default='false'),
        sa.Column('created_at', sa.DateTime(), nullable=False, server_default=sa.text('now()')),
        sa.Column('updated_at', sa.DateTime(), nullable=False, server_default=sa.text('now()')),
        sa.Column('created_by', postgresql.UUID(as_uuid=True), sa.ForeignKey('users.id', ondelete='CASCADE'), nullable=False),
        sa.Column('last_edited_by', postgresql.UUID(as_uuid=True), sa.ForeignKey('users.id', ondelete='CASCADE'), nullable=False),
        sa.Column('metadata', postgresql.JSONB(astext_type=sa.Text()), nullable=False, server_default='{}'),
    )

    # Create note_tags association table
    op.create_table(
        'note_tags',
        sa.Column('note_id', postgresql.UUID(as_uuid=True), sa.ForeignKey('notes.id', ondelete='CASCADE'), primary_key=True),
        sa.Column('tag_id', postgresql.UUID(as_uuid=True), sa.ForeignKey('tags.id', ondelete='CASCADE'), primary_key=True),
    )

    # Create indexes
    op.create_index('ix_folders_user_id', 'folders', ['user_id'])
    op.create_index('ix_folders_parent_id', 'folders', ['parent_id'])
    op.create_index('ix_tags_user_id', 'tags', ['user_id'])
    op.create_index('ix_notes_created_by', 'notes', ['created_by'])
    op.create_index('ix_notes_folder_id', 'notes', ['folder_id'])
    op.create_index('ix_notes_title', 'notes', ['title'])
    op.create_index('ix_notes_created_at', 'notes', ['created_at'])
    op.create_index('ix_notes_updated_at', 'notes', ['updated_at'])
    op.create_index('ix_notes_is_starred', 'notes', ['is_starred'])
    op.create_index('ix_notes_is_archived', 'notes', ['is_archived'])


def downgrade() -> None:
    # Drop tables
    op.drop_table('note_tags')
    op.drop_table('notes')
    op.drop_table('tags')
    op.drop_table('folders')

    # Drop enum type
    op.execute("DROP TYPE note_format")
