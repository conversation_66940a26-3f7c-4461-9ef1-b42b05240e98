"""
Note Repository Interface

This module defines the interface for the note repository.
"""

from abc import ABC, abstractmethod
from uuid import UUID

from src.domain.entities.note import Note


class NoteRepository(ABC):
    """Interface for note repository operations."""

    @abstractmethod
    async def get_by_id(self, note_id: UUID) -> Note | None:
        """
        Get a note by its ID.

        Args:
            note_id: The ID of the note to retrieve

        Returns:
            The note if found, None otherwise
        """
        pass

    @abstractmethod
    async def get_all_by_user(
        self,
        user_id: UUID,
        folder_id: UUID | None = None,
        include_archived: bool = False,
        only_starred: bool = False,
        tags: list[str] | None = None,
        search_query: str | None = None,
    ) -> list[Note]:
        """
        Get all notes for a user with optional filtering.

        Args:
            user_id: The ID of the user
            folder_id: Optional folder ID to filter by
            include_archived: Whether to include archived notes
            only_starred: Whether to only include starred notes
            tags: Optional list of tags to filter by
            search_query: Optional search query to filter by

        Returns:
            List of notes matching the criteria
        """
        pass

    @abstractmethod
    async def create(self, note: Note) -> Note:
        """
        Create a new note.

        Args:
            note: The note to create

        Returns:
            The created note with any generated fields
        """
        pass

    @abstractmethod
    async def update(self, note: Note) -> Note:
        """
        Update an existing note.

        Args:
            note: The note to update

        Returns:
            The updated note
        """
        pass

    @abstractmethod
    async def delete(self, note_id: UUID) -> bool:
        """
        Delete a note by its ID.

        Args:
            note_id: The ID of the note to delete

        Returns:
            True if the note was deleted, False otherwise
        """
        pass
