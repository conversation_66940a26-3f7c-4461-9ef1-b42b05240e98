# Protocol Integration Flow Diagram

This diagram illustrates the integration of the three main protocols in the Lonors AI Platform: Model Context Protocol (MCP), AG-UI Protocol, and A2A Protocol.

```mermaid
sequenceDiagram
    participant User as User
    participant FE as Frontend
    participant BE as Backend
    participant MCP as MCP Service
    participant AGUI as AG-UI Service
    participant A2A as A2A Service
    participant AI as AI Models
    participant Ext as External Services

    %% MCP Protocol Flow
    Note over User, AI: Model Context Protocol (MCP) Flow
    User->>FE: Send message to AI assistant
    FE->>BE: POST /api/v1/mcp/generate
    BE->>MCP: Process MCP request
    MCP->>AI: Send prompt to AI model
    AI-->>MCP: Return AI response
    MCP-->>BE: Process and format response
    BE-->>FE: Return formatted response
    FE-->>User: Display AI response

    %% AG-UI Protocol Flow
    Note over User, Ext: AG-UI Protocol Flow
    User->>FE: Interact with dynamic component
    FE->>BE: WebSocket message (component event)
    BE->>AGUI: Process component event
    AGUI->>AGUI: Generate UI update
    AGUI-->>BE: Return UI description
    BE-->>FE: WebSocket message (UI update)
    FE-->>User: Update UI dynamically

    %% A2A Protocol Flow
    Note over User, Ext: A2A Protocol Flow
    Ext->>A2A: Send event notification
    A2A->>BE: Process external event
    BE->>BE: Apply business logic
    BE->>FE: WebSocket message (notification)
    FE-->>User: Display notification

    %% Combined Protocol Flow
    Note over User, Ext: Combined Protocol Flow
    User->>FE: Request AI-generated UI
    FE->>BE: POST /api/v1/mcp/generate with UI context
    BE->>MCP: Process MCP request
    MCP->>AI: Send UI generation prompt
    AI-->>MCP: Return UI description
    MCP-->>BE: Process AI response
    BE->>AGUI: Convert to AG-UI components
    AGUI-->>BE: Return component structure
    BE-->>FE: WebSocket message (UI update)
    FE-->>User: Display AI-generated UI
    User->>FE: Interact with generated UI
    FE->>BE: WebSocket message (component event)
    BE->>A2A: Trigger external service action
    A2A->>Ext: Execute action in external service
    Ext-->>A2A: Return action result
    A2A-->>BE: Process result
    BE-->>FE: WebSocket message (result notification)
    FE-->>User: Display action result
```

## Protocol Integration Details

### Model Context Protocol (MCP)

The Model Context Protocol enables seamless integration with various AI models while maintaining context across conversations and sessions.

#### Key Components
- **Context Management**: Persistent conversation contexts with automatic cleanup
- **Token Management**: Efficient token usage tracking and optimization
- **Multi-model Support**: Integration with OpenAI, Anthropic, and other AI providers
- **Streaming Support**: Real-time response streaming for better UX

#### Flow Description
1. User sends a message to an AI assistant
2. Frontend sends request to `/api/v1/mcp/generate` endpoint
3. Backend processes request through MCP service
4. MCP service sends prompt to appropriate AI model
5. AI model generates response
6. MCP service processes and formats response
7. Backend returns formatted response to frontend
8. Frontend displays response to user

### AG-UI Protocol

The AG-UI (Augmented Generative User Interface) Protocol enables dynamic, AI-driven user interface generation and real-time synchronization between client and server.

#### Key Components
- **Dynamic Components**: Server-driven UI component creation and updates
- **Real-time Sync**: WebSocket-based state synchronization
- **Event Handling**: Bidirectional event communication
- **Layout Management**: Flexible layout systems with nested components

#### Flow Description
1. User interacts with a dynamic component
2. Frontend sends component event via WebSocket
3. Backend processes event through AG-UI service
4. AG-UI service generates UI update
5. Backend sends UI description via WebSocket
6. Frontend updates UI dynamically
7. User sees updated interface

### A2A Protocol

The A2A (Application-to-Application) Protocol facilitates secure, efficient communication between different applications and services in the Lonors ecosystem.

#### Key Components
- **Service Discovery**: Automatic discovery of available services
- **Message Queuing**: Reliable message delivery with retry mechanisms
- **Authentication**: Secure service-to-service authentication
- **Circuit Breaking**: Fault tolerance and graceful degradation

#### Flow Description
1. External service sends event notification
2. A2A service receives and processes event
3. Backend applies business logic based on event
4. Backend sends notification to frontend via WebSocket
5. Frontend displays notification to user

### Combined Protocol Integration

The true power of the Lonors platform comes from the integration of all three protocols, enabling complex workflows that combine AI, dynamic UI, and external service integration.

#### Example Combined Flow
1. User requests AI-generated UI
2. AI generates UI description via MCP
3. UI description is converted to AG-UI components
4. Frontend renders dynamic UI
5. User interacts with generated UI
6. Interaction triggers external service action via A2A
7. Action result is returned to user

## WebSocket Endpoint Structure

### Connection Establishment
- **Endpoint**: `/ws/v1/connect`
- **Authentication**: JWT token in connection parameters
- **Protocol Negotiation**: Client capabilities and protocol versions

### Channel Types
- **User Channel**: `/ws/v1/user/{user_id}`
- **Session Channel**: `/ws/v1/session/{session_id}`
- **Agent Channel**: `/ws/v1/agent/{agent_id}`
- **UI Channel**: `/ws/v1/ui/{ui_id}`

### Message Structure
```json
{
  "id": "msg_123",
  "type": "event|command|response",
  "protocol": "mcp|agui|a2a",
  "channel": "user|session|agent|ui",
  "payload": {
    // Protocol-specific payload
  },
  "timestamp": "2024-01-15T12:00:00Z"
}
```
