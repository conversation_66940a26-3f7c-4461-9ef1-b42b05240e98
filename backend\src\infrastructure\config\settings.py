"""
Application settings and configuration.

This module defines the application settings using Pydantic Settings
for type-safe configuration management with environment variable support.
"""

import logging
from functools import lru_cache
from typing import Literal

from pydantic import Field, field_validator
from pydantic_settings import BaseSettings


class Settings(BaseSettings):
    """
    Application settings with environment variable support.

    All settings can be overridden using environment variables.
    """

    # Application settings
    app_name: str = Field(default="Lonors Backend", description="Application name")
    app_version: str = Field(default="1.0.0", description="Application version")
    environment: Literal["development", "staging", "production"] = Field(
        default="development", description="Application environment"
    )
    debug: bool = Field(default=True, description="Debug mode")

    # Server settings
    host: str = Field(default="0.0.0.0", description="Server host")
    port: int = Field(default=3001, description="Server port")
    reload: bool = Field(default=True, description="Auto-reload on code changes")
    workers: int = Field(default=1, description="Number of worker processes")

    # Database settings
    database_url: str = Field(
        default="postgresql+asyncpg://lonors_user:lonors_password@localhost:5432/lonors_db",
        description="PostgreSQL database URL",
    )
    db_echo: bool = Field(default=False, description="Echo SQL queries")
    db_pool_size: int = Field(default=20, description="Database connection pool size")
    db_max_overflow: int = Field(
        default=30, description="Database connection pool max overflow"
    )
    db_pool_timeout: int = Field(
        default=30, description="Database connection pool timeout"
    )
    db_pool_recycle: int = Field(
        default=3600, description="Database connection pool recycle time"
    )

    # Redis settings
    redis_url: str = Field(default="redis://localhost:6379", description="Redis URL")
    redis_db: int = Field(default=0, description="Redis database number")
    redis_max_connections: int = Field(default=20, description="Redis max connections")

    # JWT settings
    jwt_secret: str = Field(
        default="your-super-secret-jwt-key-change-in-production",
        description="JWT secret key",
    )
    jwt_algorithm: str = Field(default="HS256", description="JWT algorithm")
    jwt_expires_in: str = Field(default="7d", description="JWT expiration time")
    jwt_refresh_expires_in: str = Field(
        default="30d", description="JWT refresh token expiration"
    )

    # CORS settings
    cors_origins: list[str] = Field(
        default=["http://localhost:5500", "http://127.0.0.1:5500"],
        description="CORS allowed origins",
    )
    cors_allow_credentials: bool = Field(
        default=True, description="CORS allow credentials"
    )

    # Security settings
    allowed_hosts: list[str] = Field(
        default=["localhost", "127.0.0.1"], description="Allowed hosts"
    )
    rate_limit_per_minute: int = Field(
        default=60, description="Rate limit requests per minute"
    )
    rate_limit_burst: int = Field(default=10, description="Rate limit burst capacity")
    rate_limit_window_ms: int = Field(
        default=900000, description="Rate limit window in ms"
    )
    rate_limit_max_requests: int = Field(
        default=100, description="Rate limit max requests"
    )
    bcrypt_rounds: int = Field(default=12, description="Bcrypt hashing rounds")

    # File upload settings
    max_file_size: int = Field(default=10485760, description="Max file size in bytes")
    upload_path: str = Field(default="./uploads", description="File upload path")
    allowed_file_types: list[str] = Field(
        default=["jpg", "jpeg", "png", "gif", "pdf", "doc", "docx"],
        description="Allowed file types",
    )

    # Email settings (optional)
    smtp_host: str = Field(default="", description="SMTP host")
    smtp_port: int = Field(default=587, description="SMTP port")
    smtp_user: str = Field(default="", description="SMTP username")
    smtp_pass: str = Field(default="", description="SMTP password")
    smtp_tls: bool = Field(default=True, description="SMTP TLS enabled")

    # Logging settings
    log_level: Literal["DEBUG", "INFO", "WARNING", "ERROR", "CRITICAL"] = Field(
        default="INFO", description="Logging level"
    )
    log_format: Literal["json", "text"] = Field(
        default="json", description="Log format"
    )
    log_file: str = Field(default="./logs/app.log", description="Log file path")

    # Protocol settings
    # Model Context Protocol (MCP)
    mcp_enabled: bool = Field(default=True, description="Enable MCP protocol")
    mcp_endpoint: str = Field(default="/api/v1/mcp", description="MCP endpoint")
    mcp_max_context_length: int = Field(
        default=4096, description="MCP max context length"
    )

    # AG-UI Protocol
    ag_ui_enabled: bool = Field(default=True, description="Enable AG-UI protocol")
    ag_ui_endpoint: str = Field(default="/api/v1/ag-ui", description="AG-UI endpoint")
    ag_ui_websocket_path: str = Field(
        default="/ws/ag-ui", description="AG-UI WebSocket path"
    )

    # A2A Protocol
    a2a_enabled: bool = Field(default=True, description="Enable A2A protocol")
    a2a_endpoint: str = Field(default="/api/v1/a2a", description="A2A endpoint")
    a2a_auth_token: str = Field(
        default="your-a2a-auth-token-change-in-production",
        description="A2A authentication token",
    )

    # AI Model settings
    models_directory: str = Field(default="./models", description="AI models directory")
    max_loaded_models: int = Field(
        default=3, description="Maximum loaded models in memory"
    )
    model_timeout_minutes: int = Field(
        default=30, description="Model timeout in minutes"
    )
    ollama_host: str = Field(
        default="http://localhost:11434", description="Ollama server host"
    )
    huggingface_cache_dir: str = Field(
        default="./models/huggingface", description="HuggingFace cache directory"
    )

    @field_validator("cors_origins", mode="before")
    @classmethod
    def parse_cors_origins(cls, v):
        """Parse CORS origins from string or list."""
        if isinstance(v, str):
            return [origin.strip() for origin in v.split(",")]
        elif isinstance(v, list):
            return v
        return ["http://localhost:5500", "http://127.0.0.1:5500"]

    @field_validator("allowed_hosts", mode="before")
    @classmethod
    def parse_allowed_hosts(cls, v):
        """Parse allowed hosts from string or list."""
        if isinstance(v, str):
            return [host.strip() for host in v.split(",")]
        return v

    @field_validator("allowed_file_types", mode="before")
    @classmethod
    def parse_allowed_file_types(cls, v):
        """Parse allowed file types from string or list."""
        if isinstance(v, str):
            return [file_type.strip() for file_type in v.split(",")]
        return v

    @field_validator("jwt_secret")
    @classmethod
    def validate_jwt_secret(cls, v):
        """Validate JWT secret is not default in production."""
        if v == "your-super-secret-jwt-key-change-in-production":
            logging.warning(
                "Using default JWT secret. Please change this in production!"
            )
        return v

    @field_validator("a2a_auth_token")
    @classmethod
    def validate_a2a_token(cls, v):
        """Validate A2A token is not default in production."""
        if v == "your-a2a-auth-token-change-in-production":
            logging.warning(
                "Using default A2A auth token. Please change this in production!"
            )
        return v

    class Config:
        """Pydantic configuration."""

        env_file = ".env"
        env_file_encoding = "utf-8"
        case_sensitive = False
        env_nested_delimiter = "__"


@lru_cache
def get_settings() -> Settings:
    """
    Get application settings with caching.

    Returns:
        Settings: Application settings instance
    """
    return Settings()
