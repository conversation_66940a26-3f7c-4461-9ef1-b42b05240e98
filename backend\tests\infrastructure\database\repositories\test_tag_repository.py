"""
Unit tests for SQLAlchemyTagRepository.

This module contains comprehensive tests for the tag repository
implementation including CRUD operations and user isolation.
"""

import uuid
from datetime import datetime
from unittest.mock import AsyncMock, MagicMock

import pytest
from sqlalchemy.ext.asyncio import AsyncSession

from src.domain.entities.tag import Tag
from src.infrastructure.database.models.tag import TagModel
from src.infrastructure.database.repositories.tag_repository_impl import SQLAlchemyTagRepository


class TestSQLAlchemyTagRepository:
    """Test cases for SQLAlchemyTagRepository."""

    @pytest.fixture
    def mock_session(self):
        """Create a mock async session."""
        return AsyncMock(spec=AsyncSession)

    @pytest.fixture
    def repository(self, mock_session):
        """Create a repository instance with mock session."""
        return SQLAlchemyTagRepository(mock_session)

    @pytest.fixture
    def sample_tag(self):
        """Create a sample tag entity."""
        return Tag(
            id=uuid.uuid4(),
            name="test-tag",
            color="#FF0000",
            user_id=uuid.uuid4(),
            created_at=datetime.now(),
            updated_at=datetime.now(),
        )

    @pytest.fixture
    def sample_tag_model(self, sample_tag):
        """Create a sample tag model."""
        return TagModel.from_domain_entity(sample_tag)

    async def test_get_by_id_found(self, repository, mock_session, sample_tag_model):
        """Test getting a tag by ID when it exists."""
        # Setup
        mock_result = MagicMock()
        mock_result.scalar_one_or_none.return_value = sample_tag_model
        mock_session.execute.return_value = mock_result

        # Execute
        result = await repository.get_by_id(sample_tag_model.id)

        # Verify
        assert result is not None
        assert result.id == sample_tag_model.id
        assert result.name == sample_tag_model.name
        mock_session.execute.assert_called_once()

    async def test_get_by_id_not_found(self, repository, mock_session):
        """Test getting a tag by ID when it doesn't exist."""
        # Setup
        mock_result = MagicMock()
        mock_result.scalar_one_or_none.return_value = None
        mock_session.execute.return_value = mock_result

        # Execute
        result = await repository.get_by_id(uuid.uuid4())

        # Verify
        assert result is None
        mock_session.execute.assert_called_once()

    async def test_get_by_name_found(self, repository, mock_session, sample_tag_model):
        """Test getting a tag by name when it exists."""
        # Setup
        mock_result = MagicMock()
        mock_result.scalar_one_or_none.return_value = sample_tag_model
        mock_session.execute.return_value = mock_result

        # Execute
        result = await repository.get_by_name(sample_tag_model.user_id, sample_tag_model.name)

        # Verify
        assert result is not None
        assert result.name == sample_tag_model.name
        assert result.user_id == sample_tag_model.user_id
        mock_session.execute.assert_called_once()

    async def test_get_by_name_not_found(self, repository, mock_session):
        """Test getting a tag by name when it doesn't exist."""
        # Setup
        mock_result = MagicMock()
        mock_result.scalar_one_or_none.return_value = None
        mock_session.execute.return_value = mock_result

        # Execute
        result = await repository.get_by_name(uuid.uuid4(), "nonexistent-tag")

        # Verify
        assert result is None
        mock_session.execute.assert_called_once()

    async def test_get_by_name_different_user(self, repository, mock_session):
        """Test getting a tag by name for a different user."""
        # Setup
        mock_result = MagicMock()
        mock_result.scalar_one_or_none.return_value = None
        mock_session.execute.return_value = mock_result

        # Execute - same tag name but different user
        result = await repository.get_by_name(uuid.uuid4(), "test-tag")

        # Verify
        assert result is None
        mock_session.execute.assert_called_once()

    async def test_get_all_by_user(self, repository, mock_session, sample_tag_model):
        """Test getting all tags for a user."""
        # Setup
        mock_result = MagicMock()
        mock_result.scalars.return_value.all.return_value = [sample_tag_model]
        mock_session.execute.return_value = mock_result

        # Execute
        result = await repository.get_all_by_user(sample_tag_model.user_id)

        # Verify
        assert len(result) == 1
        assert result[0].id == sample_tag_model.id
        mock_session.execute.assert_called_once()

    async def test_get_all_by_user_empty_result(self, repository, mock_session):
        """Test getting tags when no tags exist for user."""
        # Setup
        mock_result = MagicMock()
        mock_result.scalars.return_value.all.return_value = []
        mock_session.execute.return_value = mock_result

        # Execute
        result = await repository.get_all_by_user(uuid.uuid4())

        # Verify
        assert len(result) == 0
        mock_session.execute.assert_called_once()

    async def test_create_tag(self, repository, mock_session, sample_tag):
        """Test creating a new tag."""
        # Setup
        mock_session.flush = AsyncMock()
        mock_session.refresh = AsyncMock()

        # Execute
        result = await repository.create(sample_tag)

        # Verify
        assert result.id == sample_tag.id
        assert result.name == sample_tag.name
        assert result.color == sample_tag.color
        mock_session.add.assert_called_once()
        mock_session.flush.assert_called_once()
        mock_session.refresh.assert_called_once()

    async def test_create_tag_without_color(self, repository, mock_session):
        """Test creating a tag without color."""
        # Setup
        tag = Tag(
            id=uuid.uuid4(),
            name="no-color-tag",
            color=None,
            user_id=uuid.uuid4(),
            created_at=datetime.now(),
            updated_at=datetime.now(),
        )
        mock_session.flush = AsyncMock()
        mock_session.refresh = AsyncMock()

        # Execute
        result = await repository.create(tag)

        # Verify
        assert result.color is None
        mock_session.add.assert_called_once()

    async def test_update_tag_found(self, repository, mock_session, sample_tag, sample_tag_model):
        """Test updating an existing tag."""
        # Setup
        mock_result = MagicMock()
        mock_result.scalar_one_or_none.return_value = sample_tag_model
        mock_session.execute.return_value = mock_result
        mock_session.flush = AsyncMock()
        mock_session.refresh = AsyncMock()

        # Modify the tag
        updated_tag = sample_tag.model_copy()
        updated_tag.name = "updated-tag"
        updated_tag.color = "#00FF00"

        # Execute
        result = await repository.update(updated_tag)

        # Verify
        assert result.name == "updated-tag"
        assert result.color == "#00FF00"
        mock_session.execute.assert_called_once()
        mock_session.flush.assert_called_once()
        mock_session.refresh.assert_called_once()

    async def test_update_tag_not_found(self, repository, mock_session, sample_tag):
        """Test updating a tag that doesn't exist."""
        # Setup
        mock_result = MagicMock()
        mock_result.scalar_one_or_none.return_value = None
        mock_session.execute.return_value = mock_result

        # Execute & Verify
        with pytest.raises(ValueError, match="Tag with ID .* not found"):
            await repository.update(sample_tag)

    async def test_delete_tag_found(self, repository, mock_session, sample_tag_model):
        """Test deleting an existing tag."""
        # Setup
        mock_result = MagicMock()
        mock_result.scalar_one_or_none.return_value = sample_tag_model
        mock_session.execute.return_value = mock_result
        mock_session.delete = AsyncMock()
        mock_session.flush = AsyncMock()

        # Execute
        result = await repository.delete(sample_tag_model.id)

        # Verify
        assert result is True
        mock_session.execute.assert_called_once()
        mock_session.delete.assert_called_once_with(sample_tag_model)
        mock_session.flush.assert_called_once()

    async def test_delete_tag_not_found(self, repository, mock_session):
        """Test deleting a tag that doesn't exist."""
        # Setup
        mock_result = MagicMock()
        mock_result.scalar_one_or_none.return_value = None
        mock_session.execute.return_value = mock_result

        # Execute
        result = await repository.delete(uuid.uuid4())

        # Verify
        assert result is False
        mock_session.execute.assert_called_once()

    async def test_user_isolation(self, repository, mock_session):
        """Test that tags are properly isolated by user."""
        user1_id = uuid.uuid4()
        user2_id = uuid.uuid4()

        # Create tags for different users with same name
        tag1 = Tag(
            id=uuid.uuid4(),
            name="shared-name",
            color="#FF0000",
            user_id=user1_id,
            created_at=datetime.now(),
            updated_at=datetime.now(),
        )

        tag2 = Tag(
            id=uuid.uuid4(),
            name="shared-name",
            color="#00FF00",
            user_id=user2_id,
            created_at=datetime.now(),
            updated_at=datetime.now(),
        )

        # Setup mocks
        mock_session.flush = AsyncMock()
        mock_session.refresh = AsyncMock()

        # Create both tags
        await repository.create(tag1)
        await repository.create(tag2)

        # Verify both were created
        assert mock_session.add.call_count == 2

    async def test_tag_name_uniqueness_per_user(self, repository, mock_session):
        """Test tag name uniqueness constraint per user."""
        user_id = uuid.uuid4()

        # This test verifies the repository behavior, but the actual
        # uniqueness constraint would be enforced at the database level
        tag1 = Tag(
            id=uuid.uuid4(),
            name="unique-per-user",
            color="#FF0000",
            user_id=user_id,
            created_at=datetime.now(),
            updated_at=datetime.now(),
        )

        # Setup mocks
        mock_session.flush = AsyncMock()
        mock_session.refresh = AsyncMock()

        # Create tag
        result = await repository.create(tag1)

        # Verify creation
        assert result.name == "unique-per-user"
        mock_session.add.assert_called_once()

    async def test_multiple_tags_same_user(self, repository, mock_session):
        """Test creating multiple tags for the same user."""
        user_id = uuid.uuid4()
        
        tags = [
            Tag(
                id=uuid.uuid4(),
                name=f"tag-{i}",
                color=f"#FF000{i}",
                user_id=user_id,
                created_at=datetime.now(),
                updated_at=datetime.now(),
            )
            for i in range(3)
        ]

        # Setup mocks
        mock_session.flush = AsyncMock()
        mock_session.refresh = AsyncMock()

        # Create all tags
        for tag in tags:
            await repository.create(tag)

        # Verify all were created
        assert mock_session.add.call_count == 3

    async def test_tag_color_variations(self, repository, mock_session):
        """Test tags with different color formats."""
        user_id = uuid.uuid4()
        
        # Test various color formats and None
        colors = ["#FF0000", "#00ff00", "#0000FF", None, "#FFFFFF"]
        
        # Setup mocks
        mock_session.flush = AsyncMock()
        mock_session.refresh = AsyncMock()

        for i, color in enumerate(colors):
            tag = Tag(
                id=uuid.uuid4(),
                name=f"color-tag-{i}",
                color=color,
                user_id=user_id,
                created_at=datetime.now(),
                updated_at=datetime.now(),
            )
            
            result = await repository.create(tag)
            assert result.color == color

        # Verify all were created
        assert mock_session.add.call_count == len(colors)
