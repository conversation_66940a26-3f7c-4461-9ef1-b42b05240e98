"""
Folder DTOs Module

This module defines the Data Transfer Objects for the folders feature.
"""

from datetime import datetime
from uuid import UUID

from pydantic import BaseModel, Field


class FolderDTO(BaseModel):
    """DTO for a folder."""

    id: UUID
    name: str
    parent_id: UUID | None = None
    user_id: UUID
    is_archived: bool
    created_at: datetime
    updated_at: datetime
    metadata: dict[str, str] = Field(default_factory=dict)


class CreateFolderDTO(BaseModel):
    """DTO for creating a new folder."""

    name: str
    parent_id: UUID | None = None
    metadata: dict[str, str] = Field(default_factory=dict)


class UpdateFolderDTO(BaseModel):
    """DTO for updating an existing folder."""

    name: str | None = None
    parent_id: UUID | None = None
    is_archived: bool | None = None
    metadata: dict[str, str] | None = None


class FolderFilterDTO(BaseModel):
    """DTO for filtering folders."""

    parent_id: UUID | None = None
    include_archived: bool = False
