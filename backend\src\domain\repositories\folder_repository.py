"""
Folder Repository Interface

This module defines the interface for the folder repository.
"""

from abc import ABC, abstractmethod
from uuid import UUID

from src.domain.entities.folder import Folder


class FolderRepository(ABC):
    """Interface for folder repository operations."""

    @abstractmethod
    async def get_by_id(self, folder_id: UUID) -> Folder | None:
        """
        Get a folder by its ID.

        Args:
            folder_id: The ID of the folder to retrieve

        Returns:
            The folder if found, None otherwise
        """
        pass

    @abstractmethod
    async def get_all_by_user(
        self,
        user_id: UUID,
        parent_id: UUID | None = None,
        include_archived: bool = False,
    ) -> list[Folder]:
        """
        Get all folders for a user with optional filtering.

        Args:
            user_id: The ID of the user
            parent_id: Optional parent folder ID to filter by
            include_archived: Whether to include archived folders

        Returns:
            List of folders matching the criteria
        """
        pass

    @abstractmethod
    async def create(self, folder: Folder) -> Folder:
        """
        Create a new folder.

        Args:
            folder: The folder to create

        Returns:
            The created folder with any generated fields
        """
        pass

    @abstractmethod
    async def update(self, folder: Folder) -> Folder:
        """
        Update an existing folder.

        Args:
            folder: The folder to update

        Returns:
            The updated folder
        """
        pass

    @abstractmethod
    async def delete(self, folder_id: UUID) -> bool:
        """
        Delete a folder by its ID.

        Args:
            folder_id: The ID of the folder to delete

        Returns:
            True if the folder was deleted, False otherwise
        """
        pass
