"""
Note Repository Implementation

This module implements the note repository interface using SQLAlchemy.
"""

from uuid import UUID

from sqlalchemy import and_, or_, select
from sqlalchemy.ext.asyncio import AsyncSession

from src.domain.entities.note import Note
from src.domain.repositories.note_repository import NoteRepository
from src.infrastructure.database.models.note import NoteModel


class SQLAlchemyNoteRepository(NoteRepository):
    """SQLAlchemy implementation of the note repository."""

    def __init__(self, session: AsyncSession):
        """
        Initialize the repository.

        Args:
            session: SQLAlchemy async session
        """
        self._session = session

    async def get_by_id(self, note_id: UUID) -> Note | None:
        """
        Get a note by its ID.

        Args:
            note_id: The ID of the note to retrieve

        Returns:
            The note if found, None otherwise
        """
        query = select(NoteModel).where(NoteModel.id == note_id)

        result = await self._session.execute(query)
        note_model = result.scalar_one_or_none()

        if note_model is None:
            return None

        return note_model.to_domain_entity()

    async def get_all_by_user(
        self,
        user_id: UUID,
        folder_id: UUID | None = None,
        include_archived: bool = False,
        only_starred: bool = False,
        tags: list[str] | None = None,
        search_query: str | None = None,
    ) -> list[Note]:
        """
        Get all notes for a user with optional filtering.

        Args:
            user_id: The ID of the user
            folder_id: Optional folder ID to filter by
            include_archived: Whether to include archived notes
            only_starred: Whether to only include starred notes
            tags: Optional list of tags to filter by
            search_query: Optional search query to filter by

        Returns:
            List of notes matching the criteria
        """
        query = select(NoteModel)

        # Base filters
        filters = [NoteModel.created_by == user_id]

        # Folder filter
        if folder_id is not None:
            filters.append(NoteModel.folder_id == folder_id)

        # Archive filter
        if not include_archived:
            filters.append(NoteModel.is_archived == False)  # noqa: E712

        # Star filter
        if only_starred:
            filters.append(NoteModel.is_starred == True)  # noqa: E712

        # Tag filter - using JSON contains for tag filtering
        if tags and len(tags) > 0:
            # Check if any of the provided tags exist in the note's tags JSON array
            tag_filters = []
            for tag in tags:
                tag_filters.append(NoteModel.tags.op("?")(tag))
            filters.append(or_(*tag_filters))

        # Search query filter
        if search_query:
            search_filter = or_(
                NoteModel.title.ilike(f"%{search_query}%"),
                NoteModel.content.ilike(f"%{search_query}%"),
            )
            filters.append(search_filter)

        # Apply all filters
        query = query.where(and_(*filters))

        # Execute query
        result = await self._session.execute(query)
        note_models = result.scalars().all()

        return [note_model.to_domain_entity() for note_model in note_models]

    async def create(self, note: Note) -> Note:
        """
        Create a new note.

        Args:
            note: The note to create

        Returns:
            The created note with any generated fields
        """
        # Create note model using the from_domain_entity method
        note_model = NoteModel.from_domain_entity(note)

        self._session.add(note_model)
        await self._session.flush()
        await self._session.refresh(note_model)

        return note_model.to_domain_entity()

    async def update(self, note: Note) -> Note:
        """
        Update an existing note.

        Args:
            note: The note to update

        Returns:
            The updated note
        """
        # Get the existing note
        query = select(NoteModel).where(NoteModel.id == note.id)

        result = await self._session.execute(query)
        note_model = result.scalar_one_or_none()

        if note_model is None:
            raise ValueError(f"Note with ID {note.id} not found")

        # Update fields
        note_model.title = note.title
        note_model.content = note.content.content
        note_model.content_format = note.content.format
        note_model.content_version = note.content.version
        note_model.folder_id = note.folder_id
        note_model.is_archived = note.is_archived
        note_model.is_starred = note.is_starred
        note_model.updated_at = note.updated_at
        note_model.last_edited_by = note.last_edited_by
        note_model.tags = note.tags or []

        await self._session.flush()
        await self._session.refresh(note_model)

        return note_model.to_domain_entity()

    async def delete(self, note_id: UUID) -> bool:
        """
        Delete a note by its ID.

        Args:
            note_id: The ID of the note to delete

        Returns:
            True if the note was deleted, False otherwise
        """
        query = select(NoteModel).where(NoteModel.id == note_id)
        result = await self._session.execute(query)
        note_model = result.scalar_one_or_none()

        if note_model is None:
            return False

        await self._session.delete(note_model)
        await self._session.flush()

        return True
