"""
User cache implementation.

This module provides Redis caching for user data to improve
performance for frequently accessed user information.
"""

import json
import uuid

from redis.asyncio import Redis

from src.domain.entities.user import User
from src.infrastructure.logging.setup import LoggerMixin


class UserCache(LoggerMixin):
    """
    Redis cache for user data.

    Provides caching for frequently accessed user information
    to reduce database load and improve response times.
    """

    def __init__(self, redis: Redis, ttl: int = 3600) -> None:
        """
        Initialize user cache.

        Args:
            redis: Redis client
            ttl: Cache TTL in seconds (default: 1 hour)
        """
        self.redis = redis
        self.ttl = ttl
        self._prefix = "user:"

    def _get_key(self, key: str) -> str:
        """Get prefixed Redis key."""
        return f"{self._prefix}{key}"

    async def get_by_id(self, user_id: uuid.UUID) -> User | None:
        """
        Get user by ID from cache.

        Args:
            user_id: User ID

        Returns:
            User: User entity or None if not in cache
        """
        key = self._get_key(f"id:{user_id}")
        data = await self.redis.get(key)

        if not data:
            self.logger.debug(f"Cache miss for user ID: {user_id}")
            return None

        try:
            user_dict = json.loads(data)
            # Convert string ID back to UUID
            user_dict["id"] = uuid.UUID(user_dict["id"])

            # Import here to avoid circular imports
            from src.domain.entities.user import User, UserRole, UserStatus

            # Convert string enums back to enum values
            user_dict["role"] = UserRole(user_dict["role"])
            user_dict["status"] = UserStatus(user_dict["status"])

            self.logger.debug(f"Cache hit for user ID: {user_id}")
            return User(**user_dict)
        except Exception as e:
            self.logger.error(f"Error deserializing cached user: {e}")
            return None

    async def get_by_email(self, email: str) -> User | None:
        """
        Get user by email from cache.

        Args:
            email: Email address

        Returns:
            User: User entity or None if not in cache
        """
        key = self._get_key(f"email:{email.lower()}")
        user_id_bytes = await self.redis.get(key)

        if not user_id_bytes:
            self.logger.debug(f"Cache miss for user email: {email}")
            return None

        user_id = uuid.UUID(user_id_bytes.decode())
        return await self.get_by_id(user_id)

    async def get_by_username(self, username: str) -> User | None:
        """
        Get user by username from cache.

        Args:
            username: Username

        Returns:
            User: User entity or None if not in cache
        """
        key = self._get_key(f"username:{username.lower()}")
        user_id_bytes = await self.redis.get(key)

        if not user_id_bytes:
            self.logger.debug(f"Cache miss for username: {username}")
            return None

        user_id = uuid.UUID(user_id_bytes.decode())
        return await self.get_by_id(user_id)

    async def set(self, user: User) -> None:
        """
        Cache user data.

        Args:
            user: User entity to cache
        """
        # Cache the full user object by ID
        id_key = self._get_key(f"id:{user.id}")

        # Convert UUID to string for JSON serialization
        user_dict = user.dict()
        user_dict["id"] = str(user.id)

        # Convert enums to strings for JSON serialization
        user_dict["role"] = user.role.value
        user_dict["status"] = user.status.value

        # Cache the full user data
        await self.redis.set(id_key, json.dumps(user_dict), ex=self.ttl)

        # Cache email and username lookups (store only the ID)
        email_key = self._get_key(f"email:{user.email.lower()}")
        username_key = self._get_key(f"username:{user.username.lower()}")

        await self.redis.set(email_key, str(user.id), ex=self.ttl)
        await self.redis.set(username_key, str(user.id), ex=self.ttl)

        self.logger.debug(f"Cached user: {user.id}")

    async def invalidate(self, user: User) -> None:
        """
        Invalidate user cache.

        Args:
            user: User entity to invalidate
        """
        # Delete all cache keys for this user
        id_key = self._get_key(f"id:{user.id}")
        email_key = self._get_key(f"email:{user.email.lower()}")
        username_key = self._get_key(f"username:{user.username.lower()}")

        await self.redis.delete(id_key, email_key, username_key)
        self.logger.debug(f"Invalidated cache for user: {user.id}")

    async def invalidate_by_id(self, user_id: uuid.UUID) -> None:
        """
        Invalidate user cache by ID.

        Args:
            user_id: User ID to invalidate
        """
        # Get the user first to find email and username keys
        user = await self.get_by_id(user_id)

        if user:
            await self.invalidate(user)
        else:
            # If user not in cache, just delete the ID key
            id_key = self._get_key(f"id:{user_id}")
            await self.redis.delete(id_key)
            self.logger.debug(f"Invalidated ID cache for user: {user_id}")
