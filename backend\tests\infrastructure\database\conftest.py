"""
Database test configuration.

This module provides fixtures and configuration for database-related tests.
"""

import asyncio
import uuid
from datetime import datetime
from typing import AsyncGenerator

import pytest
import pytest_asyncio
from sqlalchemy.ext.asyncio import AsyncSession, create_async_engine
from sqlalchemy.orm import sessionmaker

from src.domain.entities.folder import Folder
from src.domain.entities.note import Note, NoteContent, NoteFormat
from src.domain.entities.tag import Tag
from src.domain.entities.user import User, UserR<PERSON>, UserStatus
from src.infrastructure.database.models.base import Base


@pytest.fixture(scope="session")
def event_loop():
    """Create an instance of the default event loop for the test session."""
    loop = asyncio.get_event_loop_policy().new_event_loop()
    yield loop
    loop.close()


@pytest_asyncio.fixture(scope="session")
async def test_engine():
    """Create a test database engine."""
    # Use in-memory SQLite for testing
    engine = create_async_engine(
        "sqlite+aiosqlite:///:memory:",
        echo=False,
        future=True,
    )
    
    # Create all tables
    async with engine.begin() as conn:
        await conn.run_sync(Base.metadata.create_all)
    
    yield engine
    
    # Clean up
    await engine.dispose()


@pytest_asyncio.fixture
async def test_session(test_engine) -> AsyncGenerator[AsyncSession, None]:
    """Create a test database session."""
    async_session = sessionmaker(
        test_engine, class_=AsyncSession, expire_on_commit=False
    )
    
    async with async_session() as session:
        yield session
        await session.rollback()


@pytest.fixture
def sample_user_id():
    """Generate a sample user ID."""
    return uuid.uuid4()


@pytest.fixture
def sample_folder_id():
    """Generate a sample folder ID."""
    return uuid.uuid4()


@pytest.fixture
def sample_user_entity(sample_user_id):
    """Create a sample user entity."""
    return User(
        id=sample_user_id,
        email="<EMAIL>",
        username="testuser",
        full_name="Test User",
        hashed_password="hashed_password_123",
        role=UserRole.USER,
        status=UserStatus.ACTIVE,
        is_verified=True,
        created_at=datetime.now(),
        updated_at=datetime.now(),
    )


@pytest.fixture
def sample_folder_entity(sample_folder_id, sample_user_id):
    """Create a sample folder entity."""
    return Folder(
        id=sample_folder_id,
        name="Test Folder",
        parent_id=None,
        user_id=sample_user_id,
        is_archived=False,
        created_at=datetime.now(),
        updated_at=datetime.now(),
        metadata={"key": "value"}
    )


@pytest.fixture
def sample_tag_entity(sample_user_id):
    """Create a sample tag entity."""
    return Tag(
        id=uuid.uuid4(),
        name="test-tag",
        color="#FF0000",
        user_id=sample_user_id,
        created_at=datetime.now(),
        updated_at=datetime.now(),
    )


@pytest.fixture
def sample_note_entity(sample_user_id, sample_folder_id):
    """Create a sample note entity."""
    return Note(
        id=uuid.uuid4(),
        title="Test Note",
        content=NoteContent(
            content="This is test content",
            format=NoteFormat.MARKDOWN,
            version=1
        ),
        folder_id=sample_folder_id,
        tags=["test", "example"],
        is_archived=False,
        is_starred=True,
        created_at=datetime.now(),
        updated_at=datetime.now(),
        created_by=sample_user_id,
        last_edited_by=sample_user_id,
    )


@pytest.fixture
def multiple_note_entities(sample_user_id, sample_folder_id):
    """Create multiple note entities for testing."""
    notes = []
    for i in range(5):
        note = Note(
            id=uuid.uuid4(),
            title=f"Test Note {i}",
            content=NoteContent(
                content=f"This is test content {i}",
                format=NoteFormat.MARKDOWN,
                version=1
            ),
            folder_id=sample_folder_id if i % 2 == 0 else None,
            tags=[f"tag{i}", "common"] if i % 3 == 0 else [],
            is_archived=i % 4 == 0,
            is_starred=i % 2 == 0,
            created_at=datetime.now(),
            updated_at=datetime.now(),
            created_by=sample_user_id,
            last_edited_by=sample_user_id,
        )
        notes.append(note)
    return notes


@pytest.fixture
def multiple_folder_entities(sample_user_id):
    """Create multiple folder entities for testing."""
    folders = []
    parent_id = None
    
    for i in range(3):
        folder = Folder(
            id=uuid.uuid4(),
            name=f"Test Folder {i}",
            parent_id=parent_id,
            user_id=sample_user_id,
            is_archived=i % 2 == 0,
            created_at=datetime.now(),
            updated_at=datetime.now(),
            metadata={"level": str(i)}
        )
        folders.append(folder)
        # Make the next folder a child of this one
        if i == 0:
            parent_id = folder.id
    
    return folders


@pytest.fixture
def multiple_tag_entities(sample_user_id):
    """Create multiple tag entities for testing."""
    colors = ["#FF0000", "#00FF00", "#0000FF", None, "#FFFF00"]
    tags = []
    
    for i, color in enumerate(colors):
        tag = Tag(
            id=uuid.uuid4(),
            name=f"tag-{i}",
            color=color,
            user_id=sample_user_id,
            created_at=datetime.now(),
            updated_at=datetime.now(),
        )
        tags.append(tag)
    
    return tags


@pytest.fixture
def different_user_id():
    """Generate a different user ID for testing isolation."""
    return uuid.uuid4()


@pytest.fixture
def archived_note_entity(sample_user_id, sample_folder_id):
    """Create an archived note entity."""
    return Note(
        id=uuid.uuid4(),
        title="Archived Note",
        content=NoteContent(
            content="This note is archived",
            format=NoteFormat.MARKDOWN,
            version=1
        ),
        folder_id=sample_folder_id,
        tags=["archived"],
        is_archived=True,
        is_starred=False,
        created_at=datetime.now(),
        updated_at=datetime.now(),
        created_by=sample_user_id,
        last_edited_by=sample_user_id,
    )


@pytest.fixture
def starred_note_entity(sample_user_id):
    """Create a starred note entity."""
    return Note(
        id=uuid.uuid4(),
        title="Starred Note",
        content=NoteContent(
            content="This note is starred",
            format=NoteFormat.RICHTEXT,
            version=2
        ),
        folder_id=None,
        tags=["important", "starred"],
        is_archived=False,
        is_starred=True,
        created_at=datetime.now(),
        updated_at=datetime.now(),
        created_by=sample_user_id,
        last_edited_by=sample_user_id,
    )
