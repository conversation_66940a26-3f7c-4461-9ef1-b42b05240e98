"""
JWT token management.

This module provides JWT token creation, validation, and management
for authentication and authorization.
"""

import uuid
from datetime import datetime, timedelta, timezone
from typing import Any, Dict, Optional

from jose import JWTError, jwt
from passlib.context import CryptContext

from src.infrastructure.config.settings import get_settings
from src.infrastructure.logging.setup import LoggerMixin

settings = get_settings()


class JWTManager(LoggerMixin):
    """
    JWT token manager for authentication.
    
    Handles JWT token creation, validation, and refresh token management
    with proper security practices.
    """
    
    def __init__(self) -> None:
        """Initialize JWT manager."""
        self.secret_key = settings.jwt_secret
        self.algorithm = settings.jwt_algorithm
        self.access_token_expire = self._parse_expire_time(settings.jwt_expires_in)
        self.refresh_token_expire = self._parse_expire_time(settings.jwt_refresh_expires_in)
    
    def _parse_expire_time(self, expire_str: str) -> timedelta:
        """
        Parse expiration time string to timedelta.
        
        Args:
            expire_str: Expiration string (e.g., "7d", "24h", "30m")
            
        Returns:
            timedelta: Parsed expiration time
        """
        if expire_str.endswith('d'):
            return timedelta(days=int(expire_str[:-1]))
        elif expire_str.endswith('h'):
            return timedelta(hours=int(expire_str[:-1]))
        elif expire_str.endswith('m'):
            return timedelta(minutes=int(expire_str[:-1]))
        else:
            # Default to seconds
            return timedelta(seconds=int(expire_str))
    
    def create_access_token(
        self,
        subject: str,
        user_id: str,
        additional_claims: Optional[Dict[str, Any]] = None
    ) -> str:
        """
        Create JWT access token.
        
        Args:
            subject: Token subject (usually email or username)
            user_id: User ID
            additional_claims: Additional claims to include
            
        Returns:
            str: Encoded JWT token
        """
        now = datetime.now(timezone.utc)
        expire = now + self.access_token_expire
        
        payload = {
            "sub": subject,
            "user_id": user_id,
            "iat": now,
            "exp": expire,
            "type": "access",
            "jti": str(uuid.uuid4()),  # JWT ID for token tracking
        }
        
        if additional_claims:
            payload.update(additional_claims)
        
        token = jwt.encode(payload, self.secret_key, algorithm=self.algorithm)
        
        self.logger.debug(f"Created access token for user: {user_id}")
        return token
    
    def create_refresh_token(self, subject: str, user_id: str) -> str:
        """
        Create JWT refresh token.
        
        Args:
            subject: Token subject (usually email or username)
            user_id: User ID
            
        Returns:
            str: Encoded JWT refresh token
        """
        now = datetime.now(timezone.utc)
        expire = now + self.refresh_token_expire
        
        payload = {
            "sub": subject,
            "user_id": user_id,
            "iat": now,
            "exp": expire,
            "type": "refresh",
            "jti": str(uuid.uuid4()),
        }
        
        token = jwt.encode(payload, self.secret_key, algorithm=self.algorithm)
        
        self.logger.debug(f"Created refresh token for user: {user_id}")
        return token
    
    def verify_token(self, token: str, token_type: str = "access") -> Dict[str, Any]:
        """
        Verify and decode JWT token.
        
        Args:
            token: JWT token to verify
            token_type: Expected token type ("access" or "refresh")
            
        Returns:
            dict: Decoded token payload
            
        Raises:
            JWTError: If token is invalid or expired
        """
        try:
            payload = jwt.decode(
                token,
                self.secret_key,
                algorithms=[self.algorithm]
            )
            
            # Verify token type
            if payload.get("type") != token_type:
                raise JWTError(f"Invalid token type. Expected {token_type}")
            
            # Verify expiration
            exp = payload.get("exp")
            if exp and datetime.fromtimestamp(exp, timezone.utc) < datetime.now(timezone.utc):
                raise JWTError("Token has expired")
            
            self.logger.debug(f"Verified {token_type} token for user: {payload.get('user_id')}")
            return payload
            
        except JWTError as e:
            self.logger.warning(f"Token verification failed: {e}")
            raise
    
    def refresh_access_token(self, refresh_token: str) -> str:
        """
        Create new access token from refresh token.
        
        Args:
            refresh_token: Valid refresh token
            
        Returns:
            str: New access token
            
        Raises:
            JWTError: If refresh token is invalid
        """
        payload = self.verify_token(refresh_token, "refresh")
        
        return self.create_access_token(
            subject=payload["sub"],
            user_id=payload["user_id"]
        )
    
    def get_token_payload(self, token: str) -> Optional[Dict[str, Any]]:
        """
        Get token payload without verification (for debugging).
        
        Args:
            token: JWT token
            
        Returns:
            dict: Token payload or None if invalid
        """
        try:
            return jwt.get_unverified_claims(token)
        except JWTError:
            return None


class PasswordManager(LoggerMixin):
    """
    Password hashing and verification manager.
    
    Handles secure password hashing using bcrypt with configurable rounds.
    """
    
    def __init__(self) -> None:
        """Initialize password manager."""
        self.pwd_context = CryptContext(
            schemes=["bcrypt"],
            deprecated="auto",
            bcrypt__rounds=settings.bcrypt_rounds
        )
    
    def hash_password(self, password: str) -> str:
        """
        Hash password using bcrypt.
        
        Args:
            password: Plain text password
            
        Returns:
            str: Hashed password
        """
        hashed = self.pwd_context.hash(password)
        self.logger.debug("Password hashed successfully")
        return hashed
    
    def verify_password(self, plain_password: str, hashed_password: str) -> bool:
        """
        Verify password against hash.
        
        Args:
            plain_password: Plain text password
            hashed_password: Hashed password
            
        Returns:
            bool: True if password matches
        """
        is_valid = self.pwd_context.verify(plain_password, hashed_password)
        self.logger.debug(f"Password verification: {'success' if is_valid else 'failed'}")
        return is_valid
    
    def needs_update(self, hashed_password: str) -> bool:
        """
        Check if password hash needs update.
        
        Args:
            hashed_password: Hashed password
            
        Returns:
            bool: True if hash needs update
        """
        return self.pwd_context.needs_update(hashed_password)


# Global instances
jwt_manager = JWTManager()
password_manager = PasswordManager()
