"""
User Data Transfer Objects for the Lonors application.

This module contains DTOs for user-related operations following
Clean Architecture principles for data transfer between layers.
"""

import math
from datetime import datetime
from typing import List, Optional

from src.domain.entities.user import User
from src.domain.exceptions import DomainValidationError
from src.domain.value_objects.email import Email
from src.domain.value_objects.password import Password
from src.domain.value_objects.username import Username


class CreateUserDTO:
    """DTO for creating a new user."""

    def __init__(
        self,
        email: str,
        username: str,
        full_name: str,
        password: str,
    ) -> None:
        """
        Initialize CreateUserDTO.

        Args:
            email: User email
            username: User username
            full_name: User full name
            password: User password

        Raises:
            DomainValidationError: If validation fails
        """
        # Validate using domain value objects
        self._email = Email(email)
        self._username = Username(username)
        self._password = Password(password)

        if not full_name or not full_name.strip():
            raise DomainValidationError("Full name cannot be empty")

        self.email = email
        self.username = username
        self.full_name = full_name.strip()
        self.password = password


class UpdateUserDTO:
    """DTO for updating user information."""

    def __init__(self, full_name: Optional[str] = None) -> None:
        """
        Initialize UpdateUserDTO.

        Args:
            full_name: New full name (optional)

        Raises:
            DomainValidationError: If validation fails
        """
        if full_name is not None:
            if not full_name or not full_name.strip():
                raise DomainValidationError("Full name cannot be empty")
            self.full_name = full_name.strip()
        else:
            self.full_name = None


class UserResponseDTO:
    """DTO for user response data."""

    def __init__(
        self,
        id: str,
        email: str,
        username: str,
        full_name: str,
        is_active: bool,
        is_verified: bool,
        status: str,
        created_at: datetime,
        updated_at: datetime,
    ) -> None:
        """
        Initialize UserResponseDTO.

        Args:
            id: User ID
            email: User email
            username: User username
            full_name: User full name
            is_active: Whether user is active
            is_verified: Whether email is verified
            status: User status
            created_at: Creation timestamp
            updated_at: Last update timestamp
        """
        self.id = id
        self.email = email
        self.username = username
        self.full_name = full_name
        self.is_active = is_active
        self.is_verified = is_verified
        self.status = status
        self.created_at = created_at
        self.updated_at = updated_at

    @classmethod
    def from_entity(cls, user: User) -> "UserResponseDTO":
        """
        Create UserResponseDTO from User entity.

        Args:
            user: User domain entity

        Returns:
            UserResponseDTO instance
        """
        return cls(
            id=user.id,
            email=user.email.value,
            username=user.username.value,
            full_name=user.full_name,
            is_active=user.is_active,
            is_verified=user.is_verified,
            status=user.status.value,
            created_at=user.created_at,
            updated_at=user.updated_at,
        )


class LoginDTO:
    """DTO for user login."""

    def __init__(self, email: str, password: str) -> None:
        """
        Initialize LoginDTO.

        Args:
            email: User email
            password: User password

        Raises:
            DomainValidationError: If validation fails
        """
        # Validate email format
        self._email = Email(email)

        self.email = email
        self.password = password


class LoginResponseDTO:
    """DTO for login response."""

    def __init__(
        self,
        access_token: str,
        refresh_token: str,
        token_type: str,
        expires_in: int,
        user: UserResponseDTO,
    ) -> None:
        """
        Initialize LoginResponseDTO.

        Args:
            access_token: JWT access token
            refresh_token: JWT refresh token
            token_type: Token type (usually "bearer")
            expires_in: Token expiration time in seconds
            user: User information
        """
        self.access_token = access_token
        self.refresh_token = refresh_token
        self.token_type = token_type
        self.expires_in = expires_in
        self.user = user


class ChangePasswordDTO:
    """DTO for changing user password."""

    def __init__(self, current_password: str, new_password: str) -> None:
        """
        Initialize ChangePasswordDTO.

        Args:
            current_password: Current password
            new_password: New password

        Raises:
            DomainValidationError: If validation fails
        """
        # Validate new password strength
        self._new_password = Password(new_password)

        self.current_password = current_password
        self.new_password = new_password


class PaginatedUsersDTO:
    """DTO for paginated user list."""

    def __init__(
        self,
        items: List[UserResponseDTO],
        total: int,
        page: int,
        size: int,
        pages: int,
    ) -> None:
        """
        Initialize PaginatedUsersDTO.

        Args:
            items: List of user DTOs
            total: Total number of users
            page: Current page number
            size: Page size
            pages: Total number of pages
        """
        self.items = items
        self.total = total
        self.page = page
        self.size = size
        self.pages = pages

    @classmethod
    def create(
        cls,
        items: List[UserResponseDTO],
        total: int,
        page: int,
        size: int,
    ) -> "PaginatedUsersDTO":
        """
        Create PaginatedUsersDTO with calculated pagination.

        Args:
            items: List of user DTOs
            total: Total number of users
            page: Current page number
            size: Page size

        Returns:
            PaginatedUsersDTO instance
        """
        pages = math.ceil(total / size) if size > 0 else 0
        return cls(
            items=items,
            total=total,
            page=page,
            size=size,
            pages=pages,
        )

    @property
    def has_next(self) -> bool:
        """Check if there is a next page."""
        return self.page < self.pages

    @property
    def has_previous(self) -> bool:
        """Check if there is a previous page."""
        return self.page > 1
