/**
 * WebSocket client utilities for MCP/A2A/AG-UI protocol integration
 */

import { API_CONFIG } from './api';

export interface WebSocketMessage {
  type: string;
  payload: any;
  timestamp: string;
  id?: string;
}

export interface WebSocketConfig {
  url: string;
  protocols?: string[];
  reconnect?: boolean;
  maxReconnectAttempts?: number;
  reconnectInterval?: number;
  heartbeatInterval?: number;
}

export class WebSocketClient {
  private ws: WebSocket | null = null;
  private config: WebSocketConfig;
  private reconnectAttempts = 0;
  private reconnectTimer: NodeJS.Timeout | null = null;
  private heartbeatTimer: NodeJS.Timeout | null = null;
  private messageHandlers = new Map<string, Set<(data: any) => void>>();
  private connectionHandlers = new Set<(connected: boolean) => void>();
  private isConnected = false;

  constructor(config: WebSocketConfig) {
    this.config = {
      reconnect: true,
      maxReconnectAttempts: 5,
      reconnectInterval: 3000,
      heartbeatInterval: 30000,
      ...config,
    };
  }

  connect(): Promise<void> {
    return new Promise((resolve, reject) => {
      try {
        this.ws = new WebSocket(this.config.url, this.config.protocols);

        this.ws.onopen = () => {
          this.isConnected = true;
          this.reconnectAttempts = 0;
          this.startHeartbeat();
          this.notifyConnectionHandlers(true);
          resolve();
        };

        this.ws.onclose = () => {
          this.isConnected = false;
          this.stopHeartbeat();
          this.notifyConnectionHandlers(false);

          if (this.config.reconnect && this.reconnectAttempts < (this.config.maxReconnectAttempts || 5)) {
            this.scheduleReconnect();
          }
        };

        this.ws.onerror = (error) => {
          console.error('WebSocket error:', error);
          reject(error);
        };

        this.ws.onmessage = (event) => {
          try {
            const message: WebSocketMessage = JSON.parse(event.data);
            this.handleMessage(message);
          } catch (error) {
            console.error('Failed to parse WebSocket message:', error);
          }
        };
      } catch (error) {
        reject(error);
      }
    });
  }

  disconnect(): void {
    this.config.reconnect = false;
    this.clearReconnectTimer();
    this.stopHeartbeat();

    if (this.ws) {
      this.ws.close();
      this.ws = null;
    }

    this.isConnected = false;
    this.notifyConnectionHandlers(false);
  }

  send(type: string, payload: any): void {
    if (!this.isConnected || !this.ws) {
      throw new Error('WebSocket is not connected');
    }

    const message: WebSocketMessage = {
      type,
      payload,
      timestamp: new Date().toISOString(),
      id: this.generateId(),
    };

    this.ws.send(JSON.stringify(message));
  }

  subscribe(messageType: string, handler: (data: any) => void): () => void {
    if (!this.messageHandlers.has(messageType)) {
      this.messageHandlers.set(messageType, new Set());
    }

    this.messageHandlers.get(messageType)!.add(handler);

    // Return unsubscribe function
    return () => {
      const handlers = this.messageHandlers.get(messageType);
      if (handlers) {
        handlers.delete(handler);
        if (handlers.size === 0) {
          this.messageHandlers.delete(messageType);
        }
      }
    };
  }

  onConnectionChange(handler: (connected: boolean) => void): () => void {
    this.connectionHandlers.add(handler);

    // Return unsubscribe function
    return () => {
      this.connectionHandlers.delete(handler);
    };
  }

  getConnectionStatus(): boolean {
    return this.isConnected;
  }

  private handleMessage(message: WebSocketMessage): void {
    const handlers = this.messageHandlers.get(message.type);
    if (handlers) {
      handlers.forEach(handler => {
        try {
          handler(message.payload);
        } catch (error) {
          console.error(`Error in message handler for type ${message.type}:`, error);
        }
      });
    }
  }

  private scheduleReconnect(): void {
    this.clearReconnectTimer();
    this.reconnectAttempts++;

    const delay = this.config.reconnectInterval! * Math.pow(2, this.reconnectAttempts - 1);

    this.reconnectTimer = setTimeout(() => {
      console.log(`Attempting to reconnect (${this.reconnectAttempts}/${this.config.maxReconnectAttempts})...`);
      this.connect().catch(error => {
        console.error('Reconnection failed:', error);
      });
    }, delay);
  }

  private clearReconnectTimer(): void {
    if (this.reconnectTimer) {
      clearTimeout(this.reconnectTimer);
      this.reconnectTimer = null;
    }
  }

  private startHeartbeat(): void {
    if (this.config.heartbeatInterval) {
      this.heartbeatTimer = setInterval(() => {
        if (this.isConnected) {
          this.send('ping', { timestamp: Date.now() });
        }
      }, this.config.heartbeatInterval);
    }
  }

  private stopHeartbeat(): void {
    if (this.heartbeatTimer) {
      clearInterval(this.heartbeatTimer);
      this.heartbeatTimer = null;
    }
  }

  private notifyConnectionHandlers(connected: boolean): void {
    this.connectionHandlers.forEach(handler => {
      try {
        handler(connected);
      } catch (error) {
        console.error('Error in connection handler:', error);
      }
    });
  }

  private generateId(): string {
    return Math.random().toString(36).substr(2, 9);
  }
}

// Protocol-specific WebSocket clients
export class MCPWebSocketClient extends WebSocketClient {
  constructor() {
    super({
      url: `${API_CONFIG.BASE_URL.replace('http', 'ws')}/ws/mcp`,
      protocols: ['mcp-v1'],
    });
  }

  sendMCPRequest(method: string, params: any): void {
    this.send('mcp_request', { method, params });
  }
}

export class A2AWebSocketClient extends WebSocketClient {
  constructor() {
    super({
      url: `${API_CONFIG.BASE_URL.replace('http', 'ws')}/ws/a2a`,
      protocols: ['a2a-v1'],
    });
  }

  sendAgentMessage(targetAgentId: string, message: any): void {
    this.send('agent_message', { targetAgentId, message });
  }
}

export class AGUIWebSocketClient extends WebSocketClient {
  constructor() {
    super({
      url: `${API_CONFIG.BASE_URL.replace('http', 'ws')}/ws/ag-ui`,
      protocols: ['ag-ui-v1'],
    });
  }

  sendUIUpdate(componentId: string, update: any): void {
    this.send('ui_update', { componentId, update });
  }
}

// Factory function for creating WebSocket clients
export function createWebSocketClient(type: 'mcp' | 'a2a' | 'ag-ui'): WebSocketClient {
  switch (type) {
    case 'mcp':
      return new MCPWebSocketClient();
    case 'a2a':
      return new A2AWebSocketClient();
    case 'ag-ui':
      return new AGUIWebSocketClient();
    default:
      throw new Error(`Unknown WebSocket client type: ${type}`);
  }
}
