// @ts-expect-error - anime.js types are not compatible with strict mode
import anime from 'animejs';

/**
 * Animation utilities using Anime.js for smooth transitions and effects
 */

// Default animation configurations
export const ANIMATION_DEFAULTS = {
  duration: 600,
  easing: 'easeOutExpo',
  delay: 0,
} as const;

// Common animation presets
export const ANIMATION_PRESETS = {
  fadeIn: {
    opacity: [0, 1],
    duration: 400,
    easing: 'easeOutQuad',
  },
  fadeOut: {
    opacity: [1, 0],
    duration: 400,
    easing: 'easeOutQuad',
  },
  slideInUp: {
    translateY: [30, 0],
    opacity: [0, 1],
    duration: 600,
    easing: 'easeOutExpo',
  },
  slideInDown: {
    translateY: [-30, 0],
    opacity: [0, 1],
    duration: 600,
    easing: 'easeOutExpo',
  },
  slideInLeft: {
    translateX: [-30, 0],
    opacity: [0, 1],
    duration: 600,
    easing: 'easeOutExpo',
  },
  slideInRight: {
    translateX: [30, 0],
    opacity: [0, 1],
    duration: 600,
    easing: 'easeOutExpo',
  },
  scaleIn: {
    scale: [0.8, 1],
    opacity: [0, 1],
    duration: 500,
    easing: 'easeOutBack',
  },
  scaleOut: {
    scale: [1, 0.8],
    opacity: [1, 0],
    duration: 300,
    easing: 'easeInBack',
  },
  bounce: {
    translateY: [0, -10, 0],
    duration: 1000,
    easing: 'easeInOutQuad',
    loop: true,
  },
  pulse: {
    scale: [1, 1.05, 1],
    duration: 1500,
    easing: 'easeInOutQuad',
    loop: true,
  },
  shake: {
    translateX: [0, -10, 10, -10, 10, 0],
    duration: 500,
    easing: 'easeInOutQuad',
  },
  rotate: {
    rotate: '1turn',
    duration: 1000,
    easing: 'easeInOutQuad',
  },
  // AI-specific animation presets
  agentStatusChange: {
    scale: [1, 1.1, 1],
    opacity: [0.8, 1],
    duration: 400,
    easing: 'easeOutBack',
  },
  agentActivate: {
    scale: [0.9, 1],
    opacity: [0, 1],
    translateY: [10, 0],
    duration: 500,
    easing: 'easeOutExpo',
  },
  agentDeactivate: {
    scale: [1, 0.9],
    opacity: [1, 0.6],
    duration: 300,
    easing: 'easeInQuad',
  },
  agentError: {
    translateX: [0, -5, 5, -5, 5, 0],
    scale: [1, 1.02, 1],
    duration: 400,
    easing: 'easeInOutQuad',
  },
  agentSuccess: {
    scale: [1, 1.15, 1],
    rotate: [0, 5, -5, 0],
    duration: 600,
    easing: 'easeOutElastic(1, .6)',
  },
  agentThinking: {
    opacity: [0.5, 1, 0.5],
    scale: [1, 1.02, 1],
    duration: 1500,
    easing: 'easeInOutSine',
    loop: true,
  },
  modelLoading: {
    rotate: '360deg',
    duration: 1200,
    easing: 'linear',
    loop: true,
  },
  cardHoverAgent: {
    translateY: [-2, 0],
    scale: [1.02, 1],
    boxShadow: [
      '0 4px 6px -1px rgba(0, 0, 0, 0.1)',
      '0 10px 15px -3px rgba(0, 0, 0, 0.1)'
    ],
    duration: 200,
    easing: 'easeOutQuad',
  },
  statusIndicatorPulse: {
    scale: [1, 1.2, 1],
    opacity: [1, 0.8, 1],
    duration: 1000,
    easing: 'easeInOutQuad',
    loop: true,
  },
} as const;

/**
 * Create an animation with default settings
 */
export function createAnimation(params: any): any {
  return anime({
    ...ANIMATION_DEFAULTS,
    ...params,
  });
}

/**
 * Animate element with a preset animation
 */
export function animateWithPreset(
  targets: string | Element | Element[],
  preset: keyof typeof ANIMATION_PRESETS,
  options: any = {}
): any {
  return createAnimation({
    targets,
    ...ANIMATION_PRESETS[preset],
    ...options,
  });
}

/**
 * Stagger animation for multiple elements
 */
export function staggerAnimation(
  targets: string | Element | Element[],
  animation: any,
  staggerDelay: number = 100
): any {
  return createAnimation({
    targets,
    delay: anime.stagger(staggerDelay),
    ...animation,
  });
}

/**
 * Timeline animation for complex sequences
 */
export function createTimeline(params?: any): any {
  return anime.timeline({
    ...ANIMATION_DEFAULTS,
    ...params,
  });
}

/**
 * Animate page transitions
 */
export function animatePageTransition(
  exitElement: string | Element,
  enterElement: string | Element,
  onComplete?: () => void
): void {
  const timeline = createTimeline({
    complete: onComplete,
  });

  timeline
    .add({
      targets: exitElement,
      opacity: [1, 0],
      translateY: [0, -20],
      duration: 300,
      easing: 'easeInQuad',
    })
    .add({
      targets: enterElement,
      opacity: [0, 1],
      translateY: [20, 0],
      duration: 400,
      easing: 'easeOutQuad',
    }, '-=100');
}

/**
 * Animate loading states
 */
export function animateLoading(targets: string | Element | Element[]): any {
  return createAnimation({
    targets,
    opacity: [0.3, 1, 0.3],
    duration: 1500,
    easing: 'easeInOutQuad',
    loop: true,
  });
}

/**
 * Animate success feedback
 */
export function animateSuccess(targets: string | Element | Element[]): any {
  return createAnimation({
    targets,
    scale: [1, 1.1, 1],
    duration: 400,
    easing: 'easeOutBack',
  });
}

/**
 * Animate error feedback
 */
export function animateError(targets: string | Element | Element[]): any {
  return createAnimation({
    targets,
    translateX: [0, -10, 10, -10, 10, 0],
    duration: 500,
    easing: 'easeInOutQuad',
  });
}

/**
 * Animate card hover effects
 */
export function animateCardHover(
  card: Element,
  isHovering: boolean
): any {
  return createAnimation({
    targets: card,
    scale: isHovering ? 1.02 : 1,
    translateY: isHovering ? -2 : 0,
    duration: 200,
    easing: 'easeOutQuad',
  });
}

/**
 * Animate modal/dialog appearance
 */
export function animateModal(
  modal: string | Element,
  isOpen: boolean,
  onComplete?: () => void
): any {
  if (isOpen) {
    return createAnimation({
      targets: modal,
      opacity: [0, 1],
      scale: [0.9, 1],
      duration: 300,
      easing: 'easeOutBack',
      complete: onComplete,
    });
  } else {
    return createAnimation({
      targets: modal,
      opacity: [1, 0],
      scale: [1, 0.9],
      duration: 200,
      easing: 'easeInQuad',
      complete: onComplete,
    });
  }
}

/**
 * Animate progress indicators
 */
export function animateProgress(
  targets: string | Element | Element[],
  progress: number
): any {
  return createAnimation({
    targets,
    width: `${progress}%`,
    duration: 800,
    easing: 'easeOutExpo',
  });
}

/**
 * Utility to check if user prefers reduced motion
 */
export function prefersReducedMotion(): boolean {
  if (typeof window === 'undefined') return false;
  return window.matchMedia('(prefers-reduced-motion: reduce)').matches;
}

/**
 * Create animation with reduced motion support
 */
export function createAccessibleAnimation(params: any): any {
  if (prefersReducedMotion()) {
    // Reduce animation duration and disable complex animations
    return createAnimation({
      ...params,
      duration: Math.min(params.duration || ANIMATION_DEFAULTS.duration, 200),
      easing: 'linear',
    });
  }
  return createAnimation(params);
}

/**
 * Animate agent status transitions with appropriate feedback
 */
export function animateAgentStatusChange(
  targets: string | Element | Element[],
  fromStatus: string,
  toStatus: string,
  onComplete?: () => void
): any {
  // Choose animation based on status transition
  let preset: keyof typeof ANIMATION_PRESETS = 'agentStatusChange';

  if (toStatus === 'running') {
    preset = 'agentActivate';
  } else if (toStatus === 'failed') {
    preset = 'agentError';
  } else if (toStatus === 'completed') {
    preset = 'agentSuccess';
  } else if (toStatus === 'idle' && fromStatus === 'running') {
    preset = 'agentDeactivate';
  }

  return animateWithPreset(targets, preset, { complete: onComplete });
}

/**
 * Animate AI model loading states
 */
export function animateModelOperation(
  targets: string | Element | Element[],
  operation: 'loading' | 'success' | 'error',
  onComplete?: () => void
): any {
  switch (operation) {
    case 'loading':
      return animateWithPreset(targets, 'modelLoading', { complete: onComplete });
    case 'success':
      return animateWithPreset(targets, 'agentSuccess', { complete: onComplete });
    case 'error':
      return animateWithPreset(targets, 'agentError', { complete: onComplete });
    default:
      return animateWithPreset(targets, 'fadeIn', { complete: onComplete });
  }
}

/**
 * Animate agent card interactions
 */
export function animateAgentCard(
  card: Element,
  interaction: 'hover' | 'select' | 'deselect',
  onComplete?: () => void
): any {
  switch (interaction) {
    case 'hover':
      return animateWithPreset(card, 'cardHoverAgent', { complete: onComplete });
    case 'select':
      return createAccessibleAnimation({
        targets: card,
        scale: [1, 1.02],
        borderColor: ['rgba(59, 130, 246, 0.2)', 'rgba(59, 130, 246, 0.5)'],
        duration: 200,
        easing: 'easeOutQuad',
        complete: onComplete,
      });
    case 'deselect':
      return createAccessibleAnimation({
        targets: card,
        scale: [1.02, 1],
        borderColor: ['rgba(59, 130, 246, 0.5)', 'rgba(59, 130, 246, 0.2)'],
        duration: 200,
        easing: 'easeOutQuad',
        complete: onComplete,
      });
    default:
      return null;
  }
}

/**
 * Animate status indicator with contextual feedback
 */
export function animateStatusIndicator(
  targets: string | Element | Element[],
  status: string,
  shouldPulse: boolean = false
): any {
  const baseAnimation = {
    targets,
    complete: () => {
      if (shouldPulse && (status === 'running' || status === 'thinking')) {
        animateWithPreset(targets, 'statusIndicatorPulse');
      }
    }
  };

  return animateWithPreset(targets, 'agentStatusChange', baseAnimation);
}
