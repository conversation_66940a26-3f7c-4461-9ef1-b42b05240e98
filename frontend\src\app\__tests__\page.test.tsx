import { fireEvent, render, screen } from '@testing-library/react';
import { beforeEach, describe, expect, it, vi } from 'vitest';
import HomePage from '../page';

// Mock all Lucide React icons
vi.mock('lucide-react', () => ({
  Bot: () => <div data-testid="bot-icon">Bot Icon</div>,
  Zap: () => <div data-testid="zap-icon">Zap Icon</div>,
  Brain: () => <div data-testid="brain-icon">Brain Icon</div>,
  Network: () => <div data-testid="network-icon">Network Icon</div>,
  Workflow: () => <div data-testid="workflow-icon">Workflow Icon</div>,
  Database: () => <div data-testid="database-icon">Database Icon</div>,
  ArrowRight: () => <div data-testid="arrow-right-icon">Arrow Right Icon</div>,
  Github: () => <div data-testid="github-icon">GitHub Icon</div>,
  ExternalLink: () => <div data-testid="external-link-icon">External Link Icon</div>,
  Loader2: () => <div data-testid="loader2-icon">Loading Icon</div>,
  CheckCircle: () => <div data-testid="check-circle-icon">Check Icon</div>,
  AlertCircle: () => <div data-testid="alert-circle-icon">Alert Icon</div>,
  Clock: () => <div data-testid="clock-icon">Clock Icon</div>,
  Users: () => <div data-testid="users-icon">Users Icon</div>,
  Settings: () => <div data-testid="settings-icon">Settings Icon</div>,
  Play: () => <div data-testid="play-icon">Play Icon</div>,
  Pause: () => <div data-testid="pause-icon">Pause Icon</div>,
  Stop: () => <div data-testid="stop-icon">Stop Icon</div>,
  Sun: () => <div data-testid="sun-icon">Sun Icon</div>,
  Moon: () => <div data-testid="moon-icon">Moon Icon</div>,
  Monitor: () => <div data-testid="monitor-icon">Monitor Icon</div>,
  ChevronDown: () => <div data-testid="chevron-down-icon">Chevron Down Icon</div>,
  Menu: () => <div data-testid="menu-icon">Menu Icon</div>,
  X: () => <div data-testid="x-icon">X Icon</div>,
}));

// Mock the UI components
vi.mock('../../shared/ui/button', () => ({
  Button: ({ children, onClick, className, disabled, ...props }: any) => (
    <button
      onClick={onClick}
      className={className}
      disabled={disabled}
      data-testid="button"
      {...props}
    >
      {children}
    </button>
  ),
}));

vi.mock('../../shared/ui/card', () => ({
  Card: ({ children, className, ...props }: any) => (
    <div data-testid="card" className={className} {...props}>
      {children}
    </div>
  ),
  CardContent: ({ children, className, ...props }: any) => (
    <div data-testid="card-content" className={className} {...props}>
      {children}
    </div>
  ),
  CardDescription: ({ children, className, ...props }: any) => (
    <p data-testid="card-description" className={className} {...props}>
      {children}
    </p>
  ),
  CardHeader: ({ children, className, ...props }: any) => (
    <div data-testid="card-header" className={className} {...props}>
      {children}
    </div>
  ),
  CardTitle: ({ children, className, ...props }: any) => (
    <h3 data-testid="card-title" className={className} {...props}>
      {children}
    </h3>
  ),
}));

vi.mock('../../shared/ui/badge', () => ({
  Badge: ({ children, variant, className, ...props }: any) => (
    <span
      data-testid="badge"
      data-variant={variant}
      className={className}
      {...props}
    >
      {children}
    </span>
  ),
}));

describe('HomePage', () => {
  beforeEach(() => {
    vi.clearAllMocks();
  });

  it('renders the main heading', () => {
    render(<HomePage />);

    expect(screen.getByText('AI Agent Platform for')).toBeInTheDocument();
    expect(screen.getByText('Everyone')).toBeInTheDocument();
  });

  it('renders the subtitle', () => {
    render(<HomePage />);

    expect(screen.getByText(/Build, deploy, and manage AI agents/)).toBeInTheDocument();
  });

  it('displays all feature cards', () => {
    render(<HomePage />);

    // Check for feature cards (only 4 cards exist)
    expect(screen.getByText('AI Agent Orchestration')).toBeInTheDocument();
    expect(screen.getByText('Visual Flow Builder')).toBeInTheDocument();
    expect(screen.getByText('Knowledge Graph')).toBeInTheDocument();
    expect(screen.getByText('Local Model Management')).toBeInTheDocument();
  });

  it('renders feature icons correctly', () => {
    render(<HomePage />);

    // Use getAllByTestId for icons that appear multiple times
    expect(screen.getAllByTestId('bot-icon')).toHaveLength(2); // Header + feature card
    expect(screen.getByTestId('workflow-icon')).toBeInTheDocument();
    expect(screen.getByTestId('brain-icon')).toBeInTheDocument();
    expect(screen.getByTestId('zap-icon')).toBeInTheDocument();
  });

  it('displays call-to-action buttons', () => {
    render(<HomePage />);

    expect(screen.getByText('Get Started')).toBeInTheDocument();
    expect(screen.getByText('View Documentation')).toBeInTheDocument();
  });

  it('renders status badges', () => {
    render(<HomePage />);

    const badges = screen.getAllByTestId('badge');
    expect(badges.length).toBeGreaterThan(0);
  });

  it('handles get started button click', async () => {
    render(<HomePage />);

    const getStartedButton = screen.getByText('Get Started');
    fireEvent.click(getStartedButton);

    // Button should be clickable without errors
    expect(getStartedButton).toBeInTheDocument();
  });

  it('handles documentation button click', async () => {
    render(<HomePage />);

    const docButton = screen.getByText('View Documentation');
    fireEvent.click(docButton);

    // Button should be clickable without errors
    expect(docButton).toBeInTheDocument();
  });

  it('renders with proper semantic structure', () => {
    render(<HomePage />);

    // Check for main content area
    const mainContent = screen.getByRole('main');
    expect(mainContent).toBeInTheDocument();
  });

  it('displays feature descriptions', () => {
    render(<HomePage />);

    expect(screen.getByText(/Create and manage intelligent agents/)).toBeInTheDocument();
    expect(screen.getByText(/Drag-and-drop interface for building/)).toBeInTheDocument();
    expect(screen.getByText(/Interactive visualization and management/)).toBeInTheDocument();
  });

  it('renders all card components', () => {
    render(<HomePage />);

    const cards = screen.getAllByTestId('card');
    expect(cards.length).toBeGreaterThan(5); // Should have multiple feature cards
  });

  it('includes proper accessibility attributes', () => {
    render(<HomePage />);

    // Check for headings hierarchy
    const mainHeading = screen.getByRole('heading', { level: 1 });
    expect(mainHeading).toBeInTheDocument();

    const subHeadings = screen.getAllByRole('heading', { level: 3 });
    expect(subHeadings.length).toBeGreaterThan(0);
  });

  it('handles loading states correctly', async () => {
    render(<HomePage />);

    // Check if loading icon is present when needed
    const loadingIcons = screen.queryAllByTestId('loader2-icon');
    // Loading icons may or may not be present depending on state
    expect(loadingIcons).toBeDefined();
  });

  it('renders arrow right icons for navigation', () => {
    render(<HomePage />);

    expect(screen.getByTestId('arrow-right-icon')).toBeInTheDocument();
  });

  it('maintains proper card structure', () => {
    render(<HomePage />);

    const cardHeaders = screen.getAllByTestId('card-header');
    const cardContents = screen.getAllByTestId('card-content');
    const cardTitles = screen.getAllByTestId('card-title');

    expect(cardHeaders.length).toBeGreaterThan(0);
    expect(cardContents.length).toBeGreaterThan(0);
    expect(cardTitles.length).toBeGreaterThan(0);
  });

  it('handles button interactions without errors', () => {
    render(<HomePage />);

    const buttons = screen.getAllByTestId('button');
    buttons.forEach(button => {
      expect(() => fireEvent.click(button)).not.toThrow();
    });
  });

  it('renders without console errors', () => {
    const consoleSpy = vi.spyOn(console, 'error').mockImplementation(() => {});

    render(<HomePage />);

    expect(consoleSpy).not.toHaveBeenCalled();
    consoleSpy.mockRestore();
  });
});
