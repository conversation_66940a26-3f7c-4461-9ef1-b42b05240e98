# Docker Development Environment - Lonors AI Platform

## Overview
This document provides instructions for running the Lonors AI Platform in a Docker development environment, ensuring consistent development experience across all platforms while maintaining exclusive pnpm/uv package manager usage.

## Prerequisites
- Docker Desktop installed and running
- Docker Compose v3.8 or higher

## Quick Start

### 1. Start Development Environment
```bash
# From project root directory
docker-compose -f docker-compose.dev.yml up --build
```

### 2. Access Services
- **Frontend**: http://localhost:5500 (React + TypeScript + Vite)
- **Backend**: http://localhost:3001 (Python + FastAPI)
- **PostgreSQL**: localhost:5432
- **Redis**: localhost:6379

### 3. Development Workflow

#### Frontend Development
- Source code changes in `frontend/src/` are automatically hot-reloaded
- pnpm commands executed inside container maintain package manager compliance
- Feature Slice Design architecture preserved
- All AI platform components available for development

#### Backend Development
- Source code changes trigger automatic server reload
- uv package manager used exclusively for Python dependencies
- Database migrations handled automatically
- Protocol integration (MCP/A2A/AG-UI) endpoints available

## Container Management

### View Logs
```bash
# All services
docker-compose -f docker-compose.dev.yml logs -f

# Specific service
docker-compose -f docker-compose.dev.yml logs -f frontend-dev
docker-compose -f docker-compose.dev.yml logs -f backend-dev
```

### Execute Commands in Containers
```bash
# Frontend container (pnpm commands)
docker-compose -f docker-compose.dev.yml exec frontend-dev pnpm test
docker-compose -f docker-compose.dev.yml exec frontend-dev pnpm type-check
docker-compose -f docker-compose.dev.yml exec frontend-dev pnpm build

# Backend container (uv commands)
docker-compose -f docker-compose.dev.yml exec backend-dev uv run pytest
docker-compose -f docker-compose.dev.yml exec backend-dev uv run alembic upgrade head
```

### Stop Environment
```bash
docker-compose -f docker-compose.dev.yml down
```

### Clean Rebuild
```bash
docker-compose -f docker-compose.dev.yml down -v
docker-compose -f docker-compose.dev.yml up --build
```

## Development Features

### Hot Module Replacement (HMR)
- Frontend: Vite HMR with polling for Docker volume compatibility
- Backend: FastAPI auto-reload on file changes
- Database: Persistent data across container restarts

### Package Manager Compliance
- **Frontend**: Exclusive pnpm usage maintained in container
- **Backend**: Exclusive uv usage maintained in container
- **Dependencies**: Frozen lockfiles ensure consistent installations

### Architecture Preservation
- **Feature Slice Design**: Full FSD structure available in development
- **AI Components**: All 25+ AI platform components accessible
- **Protocol Integration**: MCP/A2A/AG-UI endpoints functional
- **Testing**: >90% test coverage requirements maintained

## Troubleshooting

### Port Conflicts
If ports 5500 or 3001 are in use:
```bash
# Check port usage
netstat -ano | findstr :5500
netstat -ano | findstr :3001

# Modify ports in docker-compose.dev.yml if needed
```

### Volume Issues
```bash
# Reset volumes
docker-compose -f docker-compose.dev.yml down -v
docker volume prune
```

### Container Health
```bash
# Check container status
docker-compose -f docker-compose.dev.yml ps

# View health checks
docker inspect lonors_frontend-dev_1 | grep Health -A 10
```

## Production Deployment
This development environment is separate from production deployment. For production:
```bash
docker-compose -f docker-compose.prod.yml up --build -d
```

## Windows Environment Compatibility
This Docker development environment resolves Windows-specific command execution issues while maintaining all Lonors project requirements:
- Exclusive pnpm/uv package manager usage
- Feature Slice Design architecture
- >90% test coverage with TDD methodology
- WCAG 2.1 AA compliance
- <1MB bundle limits
- Protocol integration capabilities
