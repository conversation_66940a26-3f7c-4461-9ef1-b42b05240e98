"""Redis client implementation for caching and session management."""

import json
from typing import Any, Optional, Union
from datetime import timedel<PERSON>

import redis.asyncio as redis
from redis.asyncio import Redis
from redis.exceptions import RedisError

from src.infrastructure.config import Settings
from src.infrastructure.logging import Logger


class RedisClient:
    """Redis client for cache operations."""
    
    def __init__(self, settings: Settings, logger: Logger) -> None:
        """Initialize Redis client.
        
        Args:
            settings: Application settings
            logger: Logger instance
        """
        self._settings = settings
        self._logger = logger
        self._client: Optional[Redis] = None
        
    async def connect(self) -> None:
        """Connect to Redis server."""
        try:
            self._client = await redis.from_url(
                self._settings.redis_url,
                encoding="utf-8",
                decode_responses=True,
                max_connections=50,
                health_check_interval=30
            )
            
            # Test connection
            await self._client.ping()
            self._logger.info("Successfully connected to Redis")
            
        except RedisError as e:
            self._logger.error(f"Failed to connect to Redis: {e}")
            raise
            
    async def disconnect(self) -> None:
        """Disconnect from Redis server."""
        if self._client:
            await self._client.close()
            self._logger.info("Disconnected from Redis")
            
    async def get(self, key: str) -> Optional[Any]:
        """Get value from cache.
        
        Args:
            key: Cache key
            
        Returns:
            Cached value or None if not found
        """
        try:
            value = await self._client.get(key)
            
            if value is None:
                return None
                
            # Try to deserialize JSON
            try:
                return json.loads(value)
            except json.JSONDecodeError:
                return value
                
        except RedisError as e:
            self._logger.error(f"Redis get error for key {key}: {e}")
            return None
            
    async def set(
        self,
        key: str,
        value: Any,
        expire: Optional[Union[int, timedelta]] = None
    ) -> bool:
        """Set value in cache.
        
        Args:
            key: Cache key
            value: Value to cache
            expire: Expiration time in seconds or timedelta
            
        Returns:
            True if successful, False otherwise
        """
        try:
            # Serialize to JSON if not string
            if not isinstance(value, str):
                value = json.dumps(value)
                
            if expire is None:
                await self._client.set(key, value)
            else:
                if isinstance(expire, timedelta):
                    expire = int(expire.total_seconds())
                await self._client.setex(key, expire, value)
                
            return True
            
        except (RedisError, json.JSONEncodeError) as e:
            self._logger.error(f"Redis set error for key {key}: {e}")
            return False
            
    async def delete(self, key: str) -> bool:
        """Delete value from cache.
        
        Args:
            key: Cache key
            
        Returns:
            True if deleted, False otherwise
        """
        try:
            result = await self._client.delete(key)
            return bool(result)
            
        except RedisError as e:
            self._logger.error(f"Redis delete error for key {key}: {e}")
            return False
            
    async def exists(self, key: str) -> bool:
        """Check if key exists in cache.
        
        Args:
            key: Cache key
            
        Returns:
            True if exists, False otherwise
        """
        try:
            return bool(await self._client.exists(key))
            
        except RedisError as e:
            self._logger.error(f"Redis exists error for key {key}: {e}")
            return False
            
    async def expire(
        self,
        key: str,
        seconds: Union[int, timedelta]
    ) -> bool:
        """Set expiration time for a key.
        
        Args:
            key: Cache key
            seconds: Expiration time in seconds or timedelta
            
        Returns:
            True if successful, False otherwise
        """
        try:
            if isinstance(seconds, timedelta):
                seconds = int(seconds.total_seconds())
                
            return bool(await self._client.expire(key, seconds))
            
        except RedisError as e:
            self._logger.error(f"Redis expire error for key {key}: {e}")
            return False
            
    async def increment(
        self,
        key: str,
        amount: int = 1
    ) -> Optional[int]:
        """Increment a value in cache.
        
        Args:
            key: Cache key
            amount: Amount to increment by
            
        Returns:
            New value after increment or None if error
        """
        try:
            return await self._client.incrby(key, amount)
            
        except RedisError as e:
            self._logger.error(f"Redis increment error for key {key}: {e}")
            return None
            
    async def decrement(
        self,
        key: str,
        amount: int = 1
    ) -> Optional[int]:
        """Decrement a value in cache.
        
        Args:
            key: Cache key
            amount: Amount to decrement by
            
        Returns:
            New value after decrement or None if error
        """
        try:
            return await self._client.decrby(key, amount)
            
        except RedisError as e:
            self._logger.error(f"Redis decrement error for key {key}: {e}")
            return None
            
    async def get_ttl(self, key: str) -> Optional[int]:
        """Get time to live for a key.
        
        Args:
            key: Cache key
            
        Returns:
            TTL in seconds, -1 if no expiry, -2 if not exists, None if error
        """
        try:
            return await self._client.ttl(key)
            
        except RedisError as e:
            self._logger.error(f"Redis TTL error for key {key}: {e}")
            return None
            
    async def clear_pattern(self, pattern: str) -> int:
        """Clear all keys matching a pattern.
        
        Args:
            pattern: Key pattern (e.g., "user:*")
            
        Returns:
            Number of keys deleted
        """
        try:
            keys = []
            async for key in self._client.scan_iter(pattern):
                keys.append(key)
                
            if keys:
                return await self._client.delete(*keys)
                
            return 0
            
        except RedisError as e:
            self._logger.error(f"Redis clear pattern error for {pattern}: {e}")
            return 0
            
    @property
    def client(self) -> Optional[Redis]:
        """Get the Redis client instance."""
        return self._client 