# Database Integration Architecture

## Overview

The database integration layer provides a unified interface for interacting with multiple database systems, each optimized for specific data types and access patterns. This polyglot persistence approach allows the application to leverage the strengths of different database technologies.

## Database Technologies

1. **PostgreSQL**
   - Primary database for structured data
   - Handles user accounts, metadata, and relationships
   - Provides ACID transactions and data integrity
   - Used for complex queries and reporting

2. **MongoDB**
   - Document database for storing note content
   - Flexible schema for different content types
   - Efficient for storing and retrieving large documents
   - Supports versioning and history tracking

3. **Neo4j**
   - Graph database for relationships between entities
   - Stores connections between notes, tags, and concepts
   - Enables graph traversal and relationship queries
   - Powers the knowledge graph visualization

4. **Qdrant**
   - Vector database for semantic search
   - Stores embeddings for notes and content
   - Enables similarity search and recommendations
   - Supports hybrid search (vector + keyword)

5. **Dragonfly**
   - In-memory data store for caching
   - Redis-compatible API
   - Improves performance for frequently accessed data
   - Handles rate limiting and session management

## Architecture Components

### Data Access Layer

1. **Repository Pattern**
   - Abstracts database-specific implementations
   - Provides consistent interface for CRUD operations
   - Handles data mapping between domain and persistence models
   - Implements query methods for specific use cases

2. **Unit of Work**
   - Manages transactions across multiple repositories
   - Ensures data consistency
   - Provides atomic operations
   - Handles rollback on failures

3. **Connection Management**
   - Manages database connections and connection pools
   - Handles reconnection and failover
   - Monitors connection health
   - Optimizes connection usage

4. **Query Builder**
   - Constructs database-specific queries
   - Supports complex filtering and sorting
   - Handles pagination
   - Optimizes query performance

### Data Models

1. **Domain Models**
   - Business entities used in application logic
   - Independent of database implementation
   - Encapsulate business rules and validation

2. **Data Transfer Objects (DTOs)**
   - Used for API communication
   - Simplified versions of domain models
   - Tailored for specific use cases

3. **Persistence Models**
   - Database-specific representations
   - Optimized for storage and retrieval
   - Include database-specific metadata

### Integration Features

1. **Data Synchronization**
   - Keeps data consistent across databases
   - Handles eventual consistency
   - Implements change data capture (CDC)
   - Provides conflict resolution

2. **Migration Management**
   - Handles schema evolution
   - Supports versioned migrations
   - Provides rollback capabilities
   - Manages data transformation during migrations

3. **Backup and Recovery**
   - Implements consistent backup strategies
   - Supports point-in-time recovery
   - Handles disaster recovery
   - Manages data retention policies

## Implementation Details

### PostgreSQL Integration

```typescript
// PostgreSQL Repository
class PostgresRepository<T> implements Repository<T> {
  constructor(
    private tableName: string,
    private db: Pool,
    private mapper: DataMapper<T>
  ) {}

  async findById(id: string): Promise<T | null> {
    const result = await this.db.query(
      `SELECT * FROM ${this.tableName} WHERE id = $1`,
      [id]
    );
    return result.rows.length ? this.mapper.toDomain(result.rows[0]) : null;
  }

  async findAll(options?: QueryOptions): Promise<T[]> {
    // Implementation with filtering, sorting, pagination
  }

  async create(entity: T): Promise<T> {
    // Implementation
  }

  async update(id: string, entity: T): Promise<T> {
    // Implementation
  }

  async delete(id: string): Promise<boolean> {
    // Implementation
  }

  // Custom query methods
}
```

### MongoDB Integration

```typescript
// MongoDB Repository
class MongoRepository<T> implements Repository<T> {
  constructor(
    private collection: Collection,
    private mapper: DataMapper<T>
  ) {}

  async findById(id: string): Promise<T | null> {
    const result = await this.collection.findOne({ _id: new ObjectId(id) });
    return result ? this.mapper.toDomain(result) : null;
  }

  async findAll(options?: QueryOptions): Promise<T[]> {
    // Implementation with filtering, sorting, pagination
  }

  async create(entity: T): Promise<T> {
    // Implementation
  }

  async update(id: string, entity: T): Promise<T> {
    // Implementation
  }

  async delete(id: string): Promise<boolean> {
    // Implementation
  }

  // Custom query methods
}
```

### Neo4j Integration

```typescript
// Neo4j Repository
class Neo4jRepository<T> implements Repository<T> {
  constructor(
    private driver: Driver,
    private nodeLabel: string,
    private mapper: DataMapper<T>
  ) {}

  async findById(id: string): Promise<T | null> {
    const session = this.driver.session();
    try {
      const result = await session.run(
        `MATCH (n:${this.nodeLabel} {id: $id}) RETURN n`,
        { id }
      );
      return result.records.length ?
        this.mapper.toDomain(result.records[0].get('n').properties) :
        null;
    } finally {
      await session.close();
    }
  }

  async findAll(options?: QueryOptions): Promise<T[]> {
    // Implementation with filtering, sorting, pagination
  }

  async create(entity: T): Promise<T> {
    // Implementation
  }

  async update(id: string, entity: T): Promise<T> {
    // Implementation
  }

  async delete(id: string): Promise<boolean> {
    // Implementation
  }

  // Graph-specific methods
  async findConnected(id: string, relationship: string): Promise<T[]> {
    // Implementation
  }
}
```

### Qdrant Integration

```typescript
// Qdrant Repository
class QdrantRepository {
  constructor(
    private client: QdrantClient,
    private collectionName: string
  ) {}

  async findSimilar(vector: number[], limit: number = 10): Promise<SearchResult[]> {
    const result = await this.client.search(this.collectionName, {
      vector,
      limit,
      with_payload: true
    });
    return result.map(item => ({
      id: item.id,
      score: item.score,
      payload: item.payload
    }));
  }

  async insert(id: string, vector: number[], payload: any): Promise<void> {
    await this.client.upsert(this.collectionName, {
      points: [{
        id,
        vector,
        payload
      }]
    });
  }

  async delete(id: string): Promise<void> {
    await this.client.delete(this.collectionName, { ids: [id] });
  }

  // Other vector-specific methods
}
```

### Dragonfly Integration

```typescript
// Dragonfly Cache
class DragonflyCache {
  constructor(private client: RedisClient) {}

  async get<T>(key: string): Promise<T | null> {
    const data = await this.client.get(key);
    return data ? JSON.parse(data) : null;
  }

  async set<T>(key: string, value: T, ttl?: number): Promise<void> {
    const data = JSON.stringify(value);
    if (ttl) {
      await this.client.setex(key, ttl, data);
    } else {
      await this.client.set(key, data);
    }
  }

  async delete(key: string): Promise<void> {
    await this.client.del(key);
  }

  async increment(key: string, amount: number = 1): Promise<number> {
    return this.client.incrby(key, amount);
  }

  // Other cache-specific methods
}
```

## Data Flow Examples

### Creating a Note

1. Store note metadata in PostgreSQL
2. Store note content in MongoDB
3. Create node in Neo4j for relationships
4. Generate embeddings and store in Qdrant
5. Invalidate relevant caches in Dragonfly

```typescript
async function createNote(note: Note, userId: string): Promise<Note> {
  // Start a unit of work
  const unitOfWork = new UnitOfWork();

  try {
    // 1. Store metadata in PostgreSQL
    const metadataRepo = unitOfWork.getRepository('noteMetadata');
    const metadata = await metadataRepo.create({
      title: note.title,
      userId,
      folderId: note.folderId,
      isArchived: false,
      isStarred: false
    });

    // 2. Store content in MongoDB
    const contentRepo = unitOfWork.getRepository('noteContent');
    const content = await contentRepo.create({
      metadataId: metadata.id,
      content: note.content,
      format: note.format,
      version: 1
    });

    // 3. Create node in Neo4j
    const graphRepo = unitOfWork.getRepository('noteGraph');
    await graphRepo.create({
      id: metadata.id,
      title: note.title,
      userId
    });

    // 4. Add tags if present
    if (note.tags && note.tags.length > 0) {
      const tagRepo = unitOfWork.getRepository('noteTags');
      await tagRepo.addTagsToNote(metadata.id, note.tags);

      // Add relationships in Neo4j
      await graphRepo.createTagRelationships(metadata.id, note.tags);
    }

    // 5. Generate embeddings and store in Qdrant
    const aiService = new AIService();
    const embeddings = await aiService.generateEmbeddings(note.content);

    const vectorRepo = unitOfWork.getRepository('noteVectors');
    await vectorRepo.insert(metadata.id, embeddings, {
      title: note.title,
      snippet: note.content.substring(0, 100),
      metadataId: metadata.id
    });

    // 6. Invalidate caches
    const cacheService = new CacheService();
    await cacheService.invalidateUserNotes(userId);

    // Commit the unit of work
    await unitOfWork.commit();

    // Return the complete note
    return {
      id: metadata.id,
      title: note.title,
      content: note.content,
      format: note.format,
      tags: note.tags,
      folderId: note.folderId,
      createdAt: metadata.createdAt,
      updatedAt: metadata.updatedAt,
      isArchived: false,
      isStarred: false
    };
  } catch (error) {
    // Rollback on error
    await unitOfWork.rollback();
    throw error;
  }
}
```

### Searching Notes

1. Perform keyword search in PostgreSQL
2. Perform semantic search in Qdrant
3. Combine and rank results
4. Retrieve full content from MongoDB

```typescript
async function searchNotes(
  query: string,
  userId: string,
  options: SearchOptions
): Promise<SearchResult[]> {
  // Check cache first
  const cacheService = new CacheService();
  const cacheKey = `search:${userId}:${query}:${JSON.stringify(options)}`;
  const cachedResults = await cacheService.get(cacheKey);

  if (cachedResults) {
    return cachedResults;
  }

  // Parallel execution of different search strategies
  const [keywordResults, semanticResults] = await Promise.all([
    // 1. Keyword search in PostgreSQL
    searchByKeyword(query, userId, options),

    // 2. Semantic search in Qdrant
    searchBySemantic(query, userId, options)
  ]);

  // 3. Combine and rank results
  const combinedResults = combineSearchResults(
    keywordResults,
    semanticResults,
    options.weights
  );

  // 4. Retrieve full content for top results
  const topResults = combinedResults.slice(0, options.limit || 10);
  const fullResults = await enrichSearchResults(topResults);

  // Cache results
  await cacheService.set(cacheKey, fullResults, 300); // 5 minutes TTL

  return fullResults;
}
```

## Implementation Phases

### Phase 1: Core Database Setup

- Set up PostgreSQL for structured data
- Implement basic repository pattern
- Create initial schema and migrations
- Set up connection management

### Phase 2: Document Storage

- Integrate MongoDB for note content
- Implement versioning system
- Create content repositories
- Set up synchronization with PostgreSQL

### Phase 3: Graph Database

- Set up Neo4j for relationships
- Implement graph repositories
- Create graph visualization data endpoints
- Integrate with PostgreSQL for entity relationships

### Phase 4: Vector Search

- Set up Qdrant for vector storage
- Implement embedding generation and storage
- Create semantic search endpoints
- Integrate with keyword search

### Phase 5: Caching and Optimization

- Set up Dragonfly for caching
- Implement cache invalidation strategies
- Optimize query performance
- Add monitoring and metrics

## Next Steps

1. Set up Docker Compose for local development
2. Create database schemas and initial migrations
3. Implement core repository interfaces
4. Build connection management system
5. Create data synchronization mechanisms
