"""
Tests for user repository.

This module tests the user repository implementation
with database operations and business logic.
"""

import pytest
import uuid
from datetime import datetime, timezone

from src.domain.entities.user import User, UserRole, UserStatus
from src.infrastructure.database.repositories.user_repository import UserRepository


class TestUserRepository:
    """Test cases for user repository."""
    
    @pytest.fixture
    async def user_repository(self, db_session):
        """Create user repository instance."""
        return UserRepository(db_session)
    
    @pytest.fixture
    def sample_user(self):
        """Create sample user entity."""
        return User(
            email="<EMAIL>",
            username="testuser",
            full_name="Test User",
            hashed_password="hashed_password_123",
            role=UserRole.USER,
            status=UserStatus.ACTIVE,
            is_verified=True,
        )
    
    async def test_create_user(self, user_repository, sample_user):
        """Test creating a new user."""
        created_user = await user_repository.create(sample_user)
        
        assert created_user.id is not None
        assert created_user.email == sample_user.email
        assert created_user.username == sample_user.username
        assert created_user.full_name == sample_user.full_name
        assert created_user.hashed_password == sample_user.hashed_password
        assert created_user.role == sample_user.role
        assert created_user.status == sample_user.status
        assert created_user.is_verified == sample_user.is_verified
        assert isinstance(created_user.created_at, datetime)
        assert isinstance(created_user.updated_at, datetime)
    
    async def test_create_user_duplicate_email(self, user_repository, sample_user):
        """Test creating user with duplicate email fails."""
        await user_repository.create(sample_user)
        
        # Try to create another user with same email
        duplicate_user = User(
            email=sample_user.email,  # Same email
            username="different_username",
            hashed_password="different_password",
        )
        
        with pytest.raises(ValueError, match="Email already exists"):
            await user_repository.create(duplicate_user)
    
    async def test_create_user_duplicate_username(self, user_repository, sample_user):
        """Test creating user with duplicate username fails."""
        await user_repository.create(sample_user)
        
        # Try to create another user with same username
        duplicate_user = User(
            email="<EMAIL>",
            username=sample_user.username,  # Same username
            hashed_password="different_password",
        )
        
        with pytest.raises(ValueError, match="Username already exists"):
            await user_repository.create(duplicate_user)
    
    async def test_get_user_by_id(self, user_repository, sample_user):
        """Test getting user by ID."""
        created_user = await user_repository.create(sample_user)
        
        retrieved_user = await user_repository.get_by_id(created_user.id)
        
        assert retrieved_user is not None
        assert retrieved_user.id == created_user.id
        assert retrieved_user.email == created_user.email
        assert retrieved_user.username == created_user.username
    
    async def test_get_user_by_id_not_found(self, user_repository):
        """Test getting user by non-existent ID returns None."""
        non_existent_id = uuid.uuid4()
        
        retrieved_user = await user_repository.get_by_id(non_existent_id)
        
        assert retrieved_user is None
    
    async def test_get_user_by_email(self, user_repository, sample_user):
        """Test getting user by email."""
        created_user = await user_repository.create(sample_user)
        
        retrieved_user = await user_repository.get_by_email(created_user.email)
        
        assert retrieved_user is not None
        assert retrieved_user.id == created_user.id
        assert retrieved_user.email == created_user.email
    
    async def test_get_user_by_email_case_insensitive(self, user_repository, sample_user):
        """Test getting user by email is case insensitive."""
        created_user = await user_repository.create(sample_user)
        
        # Search with different case
        retrieved_user = await user_repository.get_by_email(created_user.email.upper())
        
        assert retrieved_user is not None
        assert retrieved_user.id == created_user.id
    
    async def test_get_user_by_email_not_found(self, user_repository):
        """Test getting user by non-existent email returns None."""
        retrieved_user = await user_repository.get_by_email("<EMAIL>")
        
        assert retrieved_user is None
    
    async def test_get_user_by_username(self, user_repository, sample_user):
        """Test getting user by username."""
        created_user = await user_repository.create(sample_user)
        
        retrieved_user = await user_repository.get_by_username(created_user.username)
        
        assert retrieved_user is not None
        assert retrieved_user.id == created_user.id
        assert retrieved_user.username == created_user.username
    
    async def test_get_user_by_username_case_insensitive(self, user_repository, sample_user):
        """Test getting user by username is case insensitive."""
        created_user = await user_repository.create(sample_user)
        
        # Search with different case
        retrieved_user = await user_repository.get_by_username(created_user.username.upper())
        
        assert retrieved_user is not None
        assert retrieved_user.id == created_user.id
    
    async def test_update_user(self, user_repository, sample_user):
        """Test updating user."""
        created_user = await user_repository.create(sample_user)
        
        # Update user data
        created_user.full_name = "Updated Name"
        created_user.status = UserStatus.SUSPENDED
        created_user.updated_at = datetime.now(timezone.utc)
        
        updated_user = await user_repository.update(created_user)
        
        assert updated_user.full_name == "Updated Name"
        assert updated_user.status == UserStatus.SUSPENDED
        assert updated_user.updated_at > created_user.created_at
    
    async def test_update_user_not_found(self, user_repository):
        """Test updating non-existent user fails."""
        non_existent_user = User(
            id=uuid.uuid4(),
            email="<EMAIL>",
            username="testuser",
            hashed_password="password",
        )
        
        with pytest.raises(ValueError, match="User not found"):
            await user_repository.update(non_existent_user)
    
    async def test_delete_user(self, user_repository, sample_user):
        """Test deleting user."""
        created_user = await user_repository.create(sample_user)
        
        deleted = await user_repository.delete(created_user.id)
        
        assert deleted is True
        
        # Verify user is deleted
        retrieved_user = await user_repository.get_by_id(created_user.id)
        assert retrieved_user is None
    
    async def test_delete_user_not_found(self, user_repository):
        """Test deleting non-existent user returns False."""
        non_existent_id = uuid.uuid4()
        
        deleted = await user_repository.delete(non_existent_id)
        
        assert deleted is False
    
    async def test_list_users(self, user_repository):
        """Test listing users with pagination."""
        # Create multiple users
        users = []
        for i in range(5):
            user = User(
                email=f"user{i}@example.com",
                username=f"user{i}",
                hashed_password="password",
                status=UserStatus.ACTIVE if i % 2 == 0 else UserStatus.INACTIVE,
                is_verified=True,
            )
            created_user = await user_repository.create(user)
            users.append(created_user)
        
        # Test pagination
        page1 = await user_repository.list_users(skip=0, limit=3)
        assert len(page1) == 3
        
        page2 = await user_repository.list_users(skip=3, limit=3)
        assert len(page2) == 2
        
        # Test active only filter
        active_users = await user_repository.list_users(active_only=True)
        assert len(active_users) == 3  # 3 active users (indexes 0, 2, 4)
    
    async def test_count_users(self, user_repository):
        """Test counting users."""
        # Create multiple users
        for i in range(3):
            user = User(
                email=f"user{i}@example.com",
                username=f"user{i}",
                hashed_password="password",
                status=UserStatus.ACTIVE if i % 2 == 0 else UserStatus.INACTIVE,
                is_verified=True,
            )
            await user_repository.create(user)
        
        total_count = await user_repository.count_users()
        assert total_count == 3
        
        active_count = await user_repository.count_users(active_only=True)
        assert active_count == 2  # Users 0 and 2 are active
    
    async def test_email_exists(self, user_repository, sample_user):
        """Test checking if email exists."""
        # Email should not exist initially
        exists = await user_repository.email_exists(sample_user.email)
        assert exists is False
        
        # Create user
        await user_repository.create(sample_user)
        
        # Email should exist now
        exists = await user_repository.email_exists(sample_user.email)
        assert exists is True
        
        # Test case insensitive
        exists = await user_repository.email_exists(sample_user.email.upper())
        assert exists is True
    
    async def test_username_exists(self, user_repository, sample_user):
        """Test checking if username exists."""
        # Username should not exist initially
        exists = await user_repository.username_exists(sample_user.username)
        assert exists is False
        
        # Create user
        await user_repository.create(sample_user)
        
        # Username should exist now
        exists = await user_repository.username_exists(sample_user.username)
        assert exists is True
        
        # Test case insensitive
        exists = await user_repository.username_exists(sample_user.username.upper())
        assert exists is True
