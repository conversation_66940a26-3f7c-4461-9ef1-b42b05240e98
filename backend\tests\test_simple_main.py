"""
Test suite for the simplified backend server.
"""

import pytest
from fastapi.testclient import TestClient
from unittest.mock import AsyncMock, patch
import asyncio

# Import the simple main app
import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from simple_main import app


class TestSimpleBackend:
    """Test suite for the simplified backend server."""
    
    @pytest.fixture
    def client(self):
        """Create a test client for the FastAPI app."""
        return TestClient(app)
    
    def test_root_endpoint(self, client):
        """Test the root endpoint returns correct response."""
        response = client.get("/")
        assert response.status_code == 200
        data = response.json()
        assert data["message"] == "Lonors Backend API"
        assert data["version"] == "1.0.0"
        assert data["environment"] == "development"
        assert "timestamp" in data
    
    def test_health_endpoint_no_db(self, client):
        """Test health endpoint when database is not connected."""
        response = client.get("/health")
        assert response.status_code == 200
        data = response.json()
        assert "status" in data
        assert "timestamp" in data
        assert "checks" in data
        assert "database" in data["checks"]
        assert "redis" in data["checks"]
    
    def test_mcp_protocol_endpoint(self, client):
        """Test MCP protocol endpoint."""
        response = client.get("/api/v1/mcp")
        assert response.status_code == 200
        data = response.json()
        assert data["protocol"] == "MCP"
        assert data["version"] == "1.0.0"
        assert data["status"] == "operational"
        assert data["endpoint"] == "/api/v1/mcp"
    
    def test_a2a_protocol_endpoint(self, client):
        """Test A2A protocol endpoint."""
        response = client.get("/api/v1/a2a")
        assert response.status_code == 200
        data = response.json()
        assert data["protocol"] == "A2A"
        assert data["version"] == "1.0.0"
        assert data["status"] == "operational"
        assert data["endpoint"] == "/api/v1/a2a"
    
    def test_ag_ui_protocol_endpoint(self, client):
        """Test AG-UI protocol endpoint."""
        response = client.get("/api/v1/ag-ui")
        assert response.status_code == 200
        data = response.json()
        assert data["protocol"] == "AG-UI"
        assert data["version"] == "1.0.0"
        assert data["status"] == "operational"
        assert data["endpoint"] == "/api/v1/ag-ui"
        assert data["websocket"] == "/ws/ag-ui"
    
    def test_test_endpoint(self, client):
        """Test the test API endpoint."""
        response = client.get("/api/v1/test")
        assert response.status_code == 200
        data = response.json()
        assert data["message"] == "API is working"
        assert data["endpoint"] == "/api/v1/test"
        assert "database_connected" in data
        assert "redis_connected" in data
        assert "timestamp" in data
    
    def test_websocket_endpoint(self, client):
        """Test WebSocket endpoint for AG-UI protocol."""
        with client.websocket_connect("/ws/ag-ui") as websocket:
            websocket.send_text("test message")
            data = websocket.receive_text()
            assert "AG-UI Echo: test message" in data
    
    def test_cors_headers(self, client):
        """Test CORS headers are properly set."""
        response = client.options("/", headers={
            "Origin": "http://localhost:5500",
            "Access-Control-Request-Method": "GET"
        })
        # FastAPI automatically handles CORS preflight
        assert response.status_code in [200, 405]  # 405 is acceptable for OPTIONS
    
    def test_api_documentation_available(self, client):
        """Test that API documentation is available."""
        response = client.get("/docs")
        assert response.status_code == 200
        assert "text/html" in response.headers.get("content-type", "")
    
    def test_openapi_schema(self, client):
        """Test OpenAPI schema is available."""
        response = client.get("/openapi.json")
        assert response.status_code == 200
        data = response.json()
        assert "openapi" in data
        assert "info" in data
        assert data["info"]["title"] == "Lonors Backend API"


class TestDatabaseIntegration:
    """Test database integration functionality."""
    
    @pytest.fixture
    def client(self):
        """Create a test client for the FastAPI app."""
        return TestClient(app)
    
    @patch('simple_main.db_pool')
    @patch('simple_main.redis_client')
    def test_health_with_mocked_services(self, mock_redis, mock_db, client):
        """Test health endpoint with mocked database and Redis."""
        # Mock successful database connection
        mock_db_conn = AsyncMock()
        mock_db_conn.fetchval.return_value = 1
        mock_db.acquire.return_value.__aenter__.return_value = mock_db_conn
        mock_db.acquire.return_value.__aexit__.return_value = None
        
        # Mock successful Redis connection
        mock_redis.ping.return_value = asyncio.Future()
        mock_redis.ping.return_value.set_result(True)
        
        response = client.get("/health")
        assert response.status_code == 200
        data = response.json()
        # Note: This test may not reflect actual health status due to 
        # async context limitations in sync test


class TestErrorHandling:
    """Test error handling scenarios."""
    
    @pytest.fixture
    def client(self):
        """Create a test client for the FastAPI app."""
        return TestClient(app)
    
    def test_nonexistent_endpoint(self, client):
        """Test that non-existent endpoints return 404."""
        response = client.get("/nonexistent")
        assert response.status_code == 404
    
    def test_invalid_method(self, client):
        """Test invalid HTTP methods."""
        response = client.post("/")  # Root only accepts GET
        assert response.status_code == 405


class TestPerformance:
    """Test performance requirements."""
    
    @pytest.fixture
    def client(self):
        """Create a test client for the FastAPI app."""
        return TestClient(app)
    
    def test_response_time_under_200ms(self, client):
        """Test that API responses are under 200ms."""
        import time
        
        start_time = time.time()
        response = client.get("/health")
        end_time = time.time()
        
        response_time = (end_time - start_time) * 1000  # Convert to milliseconds
        assert response.status_code == 200
        assert response_time < 200, f"Response time {response_time}ms exceeds 200ms limit"
    
    def test_concurrent_requests(self, client):
        """Test handling of concurrent requests."""
        import concurrent.futures
        import time
        
        def make_request():
            start = time.time()
            response = client.get("/")
            end = time.time()
            return response.status_code, (end - start) * 1000
        
        # Test 10 concurrent requests
        with concurrent.futures.ThreadPoolExecutor(max_workers=10) as executor:
            futures = [executor.submit(make_request) for _ in range(10)]
            results = [future.result() for future in concurrent.futures.as_completed(futures)]
        
        # All requests should succeed
        for status_code, response_time in results:
            assert status_code == 200
            assert response_time < 500  # Allow higher limit for concurrent requests
