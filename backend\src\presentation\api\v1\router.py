"""
API v1 router configuration.

This module sets up the main API router with all endpoint groups
for version 1 of the API.
"""

from fastapi import APIRouter

from src.presentation.api.v1 import folders, notes, tags
from src.presentation.api.v1.endpoints import (
    a2a,
    ag_ui,
    ag_ui_ws,
    auth,
    health,
    mcp,
    users,
)

# Import new WebSocket endpoints
try:
    from src.presentation.api.v1.endpoints import a2a_ws, mcp_ws

    WEBSOCKET_ENDPOINTS_AVAILABLE = True
except ImportError as e:
    print(f"Warning: WebSocket endpoints not available: {e}")
    WEBSOCKET_ENDPOINTS_AVAILABLE = False

# Create main API router
api_v1_router = APIRouter()

# Include endpoint routers
api_v1_router.include_router(health.router, prefix="/health", tags=["Health"])

api_v1_router.include_router(auth.router, prefix="/auth", tags=["Authentication"])

api_v1_router.include_router(users.router, prefix="/users", tags=["Users"])

api_v1_router.include_router(mcp.router, prefix="/mcp", tags=["Model Context Protocol"])

api_v1_router.include_router(ag_ui.router, prefix="/ag-ui", tags=["AG-UI Protocol"])

api_v1_router.include_router(ag_ui_ws.router, prefix="/ag-ui", tags=["AG-UI WebSocket"])

api_v1_router.include_router(a2a.router, prefix="/a2a", tags=["A2A Protocol"])

# Include notes feature routers
api_v1_router.include_router(notes.router, prefix="/notes", tags=["Notes"])
api_v1_router.include_router(folders.router, prefix="/folders", tags=["Folders"])
api_v1_router.include_router(tags.router, prefix="/tags", tags=["Tags"])

# Include WebSocket endpoints if available
if WEBSOCKET_ENDPOINTS_AVAILABLE:
    api_v1_router.include_router(mcp_ws.router, prefix="/mcp", tags=["MCP WebSocket"])
    api_v1_router.include_router(a2a_ws.router, prefix="/a2a", tags=["A2A WebSocket"])

# All routers have been implemented and included
