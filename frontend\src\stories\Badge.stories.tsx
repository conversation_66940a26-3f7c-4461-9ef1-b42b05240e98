import type { Meta, StoryObj } from '@storybook/nextjs';
import { Badge } from '../shared/ui/badge';

const meta = {
  title: 'UI/Badge',
  component: Badge,
  parameters: {
    layout: 'centered',
    docs: {
      description: {
        component: 'A small status indicator or label component.',
      },
    },
  },
  tags: ['autodocs'],
  argTypes: {
    variant: {
      control: { type: 'select' },
      options: ['default', 'secondary', 'destructive', 'outline'],
      description: 'The visual style variant of the badge',
    },
    className: {
      control: { type: 'text' },
      description: 'Additional CSS classes',
    },
  },
} satisfies Meta<typeof Badge>;

export default meta;
type Story = StoryObj<typeof meta>;

export const Default: Story = {
  args: {
    children: 'Badge',
  },
};

export const Secondary: Story = {
  args: {
    variant: 'secondary',
    children: 'Secondary',
  },
};

export const Destructive: Story = {
  args: {
    variant: 'destructive',
    children: 'Destructive',
  },
};

export const Outline: Story = {
  args: {
    variant: 'outline',
    children: 'Outline',
  },
};

export const WithIcon: Story = {
  args: {
    children: '🚀 Rocket',
  },
};

export const StatusIndicators: Story = {
  render: () => (
    <div className="flex gap-2 flex-wrap">
      <Badge variant="default">Online</Badge>
      <Badge variant="secondary">Pending</Badge>
      <Badge variant="destructive">Offline</Badge>
      <Badge variant="outline">Draft</Badge>
    </div>
  ),
};

export const Numbers: Story = {
  render: () => (
    <div className="flex gap-2 flex-wrap">
      <Badge>1</Badge>
      <Badge variant="secondary">23</Badge>
      <Badge variant="destructive">99+</Badge>
      <Badge variant="outline">∞</Badge>
    </div>
  ),
};

export const LongText: Story = {
  args: {
    children: 'This is a longer badge text',
    variant: 'secondary',
  },
};
