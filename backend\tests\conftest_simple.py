"""
Simplified test configuration for basic testing.
"""

import pytest
from fastapi.testclient import TestClient


@pytest.fixture
def test_client():
    """Create a test client for the simple backend."""
    # Import here to avoid module loading issues
    import sys
    import os
    sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
    
    from simple_main import app
    return TestClient(app)


@pytest.fixture
def mock_db_pool():
    """Mock database pool for testing."""
    from unittest.mock import AsyncMock
    return AsyncMock()


@pytest.fixture
def mock_redis_client():
    """Mock Redis client for testing."""
    from unittest.mock import AsyncMock
    return AsyncMock()
