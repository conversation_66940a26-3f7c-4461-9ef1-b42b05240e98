"""
Error handling middleware.

This middleware provides centralized error handling with proper
logging and user-friendly error responses.
"""

import logging
from typing import Callable

from fastapi import Request, Response, status
from fastapi.responses import JSONResponse
from starlette.middleware.base import BaseHTTPMiddleware

from src.infrastructure.logging.setup import get_logger

logger = get_logger(__name__)


class ErrorHandlerMiddleware(BaseHTTPMiddleware):
    """
    Middleware for centralized error handling.
    
    Catches unhandled exceptions and returns appropriate error responses
    while logging the errors for debugging.
    """
    
    async def dispatch(self, request: Request, call_next: Callable) -> Response:
        """
        Process request with error handling.
        
        Args:
            request: Incoming request
            call_next: Next middleware/endpoint
            
        Returns:
            Response: Response or error response
        """
        try:
            response = await call_next(request)
            return response
        
        except ValueError as e:
            logger.warning(f"Validation error: {e}", request_path=request.url.path)
            return JSONResponse(
                status_code=status.HTTP_400_BAD_REQUEST,
                content={
                    "error": "Validation Error",
                    "message": str(e),
                    "request_id": getattr(request.state, "request_id", None),
                }
            )
        
        except PermissionError as e:
            logger.warning(f"Permission denied: {e}", request_path=request.url.path)
            return JSONResponse(
                status_code=status.HTTP_403_FORBIDDEN,
                content={
                    "error": "Permission Denied",
                    "message": "You don't have permission to access this resource",
                    "request_id": getattr(request.state, "request_id", None),
                }
            )
        
        except FileNotFoundError as e:
            logger.warning(f"Resource not found: {e}", request_path=request.url.path)
            return JSONResponse(
                status_code=status.HTTP_404_NOT_FOUND,
                content={
                    "error": "Not Found",
                    "message": "The requested resource was not found",
                    "request_id": getattr(request.state, "request_id", None),
                }
            )
        
        except Exception as e:
            logger.error(
                f"Unhandled exception: {e}",
                request_path=request.url.path,
                exception_type=type(e).__name__,
                exc_info=True
            )
            return JSONResponse(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                content={
                    "error": "Internal Server Error",
                    "message": "An unexpected error occurred",
                    "request_id": getattr(request.state, "request_id", None),
                }
            )
