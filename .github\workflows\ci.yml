name: CI/CD Pipeline

on:
  push:
    branches: [ main, develop, staging ]
    paths-ignore:
      - '**/*.md'
      - 'docs/**'
  pull_request:
    branches: [ main, develop ]
    paths-ignore:
      - '**/*.md'
      - 'docs/**'

env:
  PYTHON_VERSION: "3.11"
  NODE_VERSION: "18"
  PNPM_VERSION: "8"

jobs:
  # Backend Testing and Quality Checks
  backend-tests:
    name: Backend Tests (Python)
    runs-on: ubuntu-latest

    services:
      postgres:
        image: postgres:15-alpine
        env:
          POSTGRES_PASSWORD: postgres
          POSTGRES_USER: postgres
          POSTGRES_DB: test_db
        options: >-
          --health-cmd pg_isready
          --health-interval 10s
          --health-timeout 5s
          --health-retries 5
        ports:
          - 5432:5432

      redis:
        image: redis:7-alpine
        options: >-
          --health-cmd "redis-cli ping"
          --health-interval 10s
          --health-timeout 5s
          --health-retries 5
        ports:
          - 6379:6379

    steps:
    - name: Checkout code
      uses: actions/checkout@v4

    - name: Cache uv uv
      uses: astral-sh/setup-uv@v3
      with:
        version: "latest"

    - name: Set up Python
      run: uv python install ${{ env.PYTHON_VERSION }}

    - name: Cache uv dependencies
      uses: actions/cache@v3
      with:
        path: ~/.cache/uv
        key: ${{ runner.os }}-uv-${{ hashFiles('backend/pyproject.toml') }}
        restore-keys: |
          ${{ runner.os }}-uv-

    - name: Install dependencies
      working-directory: ./backend
      run: uv sync --dev

    - name: Run code quality checks
      working-directory: ./backend
      run: |
        uv run ruff check src/ tests/
        uv run black --check src/ tests/
        uv run mypy src/

    - name: Run tests with coverage
      working-directory: ./backend
      env:
        DATABASE_URL: postgresql+asyncpg://postgres:postgres@localhost:5432/test_db
        REDIS_URL: redis://localhost:6379
        JWT_SECRET: test-secret-key
        ENVIRONMENT: testing
      run: |
        uv run pytest --cov=src --cov-report=xml --cov-report=html --cov-fail-under=90

    - name: Upload coverage to Codecov
      uses: codecov/codecov-action@v3
      with:
        file: ./backend/coverage.xml
        flags: backend
        name: backend-coverage

  # Frontend Testing and Quality Checks
  frontend-tests:
    name: Frontend Tests (React + TypeScript)
    runs-on: ubuntu-latest

    steps:
    - name: Checkout code
      uses: actions/checkout@v4

    - name: Setup Node.js
      uses: actions/setup-node@v4
      with:
        node-version: ${{ env.NODE_VERSION }}

    - name: Setup pnpm
      uses: pnpm/action-setup@v2
      with:
        version: ${{ env.PNPM_VERSION }}

    - name: Get pnpm store directory
      shell: bash
      run: |
        echo "STORE_PATH=$(pnpm store path --silent)" >> $GITHUB_ENV

    - name: Setup pnpm cache
      uses: actions/cache@v3
      with:
        path: ${{ env.STORE_PATH }}
        key: ${{ runner.os }}-pnpm-store-${{ hashFiles('**/pnpm-lock.yaml') }}
        restore-keys: |
          ${{ runner.os }}-pnpm-store-

    - name: Install frontend dependencies
      working-directory: ./frontend
      run: pnpm install --frozen-lockfile

    - name: Run code quality checks
      working-directory: ./frontend
      run: |
        pnpm lint
        pnpm type-check

    - name: Run tests with coverage
      working-directory: ./frontend
      run: pnpm test:parallel

    - name: Upload coverage to Codecov
      uses: codecov/codecov-action@v3
      with:
        file: ./frontend/coverage/lcov.info
        flags: frontend
        name: frontend-coverage

    - name: Build application
      working-directory: ./frontend
      run: pnpm build

  # Security Scanning
  security-scan:
    name: Security Scanning
    runs-on: ubuntu-latest

    steps:
    - name: Checkout code
      uses: actions/checkout@v4

    - name: Run Trivy vulnerability scanner
      uses: aquasecurity/trivy-action@master
      with:
        scan-type: 'fs'
        scan-ref: '.'
        format: 'sarif'
        output: 'trivy-results.sarif'

    - name: Upload Trivy scan results to GitHub Security tab
      uses: github/codeql-action/upload-sarif@v2
      if: always()
      with:
        sarif_file: 'trivy-results.sarif'

    - name: Run Bandit security linter (Python)
      working-directory: ./backend
      run: |
        pip install bandit[toml]
        bandit -r src/ -f json -o bandit-report.json || true

    - name: Upload Bandit results
      uses: actions/upload-artifact@v3
      if: always()
      with:
        name: bandit-results
        path: ./backend/bandit-report.json

  # Docker Build Test
  docker-build:
    name: Docker Build Test
    runs-on: ubuntu-latest

    steps:
    - name: Checkout code
      uses: actions/checkout@v4

    - name: Set up Docker Buildx
      uses: docker/setup-buildx-action@v3

    - name: Cache Docker layers
      uses: actions/cache@v3
      with:
        path: /tmp/.buildx-cache
        key: ${{ runner.os }}-buildx-${{ github.sha }}
        restore-keys: |
          ${{ runner.os }}-buildx-

    - name: Build backend Docker image
      uses: docker/build-push-action@v5
      with:
        context: ./backend
        file: ./backend/Dockerfile
        target: production
        push: false
        tags: lonors-backend:test
        cache-from: type=local,src=/tmp/.buildx-cache
        cache-to: type=local,dest=/tmp/.buildx-cache-new

    - name: Build frontend Docker image
      uses: docker/build-push-action@v5
      with:
        context: ./frontend
        file: ./frontend/Dockerfile
        target: production
        push: false
        tags: lonors-frontend:test
        cache-from: type=local,src=/tmp/.buildx-cache
        cache-to: type=local,dest=/tmp/.buildx-cache-new

    # Temp fix for https://github.com/docker/build-push-action/issues/252
    - name: Move cache
      run: |
        rm -rf /tmp/.buildx-cache
        mv /tmp/.buildx-cache-new /tmp/.buildx-cache

  # Integration Tests
  integration-tests:
    name: Integration Tests
    runs-on: ubuntu-latest
    needs: [backend-tests, frontend-tests]

    steps:
    - name: Checkout code
      uses: actions/checkout@v4

    - name: Start services with Docker Compose
      run: |
        cp .env.example .env
        docker-compose up -d --build

    - name: Wait for services to be ready
      run: |
        timeout 300 bash -c 'until curl -f http://localhost:3001/health; do sleep 5; done'
        timeout 300 bash -c 'until curl -f http://localhost:5500; do sleep 5; done'

    - name: Run integration tests
      run: |
        # Add integration test commands here
        echo "Integration tests would run here"

    - name: Cleanup
      if: always()
      run: docker-compose down -v

  # Deployment (only on main/staging branches)
  deploy:
    name: Deploy
    runs-on: ubuntu-latest
    needs: [backend-tests, frontend-tests, security-scan, docker-build, integration-tests]
    if: github.event_name == 'push' && (github.ref == 'refs/heads/staging' || github.ref == 'refs/heads/main')

    steps:
    - name: Checkout code
      uses: actions/checkout@v4

    - name: Set environment name
      id: env_name
      run: |
        if [[ $GITHUB_REF == 'refs/heads/main' ]]; then
          echo "env_name=production" >> $GITHUB_OUTPUT
        else
          echo "env_name=staging" >> $GITHUB_OUTPUT
        fi

    - name: Deploy to ${{ steps.env_name.outputs.env_name }}
      run: |
        echo "Deploying to ${{ steps.env_name.outputs.env_name }}"
        # Add actual deployment commands
