"""
Simplified main.py for Lonors backend with database connectivity.
"""

import logging
from collections.abc import AsyncGenerator
from contextlib import asynccontextmanager
from datetime import UTC, datetime

import asyncpg
import redis.asyncio as redis
from fastapi import FastAPI
from fastapi.middleware.cors import CORSMiddleware

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Database and Redis connections
db_pool = None
redis_client = None


@asynccontextmanager
async def lifespan(app: FastAPI) -> AsyncGenerator[None, None]:
    """Application lifespan manager."""
    global db_pool, redis_client

    logger.info("Starting Lonors Backend API")

    # Initialize PostgreSQL connection pool
    try:
        db_pool = await asyncpg.create_pool(
            "********************************************/lonors_dev",
            min_size=1,
            max_size=10,
        )
        logger.info("✅ PostgreSQL connection pool created")
    except Exception as e:
        logger.error(f"❌ Failed to connect to PostgreSQL: {e}")
        db_pool = None

    # Initialize Redis connection
    try:
        redis_client = redis.Redis.from_url("redis://redis:6379")
        await redis_client.ping()
        logger.info("✅ Redis connection established")
    except Exception as e:
        logger.error(f"❌ Failed to connect to Redis: {e}")
        redis_client = None

    logger.info("Application startup complete")

    yield

    # Cleanup
    logger.info("Shutting down Lonors Backend API")

    if db_pool:
        await db_pool.close()
        logger.info("PostgreSQL connection pool closed")

    if redis_client:
        await redis_client.close()
        logger.info("Redis connection closed")

    logger.info("Application shutdown complete")


# Create FastAPI app
app = FastAPI(
    title="Lonors Backend API",
    description="Lonors Backend API - Production Ready",
    version="1.0.0",
    lifespan=lifespan,
)

# Add CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=["http://localhost:5500", "http://127.0.0.1:5500"],
    allow_credentials=True,
    allow_methods=["GET", "POST", "PUT", "DELETE", "PATCH", "OPTIONS"],
    allow_headers=["*"],
)


@app.get("/")
async def root():
    """Root endpoint providing basic API information."""
    return {
        "message": "Lonors Backend API",
        "version": "1.0.0",
        "environment": "development",
        "docs": "/docs",
        "timestamp": datetime.now(UTC).isoformat(),
    }


@app.get("/health")
async def health_check():
    """Health check endpoint for monitoring and load balancers."""
    # Check database connectivity
    db_healthy = False
    if db_pool:
        try:
            async with db_pool.acquire() as conn:
                await conn.fetchval("SELECT 1")
            db_healthy = True
        except Exception as e:
            logger.error(f"Database health check failed: {e}")

    # Check Redis connectivity
    redis_healthy = False
    if redis_client:
        try:
            await redis_client.ping()
            redis_healthy = True
        except Exception as e:
            logger.error(f"Redis health check failed: {e}")

    overall_status = "healthy" if (db_healthy and redis_healthy) else "unhealthy"

    return {
        "status": overall_status,
        "timestamp": datetime.now(UTC).isoformat(),
        "version": "1.0.0",
        "environment": "development",
        "checks": {
            "database": "healthy" if db_healthy else "unhealthy",
            "redis": "healthy" if redis_healthy else "unhealthy",
        },
    }


# Protocol endpoints
@app.get("/api/v1/mcp")
async def mcp_endpoint():
    """Model Context Protocol endpoint."""
    return {
        "protocol": "MCP",
        "version": "1.0.0",
        "status": "operational",
        "endpoint": "/api/v1/mcp",
    }


@app.get("/api/v1/a2a")
async def a2a_endpoint():
    """Application-to-Application Protocol endpoint."""
    return {
        "protocol": "A2A",
        "version": "1.0.0",
        "status": "operational",
        "endpoint": "/api/v1/a2a",
    }


@app.get("/api/v1/ag-ui")
async def ag_ui_endpoint():
    """AG-UI Protocol endpoint."""
    return {
        "protocol": "AG-UI",
        "version": "1.0.0",
        "status": "operational",
        "endpoint": "/api/v1/ag-ui",
        "websocket": "/ws/ag-ui",
    }


@app.websocket("/ws/ag-ui/{session_id}")
async def ag_ui_websocket(websocket, session_id: str):
    """AG-UI Protocol WebSocket endpoint."""
    await websocket.accept()
    logger.info(f"AG-UI WebSocket connected: {session_id}")
    try:
        while True:
            data = await websocket.receive_text()
            # Echo back for testing
            await websocket.send_text(f"AG-UI Echo [{session_id}]: {data}")
    except Exception as e:
        logger.error(f"AG-UI WebSocket error: {e}")
    finally:
        await websocket.close()


@app.websocket("/ws/mcp/{client_id}")
async def mcp_websocket(websocket, client_id: str):
    """MCP Protocol WebSocket endpoint."""
    await websocket.accept()
    logger.info(f"MCP WebSocket connected: {client_id}")
    try:
        while True:
            data = await websocket.receive_text()
            # Echo back for testing
            await websocket.send_text(f"MCP Echo [{client_id}]: {data}")
    except Exception as e:
        logger.error(f"MCP WebSocket error: {e}")
    finally:
        await websocket.close()


@app.websocket("/ws/a2a/{agent_id}")
async def a2a_websocket(websocket, agent_id: str):
    """A2A Protocol WebSocket endpoint."""
    await websocket.accept()
    logger.info(f"A2A WebSocket connected: {agent_id}")
    try:
        while True:
            data = await websocket.receive_text()
            # Echo back for testing
            await websocket.send_text(f"A2A Echo [{agent_id}]: {data}")
    except Exception as e:
        logger.error(f"A2A WebSocket error: {e}")
    finally:
        await websocket.close()


@app.get("/api/v1/test")
async def test_endpoint():
    """Test API endpoint for validation."""
    return {
        "message": "API is working",
        "endpoint": "/api/v1/test",
        "database_connected": db_pool is not None,
        "redis_connected": redis_client is not None,
        "timestamp": datetime.now(UTC).isoformat(),
    }


if __name__ == "__main__":
    import uvicorn

    uvicorn.run(
        "simple_main:app",
        host="0.0.0.0",
        port=3001,
        reload=True,
        log_level="info",
    )
