"""
Tests for the Note entity.
"""

import uuid
from datetime import datetime, timedelta

from src.domain.entities.note import Note, NoteContent, NoteFormat


class TestNoteEntity:
    """Tests for the Note entity."""

    def test_create_note(self):
        """Test creating a note."""
        # Arrange
        user_id = uuid.uuid4()

        # Act
        note = Note(
            title="Test Note",
            content=NoteContent(content="Test content", format=NoteFormat.MARKDOWN),
            created_by=user_id,
            last_edited_by=user_id,
        )

        # Assert
        assert note.title == "Test Note"
        assert note.content.content == "Test content"
        assert note.content.format == NoteFormat.MARKDOWN
        assert note.content.version == 1
        assert note.created_by == user_id
        assert note.last_edited_by == user_id
        assert note.is_archived is False
        assert note.is_starred is False
        assert note.folder_id is None
        assert note.tags == []
        assert isinstance(note.id, uuid.UUID)
        assert isinstance(note.created_at, datetime)
        assert isinstance(note.updated_at, datetime)

    def test_archive_note(self):
        """Test archiving a note."""
        # Arrange
        user_id = uuid.uuid4()
        note = Note(
            title="Test Note",
            content=NoteContent(content="Test content", format=NoteFormat.MARKDOWN),
            created_by=user_id,
            last_edited_by=user_id,
        )
        original_updated_at = note.updated_at

        # Wait a moment to ensure updated_at will be different
        note.updated_at = original_updated_at - timedelta(seconds=1)

        # Act
        note.archive()

        # Assert
        assert note.is_archived is True
        assert note.updated_at > original_updated_at

    def test_unarchive_note(self):
        """Test unarchiving a note."""
        # Arrange
        user_id = uuid.uuid4()
        note = Note(
            title="Test Note",
            content=NoteContent(content="Test content", format=NoteFormat.MARKDOWN),
            created_by=user_id,
            last_edited_by=user_id,
            is_archived=True,
        )
        original_updated_at = note.updated_at

        # Wait a moment to ensure updated_at will be different
        note.updated_at = original_updated_at - timedelta(seconds=1)

        # Act
        note.unarchive()

        # Assert
        assert note.is_archived is False
        assert note.updated_at > original_updated_at

    def test_star_note(self):
        """Test starring a note."""
        # Arrange
        user_id = uuid.uuid4()
        note = Note(
            title="Test Note",
            content=NoteContent(content="Test content", format=NoteFormat.MARKDOWN),
            created_by=user_id,
            last_edited_by=user_id,
        )
        original_updated_at = note.updated_at

        # Wait a moment to ensure updated_at will be different
        note.updated_at = original_updated_at - timedelta(seconds=1)

        # Act
        note.star()

        # Assert
        assert note.is_starred is True
        assert note.updated_at > original_updated_at

    def test_unstar_note(self):
        """Test unstarring a note."""
        # Arrange
        user_id = uuid.uuid4()
        note = Note(
            title="Test Note",
            content=NoteContent(content="Test content", format=NoteFormat.MARKDOWN),
            created_by=user_id,
            last_edited_by=user_id,
            is_starred=True,
        )
        original_updated_at = note.updated_at

        # Wait a moment to ensure updated_at will be different
        note.updated_at = original_updated_at - timedelta(seconds=1)

        # Act
        note.unstar()

        # Assert
        assert note.is_starred is False
        assert note.updated_at > original_updated_at

    def test_update_content(self):
        """Test updating note content."""
        # Arrange
        user_id = uuid.uuid4()
        note = Note(
            title="Test Note",
            content=NoteContent(content="Test content", format=NoteFormat.MARKDOWN),
            created_by=user_id,
            last_edited_by=user_id,
        )
        original_updated_at = note.updated_at
        original_version = note.content.version

        # Wait a moment to ensure updated_at will be different
        note.updated_at = original_updated_at - timedelta(seconds=1)

        # Act
        note.update_content("Updated content")

        # Assert
        assert note.content.content == "Updated content"
        assert note.content.format == NoteFormat.MARKDOWN  # Format unchanged
        assert note.content.version == original_version + 1
        assert note.updated_at > original_updated_at

    def test_update_content_with_format_change(self):
        """Test updating note content with format change."""
        # Arrange
        user_id = uuid.uuid4()
        note = Note(
            title="Test Note",
            content=NoteContent(content="Test content", format=NoteFormat.MARKDOWN),
            created_by=user_id,
            last_edited_by=user_id,
        )
        original_updated_at = note.updated_at
        original_version = note.content.version

        # Wait a moment to ensure updated_at will be different
        note.updated_at = original_updated_at - timedelta(seconds=1)

        # Act
        note.update_content("Updated content", NoteFormat.RICHTEXT)

        # Assert
        assert note.content.content == "Updated content"
        assert note.content.format == NoteFormat.RICHTEXT
        assert note.content.version == original_version + 1
        assert note.updated_at > original_updated_at

    def test_update_title(self):
        """Test updating note title."""
        # Arrange
        user_id = uuid.uuid4()
        note = Note(
            title="Test Note",
            content=NoteContent(content="Test content", format=NoteFormat.MARKDOWN),
            created_by=user_id,
            last_edited_by=user_id,
        )
        original_updated_at = note.updated_at

        # Wait a moment to ensure updated_at will be different
        note.updated_at = original_updated_at - timedelta(seconds=1)

        # Act
        note.update_title("Updated Title")

        # Assert
        assert note.title == "Updated Title"
        assert note.updated_at > original_updated_at

    def test_update_folder(self):
        """Test updating note folder."""
        # Arrange
        user_id = uuid.uuid4()
        folder_id = uuid.uuid4()
        note = Note(
            title="Test Note",
            content=NoteContent(content="Test content", format=NoteFormat.MARKDOWN),
            created_by=user_id,
            last_edited_by=user_id,
        )
        original_updated_at = note.updated_at

        # Wait a moment to ensure updated_at will be different
        note.updated_at = original_updated_at - timedelta(seconds=1)

        # Act
        note.update_folder(folder_id)

        # Assert
        assert note.folder_id == folder_id
        assert note.updated_at > original_updated_at

    def test_update_tags(self):
        """Test updating note tags."""
        # Arrange
        user_id = uuid.uuid4()
        note = Note(
            title="Test Note",
            content=NoteContent(content="Test content", format=NoteFormat.MARKDOWN),
            created_by=user_id,
            last_edited_by=user_id,
        )
        original_updated_at = note.updated_at

        # Wait a moment to ensure updated_at will be different
        note.updated_at = original_updated_at - timedelta(seconds=1)

        # Act
        note.update_tags(["tag1", "tag2"])

        # Assert
        assert note.tags == ["tag1", "tag2"]
        assert note.updated_at > original_updated_at

    def test_update_editor(self):
        """Test updating note editor."""
        # Arrange
        user_id = uuid.uuid4()
        new_editor_id = uuid.uuid4()
        note = Note(
            title="Test Note",
            content=NoteContent(content="Test content", format=NoteFormat.MARKDOWN),
            created_by=user_id,
            last_edited_by=user_id,
        )
        original_updated_at = note.updated_at

        # Wait a moment to ensure updated_at will be different
        note.updated_at = original_updated_at - timedelta(seconds=1)

        # Act
        note.update_editor(new_editor_id)

        # Assert
        assert note.last_edited_by == new_editor_id
        assert note.updated_at > original_updated_at
