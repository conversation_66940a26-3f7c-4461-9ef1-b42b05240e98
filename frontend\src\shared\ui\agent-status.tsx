'use client';

import { cn } from '@/shared/lib/utils';
import { AgentStatus } from '@/shared/types';
import { cva, type VariantProps } from 'class-variance-authority';
import { 
  CheckCircle, 
  Circle, 
  Clock, 
  Loader2, 
  Pause, 
  XCircle,
  AlertTriangle 
} from 'lucide-react';
import React, { useEffect, useRef } from 'react';
import { animateStatusIndicator } from '@/shared/lib/animations';

const statusVariants = cva(
  'inline-flex items-center gap-2 px-2 py-1 rounded-full text-xs font-medium transition-all duration-200',
  {
    variants: {
      status: {
        [AgentStatus.IDLE]: 'bg-slate-100 text-slate-700 dark:bg-slate-800 dark:text-slate-300',
        [AgentStatus.RUNNING]: 'bg-green-100 text-green-700 dark:bg-green-900/30 dark:text-green-400',
        [AgentStatus.PAUSED]: 'bg-yellow-100 text-yellow-700 dark:bg-yellow-900/30 dark:text-yellow-400',
        [AgentStatus.COMPLETED]: 'bg-blue-100 text-blue-700 dark:bg-blue-900/30 dark:text-blue-400',
        [AgentStatus.FAILED]: 'bg-red-100 text-red-700 dark:bg-red-900/30 dark:text-red-400',
        [AgentStatus.CANCELLED]: 'bg-gray-100 text-gray-700 dark:bg-gray-800 dark:text-gray-400',
      },
      size: {
        sm: 'px-1.5 py-0.5 text-xs',
        md: 'px-2 py-1 text-xs',
        lg: 'px-3 py-1.5 text-sm',
      },
      variant: {
        default: '',
        minimal: 'bg-transparent px-0',
        outlined: 'border border-current bg-transparent',
      },
    },
    defaultVariants: {
      status: AgentStatus.IDLE,
      size: 'md',
      variant: 'default',
    },
  }
);

const iconVariants = cva('flex-shrink-0', {
  variants: {
    size: {
      sm: 'h-3 w-3',
      md: 'h-3.5 w-3.5',
      lg: 'h-4 w-4',
    },
  },
  defaultVariants: {
    size: 'md',
  },
});

interface AgentStatusIndicatorProps 
  extends React.HTMLAttributes<HTMLDivElement>,
    VariantProps<typeof statusVariants> {
  status: AgentStatus;
  showIcon?: boolean;
  showText?: boolean;
  animate?: boolean;
  pulse?: boolean;
  customText?: string;
  'aria-label'?: string;
}

const statusConfig = {
  [AgentStatus.IDLE]: {
    icon: Circle,
    text: 'Idle',
    ariaLabel: 'Agent is idle and ready for tasks',
  },
  [AgentStatus.RUNNING]: {
    icon: Loader2,
    text: 'Running',
    ariaLabel: 'Agent is currently executing a task',
    animated: true,
  },
  [AgentStatus.PAUSED]: {
    icon: Pause,
    text: 'Paused',
    ariaLabel: 'Agent execution is paused',
  },
  [AgentStatus.COMPLETED]: {
    icon: CheckCircle,
    text: 'Completed',
    ariaLabel: 'Agent has completed its task successfully',
  },
  [AgentStatus.FAILED]: {
    icon: XCircle,
    text: 'Failed',
    ariaLabel: 'Agent task execution failed',
  },
  [AgentStatus.CANCELLED]: {
    icon: AlertTriangle,
    text: 'Cancelled',
    ariaLabel: 'Agent task was cancelled',
  },
} as const;

const AgentStatusIndicator = React.forwardRef<HTMLDivElement, AgentStatusIndicatorProps>(
  ({ 
    status, 
    size, 
    variant, 
    showIcon = true, 
    showText = true, 
    animate = true,
    pulse = false,
    customText,
    className,
    'aria-label': ariaLabel,
    ...props 
  }, ref) => {
    const indicatorRef = useRef<HTMLDivElement>(null);
    const previousStatus = useRef<AgentStatus>(status);

    const config = statusConfig[status];
    const Icon = config.icon;
    const displayText = customText || config.text;
    const shouldPulse = pulse || (status === AgentStatus.RUNNING);

    // Animate status changes
    useEffect(() => {
      if (animate && indicatorRef.current && previousStatus.current !== status) {
        animateStatusIndicator(
          indicatorRef.current,
          status,
          shouldPulse
        );
        previousStatus.current = status;
      }
    }, [status, animate, shouldPulse]);

    return (
      <div
        ref={ref || indicatorRef}
        className={cn(statusVariants({ status, size, variant }), className)}
        role="status"
        aria-label={ariaLabel || config.ariaLabel}
        aria-live="polite"
        {...props}
      >
        {showIcon && (
          <Icon 
            className={cn(
              iconVariants({ size }),
              config.animated && 'animate-spin',
              shouldPulse && status === AgentStatus.RUNNING && 'animate-pulse'
            )}
            aria-hidden="true"
          />
        )}
        {showText && (
          <span className="font-medium">
            {displayText}
          </span>
        )}
      </div>
    );
  }
);

AgentStatusIndicator.displayName = 'AgentStatusIndicator';

export { AgentStatusIndicator, statusVariants, type AgentStatusIndicatorProps };

// Utility function to get status color for external use
export const getStatusColor = (status: AgentStatus): string => {
  switch (status) {
    case AgentStatus.RUNNING:
      return 'green';
    case AgentStatus.PAUSED:
      return 'yellow';
    case AgentStatus.COMPLETED:
      return 'blue';
    case AgentStatus.FAILED:
      return 'red';
    case AgentStatus.CANCELLED:
      return 'gray';
    default:
      return 'slate';
  }
};

// Utility function to check if status is active
export const isActiveStatus = (status: AgentStatus): boolean => {
  return status === AgentStatus.RUNNING || status === AgentStatus.PAUSED;
};

// Utility function to check if status indicates completion
export const isCompletedStatus = (status: AgentStatus): boolean => {
  return status === AgentStatus.COMPLETED || 
         status === AgentStatus.FAILED || 
         status === AgentStatus.CANCELLED;
};
