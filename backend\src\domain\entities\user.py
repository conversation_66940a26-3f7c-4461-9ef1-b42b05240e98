"""
User domain entity for the Lonors application.

This module contains the User entity that represents a user in the system
following Domain-Driven Design principles with domain events.
"""

from datetime import UTC, datetime
from enum import Enum
from typing import Any

from ..events.base import DomainEvent
from ..events.user_events import UserActivatedEvent, UserEmailVerifiedEvent
from ..exceptions import DomainValidationError
from ..value_objects.email import Email
from ..value_objects.username import Username


class UserStatus(Enum):
    """User status enumeration."""

    ACTIVE = "active"
    INACTIVE = "inactive"
    SUSPENDED = "suspended"
    PENDING = "pending"


class User:
    """User domain entity with business logic and domain events."""

    def __init__(
        self,
        id: str,
        email: Email,
        username: Userna<PERSON>,
        full_name: str,
        hashed_password: str,
        is_active: bool = True,
        is_verified: bool = False,
        created_at: datetime | None = None,
        updated_at: datetime | None = None,
    ) -> None:
        """
        Initialize user entity.

        Args:
            id: Unique user identifier
            email: User email (Email value object)
            username: User username (Username value object)
            full_name: User's full name
            hashed_password: Hashed password
            is_active: Whether user is active
            is_verified: Whether email is verified
            created_at: Creation timestamp
            updated_at: Last update timestamp

        Raises:
            DomainValidationError: If validation fails
        """
        self._validate_full_name(full_name)

        self._id = id
        self._email = email
        self._username = username
        self._full_name = full_name
        self._hashed_password = hashed_password
        self._is_active = is_active
        self._is_verified = is_verified
        self._created_at = created_at or datetime.now(UTC)
        self._updated_at = updated_at or datetime.now(UTC)

        # Domain events
        self._events: list[DomainEvent] = []

    @property
    def id(self) -> str:
        """Get user ID."""
        return self._id

    @property
    def email(self) -> Email:
        """Get user email."""
        return self._email

    @property
    def username(self) -> Username:
        """Get user username."""
        return self._username

    @property
    def full_name(self) -> str:
        """Get user full name."""
        return self._full_name

    @property
    def hashed_password(self) -> str:
        """Get user hashed password."""
        return self._hashed_password

    @property
    def is_active(self) -> bool:
        """Get user active status."""
        return self._is_active

    @property
    def is_verified(self) -> bool:
        """Get user verification status."""
        return self._is_verified

    @property
    def status(self) -> UserStatus:
        """Get user status based on active state."""
        if self._is_active:
            return UserStatus.ACTIVE
        return UserStatus.INACTIVE

    @property
    def created_at(self) -> datetime:
        """Get creation timestamp."""
        return self._created_at

    @property
    def updated_at(self) -> datetime:
        """Get last update timestamp."""
        return self._updated_at

    def activate(self) -> None:
        """Activate the user."""
        if not self._is_active:
            self._is_active = True
            self._updated_at = datetime.now(UTC)
            self._add_event(UserActivatedEvent(user_id=self._id))

    def deactivate(self) -> None:
        """Deactivate the user."""
        if self._is_active:
            self._is_active = False
            self._updated_at = datetime.now(UTC)

    def verify_email(self) -> None:
        """Verify user's email."""
        if not self._is_verified:
            self._is_verified = True
            self._updated_at = datetime.now(UTC)
            self._add_event(
                UserEmailVerifiedEvent(user_id=self._id, email=self._email.value)
            )

    def update_profile(self, full_name: str | None = None) -> None:
        """
        Update user profile.

        Args:
            full_name: New full name

        Raises:
            DomainValidationError: If validation fails
        """
        if full_name is not None:
            self._validate_full_name(full_name)
            self._full_name = full_name
            self._updated_at = datetime.now(UTC)

    def change_password(self, new_hashed_password: str) -> None:
        """
        Change user password.

        Args:
            new_hashed_password: New hashed password
        """
        self._hashed_password = new_hashed_password
        self._updated_at = datetime.now(UTC)

    def to_dict(self) -> dict[str, Any]:
        """
        Convert user to dictionary representation.

        Returns:
            Dictionary representation of user (without password)
        """
        return {
            "id": self._id,
            "email": self._email.value,
            "username": self._username.value,
            "full_name": self._full_name,
            "is_active": self._is_active,
            "is_verified": self._is_verified,
            "status": self.status.value,
            "created_at": self._created_at.isoformat(),
            "updated_at": self._updated_at.isoformat(),
        }

    def get_events(self) -> list[DomainEvent]:
        """Get domain events."""
        return self._events.copy()

    def clear_events(self) -> None:
        """Clear domain events."""
        self._events.clear()

    def _add_event(self, event: DomainEvent) -> None:
        """Add domain event."""
        self._events.append(event)

    def _validate_full_name(self, full_name: str) -> None:
        """
        Validate full name.

        Args:
            full_name: Full name to validate

        Raises:
            DomainValidationError: If full name is invalid
        """
        if not full_name or not full_name.strip():
            raise DomainValidationError("Full name cannot be empty")

        if len(full_name.strip()) > 100:
            raise DomainValidationError("Full name cannot exceed 100 characters")

    def __eq__(self, other: Any) -> bool:
        """Check equality with another User object."""
        if not isinstance(other, User):
            return False
        return self._id == other._id

    def __hash__(self) -> int:
        """Return hash of the user ID."""
        return hash(self._id)

    def __str__(self) -> str:
        """Return string representation of the user."""
        return f"User(id={self._id}, username={self._username.value}, email={self._email.value})"

    def __repr__(self) -> str:
        """Return detailed string representation of the user."""
        return (
            f"User(id='{self._id}', email='{self._email.value}', "
            f"username='{self._username.value}', full_name='{self._full_name}', "
            f"is_active={self._is_active}, is_verified={self._is_verified})"
        )
