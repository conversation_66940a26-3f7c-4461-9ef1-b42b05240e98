# Lonors Backend

FastAPI backend for the Lonors platform implementing clean architecture with comprehensive protocol support.

## 🏗️ Architecture

The backend follows **Clean Architecture** principles with clear separation of concerns:

```
src/
├── domain/           # Business logic and entities
├── application/      # Use cases and application services
├── infrastructure/   # External concerns (database, security, etc.)
└── presentation/     # API endpoints and HTTP concerns
```

### Key Features

- ✅ **FastAPI** with async/await support
- ✅ **Clean Architecture** with dependency injection
- ✅ **JWT Authentication** with role-based access control
- ✅ **PostgreSQL** with SQLAlchemy async ORM
- ✅ **Alembic** database migrations
- ✅ **Comprehensive Testing** with pytest
- ✅ **Protocol Integration** (MCP, AG-UI, A2A)
- ✅ **OWASP Security** compliance
- ✅ **Structured Logging** with correlation IDs

## 🚀 Quick Start

### Prerequisites

- Python 3.11+
- PostgreSQL 14+
- uv package manager

### Installation

1. **Install dependencies:**
```bash
cd backend
uv sync
```

2. **Set up environment variables:**
```bash
cp .env.example .env
# Edit .env with your configuration
```

3. **Set up database:**
```bash
# Create database
createdb lonors_dev

# Run migrations
uv run alembic upgrade head
```

4. **Start development server:**
```bash
uv run uvicorn src.main:app --reload --host 0.0.0.0 --port 8000
```

The API will be available at `http://localhost:8000`

## 📚 API Documentation

- **Interactive Docs**: `http://localhost:8000/docs`
- **ReDoc**: `http://localhost:8000/redoc`
- **OpenAPI JSON**: `http://localhost:8000/openapi.json`

## 🧪 Testing

### Run All Tests
```bash
uv run pytest
```

### Run with Coverage
```bash
uv run pytest --cov=src --cov-report=html
```

### Run Specific Test Categories
```bash
# Unit tests only
uv run pytest tests/unit/

# Integration tests only
uv run pytest tests/integration/

# Authentication tests
uv run pytest tests/test_auth.py -v
```

## 🗄️ Database

### Migrations

```bash
# Create new migration
uv run alembic revision --autogenerate -m "Description"

# Apply migrations
uv run alembic upgrade head

# Rollback migration
uv run alembic downgrade -1

# Show migration history
uv run alembic history
```

### Database Schema

The current schema includes:

- **Users**: User accounts with authentication and profile data
- **MCP Contexts**: AI model conversation contexts
- **AG-UI Layouts**: Dynamic UI component definitions
- **A2A Messages**: Inter-service communication logs

## 🔐 Authentication & Security

### JWT Authentication

The API uses JWT tokens for authentication:

```bash
# Login to get tokens
curl -X POST "http://localhost:8000/api/v1/auth/login" \
  -H "Content-Type: application/json" \
  -d '{"email": "<EMAIL>", "password": "password"}'

# Use access token in requests
curl -H "Authorization: Bearer <access_token>" \
  "http://localhost:8000/api/v1/users/me"
```

### Security Features

- **Password Hashing**: bcrypt with configurable rounds
- **JWT Tokens**: Access and refresh token system
- **CORS Protection**: Configurable CORS policies
- **Rate Limiting**: Per-endpoint rate limiting
- **Input Validation**: Pydantic model validation
- **SQL Injection Protection**: SQLAlchemy ORM
- **OWASP Headers**: Security headers middleware

## 🔌 Protocol Integration

### Model Context Protocol (MCP)

AI model integration with context management:

```python
# Create context
POST /api/v1/mcp/contexts
{
  "type": "conversation",
  "title": "AI Chat",
  "model_id": "gpt-4"
}

# Generate response
POST /api/v1/mcp/generate
{
  "context_id": "uuid",
  "model_id": "gpt-4",
  "messages": [{"role": "user", "content": "Hello!"}]
}
```

## 📊 Monitoring & Logging

### Health Checks

```bash
# Basic health check
curl http://localhost:8000/api/v1/health/

# Detailed health check
curl http://localhost:8000/api/v1/health/detailed
```

### Logging

Structured JSON logging with correlation IDs:

```json
{
  "timestamp": "2024-01-15T12:00:00Z",
  "level": "INFO",
  "logger": "src.presentation.api.v1.endpoints.auth",
  "message": "User authenticated successfully",
  "correlation_id": "req_123",
  "user_id": "user_456",
  "endpoint": "/api/v1/auth/login"
}
```

## 🔧 Configuration

### Environment Variables

```bash
# Database
DATABASE_URL=postgresql+asyncpg://user:pass@localhost/lonors_dev

# Security
SECRET_KEY=your-secret-key
JWT_SECRET_KEY=your-jwt-secret
JWT_ACCESS_TOKEN_EXPIRE_MINUTES=30

# External Services
OPENAI_API_KEY=your-openai-key
ANTHROPIC_API_KEY=your-anthropic-key

# Logging
LOG_LEVEL=INFO
LOG_FORMAT=json

# CORS
CORS_ORIGINS=["http://localhost:3000", "http://localhost:5173"]
```

## 🚀 Deployment

### Docker

```bash
# Build image
docker build -t lonors-backend .

# Run container
docker run -p 8000:8000 \
  -e DATABASE_URL=postgresql://... \
  -e SECRET_KEY=... \
  lonors-backend
```

## 🤝 Contributing

### Development Workflow

1. **Create feature branch:**
```bash
git checkout -b feature/your-feature-name
```

2. **Make changes and add tests:**
```bash
# Add your changes
# Write tests for new functionality
uv run pytest
```

3. **Run quality checks:**
```bash
# Type checking
uv run mypy src/

# Code formatting
uv run black src/ tests/
uv run isort src/ tests/

# Linting
uv run flake8 src/ tests/
```

4. **Commit with conventional commits:**
```bash
git commit -m "feat: add user profile management endpoints"
```

### Code Style

- **Black** for code formatting
- **isort** for import sorting
- **mypy** for type checking
- **flake8** for linting
- **Conventional Commits** for commit messages

### Testing Guidelines

- Write tests for all new functionality
- Maintain >90% test coverage
- Use descriptive test names
- Follow AAA pattern (Arrange, Act, Assert)
- Mock external dependencies

## 📖 Documentation

- [API Documentation](../docs/API_DOCUMENTATION.md)
- [Protocol Documentation](../docs/PROTOCOLS.md)
- [Architecture Decisions](../docs/adr/)

## 🐛 Troubleshooting

### Common Issues

**Database Connection Issues:**
```bash
# Check PostgreSQL is running
pg_isready -h localhost -p 5432

# Check database exists
psql -l | grep lonors
```

**Migration Issues:**
```bash
# Reset migrations (development only)
uv run alembic downgrade base
uv run alembic upgrade head
```

**Import Errors:**
```bash
# Ensure you're in the backend directory
cd backend

# Check Python path
uv run python -c "import src; print('OK')"
```

## 📄 License

This project is licensed under the MIT License - see the [LICENSE](../LICENSE) file for details.
