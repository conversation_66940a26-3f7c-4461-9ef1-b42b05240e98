import { AgentStatus } from '@/shared/types';
import { AgentStatusIndicator } from '@/shared/ui/agent-status';
import type { Meta, StoryObj } from '@storybook/react';
import React from 'react';

const meta: Meta<typeof AgentStatusIndicator> = {
  title: 'Shared/UI/AgentStatusIndicator',
  component: AgentStatusIndicator,
  parameters: {
    layout: 'centered',
    docs: {
      description: {
        component: 'A status indicator component for AI agents with animated transitions, accessibility support, and multiple variants. Displays real-time agent status with appropriate visual feedback.',
      },
    },
  },
  tags: ['autodocs'],
  argTypes: {
    status: {
      control: 'select',
      options: Object.values(AgentStatus),
      description: 'The current status of the agent',
    },
    size: {
      control: 'select',
      options: ['sm', 'md', 'lg'],
      description: 'Size variant of the indicator',
    },
    variant: {
      control: 'select',
      options: ['default', 'minimal', 'outlined'],
      description: 'Visual variant of the indicator',
    },
    showIcon: {
      control: 'boolean',
      description: 'Whether to show the status icon',
    },
    showText: {
      control: 'boolean',
      description: 'Whether to show the status text',
    },
    animate: {
      control: 'boolean',
      description: 'Whether to enable status change animations',
    },
    pulse: {
      control: 'boolean',
      description: 'Whether to show pulse animation',
    },
    customText: {
      control: 'text',
      description: 'Custom text to display instead of default status text',
    },
  },
};

export default meta;
type Story = StoryObj<typeof meta>;

export const Default: Story = {
  args: {
    status: AgentStatus.IDLE,
  },
};

export const AllStatuses: Story = {
  render: () => (
    <div className="space-y-4">
      <div className="grid grid-cols-2 md:grid-cols-3 gap-4">
        {Object.values(AgentStatus).map((status) => (
          <div key={status} className="flex flex-col items-center space-y-2 p-4 border rounded-lg">
            <AgentStatusIndicator status={status} />
            <span className="text-sm text-muted-foreground capitalize">{status}</span>
          </div>
        ))}
      </div>
    </div>
  ),
  parameters: {
    docs: {
      description: {
        story: 'Shows all available agent status variants with their default styling.',
      },
    },
  },
};

export const Sizes: Story = {
  render: () => (
    <div className="space-y-6">
      <div className="space-y-2">
        <h3 className="text-lg font-semibold">Small</h3>
        <div className="flex flex-wrap gap-3">
          {Object.values(AgentStatus).map((status) => (
            <AgentStatusIndicator key={status} status={status} size="sm" />
          ))}
        </div>
      </div>

      <div className="space-y-2">
        <h3 className="text-lg font-semibold">Medium (Default)</h3>
        <div className="flex flex-wrap gap-3">
          {Object.values(AgentStatus).map((status) => (
            <AgentStatusIndicator key={status} status={status} size="md" />
          ))}
        </div>
      </div>

      <div className="space-y-2">
        <h3 className="text-lg font-semibold">Large</h3>
        <div className="flex flex-wrap gap-3">
          {Object.values(AgentStatus).map((status) => (
            <AgentStatusIndicator key={status} status={status} size="lg" />
          ))}
        </div>
      </div>
    </div>
  ),
  parameters: {
    docs: {
      description: {
        story: 'Demonstrates the three available size variants: small, medium, and large.',
      },
    },
  },
};

export const Variants: Story = {
  render: () => (
    <div className="space-y-6">
      <div className="space-y-2">
        <h3 className="text-lg font-semibold">Default</h3>
        <div className="flex flex-wrap gap-3">
          {Object.values(AgentStatus).map((status) => (
            <AgentStatusIndicator key={status} status={status} variant="default" />
          ))}
        </div>
      </div>

      <div className="space-y-2">
        <h3 className="text-lg font-semibold">Minimal</h3>
        <div className="flex flex-wrap gap-3">
          {Object.values(AgentStatus).map((status) => (
            <AgentStatusIndicator key={status} status={status} variant="minimal" />
          ))}
        </div>
      </div>

      <div className="space-y-2">
        <h3 className="text-lg font-semibold">Outlined</h3>
        <div className="flex flex-wrap gap-3">
          {Object.values(AgentStatus).map((status) => (
            <AgentStatusIndicator key={status} status={status} variant="outlined" />
          ))}
        </div>
      </div>
    </div>
  ),
  parameters: {
    docs: {
      description: {
        story: 'Shows the three visual variants: default (filled), minimal (transparent), and outlined (border only).',
      },
    },
  },
};

export const IconOnly: Story = {
  args: {
    status: AgentStatus.RUNNING,
    showText: false,
  },
  parameters: {
    docs: {
      description: {
        story: 'Status indicator with only the icon, useful for compact layouts.',
      },
    },
  },
};

export const TextOnly: Story = {
  args: {
    status: AgentStatus.RUNNING,
    showIcon: false,
  },
  parameters: {
    docs: {
      description: {
        story: 'Status indicator with only text, useful when space is limited.',
      },
    },
  },
};

export const CustomText: Story = {
  args: {
    status: AgentStatus.RUNNING,
    customText: 'Processing...',
  },
  parameters: {
    docs: {
      description: {
        story: 'Status indicator with custom text instead of the default status text.',
      },
    },
  },
};

export const WithPulse: Story = {
  args: {
    status: AgentStatus.RUNNING,
    pulse: true,
  },
  parameters: {
    docs: {
      description: {
        story: 'Running status with pulse animation to draw attention.',
      },
    },
  },
};

export const Interactive: Story = {
  render: () => {
    const [currentStatus, setCurrentStatus] = React.useState<AgentStatus>(AgentStatus.IDLE);

    const statusFlow = [
      AgentStatus.IDLE,
      AgentStatus.RUNNING,
      AgentStatus.PAUSED,
      AgentStatus.RUNNING,
      AgentStatus.COMPLETED,
      AgentStatus.IDLE,
    ];

    const nextStatus = () => {
      const currentIndex = statusFlow.indexOf(currentStatus);
      const nextIndex = (currentIndex + 1) % statusFlow.length;
      setCurrentStatus(statusFlow[nextIndex]);
    };

    return (
      <div className="space-y-6 text-center">
        <div className="space-y-4">
          <AgentStatusIndicator
            status={currentStatus}
            size="lg"
            animate={true}
            pulse={currentStatus === AgentStatus.RUNNING}
          />
          <p className="text-sm text-muted-foreground">
            Current status: <span className="font-medium capitalize">{currentStatus}</span>
          </p>
        </div>

        <button
          onClick={nextStatus}
          className="px-4 py-2 bg-primary text-primary-foreground rounded-md hover:bg-primary/90 transition-colors"
        >
          Next Status
        </button>

        <div className="text-xs text-muted-foreground">
          Click to cycle through: {statusFlow.join(' → ')}
        </div>
      </div>
    );
  },
  parameters: {
    docs: {
      description: {
        story: 'Interactive demo showing status transitions with animations. Click the button to cycle through different statuses.',
      },
    },
  },
};

export const AccessibilityDemo: Story = {
  render: () => (
    <div className="space-y-4">
      <div className="space-y-2">
        <h3 className="text-lg font-semibold">With ARIA Labels</h3>
        <div className="flex flex-wrap gap-3">
          <AgentStatusIndicator
            status={AgentStatus.RUNNING}
            aria-label="Customer service agent is currently processing a customer inquiry"
          />
          <AgentStatusIndicator
            status={AgentStatus.FAILED}
            aria-label="Data processing agent encountered an error and requires attention"
          />
          <AgentStatusIndicator
            status={AgentStatus.COMPLETED}
            aria-label="Workflow agent has successfully completed all assigned tasks"
          />
        </div>
      </div>

      <div className="text-sm text-muted-foreground">
        <p>These indicators include descriptive ARIA labels for screen readers.</p>
        <p>The component automatically provides live region updates when status changes.</p>
      </div>
    </div>
  ),
  parameters: {
    docs: {
      description: {
        story: 'Demonstrates accessibility features including ARIA labels and live regions for screen reader support.',
      },
    },
  },
};
