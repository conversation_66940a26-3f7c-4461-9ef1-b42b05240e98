import { NextRequest } from "next/server";

/**
 * CopilotKit API route for self-hosted integration with FastAPI backend.
 * Handles AI chat requests and forwards them to our backend AI infrastructure.
 */

/**
 * Handle POST requests for CopilotKit chat interactions
 */
export async function POST(req: NextRequest) {
  try {
    // For now, return a simple response until we configure the backend properly
    const body = await req.json();

    return new Response(
      JSON.stringify({
        message: "CopilotKit API endpoint is ready",
        received: body,
        timestamp: new Date().toISOString(),
      }),
      {
        status: 200,
        headers: { "Content-Type": "application/json" },
      }
    );
  } catch (error) {
    console.error("CopilotKit API error:", error);
    return new Response(
      JSON.stringify({
        error: "Internal server error",
        message: "Failed to process CopilotKit request",
      }),
      {
        status: 500,
        headers: { "Content-Type": "application/json" },
      }
    );
  }
}

/**
 * Handle GET requests for health checks
 */
export async function GET() {
  return new Response(
    JSON.stringify({
      status: "healthy",
      service: "copilotkit-api",
      timestamp: new Date().toISOString(),
    }),
    {
      status: 200,
      headers: { "Content-Type": "application/json" },
    }
  );
}
