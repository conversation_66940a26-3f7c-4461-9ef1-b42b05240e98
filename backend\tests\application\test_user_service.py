"""
TDD Tests for User Application Service - RED PHASE

This module contains comprehensive tests for the User application service
following TDD methodology. These tests will initially fail and drive
the implementation of the application layer.
"""

import pytest
from datetime import datetime, UTC
from uuid import uuid4
from unittest.mock import AsyncMock, MagicMock

# These imports will fail initially - this is the RED phase
try:
    from src.application.services.user_service import UserService
    from src.application.dto.user_dto import (
        CreateUserDTO,
        UpdateUserDTO,
        UserResponseDTO,
        LoginDTO,
        ChangePasswordDTO,
    )
    from src.domain.entities.user import User, UserStatus
    from src.domain.value_objects.email import Email
    from src.domain.value_objects.username import Username
    from src.domain.value_objects.password import Password
    from src.domain.exceptions import (
        EntityNotFoundError,
        DuplicateEntityError,
        DomainValidationError,
        UnauthorizedOperationError,
    )
except ImportError:
    # Expected during RED phase
    pass


class TestUserService:
    """Test suite for User application service."""

    @pytest.fixture
    def mock_user_repository(self):
        """Create mock user repository."""
        return AsyncMock()

    @pytest.fixture
    def mock_password_service(self):
        """Create mock password service."""
        mock_service = MagicMock()
        mock_service.hash_password.return_value = "hashed_password"
        mock_service.verify_password.return_value = True
        return mock_service

    @pytest.fixture
    def mock_jwt_service(self):
        """Create mock JWT service."""
        mock_service = MagicMock()
        mock_service.create_access_token.return_value = "access_token"
        mock_service.create_refresh_token.return_value = "refresh_token"
        return mock_service

    @pytest.fixture
    def user_service(self, mock_user_repository, mock_password_service, mock_jwt_service):
        """Create user service with mocked dependencies."""
        return UserService(
            user_repository=mock_user_repository,
            password_service=mock_password_service,
            jwt_service=mock_jwt_service,
        )

    @pytest.fixture
    def sample_user(self):
        """Create sample user entity."""
        return User(
            id=str(uuid4()),
            email=Email("<EMAIL>"),
            username=Username("testuser"),
            full_name="Test User",
            hashed_password="hashed_password",
            is_active=True,
            is_verified=True,
        )

    @pytest.mark.unit
    @pytest.mark.asyncio
    async def test_create_user_success(self, user_service, mock_user_repository):
        """Test successful user creation."""
        # RED PHASE: This test will fail because UserService doesn't exist yet
        create_dto = CreateUserDTO(
            email="<EMAIL>",
            username="testuser",
            full_name="Test User",
            password="StrongP@ssw0rd!",
        )

        mock_user_repository.get_by_email.return_value = None
        mock_user_repository.get_by_username.return_value = None
        mock_user_repository.create.return_value = None

        result = await user_service.create_user(create_dto)

        assert isinstance(result, UserResponseDTO)
        assert result.email == create_dto.email
        assert result.username == create_dto.username
        assert result.full_name == create_dto.full_name
        assert result.is_active is True
        assert result.is_verified is False

        mock_user_repository.create.assert_called_once()

    @pytest.mark.unit
    @pytest.mark.asyncio
    async def test_create_user_duplicate_email(self, user_service, mock_user_repository, sample_user):
        """Test user creation fails with duplicate email."""
        # RED PHASE: This test will fail because validation doesn't exist yet
        create_dto = CreateUserDTO(
            email="<EMAIL>",
            username="newuser",
            full_name="New User",
            password="StrongP@ssw0rd!",
        )

        mock_user_repository.get_by_email.return_value = sample_user

        with pytest.raises(DuplicateEntityError, match="User with email"):
            await user_service.create_user(create_dto)

    @pytest.mark.unit
    @pytest.mark.asyncio
    async def test_create_user_duplicate_username(self, user_service, mock_user_repository, sample_user):
        """Test user creation fails with duplicate username."""
        # RED PHASE: This test will fail because validation doesn't exist yet
        create_dto = CreateUserDTO(
            email="<EMAIL>",
            username="testuser",
            full_name="New User",
            password="StrongP@ssw0rd!",
        )

        mock_user_repository.get_by_email.return_value = None
        mock_user_repository.get_by_username.return_value = sample_user

        with pytest.raises(DuplicateEntityError, match="User with username"):
            await user_service.create_user(create_dto)

    @pytest.mark.unit
    @pytest.mark.asyncio
    async def test_get_user_by_id_success(self, user_service, mock_user_repository, sample_user):
        """Test successful user retrieval by ID."""
        # RED PHASE: This test will fail because method doesn't exist yet
        user_id = sample_user.id
        mock_user_repository.get_by_id.return_value = sample_user

        result = await user_service.get_user_by_id(user_id)

        assert isinstance(result, UserResponseDTO)
        assert result.id == user_id
        assert result.email == sample_user.email.value
        assert result.username == sample_user.username.value

        mock_user_repository.get_by_id.assert_called_once_with(user_id)

    @pytest.mark.unit
    @pytest.mark.asyncio
    async def test_get_user_by_id_not_found(self, user_service, mock_user_repository):
        """Test user retrieval fails when user not found."""
        # RED PHASE: This test will fail because error handling doesn't exist yet
        user_id = str(uuid4())
        mock_user_repository.get_by_id.return_value = None

        with pytest.raises(EntityNotFoundError, match="User with id"):
            await user_service.get_user_by_id(user_id)

    @pytest.mark.unit
    @pytest.mark.asyncio
    async def test_update_user_success(self, user_service, mock_user_repository, sample_user):
        """Test successful user update."""
        # RED PHASE: This test will fail because method doesn't exist yet
        user_id = sample_user.id
        update_dto = UpdateUserDTO(full_name="Updated Name")

        mock_user_repository.get_by_id.return_value = sample_user
        mock_user_repository.update.return_value = None

        result = await user_service.update_user(user_id, update_dto)

        assert isinstance(result, UserResponseDTO)
        assert result.full_name == "Updated Name"

        mock_user_repository.update.assert_called_once()

    @pytest.mark.unit
    @pytest.mark.asyncio
    async def test_authenticate_user_success(self, user_service, mock_user_repository, sample_user):
        """Test successful user authentication."""
        # RED PHASE: This test will fail because method doesn't exist yet
        login_dto = LoginDTO(email="<EMAIL>", password="StrongP@ssw0rd!")

        mock_user_repository.get_by_email.return_value = sample_user

        result = await user_service.authenticate_user(login_dto)

        assert result.access_token == "access_token"
        assert result.refresh_token == "refresh_token"
        assert result.token_type == "bearer"
        assert isinstance(result.user, UserResponseDTO)

    @pytest.mark.unit
    @pytest.mark.asyncio
    async def test_authenticate_user_invalid_email(self, user_service, mock_user_repository):
        """Test authentication fails with invalid email."""
        # RED PHASE: This test will fail because validation doesn't exist yet
        login_dto = LoginDTO(email="<EMAIL>", password="password")

        mock_user_repository.get_by_email.return_value = None

        with pytest.raises(UnauthorizedOperationError, match="Invalid credentials"):
            await user_service.authenticate_user(login_dto)

    @pytest.mark.unit
    @pytest.mark.asyncio
    async def test_authenticate_user_invalid_password(
        self, user_service, mock_user_repository, mock_password_service, sample_user
    ):
        """Test authentication fails with invalid password."""
        # RED PHASE: This test will fail because validation doesn't exist yet
        login_dto = LoginDTO(email="<EMAIL>", password="wrongpassword")

        mock_user_repository.get_by_email.return_value = sample_user
        mock_password_service.verify_password.return_value = False

        with pytest.raises(UnauthorizedOperationError, match="Invalid credentials"):
            await user_service.authenticate_user(login_dto)

    @pytest.mark.unit
    @pytest.mark.asyncio
    async def test_change_password_success(self, user_service, mock_user_repository, sample_user):
        """Test successful password change."""
        # RED PHASE: This test will fail because method doesn't exist yet
        user_id = sample_user.id
        change_password_dto = ChangePasswordDTO(
            current_password="OldP@ssw0rd!",
            new_password="NewP@ssw0rd!",
        )

        mock_user_repository.get_by_id.return_value = sample_user
        mock_user_repository.update.return_value = None

        await user_service.change_password(user_id, change_password_dto)

        mock_user_repository.update.assert_called_once()

    @pytest.mark.unit
    @pytest.mark.asyncio
    async def test_change_password_invalid_current(
        self, user_service, mock_user_repository, mock_password_service, sample_user
    ):
        """Test password change fails with invalid current password."""
        # RED PHASE: This test will fail because validation doesn't exist yet
        user_id = sample_user.id
        change_password_dto = ChangePasswordDTO(
            current_password="WrongP@ssw0rd!",
            new_password="NewP@ssw0rd!",
        )

        mock_user_repository.get_by_id.return_value = sample_user
        mock_password_service.verify_password.return_value = False

        with pytest.raises(UnauthorizedOperationError, match="Invalid current password"):
            await user_service.change_password(user_id, change_password_dto)

    @pytest.mark.unit
    @pytest.mark.asyncio
    async def test_activate_user_success(self, user_service, mock_user_repository, sample_user):
        """Test successful user activation."""
        # RED PHASE: This test will fail because method doesn't exist yet
        user_id = sample_user.id
        sample_user._is_active = False  # Make user inactive

        mock_user_repository.get_by_id.return_value = sample_user
        mock_user_repository.update.return_value = None

        result = await user_service.activate_user(user_id)

        assert isinstance(result, UserResponseDTO)
        assert result.is_active is True

        mock_user_repository.update.assert_called_once()

    @pytest.mark.unit
    @pytest.mark.asyncio
    async def test_deactivate_user_success(self, user_service, mock_user_repository, sample_user):
        """Test successful user deactivation."""
        # RED PHASE: This test will fail because method doesn't exist yet
        user_id = sample_user.id

        mock_user_repository.get_by_id.return_value = sample_user
        mock_user_repository.update.return_value = None

        result = await user_service.deactivate_user(user_id)

        assert isinstance(result, UserResponseDTO)
        assert result.is_active is False

        mock_user_repository.update.assert_called_once()

    @pytest.mark.unit
    @pytest.mark.asyncio
    async def test_verify_email_success(self, user_service, mock_user_repository, sample_user):
        """Test successful email verification."""
        # RED PHASE: This test will fail because method doesn't exist yet
        user_id = sample_user.id
        sample_user._is_verified = False  # Make user unverified

        mock_user_repository.get_by_id.return_value = sample_user
        mock_user_repository.update.return_value = None

        result = await user_service.verify_email(user_id)

        assert isinstance(result, UserResponseDTO)
        assert result.is_verified is True

        mock_user_repository.update.assert_called_once()

    @pytest.mark.unit
    @pytest.mark.asyncio
    async def test_list_users_success(self, user_service, mock_user_repository, sample_user):
        """Test successful user listing with pagination."""
        # RED PHASE: This test will fail because method doesn't exist yet
        users = [sample_user]
        mock_user_repository.list.return_value = (users, 1)

        result = await user_service.list_users(page=1, size=10)

        assert len(result.items) == 1
        assert result.total == 1
        assert result.page == 1
        assert result.size == 10
        assert isinstance(result.items[0], UserResponseDTO)

        mock_user_repository.list.assert_called_once_with(offset=0, limit=10)
