'use client';

import React, { useState } from 'react';
import { WorkflowBuilder } from '@/features/workflow-builder/components/workflow-builder';
import { WorkflowNode, WorkflowEdge } from '@/features/workflow-builder/types';
import { Button } from '@/shared/ui/button';
import { Badge } from '@/shared/ui/badge';
import { useToast } from '@/shared/ui/use-toast';
import {
  ArrowLeft,
  Save,
  Share,
  Settings,
  Play,
  History,
  Users,
} from 'lucide-react';

const WorkflowBuilderPage: React.FC = () => {
  const [workflowName, setWorkflowName] = useState('Untitled Workflow');
  const [nodes, setNodes] = useState<WorkflowNode[]>([]);
  const [edges, setEdges] = useState<WorkflowEdge[]>([]);
  const [isModified, setIsModified] = useState(false);
  const { toast } = useToast();

  const handleSave = (savedNodes: WorkflowNode[], savedEdges: WorkflowEdge[]) => {
    // TODO: Implement actual save logic to backend
    console.log('Saving workflow:', { nodes: savedNodes, edges: savedEdges });
    setIsModified(false);
  };

  const handleExecute = (workflowId: string) => {
    // TODO: Implement actual execution logic
    console.log('Executing workflow:', workflowId);
  };

  const handleNodesChange = (updatedNodes: WorkflowNode[]) => {
    setNodes(updatedNodes);
    setIsModified(true);
  };

  const handleEdgesChange = (updatedEdges: WorkflowEdge[]) => {
    setEdges(updatedEdges);
    setIsModified(true);
  };

  const handleShare = () => {
    // TODO: Implement sharing logic
    toast({
      title: 'Share Workflow',
      description: 'Sharing functionality will be implemented soon.',
    });
  };

  const handleSettings = () => {
    // TODO: Implement workflow settings
    toast({
      title: 'Workflow Settings',
      description: 'Settings panel will be implemented soon.',
    });
  };

  const handleHistory = () => {
    // TODO: Implement version history
    toast({
      title: 'Version History',
      description: 'Version history will be implemented soon.',
    });
  };

  return (
    <div className="h-screen flex flex-col bg-background">
      {/* Header */}
      <header className="border-b border-border bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60">
        <div className="flex items-center justify-between px-6 py-3">
          <div className="flex items-center gap-4">
            <Button variant="ghost" size="sm">
              <ArrowLeft className="h-4 w-4 mr-2" />
              Back to Dashboard
            </Button>
            
            <div className="flex items-center gap-2">
              <h1 className="text-xl font-semibold">{workflowName}</h1>
              {isModified && (
                <Badge variant="secondary" className="text-xs">
                  Unsaved
                </Badge>
              )}
            </div>
          </div>

          <div className="flex items-center gap-2">
            <Button variant="ghost" size="sm" onClick={handleHistory}>
              <History className="h-4 w-4 mr-2" />
              History
            </Button>
            
            <Button variant="ghost" size="sm" onClick={handleShare}>
              <Share className="h-4 w-4 mr-2" />
              Share
            </Button>
            
            <Button variant="ghost" size="sm" onClick={handleSettings}>
              <Settings className="h-4 w-4 mr-2" />
              Settings
            </Button>
            
            <div className="w-px h-6 bg-border mx-2" />
            
            <Button variant="outline" size="sm">
              <Save className="h-4 w-4 mr-2" />
              Save
            </Button>
            
            <Button size="sm">
              <Play className="h-4 w-4 mr-2" />
              Execute
            </Button>
          </div>
        </div>
      </header>

      {/* Workflow Builder */}
      <main className="flex-1 overflow-hidden">
        <WorkflowBuilder
          workflowId="demo-workflow"
          onSave={handleSave}
          onExecute={handleExecute}
          onNodesChange={handleNodesChange}
          onEdgesChange={handleEdgesChange}
          className="h-full"
        />
      </main>

      {/* Status Bar */}
      <footer className="border-t border-border bg-muted/50 px-6 py-2">
        <div className="flex items-center justify-between text-sm text-muted-foreground">
          <div className="flex items-center gap-4">
            <span>Nodes: {nodes.length}</span>
            <span>Connections: {edges.length}</span>
            <span>Status: Draft</span>
          </div>
          
          <div className="flex items-center gap-4">
            <span>Auto-save: Enabled</span>
            <span>Last saved: Never</span>
          </div>
        </div>
      </footer>
    </div>
  );
};

export default WorkflowBuilderPage;
