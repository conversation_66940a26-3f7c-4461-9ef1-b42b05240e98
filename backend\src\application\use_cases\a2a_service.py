"""
A2A service use cases.

This module contains use cases for A2A (Application-to-Application)
operations including service discovery, message routing, and connection management.
"""

import asyncio
import random
import uuid
from datetime import datetime, timedelta, timezone
from typing import Dict, List, Optional

from src.domain.entities.a2a import (
    A2AConnection,
    A2AConnectionResponse,
    A2AMessage,
    A2AMessageResponse,
    A2AMessageSend,
    A2ARoute,
    A2ARouteCreate,
    A2ARouteResponse,
    A2AService,
    A2AServiceRegister,
    A2AServiceResponse,
    A2AServiceUpdate,
    MessageType,
    RoutingStrategy,
    ServiceStatus,
)
from src.infrastructure.logging.setup import LoggerMixin


class A2AService(LoggerMixin):
    """
    A2A service containing business logic for application-to-application communication.

    Orchestrates service discovery, message routing, and connection management
    for inter-service communication.
    """

    def __init__(self) -> None:
        """Initialize A2A service."""
        # In a real implementation, these would be injected dependencies
        self._services: Dict[str, A2AService] = {}
        self._routes: Dict[str, A2ARoute] = {}
        self._connections: Dict[str, A2AConnection] = {}
        self._messages: Dict[str, A2AMessage] = {}
        self._message_queue: asyncio.Queue = asyncio.Queue()

        # Service instance counters for load balancing
        self._service_counters: Dict[str, int] = {}

        # Start background tasks
        self._processing_task = None
        self._start_background_tasks()

    def _start_background_tasks(self) -> None:
        """Start background tasks for message processing."""
        if self._processing_task is None or self._processing_task.done():
            self._processing_task = asyncio.create_task(self._process_message_queue())

    async def _process_message_queue(self) -> None:
        """Process messages from the queue."""
        while True:
            try:
                # Get message from queue with timeout
                message = await asyncio.wait_for(
                    self._message_queue.get(),
                    timeout=1.0
                )

                # Process the message
                await self._handle_message(message)

                # Mark task as done
                self._message_queue.task_done()

            except asyncio.TimeoutError:
                # No message in queue, continue
                continue
            except Exception as e:
                self.logger.error(f"Error processing message queue: {e}")
                await asyncio.sleep(1)

    async def register_service(
        self,
        service_data: A2AServiceRegister
    ) -> A2AServiceResponse:
        """
        Register a new service.

        Args:
            service_data: Service registration data

        Returns:
            A2AServiceResponse: Registered service information

        Raises:
            ValueError: If service already exists
        """
        if service_data.id in self._services:
            # Update existing service
            existing_service = self._services[service_data.id]
            existing_service.name = service_data.name
            existing_service.version = service_data.version
            existing_service.description = service_data.description
            existing_service.host = service_data.host
            existing_service.port = service_data.port
            existing_service.endpoints = service_data.endpoints
            existing_service.events = service_data.events
            existing_service.health_check_url = service_data.health_check_url
            existing_service.metadata = service_data.metadata
            existing_service.update_heartbeat()
            existing_service.status = ServiceStatus.ONLINE

            service = existing_service
        else:
            # Create new service
            service = A2AService(
                id=service_data.id,
                name=service_data.name,
                version=service_data.version,
                description=service_data.description,
                host=service_data.host,
                port=service_data.port,
                endpoints=service_data.endpoints,
                events=service_data.events,
                health_check_url=service_data.health_check_url,
                metadata=service_data.metadata,
            )

            self._services[service.id] = service

        self.logger.info(f"Registered A2A service: {service.id}")

        return A2AServiceResponse(
            **service.dict(),
            is_healthy=service.is_healthy()
        )

    async def unregister_service(self, service_id: str) -> bool:
        """
        Unregister a service.

        Args:
            service_id: Service ID to unregister

        Returns:
            bool: True if unregistered, False if not found
        """
        if service_id not in self._services:
            return False

        # Mark service as offline
        service = self._services[service_id]
        service.status = ServiceStatus.OFFLINE

        # Clean up connections
        connections_to_remove = [
            conn_id for conn_id, conn in self._connections.items()
            if conn.source_service == service_id or conn.target_service == service_id
        ]

        for conn_id in connections_to_remove:
            del self._connections[conn_id]

        self.logger.info(f"Unregistered A2A service: {service_id}")
        return True

    async def update_service(
        self,
        service_id: str,
        update_data: A2AServiceUpdate
    ) -> Optional[A2AServiceResponse]:
        """
        Update service information.

        Args:
            service_id: Service ID
            update_data: Update data

        Returns:
            A2AServiceResponse: Updated service information or None if not found
        """
        service = self._services.get(service_id)
        if not service:
            return None

        # Update fields
        if update_data.name:
            service.name = update_data.name
        if update_data.version:
            service.version = update_data.version
        if update_data.description is not None:
            service.description = update_data.description
        if update_data.endpoints is not None:
            service.endpoints = update_data.endpoints
        if update_data.events is not None:
            service.events = update_data.events
        if update_data.status is not None:
            service.status = update_data.status
        if update_data.metadata is not None:
            service.metadata.update(update_data.metadata)

        service.update_heartbeat()

        self.logger.info(f"Updated A2A service: {service_id}")

        return A2AServiceResponse(
            **service.dict(),
            is_healthy=service.is_healthy()
        )

    async def get_service(self, service_id: str) -> Optional[A2AServiceResponse]:
        """
        Get service by ID.

        Args:
            service_id: Service ID

        Returns:
            A2AServiceResponse: Service information or None if not found
        """
        service = self._services.get(service_id)
        if service:
            return A2AServiceResponse(
                **service.dict(),
                is_healthy=service.is_healthy()
            )
        return None

    async def list_services(
        self,
        status_filter: Optional[ServiceStatus] = None,
        healthy_only: bool = False
    ) -> List[A2AServiceResponse]:
        """
        List registered services.

        Args:
            status_filter: Filter by service status
            healthy_only: Return only healthy services

        Returns:
            List[A2AServiceResponse]: List of services
        """
        services = []

        for service in self._services.values():
            # Apply filters
            if status_filter and service.status != status_filter:
                continue

            if healthy_only and not service.is_healthy():
                continue

            services.append(A2AServiceResponse(
                **service.dict(),
                is_healthy=service.is_healthy()
            ))

        # Sort by registration time
        services.sort(key=lambda x: x.registered_at)

        self.logger.debug(f"Listed {len(services)} A2A services")
        return services

    async def send_message(
        self,
        source_service: str,
        message_data: A2AMessageSend
    ) -> A2AMessageResponse:
        """
        Send message to target service.

        Args:
            source_service: Source service ID
            message_data: Message data

        Returns:
            A2AMessageResponse: Message information

        Raises:
            ValueError: If message is invalid
        """
        # Validate source service
        if source_service not in self._services:
            raise ValueError(f"Source service not found: {source_service}")

        # Validate target service for direct messages
        if message_data.target_service and message_data.target_service not in self._services:
            raise ValueError(f"Target service not found: {message_data.target_service}")

        # Create message
        message = A2AMessage(
            type=message_data.type,
            source_service=source_service,
            target_service=message_data.target_service,
            method=message_data.method,
            event=message_data.event,
            payload=message_data.payload,
            headers=message_data.headers,
            priority=message_data.priority,
            timeout=message_data.timeout,
            correlation_id=message_data.correlation_id,
        )

        # Set expiration if timeout is specified
        if message.timeout:
            message.expires_at = datetime.now(timezone.utc) + timedelta(seconds=message.timeout)

        # Store message
        self._messages[message.id] = message

        # Add to processing queue
        await self._message_queue.put(message)

        self.logger.info(f"Queued A2A message: {message.id} from {source_service}")

        return A2AMessageResponse(
            **message.dict(),
            is_expired=message.is_expired(),
            can_retry=message.can_retry()
        )

    async def _handle_message(self, message: A2AMessage) -> None:
        """
        Handle message processing.

        Args:
            message: Message to process
        """
        try:
            # Check if message is expired
            if message.is_expired():
                self.logger.warning(f"Message expired: {message.id}")
                return

            # Route message based on type
            if message.type == MessageType.REQUEST:
                await self._handle_request_message(message)
            elif message.type == MessageType.EVENT:
                await self._handle_event_message(message)
            elif message.type == MessageType.COMMAND:
                await self._handle_command_message(message)
            else:
                self.logger.warning(f"Unknown message type: {message.type}")

        except Exception as e:
            self.logger.error(f"Error handling message {message.id}: {e}")

            # Retry if possible
            if message.can_retry():
                message.increment_retry()
                await asyncio.sleep(2 ** message.retry_count)  # Exponential backoff
                await self._message_queue.put(message)

    async def _handle_request_message(self, message: A2AMessage) -> None:
        """Handle request message."""
        if not message.target_service:
            self.logger.warning(f"Request message without target service: {message.id}")
            return

        # Find target service instance
        target_service = await self._find_service_instance(
            message.target_service,
            message.method
        )

        if not target_service:
            self.logger.warning(f"No available instance for service: {message.target_service}")
            return

        # Create connection if not exists
        connection = await self._get_or_create_connection(
            message.source_service,
            target_service.id
        )

        # Simulate message delivery (in real implementation, this would make HTTP call)
        connection.increment_message_count()

        self.logger.debug(f"Delivered request message {message.id} to {target_service.id}")

    async def _handle_event_message(self, message: A2AMessage) -> None:
        """Handle event message."""
        if not message.event:
            self.logger.warning(f"Event message without event name: {message.id}")
            return

        # Find all services that subscribe to this event
        subscribers = [
            service for service in self._services.values()
            if message.event in service.events and service.id != message.source_service
        ]

        # Deliver to all subscribers
        for subscriber in subscribers:
            connection = await self._get_or_create_connection(
                message.source_service,
                subscriber.id
            )
            connection.increment_message_count()

        self.logger.debug(f"Delivered event {message.event} to {len(subscribers)} subscribers")

    async def _handle_command_message(self, message: A2AMessage) -> None:
        """Handle command message."""
        # Similar to request but with different semantics
        await self._handle_request_message(message)

    async def _find_service_instance(
        self,
        service_id: str,
        method: Optional[str] = None
    ) -> Optional[A2AService]:
        """
        Find available service instance using load balancing.

        Args:
            service_id: Service ID
            method: Optional method name

        Returns:
            A2AService: Service instance or None if not available
        """
        # Find all healthy instances of the service
        instances = [
            service for service in self._services.values()
            if (service.id == service_id or service.id.startswith(f"{service_id}-"))
            and service.is_healthy()
            and service.status == ServiceStatus.ONLINE
        ]

        if not instances:
            return None

        # Apply load balancing strategy
        # For now, use round-robin
        if service_id not in self._service_counters:
            self._service_counters[service_id] = 0

        instance_index = self._service_counters[service_id] % len(instances)
        self._service_counters[service_id] += 1

        return instances[instance_index]

    async def _get_or_create_connection(
        self,
        source_service: str,
        target_service: str
    ) -> A2AConnection:
        """
        Get or create connection between services.

        Args:
            source_service: Source service ID
            target_service: Target service ID

        Returns:
            A2AConnection: Connection instance
        """
        # Look for existing connection
        for connection in self._connections.values():
            if (connection.source_service == source_service and
                connection.target_service == target_service):
                connection.update_activity()
                return connection

        # Create new connection
        connection = A2AConnection(
            source_service=source_service,
            target_service=target_service,
            connection_type="http",
        )

        self._connections[connection.id] = connection

        self.logger.debug(f"Created A2A connection: {connection.id}")
        return connection

    async def list_connections(
        self,
        source_service: Optional[str] = None,
        target_service: Optional[str] = None
    ) -> List[A2AConnectionResponse]:
        """
        List connections with optional filtering.

        Args:
            source_service: Filter by source service
            target_service: Filter by target service

        Returns:
            List[A2AConnectionResponse]: List of connections
        """
        connections = []

        for connection in self._connections.values():
            # Apply filters
            if source_service and connection.source_service != source_service:
                continue
            if target_service and connection.target_service != target_service:
                continue

            connections.append(A2AConnectionResponse(**connection.dict()))

        # Sort by establishment time
        connections.sort(key=lambda x: x.established_at, reverse=True)

        self.logger.debug(f"Listed {len(connections)} A2A connections")
        return connections

    async def get_message(self, message_id: str) -> Optional[A2AMessageResponse]:
        """
        Get message by ID.

        Args:
            message_id: Message ID

        Returns:
            A2AMessageResponse: Message information or None if not found
        """
        message = self._messages.get(message_id)
        if message:
            return A2AMessageResponse(
                **message.dict(),
                is_expired=message.is_expired(),
                can_retry=message.can_retry()
            )
        return None

    async def list_messages(
        self,
        source_service: Optional[str] = None,
        target_service: Optional[str] = None,
        message_type: Optional[MessageType] = None,
        limit: int = 100
    ) -> List[A2AMessageResponse]:
        """
        List messages with optional filtering.

        Args:
            source_service: Filter by source service
            target_service: Filter by target service
            message_type: Filter by message type
            limit: Maximum number of messages to return

        Returns:
            List[A2AMessageResponse]: List of messages
        """
        messages = []

        for message in self._messages.values():
            # Apply filters
            if source_service and message.source_service != source_service:
                continue
            if target_service and message.target_service != target_service:
                continue
            if message_type and message.type != message_type:
                continue

            messages.append(A2AMessageResponse(
                **message.dict(),
                is_expired=message.is_expired(),
                can_retry=message.can_retry()
            ))

        # Sort by creation time (newest first)
        messages.sort(key=lambda x: x.created_at, reverse=True)

        # Apply limit
        messages = messages[:limit]

        self.logger.debug(f"Listed {len(messages)} A2A messages")
        return messages

    async def create_route(self, route_data: A2ARouteCreate) -> A2ARouteResponse:
        """
        Create routing rule.

        Args:
            route_data: Route creation data

        Returns:
            A2ARouteResponse: Created route information
        """
        route = A2ARoute(
            source_pattern=route_data.source_pattern,
            target_pattern=route_data.target_pattern,
            method_pattern=route_data.method_pattern,
            strategy=route_data.strategy,
            weight=route_data.weight,
            metadata=route_data.metadata,
        )

        self._routes[route.id] = route

        self.logger.info(f"Created A2A route: {route.id}")

        return A2ARouteResponse(**route.dict())

    async def list_routes(self) -> List[A2ARouteResponse]:
        """
        List routing rules.

        Returns:
            List[A2ARouteResponse]: List of routes
        """
        routes = [
            A2ARouteResponse(**route.dict())
            for route in self._routes.values()
        ]

        # Sort by creation time
        routes.sort(key=lambda x: x.created_at)

        self.logger.debug(f"Listed {len(routes)} A2A routes")
        return routes

    async def delete_route(self, route_id: str) -> bool:
        """
        Delete routing rule.

        Args:
            route_id: Route ID

        Returns:
            bool: True if deleted, False if not found
        """
        if route_id not in self._routes:
            return False

        del self._routes[route_id]

        self.logger.info(f"Deleted A2A route: {route_id}")
        return True

    async def heartbeat(self, service_id: str) -> bool:
        """
        Update service heartbeat.

        Args:
            service_id: Service ID

        Returns:
            bool: True if updated, False if service not found
        """
        service = self._services.get(service_id)
        if not service:
            return False

        service.update_heartbeat()

        self.logger.debug(f"Updated heartbeat for service: {service_id}")
        return True

    async def get_service_health(self, service_id: str) -> Optional[dict]:
        """
        Get service health information.

        Args:
            service_id: Service ID

        Returns:
            dict: Health information or None if service not found
        """
        service = self._services.get(service_id)
        if not service:
            return None

        return {
            "service_id": service.id,
            "status": service.status,
            "is_healthy": service.is_healthy(),
            "last_heartbeat": service.last_heartbeat.isoformat(),
            "registered_at": service.registered_at.isoformat(),
        }

    async def get_statistics(self) -> dict:
        """
        Get A2A system statistics.

        Returns:
            dict: System statistics
        """
        # Count services by status
        service_stats = {}
        for status in ServiceStatus:
            service_stats[status.value] = len([
                s for s in self._services.values() if s.status == status
            ])

        # Count messages by type
        message_stats = {}
        for msg_type in MessageType:
            message_stats[msg_type.value] = len([
                m for m in self._messages.values() if m.type == msg_type
            ])

        # Connection statistics
        active_connections = len([
            c for c in self._connections.values() if c.status == "active"
        ])

        return {
            "services": {
                "total": len(self._services),
                "by_status": service_stats,
                "healthy": len([s for s in self._services.values() if s.is_healthy()])
            },
            "messages": {
                "total": len(self._messages),
                "by_type": message_stats,
                "queue_size": self._message_queue.qsize()
            },
            "connections": {
                "total": len(self._connections),
                "active": active_connections
            },
            "routes": {
                "total": len(self._routes),
                "enabled": len([r for r in self._routes.values() if r.enabled])
            },
            "timestamp": datetime.now(timezone.utc).isoformat()
        }
