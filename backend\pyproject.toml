[project]
name = "lonors-backend"
version = "1.0.0"
description = "Lonors Backend API - Python FastAPI Application"
authors = [{ name = "simyropandos", email = "<EMAIL>" }]
# readme = "README.md"  # Temporarily disabled for Docker build
license = { text = "MIT" }
requires-python = ">=3.11"
keywords = ["fastapi", "async", "api", "backend", "lonors"]
classifiers = [
    "Development Status :: 4 - Beta",
    "Intended Audience :: Developers",
    "License :: OSI Approved :: MIT License",
    "Operating System :: OS Independent",
    "Programming Language :: Python :: 3",
    "Programming Language :: Python :: 3.11",
    "Programming Language :: Python :: 3.12",
    "Framework :: FastAPI",
    "Framework :: AsyncIO",
    "Topic :: Internet :: WWW/HTTP :: HTTP Servers",
    "Topic :: Software Development :: Libraries :: Application Frameworks",
]

dependencies = [
    # Core FastAPI and ASGI
    "fastapi>=0.104.1",
    "uvicorn[standard]>=0.24.0",
    # Database and ORM
    "sqlalchemy[asyncio]>=2.0.23",
    "asyncpg>=0.29.0",
    "alembic>=1.13.0",
    # Redis and Caching
    "redis[hiredis]>=5.0.1",
    "aioredis>=2.0.1",
    # Authentication and Security
    "python-jose[cryptography]>=3.3.0",
    "passlib[bcrypt]>=1.7.4",
    # Validation and Serialization
    "pydantic>=2.5.0",
    # HTTP and Networking
    "httpx>=0.25.2",
    "aiofiles>=23.2.1",
    # Utilities
    "python-dotenv>=1.0.0",
    "structlog>=23.2.0",
    "rich>=13.7.0",
    "python-json-logger>=2.0.7",
    # Protocol Support
    "websockets>=12.0",
    "sse-starlette>=1.8.2",
    # Monitoring and Observability
    "prometheus-client>=0.19.0",
    "opentelemetry-api>=1.21.0",
    "opentelemetry-sdk>=1.21.0",
    "opentelemetry-instrumentation-fastapi>=0.42b0",
    # AI and Machine Learning
    "ollama>=0.3.0",
    "transformers>=4.35.0",
    "torch>=2.1.0",
    "huggingface-hub>=0.19.0",
    "tokenizers>=0.15.0",
]

[project.optional-dependencies]
dev = [
    # Testing
    "pytest>=7.4.3",
    "pytest-asyncio>=0.21.1",
    "pytest-cov>=4.1.0",
    "pytest-mock>=3.12.0",
    "httpx>=0.25.2",
    "factory-boy>=3.3.0",

    # Code Quality
    "ruff>=0.1.6",
    "black>=23.11.0",
    "mypy>=1.7.1",
    "pre-commit>=3.6.0",

    # Development Tools
    "watchfiles>=0.21.0",
    "ipython>=8.17.2",
    "ipdb>=0.13.13",

    # Documentation
    "mkdocs>=1.5.3",
    "mkdocs-material>=9.4.8",
    "mkdocstrings[python]>=0.24.0",
]

production = ["gunicorn>=21.2.0", "sentry-sdk[fastapi]>=1.38.0"]

[project.urls]
Homepage = "https://github.com/simyropandos/lonors"
Documentation = "https://github.com/simyropandos/lonors/docs"
Repository = "https://github.com/simyropandos/lonors"
Issues = "https://github.com/simyropandos/lonors/issues"

[project.scripts]
lonors-backend = "src.main:main"

[build-system]
requires = ["hatchling"]
build-backend = "hatchling.build"

[tool.hatch.build.targets.wheel]
packages = ["src"]

[tool.pytest.ini_options]
minversion = "7.0"
addopts = [
    "-ra",
    "--strict-markers",
    "--strict-config",
    "--cov=src",
    "--cov-report=term-missing",
    "--cov-report=html",
    "--cov-report=xml",
    "--cov-fail-under=90",
]
testpaths = ["tests"]
python_files = ["test_*.py", "*_test.py"]
python_classes = ["Test*"]
python_functions = ["test_*"]
asyncio_mode = "auto"
markers = [
    "slow: marks tests as slow (deselect with '-m \"not slow\"')",
    "integration: marks tests as integration tests",
    "unit: marks tests as unit tests",
    "e2e: marks tests as end-to-end tests",
]

[tool.coverage.run]
source = ["src"]
omit = [
    "*/tests/*",
    "*/migrations/*",
    "*/__pycache__/*",
    "*/venv/*",
    "*/.venv/*",
]

[tool.coverage.report]
exclude_lines = [
    "pragma: no cover",
    "def __repr__",
    "if self.debug:",
    "if settings.DEBUG",
    "raise AssertionError",
    "raise NotImplementedError",
    "if 0:",
    "if __name__ == .__main__.:",
    "class .*\\bProtocol\\):",
    "@(abc\\.)?abstractmethod",
]

[tool.black]
line-length = 88
target-version = ["py311"]
include = '\.pyi?$'
extend-exclude = '''
/(
  # directories
  \.eggs
  | \.git
  | \.hg
  | \.mypy_cache
  | \.tox
  | \.venv
  | build
  | dist
  | migrations
)/
'''

[tool.ruff]
target-version = "py311"
line-length = 88
select = [
    "E",      # pycodestyle errors
    "W",      # pycodestyle warnings
    "F",      # pyflakes
    "I",      # isort
    "B",      # flake8-bugbear
    "C4",     # flake8-comprehensions
    "UP",     # pyupgrade
    "ARG001", # unused-function-args
    "SIM",    # flake8-simplify
    "TCH",    # flake8-type-checking
    "TID",    # flake8-tidy-imports
    "Q",      # flake8-quotes
    "FLY",    # flynt
    "PERF",   # perflint
    "RUF",    # ruff-specific rules
]
ignore = [
    "E501", # line too long, handled by black
    "B008", # do not perform function calls in argument defaults
    "C901", # too complex
    "W191", # indentation contains tabs
]
unfixable = [
    "F841", # Remove unused variables
]
exclude = [
    ".bzr",
    ".direnv",
    ".eggs",
    ".git",
    ".git-rewrite",
    ".hg",
    ".mypy_cache",
    ".nox",
    ".pants.d",
    ".pytype",
    ".ruff_cache",
    ".svn",
    ".tox",
    ".venv",
    "__pypackages__",
    "_build",
    "buck-out",
    "build",
    "dist",
    "node_modules",
    "venv",
    "migrations",
]

[tool.ruff.mccabe]
max-complexity = 10

[tool.ruff.isort]
known-first-party = ["src"]

[tool.mypy]
python_version = "3.11"
check_untyped_defs = true
disallow_any_generics = true
disallow_incomplete_defs = true
disallow_untyped_defs = true
no_implicit_optional = true
warn_redundant_casts = true
warn_unused_ignores = true
warn_return_any = true
strict_equality = true
strict_concatenate = true
exclude = ["migrations/", "tests/", "venv/", ".venv/"]

[[tool.mypy.overrides]]
module = ["redis.*", "passlib.*", "jose.*", "factory.*"]
ignore_missing_imports = true
