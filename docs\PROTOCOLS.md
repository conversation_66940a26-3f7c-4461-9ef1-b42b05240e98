# Protocol Documentation

This document describes the protocol implementations in the Lonors platform.

## Overview

Lonors implements three main protocols for different types of interactions:

1. **Model Context Protocol (MCP)** - AI model integration and context management
2. **AG-UI Protocol** - Dynamic UI component communication
3. **A2A Protocol** - Application-to-application communication

## Model Context Protocol (MCP)

### Purpose

The Model Context Protocol enables seamless integration with various AI models while maintaining context across conversations and sessions.

### Key Features

- **Multi-model Support**: Integration with OpenAI, Anthropic, and other AI providers
- **Context Management**: Persistent conversation contexts with automatic cleanup
- **Token Management**: Efficient token usage tracking and optimization
- **Streaming Support**: Real-time response streaming for better UX

### Architecture

```
Client → MCP API → MCP Service → Model Provider
                ↓
            Context Store
```

### Context Lifecycle

1. **Creation**: User creates a new context with specific model and parameters
2. **Usage**: Messages are added to context, maintaining conversation history
3. **Management**: Context length is monitored and managed automatically
4. **Expiration**: Contexts expire based on configured TTL or manual deletion

### Model Types

- **Text Generation**: General purpose text generation models
- **Code Generation**: Specialized code generation and completion
- **Chat Completion**: Conversational AI models
- **Embedding**: Text embedding models for semantic search
- **Image Generation**: Image creation models

### Context Types

- **Conversation**: General chat conversations
- **Code Session**: Code development and debugging sessions
- **Document**: Document analysis and processing
- **Task**: Specific task-oriented interactions

### Usage Example

```python
# Create context
context = await mcp_service.create_context(
    user_id=user_id,
    context_data=MCPContextCreate(
        type=ContextType.CONVERSATION,
        title="AI Assistant Chat",
        model_id="gpt-4",
        max_length=4096
    )
)

# Generate response
response = await mcp_service.process_request(
    user_id=user_id,
    request=MCPRequest(
        context_id=context.id,
        model_id="gpt-4",
        messages=[
            {"role": "user", "content": "Hello!"}
        ]
    )
)
```

## AG-UI Protocol

### Purpose

The AG-UI (Augmented Generative User Interface) Protocol enables dynamic, AI-driven user interface generation and real-time synchronization between client and server.

### Key Features

- **Dynamic Components**: Server-driven UI component creation and updates
- **Real-time Sync**: WebSocket-based state synchronization
- **Event Handling**: Bidirectional event communication
- **Layout Management**: Flexible layout systems with nested components
- **State Persistence**: Session state management across connections

### Architecture

```
Client UI ←→ WebSocket ←→ AG-UI Service ←→ Layout Store
    ↓                           ↓
Event Bus                   Component Registry
```

### Component System

#### Component Types

- **Input Components**: text, number, select, checkbox, radio
- **Display Components**: card, modal, table, chart
- **Layout Components**: form, grid, flex containers
- **Interactive Components**: button, toggle, slider
- **Custom Components**: User-defined components

#### Component Lifecycle

1. **Creation**: Components are created server-side with initial properties
2. **Rendering**: Client receives component definitions and renders them
3. **Interaction**: User interactions trigger events sent to server
4. **Updates**: Server processes events and sends component updates
5. **Cleanup**: Components are removed when no longer needed

### State Management

#### Session State

Each AG-UI session maintains:
- **Component State**: Current values and properties of all components
- **Layout State**: Current layout configuration and hierarchy
- **User State**: User-specific preferences and settings
- **Application State**: Global application state relevant to the session

#### Synchronization

State synchronization occurs through:
- **Initial Load**: Full state sent on connection establishment
- **Incremental Updates**: Only changed state sent for efficiency
- **Conflict Resolution**: Server state takes precedence in conflicts
- **Offline Handling**: Client state cached for offline scenarios

### Event System

#### Event Types

- **User Events**: click, input, change, focus, blur
- **System Events**: component lifecycle, layout changes
- **Custom Events**: Application-specific events

#### Event Flow

```
User Action → Client Event → WebSocket → Server Handler → State Update → Client Update
```

### Usage Example

```python
# Create layout
layout = await agui_service.create_layout(
    user_id=user_id,
    layout_data=AGUILayoutCreate(
        name="User Profile Form",
        components=[
            AGUIComponentCreate(
                type=ComponentType.INPUT,
                name="email",
                label="Email Address",
                properties={"type": "email", "required": True}
            ),
            AGUIComponentCreate(
                type=ComponentType.BUTTON,
                name="submit",
                label="Save Profile",
                events=[EventType.CLICK]
            )
        ]
    )
)

# Handle events
async def handle_event(session_id: str, event: AGUIEvent):
    if event.component_id == "submit" and event.event_type == EventType.CLICK:
        # Process form submission
        await process_profile_update(session_id, event.data)
```

## A2A Protocol (Application-to-Application)

### Purpose

The A2A Protocol facilitates secure, efficient communication between different applications and services in the Lonors ecosystem.

### Key Features

- **Service Discovery**: Automatic discovery of available services
- **Message Queuing**: Reliable message delivery with retry mechanisms
- **Authentication**: Secure service-to-service authentication
- **Load Balancing**: Automatic load distribution across service instances
- **Circuit Breaking**: Fault tolerance and graceful degradation

### Architecture

```
Service A → A2A Gateway → Message Queue → Service B
    ↓           ↓             ↓           ↓
Auth Layer  Routing      Persistence  Processing
```

### Message Types

#### Request/Response

Synchronous communication for immediate responses:

```json
{
  "id": "req_123",
  "type": "request",
  "service": "user-service",
  "method": "get_user",
  "payload": {"user_id": "123"},
  "timeout": 5000
}
```

#### Events

Asynchronous event notifications:

```json
{
  "id": "evt_456",
  "type": "event",
  "source": "user-service",
  "event": "user.created",
  "payload": {"user_id": "123", "email": "<EMAIL>"},
  "timestamp": "2024-01-15T12:00:00Z"
}
```

#### Commands

Asynchronous command execution:

```json
{
  "id": "cmd_789",
  "type": "command",
  "target": "email-service",
  "command": "send_welcome_email",
  "payload": {"user_id": "123", "template": "welcome"},
  "priority": "normal"
}
```

### Service Registration

Services register themselves with the A2A gateway:

```python
await a2a_client.register_service(
    service_id="user-service",
    endpoints=[
        {"method": "get_user", "handler": get_user_handler},
        {"method": "create_user", "handler": create_user_handler}
    ],
    events=["user.created", "user.updated", "user.deleted"]
)
```

### Message Routing

Messages are routed based on:
- **Service ID**: Target service identifier
- **Method/Event**: Specific operation or event type
- **Load Balancing**: Round-robin, least connections, or custom strategies
- **Circuit Breaker**: Health-based routing decisions

### Error Handling

#### Retry Policies

- **Exponential Backoff**: Increasing delays between retries
- **Circuit Breaker**: Stop retrying after consecutive failures
- **Dead Letter Queue**: Store failed messages for manual processing

#### Error Responses

```json
{
  "id": "req_123",
  "type": "error",
  "error": {
    "code": "SERVICE_UNAVAILABLE",
    "message": "Target service is temporarily unavailable",
    "retry_after": 30
  }
}
```

## Security Considerations

### Authentication

- **JWT Tokens**: Service-to-service authentication using JWT
- **API Keys**: Long-lived credentials for trusted services
- **mTLS**: Mutual TLS for high-security communications

### Authorization

- **RBAC**: Role-based access control for service permissions
- **Scopes**: Fine-grained permission scopes for operations
- **Rate Limiting**: Per-service rate limiting to prevent abuse

### Data Protection

- **Encryption**: All inter-service communication encrypted
- **Data Masking**: Sensitive data masked in logs and traces
- **Audit Logging**: Complete audit trail of all communications

## Monitoring and Observability

### Metrics

- **Request/Response Times**: Latency monitoring for all protocols
- **Error Rates**: Error tracking and alerting
- **Throughput**: Message volume and processing rates
- **Resource Usage**: CPU, memory, and network utilization

### Tracing

- **Distributed Tracing**: End-to-end request tracing across services
- **Correlation IDs**: Request correlation across protocol boundaries
- **Span Attributes**: Rich metadata for debugging and analysis

### Logging

- **Structured Logging**: JSON-formatted logs with consistent schema
- **Log Aggregation**: Centralized log collection and analysis
- **Log Levels**: Configurable log levels per service and protocol

## Performance Optimization

### Caching

- **Response Caching**: Cache frequently requested data
- **Connection Pooling**: Reuse connections for efficiency
- **Batch Processing**: Group operations for better throughput

### Compression

- **Message Compression**: Compress large payloads
- **Protocol Optimization**: Use binary protocols where appropriate
- **Streaming**: Stream large responses to reduce memory usage

### Scaling

- **Horizontal Scaling**: Add more service instances
- **Load Balancing**: Distribute load across instances
- **Auto-scaling**: Automatic scaling based on metrics

## Best Practices

### Protocol Design

1. **Idempotency**: Design operations to be idempotent
2. **Versioning**: Version protocols for backward compatibility
3. **Documentation**: Maintain comprehensive protocol documentation
4. **Testing**: Implement thorough protocol testing

### Error Handling

1. **Graceful Degradation**: Handle failures gracefully
2. **Timeout Management**: Set appropriate timeouts
3. **Retry Logic**: Implement intelligent retry mechanisms
4. **Circuit Breakers**: Use circuit breakers for fault tolerance

### Security

1. **Least Privilege**: Grant minimum necessary permissions
2. **Input Validation**: Validate all inputs thoroughly
3. **Audit Trails**: Maintain complete audit logs
4. **Regular Updates**: Keep security measures up to date

