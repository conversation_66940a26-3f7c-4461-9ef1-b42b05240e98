"""
Email value object for the Lonors application.

This module contains the Email value object that encapsulates email validation
and behavior following Domain-Driven Design principles.
"""

import re
from typing import Any

from ..exceptions import DomainValidationError


class Email:
    """Email value object with validation and behavior."""

    # RFC 5322 compliant email regex (simplified)
    EMAIL_PATTERN = re.compile(
        r"^[a-zA-Z0-9.!#$%&'*+/=?^_`{|}~-]+@[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?(?:\.[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?)*$"
    )

    def __init__(self, value: str) -> None:
        """
        Initialize email value object.

        Args:
            value: Email string to validate and store

        Raises:
            DomainValidationError: If email format is invalid
        """
        if not value or not value.strip():
            raise DomainValidationError("Invalid email format")

        # Normalize email to lowercase
        normalized_value = value.strip().lower()

        if not self._is_valid_email(normalized_value):
            raise DomainValidationError("Invalid email format")

        self._value = normalized_value

    @property
    def value(self) -> str:
        """Get the email value."""
        return self._value

    @property
    def domain(self) -> str:
        """Get the domain part of the email."""
        return self._value.split("@")[1]

    @property
    def local_part(self) -> str:
        """Get the local part of the email."""
        return self._value.split("@")[0]

    def _is_valid_email(self, email: str) -> bool:
        """
        Validate email format.

        Args:
            email: Email string to validate

        Returns:
            True if email is valid, False otherwise
        """
        if not email or len(email) > 254:  # RFC 5321 limit
            return False

        if email.count("@") != 1:
            return False

        local, domain = email.split("@")

        # Validate local part
        if not local or len(local) > 64:  # RFC 5321 limit
            return False

        # Check for invalid local part patterns
        if ".." in local or local.startswith(".") or local.endswith("."):
            return False

        # Validate domain part
        if not domain or len(domain) > 253:
            return False

        # Check for invalid domain patterns
        if (
            domain.startswith(".")
            or domain.endswith(".")
            or ".." in domain
            or domain.startswith("-")
            or domain.endswith("-")
        ):
            return False

        # Domain must contain at least one dot
        if "." not in domain:
            return False

        # Check against regex pattern
        return bool(self.EMAIL_PATTERN.match(email))

    def __eq__(self, other: Any) -> bool:
        """Check equality with another Email object."""
        if not isinstance(other, Email):
            return False
        return self._value == other._value

    def __hash__(self) -> int:
        """Return hash of the email value."""
        return hash(self._value)

    def __str__(self) -> str:
        """Return string representation of the email."""
        return self._value

    def __repr__(self) -> str:
        """Return detailed string representation of the email."""
        return f"Email('{self._value}')"
