"""
Unit tests for SQLAlchemyNoteRepository.

This module contains comprehensive tests for the note repository
implementation including CRUD operations and filtering.
"""

import uuid
from datetime import datetime
from unittest.mock import AsyncMock, MagicMock

import pytest
from sqlalchemy.ext.asyncio import AsyncSession

from src.domain.entities.note import Note, NoteContent, NoteFormat
from src.infrastructure.database.models.note import NoteModel
from src.infrastructure.database.repositories.note_repository_impl import SQLAlchemyNoteRepository


class TestSQLAlchemyNoteRepository:
    """Test cases for SQLAlchemyNoteRepository."""

    @pytest.fixture
    def mock_session(self):
        """Create a mock async session."""
        return AsyncMock(spec=AsyncSession)

    @pytest.fixture
    def repository(self, mock_session):
        """Create a repository instance with mock session."""
        return SQLAlchemyNoteRepository(mock_session)

    @pytest.fixture
    def sample_note(self):
        """Create a sample note entity."""
        return Note(
            id=uuid.uuid4(),
            title="Test Note",
            content=NoteContent(
                content="This is test content",
                format=NoteFormat.MARKDOWN,
                version=1
            ),
            folder_id=uuid.uuid4(),
            tags=["test", "example"],
            is_archived=False,
            is_starred=True,
            created_at=datetime.now(),
            updated_at=datetime.now(),
            created_by=uuid.uuid4(),
            last_edited_by=uuid.uuid4(),
        )

    @pytest.fixture
    def sample_note_model(self, sample_note):
        """Create a sample note model."""
        return NoteModel.from_domain_entity(sample_note)

    async def test_get_by_id_found(self, repository, mock_session, sample_note_model):
        """Test getting a note by ID when it exists."""
        # Setup
        mock_result = MagicMock()
        mock_result.scalar_one_or_none.return_value = sample_note_model
        mock_session.execute.return_value = mock_result

        # Execute
        result = await repository.get_by_id(sample_note_model.id)

        # Verify
        assert result is not None
        assert result.id == sample_note_model.id
        assert result.title == sample_note_model.title
        mock_session.execute.assert_called_once()

    async def test_get_by_id_not_found(self, repository, mock_session):
        """Test getting a note by ID when it doesn't exist."""
        # Setup
        mock_result = MagicMock()
        mock_result.scalar_one_or_none.return_value = None
        mock_session.execute.return_value = mock_result

        # Execute
        result = await repository.get_by_id(uuid.uuid4())

        # Verify
        assert result is None
        mock_session.execute.assert_called_once()

    async def test_get_all_by_user_basic(self, repository, mock_session, sample_note_model):
        """Test getting all notes for a user with basic filtering."""
        # Setup
        mock_result = MagicMock()
        mock_result.scalars.return_value.all.return_value = [sample_note_model]
        mock_session.execute.return_value = mock_result

        # Execute
        result = await repository.get_all_by_user(sample_note_model.created_by)

        # Verify
        assert len(result) == 1
        assert result[0].id == sample_note_model.id
        mock_session.execute.assert_called_once()

    async def test_get_all_by_user_with_filters(self, repository, mock_session, sample_note_model):
        """Test getting notes with various filters."""
        # Setup
        mock_result = MagicMock()
        mock_result.scalars.return_value.all.return_value = [sample_note_model]
        mock_session.execute.return_value = mock_result

        # Execute
        result = await repository.get_all_by_user(
            user_id=sample_note_model.created_by,
            folder_id=sample_note_model.folder_id,
            include_archived=True,
            only_starred=True,
            tags=["test"],
            search_query="test"
        )

        # Verify
        assert len(result) == 1
        mock_session.execute.assert_called_once()

    async def test_create_note(self, repository, mock_session, sample_note):
        """Test creating a new note."""
        # Setup
        mock_session.flush = AsyncMock()
        mock_session.refresh = AsyncMock()

        # Execute
        result = await repository.create(sample_note)

        # Verify
        assert result.id == sample_note.id
        assert result.title == sample_note.title
        mock_session.add.assert_called_once()
        mock_session.flush.assert_called_once()
        mock_session.refresh.assert_called_once()

    async def test_update_note_found(self, repository, mock_session, sample_note, sample_note_model):
        """Test updating an existing note."""
        # Setup
        mock_result = MagicMock()
        mock_result.scalar_one_or_none.return_value = sample_note_model
        mock_session.execute.return_value = mock_result
        mock_session.flush = AsyncMock()
        mock_session.refresh = AsyncMock()

        # Modify the note
        updated_note = sample_note.model_copy()
        updated_note.title = "Updated Title"

        # Execute
        result = await repository.update(updated_note)

        # Verify
        assert result.title == "Updated Title"
        mock_session.execute.assert_called_once()
        mock_session.flush.assert_called_once()
        mock_session.refresh.assert_called_once()

    async def test_update_note_not_found(self, repository, mock_session, sample_note):
        """Test updating a note that doesn't exist."""
        # Setup
        mock_result = MagicMock()
        mock_result.scalar_one_or_none.return_value = None
        mock_session.execute.return_value = mock_result

        # Execute & Verify
        with pytest.raises(ValueError, match="Note with ID .* not found"):
            await repository.update(sample_note)

    async def test_delete_note_found(self, repository, mock_session, sample_note_model):
        """Test deleting an existing note."""
        # Setup
        mock_result = MagicMock()
        mock_result.scalar_one_or_none.return_value = sample_note_model
        mock_session.execute.return_value = mock_result
        mock_session.delete = AsyncMock()
        mock_session.flush = AsyncMock()

        # Execute
        result = await repository.delete(sample_note_model.id)

        # Verify
        assert result is True
        mock_session.execute.assert_called_once()
        mock_session.delete.assert_called_once_with(sample_note_model)
        mock_session.flush.assert_called_once()

    async def test_delete_note_not_found(self, repository, mock_session):
        """Test deleting a note that doesn't exist."""
        # Setup
        mock_result = MagicMock()
        mock_result.scalar_one_or_none.return_value = None
        mock_session.execute.return_value = mock_result

        # Execute
        result = await repository.delete(uuid.uuid4())

        # Verify
        assert result is False
        mock_session.execute.assert_called_once()

    async def test_get_all_by_user_empty_result(self, repository, mock_session):
        """Test getting notes when no notes exist."""
        # Setup
        mock_result = MagicMock()
        mock_result.scalars.return_value.all.return_value = []
        mock_session.execute.return_value = mock_result

        # Execute
        result = await repository.get_all_by_user(uuid.uuid4())

        # Verify
        assert len(result) == 0
        mock_session.execute.assert_called_once()

    async def test_get_all_by_user_archived_filter(self, repository, mock_session):
        """Test filtering archived notes."""
        # Setup
        mock_result = MagicMock()
        mock_result.scalars.return_value.all.return_value = []
        mock_session.execute.return_value = mock_result

        # Execute with include_archived=False (default)
        await repository.get_all_by_user(uuid.uuid4(), include_archived=False)

        # Verify that the query was executed
        mock_session.execute.assert_called_once()

    async def test_get_all_by_user_starred_filter(self, repository, mock_session):
        """Test filtering starred notes."""
        # Setup
        mock_result = MagicMock()
        mock_result.scalars.return_value.all.return_value = []
        mock_session.execute.return_value = mock_result

        # Execute with only_starred=True
        await repository.get_all_by_user(uuid.uuid4(), only_starred=True)

        # Verify that the query was executed
        mock_session.execute.assert_called_once()

    async def test_get_all_by_user_folder_filter(self, repository, mock_session):
        """Test filtering by folder."""
        # Setup
        mock_result = MagicMock()
        mock_result.scalars.return_value.all.return_value = []
        mock_session.execute.return_value = mock_result

        # Execute with folder_id filter
        folder_id = uuid.uuid4()
        await repository.get_all_by_user(uuid.uuid4(), folder_id=folder_id)

        # Verify that the query was executed
        mock_session.execute.assert_called_once()

    async def test_get_all_by_user_search_filter(self, repository, mock_session):
        """Test search query filtering."""
        # Setup
        mock_result = MagicMock()
        mock_result.scalars.return_value.all.return_value = []
        mock_session.execute.return_value = mock_result

        # Execute with search query
        await repository.get_all_by_user(uuid.uuid4(), search_query="test search")

        # Verify that the query was executed
        mock_session.execute.assert_called_once()
