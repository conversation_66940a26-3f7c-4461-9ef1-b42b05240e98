"""
Database dependency injection.

This module provides database session dependencies for FastAPI endpoints.
"""

from collections.abc import AsyncGenerator
from typing import Annotated

from fastapi import Depends
from sqlalchemy.ext.asyncio import AsyncSession

from src.infrastructure.container import get_container
from src.infrastructure.database.connection import DatabaseManager


async def get_db_session() -> AsyncGenerator[AsyncSession, None]:
    """
    Get database session dependency.
    
    Yields:
        AsyncSession: Database session
        
    Raises:
        Exception: If database connection fails
    """
    container = get_container()
    db_manager = container.get(DatabaseManager)
    
    async with db_manager.get_session() as session:
        try:
            yield session
        except Exception:
            await session.rollback()
            raise
        finally:
            await session.close()


# Type alias for database session dependency
DatabaseSession = Annotated[AsyncSession, Depends(get_db_session)]
