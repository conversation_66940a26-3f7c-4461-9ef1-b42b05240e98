"""
Username value object for the Lonors application.

This module contains the Username value object that encapsulates username validation
and behavior following Domain-Driven Design principles.
"""

import re
from typing import Any

from ..exceptions import DomainValidationError


class Username:
    """Username value object with validation and behavior."""

    # Username validation pattern: alphanumeric, underscore, hyphen, 3-30 chars
    USERNAME_PATTERN = re.compile(r"^[a-zA-Z0-9_-]{3,30}$")

    MIN_LENGTH = 3
    MAX_LENGTH = 30

    def __init__(self, value: str) -> None:
        """
        Initialize username value object.

        Args:
            value: Username string to validate and store

        Raises:
            DomainValidationError: If username format is invalid
        """
        if not value or not value.strip():
            raise DomainValidationError("Invalid username")

        # Normalize username to lowercase
        normalized_value = value.strip().lower()

        if not self._is_valid_username(normalized_value):
            raise DomainValidationError("Invalid username")

        self._value = normalized_value

    @property
    def value(self) -> str:
        """Get the username value."""
        return self._value

    @property
    def length(self) -> int:
        """Get the length of the username."""
        return len(self._value)

    def is_valid_length(self) -> bool:
        """Check if username length is valid."""
        return self.MIN_LENGTH <= len(self._value) <= self.MAX_LENGTH

    def _is_valid_username(self, username: str) -> bool:
        """
        Validate username format.

        Args:
            username: Username string to validate

        Returns:
            True if username is valid, False otherwise
        """
        if not username:
            return False

        # Check length
        if not self.MIN_LENGTH <= len(username) <= self.MAX_LENGTH:
            return False

        # Check for invalid patterns
        if (
            username.startswith(".")
            or username.endswith(".")
            or ".." in username
            or "--" in username
            or "__" in username
            or " " in username
            or "@" in username
        ):
            return False

        # Check against regex pattern
        return bool(self.USERNAME_PATTERN.match(username))

    def __eq__(self, other: Any) -> bool:
        """Check equality with another Username object."""
        if not isinstance(other, Username):
            return False
        return self._value == other._value

    def __hash__(self) -> int:
        """Return hash of the username value."""
        return hash(self._value)

    def __str__(self) -> str:
        """Return string representation of the username."""
        return self._value

    def __repr__(self) -> str:
        """Return detailed string representation of the username."""
        return f"Username('{self._value}')"
