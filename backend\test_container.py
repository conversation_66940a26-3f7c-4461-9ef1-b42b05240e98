#!/usr/bin/env python3
"""
Simple test script to verify container registration works.
"""

import sys
import os

# Add src to path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

def test_container_basic():
    """Test basic container functionality."""
    try:
        from src.infrastructure.container import get_container
        
        container = get_container()
        print("✓ Container created successfully")
        
        # Test basic services
        from src.infrastructure.config.settings import Settings
        settings = container.get(Settings)
        print(f"✓ Settings loaded: {settings.app_name}")
        
        return True
        
    except Exception as e:
        print(f"✗ Container test failed: {e}")
        return False

def test_ai_services():
    """Test AI services registration."""
    try:
        from src.infrastructure.container import get_container, AI_SERVICES_AVAILABLE
        
        container = get_container()
        
        if AI_SERVICES_AVAILABLE:
            from src.infrastructure.ai.models.local_model_service import LocalModelService
            service = container.get(LocalModelService)
            print("✓ LocalModelService registered and available")
        else:
            print("⚠ AI services not available (missing dependencies)")
            
        return True
        
    except Exception as e:
        print(f"✗ AI services test failed: {e}")
        return False

def test_mcp_endpoint():
    """Test MCP endpoint import."""
    try:
        from src.presentation.api.v1.endpoints import mcp_ws
        print("✓ MCP WebSocket endpoint imported successfully")
        return True
        
    except Exception as e:
        print(f"✗ MCP endpoint test failed: {e}")
        return False

if __name__ == "__main__":
    print("Testing Lonors Backend Container...")
    
    tests = [
        ("Basic Container", test_container_basic),
        ("AI Services", test_ai_services),
        ("MCP Endpoint", test_mcp_endpoint),
    ]
    
    passed = 0
    total = len(tests)
    
    for name, test_func in tests:
        print(f"\n--- Testing {name} ---")
        if test_func():
            passed += 1
    
    print(f"\n--- Results ---")
    print(f"Passed: {passed}/{total}")
    
    if passed == total:
        print("🎉 All tests passed!")
        sys.exit(0)
    else:
        print("❌ Some tests failed")
        sys.exit(1)
