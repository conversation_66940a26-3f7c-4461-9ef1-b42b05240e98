# syntax=docker/dockerfile:1.7
# Multi-stage Dockerfile for Python/FastAPI backend with BuildKit optimizations
# Supports multi-platform builds (AMD64/ARM64) with advanced caching and security

# Stage 1: Base image with uv package manager
FROM --platform=$BUILDPLATFORM python:3.11-slim as base

# Build arguments for multi-platform support
ARG TARGETPLATFORM
ARG BUILDPLATFORM
ARG TARGETOS
ARG TARGETARCH

# Security: Update packages and install security updates
RUN apt-get update && apt-get upgrade -y && apt-get install -y --no-install-recommends \
    ca-certificates \
    curl \
    && rm -rf /var/lib/apt/lists/* \
    && apt-get clean

# Install uv package manager with specific version for reproducibility
RUN pip install --no-cache-dir uv==0.1.18

# Set working directory
WORKDIR /app

# Security: Create non-root user early
RUN groupadd -r appuser && useradd -r -g appuser -u 1001 appuser

# Set Python environment variables for optimization
ENV PYTHONUNBUFFERED=1 \
    PYTHONDONTWRITEBYTECODE=1 \
    PYTHONHASHSEED=random \
    PIP_NO_CACHE_DIR=1 \
    PIP_DISABLE_PIP_VERSION_CHECK=1

# Stage 2: Dependencies installation with cache optimization
FROM base as deps

# Copy dependency files
COPY pyproject.toml uv.lock* ./

# Install dependencies with BuildKit cache mount
RUN --mount=type=cache,target=/root/.cache/uv \
    --mount=type=cache,target=/root/.cache/pip \
    uv pip install --system --all-extras --requirement pyproject.toml

# Stage 3: Development environment
FROM base as development

# Install development tools and runtime dependencies
RUN apt-get update && apt-get install -y --no-install-recommends \
    git \
    curl \
    wget \
    libpq5 \
    postgresql-client \
    redis-tools \
    && rm -rf /var/lib/apt/lists/* \
    && apt-get clean

# Copy dependencies from deps stage
COPY --from=deps /usr/local/lib/python3.11/site-packages /usr/local/lib/python3.11/site-packages
COPY --from=deps /usr/local/bin /usr/local/bin

# Copy application code (will be overridden by volume in development)
COPY . .

# Change ownership to non-root user
RUN chown -R appuser:appuser /app

# Switch to non-root user for development
USER appuser

# Expose port
EXPOSE 3001

# Health check for development with longer startup time
HEALTHCHECK --interval=30s --timeout=10s --start-period=60s --retries=3 \
    CMD curl -f http://localhost:3001/health || exit 1

# Development server with hot reload
CMD ["uv", "run", "fastapi", "dev", "src/main.py", "--host", "0.0.0.0", "--port", "3001", "--reload"]

# Stage 4: Production builder
FROM base as builder

# Copy dependencies from deps stage
COPY --from=deps /usr/local/lib/python3.11/site-packages /usr/local/lib/python3.11/site-packages
COPY --from=deps /usr/local/bin /usr/local/bin

# Copy application source
COPY . .

# Build the application (compile Python files, run any build steps)
RUN python -m compileall src/ && \
    find . -name "*.pyc" -delete && \
    find . -name "__pycache__" -type d -exec rm -rf {} + || true

# Stage 5: Production runtime with security hardening
FROM python:3.11-slim as production

# Security: Install only runtime dependencies and security updates
RUN apt-get update && apt-get upgrade -y && apt-get install -y --no-install-recommends \
    libpq5 \
    curl \
    wget \
    ca-certificates \
    && rm -rf /var/lib/apt/lists/* \
    && apt-get clean

# Security: Create non-root user
RUN groupadd -r appuser && useradd -r -g appuser -u 1001 appuser

# Set working directory
WORKDIR /app

# Set production environment variables
ENV PYTHONUNBUFFERED=1 \
    PYTHONDONTWRITEBYTECODE=1 \
    PYTHONHASHSEED=random \
    PIP_NO_CACHE_DIR=1 \
    PIP_DISABLE_PIP_VERSION_CHECK=1 \
    ENVIRONMENT=production

# Copy Python dependencies from builder
COPY --from=builder /usr/local/lib/python3.11/site-packages /usr/local/lib/python3.11/site-packages
COPY --from=builder /usr/local/bin /usr/local/bin

# Copy application code (only what's needed for production)
COPY --from=builder --chown=appuser:appuser /app/src ./src
COPY --from=builder --chown=appuser:appuser /app/alembic.ini ./
COPY --from=builder --chown=appuser:appuser /app/pyproject.toml ./

# Security: Set proper permissions
RUN chown -R appuser:appuser /app && \
    chmod -R 755 /app

# Security: Remove unnecessary packages and files
RUN rm -rf /var/cache/apt/* /tmp/* /var/tmp/* /root/.cache

# Switch to non-root user
USER appuser

# Expose port
EXPOSE 3001

# Health check with improved reliability
HEALTHCHECK --interval=30s --timeout=10s --start-period=40s --retries=3 \
    CMD curl -f http://localhost:3001/health || \
        wget --no-verbose --tries=1 --spider --timeout=5 http://localhost:3001/health || exit 1

# Add labels for better container management
LABEL maintainer="simyropandos <<EMAIL>>"
LABEL version="1.0.0"
LABEL description="Lonors Backend - Python FastAPI Application"
LABEL org.opencontainers.image.source="https://github.com/simyropandos/lonors"

# Run the application with proper process management
CMD ["uvicorn", "src.main:app", "--host", "0.0.0.0", "--port", "3001", "--workers", "1"]
