# Agent Integration Guide

This document provides comprehensive guidance on integrating AI agents with the Lonors platform, including CopilotKit integration, ADK (Agent Development Kit) usage patterns, and swappable backend architectures.

## Table of Contents

- [Overview](#overview)
- [CopilotKit Integration](#copilotkit-integration)
- [Agent Development Kit (ADK)](#agent-development-kit-adk)
- [Swappable Backend Architecture](#swappable-backend-architecture)
- [Agent Types and Capabilities](#agent-types-and-capabilities)
- [Frontend Components](#frontend-components)
- [API Integration](#api-integration)
- [Best Practices](#best-practices)

## Overview

The Lonors platform supports multiple AI agent frameworks and provides a unified interface for agent management, interaction, and deployment. The system is designed to be backend-agnostic, allowing seamless switching between different agent implementations.

### Supported Backends

- **CopilotKit**: React-native AI assistant integration
- **AG2 (AutoGen 2.0)**: Multi-agent conversation framework
- **LangGraph**: Graph-based agent workflow system
- **Custom**: User-defined agent implementations

### Key Features

- **Unified API**: Consistent interface across all agent backends
- **Real-time Communication**: WebSocket support for live agent interactions
- **Session Management**: Persistent conversation contexts
- **Task Orchestration**: Complex multi-step agent workflows
- **Monitoring & Analytics**: Comprehensive agent performance tracking

## CopilotKit Integration

### Setup and Configuration

```typescript
// App.tsx - CopilotKit Provider Setup
import { CopilotKit } from '@copilotkit/react-core'
import { CopilotSidebar } from '@copilotkit/react-ui'

function App() {
  return (
    <CopilotKit
      runtimeUrl="/api/copilot"
      showDevConsole={import.meta.env.DEV}
    >
      <YourApp />
      <CopilotSidebar
        labels={{
          title: "Lonors AI Assistant",
          initial: "Hi! I'm your AI assistant. How can I help you today?",
        }}
        defaultOpen={false}
        clickOutsideToClose={true}
      />
    </CopilotKit>
  )
}
```

### CopilotKit Actions

```typescript
// Define custom actions for CopilotKit
import { useCopilotAction, useCopilotReadable } from '@copilotkit/react-core'

export function useAgentActions() {
  // Make current context readable to Copilot
  useCopilotReadable({
    description: 'Current agent management context',
    value: {
      activeAgents: agents.filter(a => a.status === 'active'),
      totalTasks: tasks.length,
      recentActivity: recentActivity,
    },
  })

  // Register agent management actions
  useCopilotAction({
    name: 'createAgent',
    description: 'Create a new AI agent',
    parameters: [
      {
        name: 'name',
        type: 'string',
        description: 'Name of the agent',
        required: true,
      },
      {
        name: 'type',
        type: 'string',
        description: 'Type of agent (assistant, task_executor, etc.)',
        required: true,
      },
      {
        name: 'backend',
        type: 'string',
        description: 'Backend framework (ag2, langgraph, copilotkit)',
        required: true,
      },
    ],
    handler: async ({ name, type, backend }) => {
      const agent = await agentApi.createAgent({
        name,
        type: type as AgentType,
        backend: backend as AgentBackend,
        description: `AI agent created via CopilotKit`,
        configuration: getDefaultConfiguration(backend),
      })
      
      return `Successfully created agent "${name}" with ID ${agent.id}`
    },
  })

  useCopilotAction({
    name: 'startAgent',
    description: 'Start an existing agent',
    parameters: [
      {
        name: 'agentId',
        type: 'string',
        description: 'ID of the agent to start',
        required: true,
      },
    ],
    handler: async ({ agentId }) => {
      await agentApi.startAgent(agentId)
      return `Agent ${agentId} has been started`
    },
  })
}
```

### CopilotKit Textarea Integration

```typescript
// Enhanced textarea with AI suggestions
import { CopilotTextarea } from '@copilotkit/react-textarea'

export function AgentPromptInput({ onSubmit }: { onSubmit: (prompt: string) => void }) {
  const [prompt, setPrompt] = useState('')

  return (
    <CopilotTextarea
      className="w-full min-h-[100px] p-3 border rounded-md"
      placeholder="Describe what you want the agent to do..."
      value={prompt}
      onChange={(e) => setPrompt(e.target.value)}
      autosuggestionsConfig={{
        textareaPurpose: 'Agent task description and instructions',
        chatApiEndpoint: '/api/copilot/suggestions',
      }}
      onKeyDown={(e) => {
        if (e.key === 'Enter' && e.ctrlKey) {
          onSubmit(prompt)
        }
      }}
    />
  )
}
```

## Agent Development Kit (ADK)

### Agent Interface Definition

```typescript
// Core agent interface that all backends must implement
export interface AgentInterface {
  id: string
  name: string
  backend: AgentBackend
  status: AgentStatus
  
  // Lifecycle methods
  initialize(config: AgentConfiguration): Promise<void>
  start(): Promise<void>
  stop(): Promise<void>
  restart(): Promise<void>
  
  // Communication methods
  sendMessage(message: string, context?: any): Promise<string>
  streamMessage(message: string, context?: any): AsyncGenerator<string>
  
  // Task management
  executeTask(task: AgentTask): Promise<any>
  cancelTask(taskId: string): Promise<void>
  
  // Health and monitoring
  getHealth(): Promise<AgentHealth>
  getMetrics(): Promise<AgentMetrics>
}
```

### Backend Factory Pattern

```typescript
// Factory for creating agents with different backends
export class AgentFactory {
  static async createAgent(config: AgentCreateRequest): Promise<AgentInterface> {
    switch (config.backend) {
      case 'ag2':
        return new AG2Agent(config)
      case 'langgraph':
        return new LangGraphAgent(config)
      case 'copilotkit':
        return new CopilotKitAgent(config)
      case 'custom':
        return new CustomAgent(config)
      default:
        throw new Error(`Unsupported backend: ${config.backend}`)
    }
  }
}
```

### AG2 Agent Implementation

```typescript
// AG2 (AutoGen 2.0) agent implementation
export class AG2Agent implements AgentInterface {
  private client: AG2Client
  private config: AgentConfiguration
  
  constructor(config: AgentCreateRequest) {
    this.config = config.configuration
    this.client = new AG2Client({
      model: config.configuration.model || 'gpt-4',
      temperature: config.configuration.temperature || 0.7,
    })
  }
  
  async initialize(): Promise<void> {
    await this.client.connect()
    await this.client.setupAgent({
      name: this.name,
      systemPrompt: this.config.system_prompt,
      tools: this.config.tools || [],
    })
  }
  
  async sendMessage(message: string, context?: any): Promise<string> {
    const response = await this.client.chat({
      message,
      context,
      sessionId: context?.sessionId,
    })
    
    return response.content
  }
  
  async *streamMessage(message: string, context?: any): AsyncGenerator<string> {
    const stream = this.client.streamChat({
      message,
      context,
      sessionId: context?.sessionId,
    })
    
    for await (const chunk of stream) {
      yield chunk.content
    }
  }
  
  async executeTask(task: AgentTask): Promise<any> {
    return await this.client.executeTask({
      taskId: task.id,
      description: task.description,
      input: task.input,
    })
  }
}
```

### LangGraph Agent Implementation

```typescript
// LangGraph agent implementation
export class LangGraphAgent implements AgentInterface {
  private graph: LangGraph
  private config: AgentConfiguration
  
  constructor(config: AgentCreateRequest) {
    this.config = config.configuration
    this.graph = new LangGraph({
      model: config.configuration.model || 'gpt-4',
      nodes: this.buildGraphNodes(),
      edges: this.buildGraphEdges(),
    })
  }
  
  private buildGraphNodes() {
    return [
      {
        id: 'input',
        type: 'input',
        processor: (data) => ({ message: data.message }),
      },
      {
        id: 'reasoning',
        type: 'llm',
        model: this.config.model,
        prompt: this.config.system_prompt,
      },
      {
        id: 'tools',
        type: 'tools',
        tools: this.config.tools || [],
      },
      {
        id: 'output',
        type: 'output',
        formatter: (data) => data.response,
      },
    ]
  }
  
  async sendMessage(message: string, context?: any): Promise<string> {
    const result = await this.graph.execute({
      input: { message, context },
      sessionId: context?.sessionId,
    })
    
    return result.output
  }
}
```

## Swappable Backend Architecture

### Backend Registry

```typescript
// Registry for managing different agent backends
export class AgentBackendRegistry {
  private static backends = new Map<AgentBackend, AgentBackendInterface>()
  
  static register(backend: AgentBackend, implementation: AgentBackendInterface) {
    this.backends.set(backend, implementation)
  }
  
  static get(backend: AgentBackend): AgentBackendInterface {
    const implementation = this.backends.get(backend)
    if (!implementation) {
      throw new Error(`Backend ${backend} not registered`)
    }
    return implementation
  }
  
  static getAvailable(): AgentBackend[] {
    return Array.from(this.backends.keys())
  }
}

// Register available backends
AgentBackendRegistry.register('ag2', new AG2Backend())
AgentBackendRegistry.register('langgraph', new LangGraphBackend())
AgentBackendRegistry.register('copilotkit', new CopilotKitBackend())
```

### Backend Migration

```typescript
// Utility for migrating agents between backends
export class AgentMigrator {
  static async migrateAgent(
    agentId: string,
    fromBackend: AgentBackend,
    toBackend: AgentBackend
  ): Promise<void> {
    // 1. Export agent state from current backend
    const currentAgent = await agentApi.getAgent(agentId)
    const exportedState = await this.exportAgentState(currentAgent, fromBackend)
    
    // 2. Create new agent with target backend
    const newAgent = await agentApi.createAgent({
      ...currentAgent,
      backend: toBackend,
      name: `${currentAgent.name} (migrated)`,
    })
    
    // 3. Import state to new backend
    await this.importAgentState(newAgent, exportedState, toBackend)
    
    // 4. Update references and cleanup
    await this.updateAgentReferences(agentId, newAgent.id)
    await agentApi.deleteAgent(agentId)
  }
  
  private static async exportAgentState(
    agent: Agent,
    backend: AgentBackend
  ): Promise<AgentState> {
    const backendImpl = AgentBackendRegistry.get(backend)
    return await backendImpl.exportState(agent.id)
  }
  
  private static async importAgentState(
    agent: Agent,
    state: AgentState,
    backend: AgentBackend
  ): Promise<void> {
    const backendImpl = AgentBackendRegistry.get(backend)
    await backendImpl.importState(agent.id, state)
  }
}
```

## Agent Types and Capabilities

### Agent Type Definitions

```typescript
export enum AgentType {
  ASSISTANT = 'assistant',           // General-purpose AI assistant
  TASK_EXECUTOR = 'task_executor',   // Executes specific tasks
  DATA_PROCESSOR = 'data_processor', // Processes and analyzes data
  CODE_GENERATOR = 'code_generator', // Generates and reviews code
  WORKFLOW_MANAGER = 'workflow_manager', // Manages complex workflows
  CUSTOM = 'custom',                 // User-defined agent type
}

export interface AgentCapability {
  id: string
  name: string
  description: string
  parameters: CapabilityParameter[]
  handler: (params: any) => Promise<any>
}

export const BUILT_IN_CAPABILITIES: Record<string, AgentCapability> = {
  text_generation: {
    id: 'text_generation',
    name: 'Text Generation',
    description: 'Generate human-like text based on prompts',
    parameters: [
      { name: 'prompt', type: 'string', required: true },
      { name: 'max_tokens', type: 'number', default: 1000 },
      { name: 'temperature', type: 'number', default: 0.7 },
    ],
    handler: async (params) => {
      // Implementation
    },
  },
  
  code_analysis: {
    id: 'code_analysis',
    name: 'Code Analysis',
    description: 'Analyze code for bugs, performance, and best practices',
    parameters: [
      { name: 'code', type: 'string', required: true },
      { name: 'language', type: 'string', required: true },
      { name: 'analysis_type', type: 'string', default: 'comprehensive' },
    ],
    handler: async (params) => {
      // Implementation
    },
  },
  
  data_visualization: {
    id: 'data_visualization',
    name: 'Data Visualization',
    description: 'Create charts and graphs from data',
    parameters: [
      { name: 'data', type: 'object', required: true },
      { name: 'chart_type', type: 'string', default: 'auto' },
      { name: 'title', type: 'string' },
    ],
    handler: async (params) => {
      // Implementation
    },
  },
}
```

## Frontend Components

### Agent Management Components

```typescript
// Agent card component for displaying agent information
export function AgentCard({ agent, onAction }: AgentCardProps) {
  return (
    <Card variant="elevated">
      <CardHeader>
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-3">
            <AgentAvatar agent={agent} />
            <div>
              <CardTitle>{agent.name}</CardTitle>
              <p className="text-sm text-muted-foreground">
                {agent.backend} • {agent.type}
              </p>
            </div>
          </div>
          <AgentStatusBadge status={agent.status} />
        </div>
      </CardHeader>
      
      <CardContent>
        <p className="text-sm mb-4">{agent.description}</p>
        
        <div className="flex flex-wrap gap-1 mb-4">
          {agent.capabilities.map((capability) => (
            <Badge key={capability} variant="secondary">
              {capability}
            </Badge>
          ))}
        </div>
        
        <AgentActions agent={agent} onAction={onAction} />
      </CardContent>
    </Card>
  )
}

// Agent chat interface
export function AgentChat({ agentId, sessionId }: AgentChatProps) {
  const [messages, setMessages] = useState<AgentMessage[]>([])
  const [input, setInput] = useState('')
  const [isStreaming, setIsStreaming] = useState(false)
  
  const sendMessage = async (content: string) => {
    const userMessage = createUserMessage(content)
    setMessages(prev => [...prev, userMessage])
    setInput('')
    setIsStreaming(true)
    
    try {
      // Stream response from agent
      const stream = await agentApi.streamMessage({
        session_id: sessionId,
        content,
      })
      
      let assistantMessage = createAssistantMessage('')
      setMessages(prev => [...prev, assistantMessage])
      
      for await (const chunk of stream) {
        assistantMessage.content += chunk
        setMessages(prev => 
          prev.map(msg => 
            msg.id === assistantMessage.id 
              ? { ...msg, content: assistantMessage.content }
              : msg
          )
        )
      }
    } catch (error) {
      console.error('Error sending message:', error)
    } finally {
      setIsStreaming(false)
    }
  }
  
  return (
    <div className="flex flex-col h-full">
      <ChatMessages messages={messages} />
      <ChatInput 
        value={input}
        onChange={setInput}
        onSubmit={sendMessage}
        disabled={isStreaming}
      />
    </div>
  )
}
```

## API Integration

### Agent API Client

```typescript
// Comprehensive agent API client
export const agentApi = {
  // Agent lifecycle
  async createAgent(data: AgentCreateRequest): Promise<Agent> {
    const response = await apiClient.post('/agents', data)
    return response.data
  },
  
  async getAgents(filters?: AgentFilters): Promise<PaginatedResponse<Agent>> {
    const response = await apiClient.get('/agents', { params: filters })
    return response.data
  },
  
  async updateAgent(id: string, data: AgentUpdateRequest): Promise<Agent> {
    const response = await apiClient.put(`/agents/${id}`, data)
    return response.data
  },
  
  async deleteAgent(id: string): Promise<void> {
    await apiClient.delete(`/agents/${id}`)
  },
  
  // Agent operations
  async startAgent(id: string): Promise<Agent> {
    const response = await apiClient.post(`/agents/${id}/start`)
    return response.data
  },
  
  async stopAgent(id: string): Promise<Agent> {
    const response = await apiClient.post(`/agents/${id}/stop`)
    return response.data
  },
  
  // Communication
  async sendMessage(data: MessageSendRequest): Promise<AgentMessage> {
    const response = await apiClient.post('/agents/messages', data)
    return response.data
  },
  
  async *streamMessage(data: MessageSendRequest): AsyncGenerator<string> {
    const response = await fetch('/api/v1/agents/messages/stream', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${getToken()}`,
      },
      body: JSON.stringify(data),
    })
    
    const reader = response.body?.getReader()
    if (!reader) throw new Error('No response body')
    
    while (true) {
      const { done, value } = await reader.read()
      if (done) break
      
      const chunk = new TextDecoder().decode(value)
      const lines = chunk.split('\n').filter(line => line.trim())
      
      for (const line of lines) {
        if (line.startsWith('data: ')) {
          const data = line.slice(6)
          if (data === '[DONE]') return
          
          try {
            const parsed = JSON.parse(data)
            yield parsed.content
          } catch (e) {
            console.warn('Failed to parse SSE data:', data)
          }
        }
      }
    }
  },
}
```

## Best Practices

### Performance Optimization

1. **Lazy Loading**: Load agent components only when needed
2. **Memoization**: Use React.memo for expensive agent components
3. **Virtualization**: Implement virtual scrolling for large agent lists
4. **Debouncing**: Debounce user inputs in chat interfaces
5. **Caching**: Cache agent responses and configurations

### Error Handling

```typescript
// Comprehensive error handling for agent operations
export function useAgentErrorHandler() {
  return useCallback((error: Error, context: string) => {
    console.error(`Agent error in ${context}:`, error)
    
    // Log to monitoring service
    if (import.meta.env.PROD) {
      analytics.track('agent_error', {
        error: error.message,
        context,
        timestamp: new Date().toISOString(),
      })
    }
    
    // Show user-friendly error message
    toast.error(getErrorMessage(error, context))
  }, [])
}

function getErrorMessage(error: Error, context: string): string {
  if (error.message.includes('network')) {
    return 'Network error. Please check your connection.'
  }
  
  if (error.message.includes('unauthorized')) {
    return 'Session expired. Please log in again.'
  }
  
  if (context === 'agent_creation') {
    return 'Failed to create agent. Please try again.'
  }
  
  return 'An unexpected error occurred. Please try again.'
}
```

### Security Considerations

1. **Input Validation**: Validate all agent inputs on both client and server
2. **Rate Limiting**: Implement rate limiting for agent interactions
3. **Authentication**: Ensure proper authentication for agent operations
4. **Sandboxing**: Isolate agent execution environments
5. **Audit Logging**: Log all agent activities for security monitoring

This comprehensive guide provides the foundation for building robust, scalable agent integrations within the Lonors platform.
