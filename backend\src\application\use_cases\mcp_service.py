"""
Model Context Protocol (MCP) service.

This module contains use cases for MCP operations including
context management and model interactions.
"""

import uuid
from datetime import datetime, timedelta, timezone
from typing import Dict, List, Optional

from src.domain.entities.mcp import (
    MCPContext,
    MCPContextCreate,
    MCPContextResponse,
    MCPContextUpdate,
    MCPMessage,
    MCPModel,
    MCPModelResponse,
    MCPRequest,
    MCPResponse,
    ModelStatus,
)
from src.infrastructure.logging.setup import LoggerMixin


class MCPService(LoggerMixin):
    """
    MCP service containing business logic for model context protocol operations.
    
    Orchestrates MCP-related use cases and manages model interactions
    and context sessions.
    """
    
    def __init__(self) -> None:
        """Initialize MCP service."""
        # In a real implementation, these would be injected dependencies
        self._models: Dict[str, MCPModel] = {}
        self._contexts: Dict[uuid.UUID, MCPContext] = {}
        self._messages: Dict[uuid.UUID, List[MCPMessage]] = {}
        
        # Initialize with some sample models
        self._initialize_sample_models()
    
    def _initialize_sample_models(self) -> None:
        """Initialize sample models for demonstration."""
        sample_models = [
            MCPModel(
                id="gpt-4",
                name="GPT-4",
                type="text_generation",
                provider="OpenAI",
                version="gpt-4-0613",
                status=ModelStatus.AVAILABLE,
                max_context_length=8192,
                capabilities=["text_generation", "code_generation", "chat_completion"],
                metadata={"cost_per_token": 0.00003}
            ),
            MCPModel(
                id="claude-3",
                name="Claude 3",
                type="text_generation",
                provider="Anthropic",
                version="claude-3-sonnet-20240229",
                status=ModelStatus.AVAILABLE,
                max_context_length=200000,
                capabilities=["text_generation", "code_generation", "chat_completion"],
                metadata={"cost_per_token": 0.000015}
            ),
            MCPModel(
                id="codellama",
                name="Code Llama",
                type="code_generation",
                provider="Meta",
                version="codellama-34b",
                status=ModelStatus.AVAILABLE,
                max_context_length=4096,
                capabilities=["code_generation", "code_completion"],
                metadata={"specialized": "code"}
            ),
        ]
        
        for model in sample_models:
            self._models[model.id] = model
    
    async def list_models(self) -> List[MCPModelResponse]:
        """
        List available models.
        
        Returns:
            List[MCPModelResponse]: List of available models
        """
        models = [
            MCPModelResponse.from_attributes(model)
            for model in self._models.values()
        ]
        
        self.logger.debug(f"Listed {len(models)} available models")
        return models
    
    async def get_model(self, model_id: str) -> Optional[MCPModelResponse]:
        """
        Get model by ID.
        
        Args:
            model_id: Model identifier
            
        Returns:
            MCPModelResponse: Model information or None if not found
        """
        model = self._models.get(model_id)
        if model:
            self.logger.debug(f"Retrieved model: {model_id}")
            return MCPModelResponse.from_attributes(model)
        
        self.logger.warning(f"Model not found: {model_id}")
        return None
    
    async def create_context(
        self,
        user_id: uuid.UUID,
        context_data: MCPContextCreate
    ) -> MCPContextResponse:
        """
        Create new MCP context.
        
        Args:
            user_id: User ID creating the context
            context_data: Context creation data
            
        Returns:
            MCPContextResponse: Created context information
            
        Raises:
            ValueError: If model not found or invalid data
        """
        # Validate model exists
        if context_data.model_id not in self._models:
            raise ValueError(f"Model not found: {context_data.model_id}")
        
        model = self._models[context_data.model_id]
        
        # Calculate expiration time
        expires_at = None
        if context_data.expires_in_hours:
            expires_at = datetime.now(timezone.utc) + timedelta(hours=context_data.expires_in_hours)
        
        # Create context
        context = MCPContext(
            user_id=user_id,
            type=context_data.type,
            title=context_data.title,
            description=context_data.description,
            model_id=context_data.model_id,
            max_length=context_data.max_length or model.max_context_length,
            expires_at=expires_at,
            metadata=context_data.metadata,
        )
        
        # Store context
        self._contexts[context.id] = context
        self._messages[context.id] = []
        
        self.logger.info(f"Created MCP context: {context.id} for user: {user_id}")
        
        return MCPContextResponse(
            **context.dict(),
            is_expired=context.is_expired(),
            is_full=context.is_full(),
        )
    
    async def get_context(
        self,
        context_id: uuid.UUID,
        user_id: uuid.UUID
    ) -> Optional[MCPContextResponse]:
        """
        Get MCP context by ID.
        
        Args:
            context_id: Context ID
            user_id: User ID (for authorization)
            
        Returns:
            MCPContextResponse: Context information or None if not found
        """
        context = self._contexts.get(context_id)
        
        if not context:
            self.logger.warning(f"Context not found: {context_id}")
            return None
        
        # Check ownership
        if context.user_id != user_id:
            self.logger.warning(f"Unauthorized context access: {context_id} by user: {user_id}")
            return None
        
        self.logger.debug(f"Retrieved context: {context_id}")
        
        return MCPContextResponse(
            **context.dict(),
            is_expired=context.is_expired(),
            is_full=context.is_full(),
        )
    
    async def update_context(
        self,
        context_id: uuid.UUID,
        user_id: uuid.UUID,
        update_data: MCPContextUpdate
    ) -> Optional[MCPContextResponse]:
        """
        Update MCP context.
        
        Args:
            context_id: Context ID
            user_id: User ID (for authorization)
            update_data: Update data
            
        Returns:
            MCPContextResponse: Updated context information or None if not found
        """
        context = self._contexts.get(context_id)
        
        if not context:
            return None
        
        # Check ownership
        if context.user_id != user_id:
            return None
        
        # Update fields
        if update_data.title:
            context.title = update_data.title
        
        if update_data.description is not None:
            context.description = update_data.description
        
        if update_data.metadata:
            context.metadata.update(update_data.metadata)
        
        context.updated_at = datetime.now(timezone.utc)
        
        self.logger.info(f"Updated context: {context_id}")
        
        return MCPContextResponse(
            **context.dict(),
            is_expired=context.is_expired(),
            is_full=context.is_full(),
        )
    
    async def delete_context(
        self,
        context_id: uuid.UUID,
        user_id: uuid.UUID
    ) -> bool:
        """
        Delete MCP context.
        
        Args:
            context_id: Context ID
            user_id: User ID (for authorization)
            
        Returns:
            bool: True if deleted, False if not found
        """
        context = self._contexts.get(context_id)
        
        if not context:
            return False
        
        # Check ownership
        if context.user_id != user_id:
            return False
        
        # Delete context and messages
        del self._contexts[context_id]
        if context_id in self._messages:
            del self._messages[context_id]
        
        self.logger.info(f"Deleted context: {context_id}")
        return True
    
    async def list_user_contexts(
        self,
        user_id: uuid.UUID,
        skip: int = 0,
        limit: int = 100
    ) -> List[MCPContextResponse]:
        """
        List user's MCP contexts.
        
        Args:
            user_id: User ID
            skip: Number of contexts to skip
            limit: Maximum number of contexts to return
            
        Returns:
            List[MCPContextResponse]: List of user's contexts
        """
        user_contexts = [
            context for context in self._contexts.values()
            if context.user_id == user_id
        ]
        
        # Sort by creation date (newest first)
        user_contexts.sort(key=lambda x: x.created_at, reverse=True)
        
        # Apply pagination
        paginated_contexts = user_contexts[skip:skip + limit]
        
        contexts = [
            MCPContextResponse(
                **context.dict(),
                is_expired=context.is_expired(),
                is_full=context.is_full(),
            )
            for context in paginated_contexts
        ]
        
        self.logger.debug(f"Listed {len(contexts)} contexts for user: {user_id}")
        return contexts
    
    async def process_request(
        self,
        user_id: uuid.UUID,
        request: MCPRequest
    ) -> MCPResponse:
        """
        Process MCP request.
        
        Args:
            user_id: User ID making the request
            request: MCP request data
            
        Returns:
            MCPResponse: Generated response
            
        Raises:
            ValueError: If request is invalid
        """
        # Validate model exists
        if request.model_id not in self._models:
            raise ValueError(f"Model not found: {request.model_id}")
        
        model = self._models[request.model_id]
        
        # Check model status
        if model.status != ModelStatus.AVAILABLE:
            raise ValueError(f"Model not available: {request.model_id} (status: {model.status})")
        
        # Get or create context
        context = None
        if request.context_id:
            context = self._contexts.get(request.context_id)
            if not context or context.user_id != user_id:
                raise ValueError("Invalid context ID")
            
            if context.is_expired():
                raise ValueError("Context has expired")
        
        # TODO: Implement actual model inference
        # For now, return a mock response
        response_content = f"Mock response from {model.name} for {len(request.messages)} messages"
        
        # Calculate token usage (mock)
        prompt_tokens = sum(len(msg.get("content", "").split()) for msg in request.messages)
        completion_tokens = len(response_content.split())
        total_tokens = prompt_tokens + completion_tokens
        
        # Create response
        response = MCPResponse(
            id=str(uuid.uuid4()),
            context_id=context.id if context else uuid.uuid4(),
            model_id=request.model_id,
            content=response_content,
            usage={
                "prompt_tokens": prompt_tokens,
                "completion_tokens": completion_tokens,
                "total_tokens": total_tokens,
            },
            metadata=request.metadata,
        )
        
        # Update context if provided
        if context:
            context.add_content_length(total_tokens)
            
            # Add messages to context
            for msg in request.messages:
                message = MCPMessage(
                    context_id=context.id,
                    role=msg.get("role", "user"),
                    content=msg.get("content", ""),
                    content_length=len(msg.get("content", "").split()),
                )
                self._messages[context.id].append(message)
            
            # Add response message
            response_message = MCPMessage(
                context_id=context.id,
                role="assistant",
                content=response_content,
                content_length=completion_tokens,
            )
            self._messages[context.id].append(response_message)
        
        self.logger.info(f"Processed MCP request for user: {user_id}, model: {request.model_id}")
        return response
