"""
User Application Service for the Lonors application.

This module contains the UserService class that orchestrates
user-related business operations following Clean Architecture principles.
"""

from typing import Optional, Protocol, Tuple

from src.application.dto.user_dto import (
    ChangePasswordDTO,
    CreateUserDTO,
    LoginDTO,
    LoginResponseDTO,
    PaginatedUsersDTO,
    UpdateUserDTO,
    UserResponseDTO,
)
from src.domain.entities.user import User
from src.domain.exceptions import (
    DuplicateEntityError,
    EntityNotFoundError,
    UnauthorizedOperationError,
)
from src.domain.repositories.user_repository import UserRepository
from src.domain.value_objects.email import Email
from src.domain.value_objects.password import Password
from src.domain.value_objects.username import Username


class PasswordService(Protocol):
    """Protocol for password service."""

    def hash_password(self, password: str) -> str:
        """Hash a password."""
        ...

    def verify_password(self, password: str, hashed_password: str) -> bool:
        """Verify a password against its hash."""
        ...


class JWTService(Protocol):
    """Protocol for JWT service."""

    def create_access_token(self, user_id: str) -> str:
        """Create an access token."""
        ...

    def create_refresh_token(self, user_id: str) -> str:
        """Create a refresh token."""
        ...


class UserService:
    """Application service for user operations."""

    def __init__(
        self,
        user_repository: UserRepository,
        password_service: PasswordService,
        jwt_service: JWTService,
    ) -> None:
        """
        Initialize UserService.

        Args:
            user_repository: User repository
            password_service: Password service
            jwt_service: JWT service
        """
        self._user_repository = user_repository
        self._password_service = password_service
        self._jwt_service = jwt_service

    async def create_user(self, dto: CreateUserDTO) -> UserResponseDTO:
        """
        Create a new user.

        Args:
            dto: User creation data

        Returns:
            Created user data

        Raises:
            DuplicateEntityError: If user already exists
        """
        # Check for existing user with same email
        existing_user = await self._user_repository.get_by_email(dto.email)
        if existing_user:
            raise DuplicateEntityError("User with email already exists")

        # Check for existing user with same username
        existing_user = await self._user_repository.get_by_username(dto.username)
        if existing_user:
            raise DuplicateEntityError("User with username already exists")

        # Hash password
        hashed_password = self._password_service.hash_password(dto.password)

        # Create user entity
        user = User(
            email=Email(dto.email),
            username=Username(dto.username),
            full_name=dto.full_name,
            hashed_password=hashed_password,
            is_active=True,
            is_verified=False,
        )

        # Save user
        await self._user_repository.create(user)

        return UserResponseDTO.from_entity(user)

    async def get_user_by_id(self, user_id: str) -> UserResponseDTO:
        """
        Get user by ID.

        Args:
            user_id: User ID

        Returns:
            User data

        Raises:
            EntityNotFoundError: If user not found
        """
        user = await self._user_repository.get_by_id(user_id)
        if not user:
            raise EntityNotFoundError(f"User with id {user_id} not found")

        return UserResponseDTO.from_entity(user)

    async def update_user(self, user_id: str, dto: UpdateUserDTO) -> UserResponseDTO:
        """
        Update user information.

        Args:
            user_id: User ID
            dto: Update data

        Returns:
            Updated user data

        Raises:
            EntityNotFoundError: If user not found
        """
        user = await self._user_repository.get_by_id(user_id)
        if not user:
            raise EntityNotFoundError(f"User with id {user_id} not found")

        # Update user
        if dto.full_name is not None:
            user.update_profile(full_name=dto.full_name)

        await self._user_repository.update(user)

        return UserResponseDTO.from_entity(user)

    async def authenticate_user(self, dto: LoginDTO) -> LoginResponseDTO:
        """
        Authenticate user and return tokens.

        Args:
            dto: Login credentials

        Returns:
            Authentication tokens and user data

        Raises:
            UnauthorizedOperationError: If authentication fails
        """
        # Get user by email
        user = await self._user_repository.get_by_email(dto.email)
        if not user:
            raise UnauthorizedOperationError("Invalid credentials")

        # Verify password
        if not self._password_service.verify_password(dto.password, user.hashed_password):
            raise UnauthorizedOperationError("Invalid credentials")

        # Create tokens
        access_token = self._jwt_service.create_access_token(user.id)
        refresh_token = self._jwt_service.create_refresh_token(user.id)

        return LoginResponseDTO(
            access_token=access_token,
            refresh_token=refresh_token,
            token_type="bearer",
            expires_in=3600,  # 1 hour
            user=UserResponseDTO.from_entity(user),
        )

    async def change_password(self, user_id: str, dto: ChangePasswordDTO) -> None:
        """
        Change user password.

        Args:
            user_id: User ID
            dto: Password change data

        Raises:
            EntityNotFoundError: If user not found
            UnauthorizedOperationError: If current password is invalid
        """
        user = await self._user_repository.get_by_id(user_id)
        if not user:
            raise EntityNotFoundError(f"User with id {user_id} not found")

        # Verify current password
        if not self._password_service.verify_password(dto.current_password, user.hashed_password):
            raise UnauthorizedOperationError("Invalid current password")

        # Hash new password
        new_hashed_password = self._password_service.hash_password(dto.new_password)

        # Update password
        user.change_password(new_hashed_password)

        await self._user_repository.update(user)

    async def activate_user(self, user_id: str) -> UserResponseDTO:
        """
        Activate user.

        Args:
            user_id: User ID

        Returns:
            Updated user data

        Raises:
            EntityNotFoundError: If user not found
        """
        user = await self._user_repository.get_by_id(user_id)
        if not user:
            raise EntityNotFoundError(f"User with id {user_id} not found")

        user.activate()
        await self._user_repository.update(user)

        return UserResponseDTO.from_entity(user)

    async def deactivate_user(self, user_id: str) -> UserResponseDTO:
        """
        Deactivate user.

        Args:
            user_id: User ID

        Returns:
            Updated user data

        Raises:
            EntityNotFoundError: If user not found
        """
        user = await self._user_repository.get_by_id(user_id)
        if not user:
            raise EntityNotFoundError(f"User with id {user_id} not found")

        user.deactivate()
        await self._user_repository.update(user)

        return UserResponseDTO.from_entity(user)

    async def verify_email(self, user_id: str) -> UserResponseDTO:
        """
        Verify user email.

        Args:
            user_id: User ID

        Returns:
            Updated user data

        Raises:
            EntityNotFoundError: If user not found
        """
        user = await self._user_repository.get_by_id(user_id)
        if not user:
            raise EntityNotFoundError(f"User with id {user_id} not found")

        user.verify_email()
        await self._user_repository.update(user)

        return UserResponseDTO.from_entity(user)

    async def list_users(self, page: int = 1, size: int = 10) -> PaginatedUsersDTO:
        """
        List users with pagination.

        Args:
            page: Page number (1-based)
            size: Page size

        Returns:
            Paginated user list
        """
        offset = (page - 1) * size
        users, total = await self._user_repository.list(offset=offset, limit=size)

        user_dtos = [UserResponseDTO.from_entity(user) for user in users]

        return PaginatedUsersDTO.create(
            items=user_dtos,
            total=total,
            page=page,
            size=size,
        )
