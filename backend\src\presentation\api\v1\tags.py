"""
Tags API Endpoints

This module defines the API endpoints for tags.
"""

from uuid import UUID

from fastapi import APIRouter, Depends, HTTPException, status

from src.application.dto.tag_dto import CreateTagDTO, TagDTO, UpdateTagDTO
from src.application.use_cases.tag_service import TagService
from src.presentation.dependencies.auth import get_current_user
from src.presentation.dependencies.services import get_tag_service

router = APIRouter(prefix="/tags", tags=["Tags"])


@router.get("/", response_model=list[TagDTO])
async def get_tags(
    current_user=Depends(get_current_user),
    tag_service: TagService = Depends(get_tag_service),
):
    """
    Get all tags for the current user.
    """
    return await tag_service.get_tags(current_user.id)


@router.get("/{tag_id}", response_model=TagDTO)
async def get_tag(
    tag_id: UUID,
    current_user=Depends(get_current_user),
    tag_service: TagService = Depends(get_tag_service),
):
    """
    Get a specific tag by ID.
    """
    tag = await tag_service.get_tag(tag_id, current_user.id)

    if tag is None:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Tag not found",
        )

    return tag


@router.post("/", response_model=TagDTO, status_code=status.HTTP_201_CREATED)
async def create_tag(
    create_dto: CreateTagDTO,
    current_user=Depends(get_current_user),
    tag_service: TagService = Depends(get_tag_service),
):
    """
    Create a new tag.
    """
    return await tag_service.create_tag(current_user.id, create_dto)


@router.put("/{tag_id}", response_model=TagDTO)
async def update_tag(
    tag_id: UUID,
    update_dto: UpdateTagDTO,
    current_user=Depends(get_current_user),
    tag_service: TagService = Depends(get_tag_service),
):
    """
    Update an existing tag.
    """
    tag = await tag_service.update_tag(tag_id, current_user.id, update_dto)

    if tag is None:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Tag not found",
        )

    return tag


@router.delete("/{tag_id}", status_code=status.HTTP_204_NO_CONTENT)
async def delete_tag(
    tag_id: UUID,
    current_user=Depends(get_current_user),
    tag_service: TagService = Depends(get_tag_service),
):
    """
    Delete a tag.
    """
    success = await tag_service.delete_tag(tag_id, current_user.id)

    if not success:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Tag not found",
        )
