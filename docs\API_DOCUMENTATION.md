# API Documentation

This document provides comprehensive documentation for the Lonors backend API endpoints.

## Base URL

- **Development**: `http://localhost:8000`
- **Production**: `https://api.lonors.com`

## API Version

Current API version: `v1`

All endpoints are prefixed with `/api/v1`

## Authentication

The API uses JWT (JSON Web Tokens) for authentication. Include the token in the Authorization header:

```
Authorization: Bearer <your_jwt_token>
```

## Health Check Endpoints

### GET /api/v1/health/

Check API health status.

**Response:**
```json
{
  "status": "healthy",
  "timestamp": "2024-01-15T12:00:00Z",
  "version": "1.0.0",
  "environment": "development"
}
```

### GET /api/v1/health/detailed

Get detailed health information including database connectivity.

**Response:**
```json
{
  "status": "healthy",
  "timestamp": "2024-01-15T12:00:00Z",
  "version": "1.0.0",
  "environment": "development",
  "checks": {
    "database": "healthy",
    "redis": "healthy"
  }
}
```

## Authentication Endpoints

### POST /api/v1/auth/register

Register a new user account.

**Request Body:**
```json
{
  "email": "<EMAIL>",
  "username": "username",
  "full_name": "Full Name",
  "password": "secure_password"
}
```

**Response (201 Created):**
```json
{
  "id": "123e4567-e89b-12d3-a456-************",
  "email": "<EMAIL>",
  "username": "username",
  "full_name": "Full Name",
  "role": "user",
  "status": "pending_verification",
  "is_verified": false,
  "created_at": "2024-01-15T12:00:00Z"
}
```

### POST /api/v1/auth/login

Authenticate user and receive access tokens.

**Request Body:**
```json
{
  "email": "<EMAIL>",
  "password": "secure_password"
}
```

**Response (200 OK):**
```json
{
  "access_token": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9...",
  "refresh_token": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9...",
  "token_type": "bearer",
  "expires_in": 3600,
  "user": {
    "id": "123e4567-e89b-12d3-a456-************",
    "email": "<EMAIL>",
    "username": "username",
    "full_name": "Full Name",
    "role": "user"
  }
}
```

### POST /api/v1/auth/refresh

Refresh access token using refresh token.

**Request Body:**
```json
{
  "refresh_token": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9..."
}
```

**Response (200 OK):**
```json
{
  "access_token": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9...",
  "token_type": "bearer"
}
```

### POST /api/v1/auth/logout

Logout user (invalidate tokens).

**Headers:** `Authorization: Bearer <token>`

**Response (200 OK):**
```json
{
  "message": "Successfully logged out"
}
```

### GET /api/v1/auth/me

Get current user information.

**Headers:** `Authorization: Bearer <token>`

**Response (200 OK):**
```json
{
  "id": "123e4567-e89b-12d3-a456-************",
  "email": "<EMAIL>",
  "username": "username",
  "full_name": "Full Name",
  "role": "user",
  "status": "active",
  "is_verified": true,
  "created_at": "2024-01-15T12:00:00Z",
  "last_login": "2024-01-15T12:30:00Z"
}
```

### POST /api/v1/auth/change-password

Change user password.

**Headers:** `Authorization: Bearer <token>`

**Request Body:**
```json
{
  "current_password": "old_password",
  "new_password": "new_secure_password"
}
```

**Response (200 OK):**
```json
{
  "message": "Password changed successfully"
}
```

## User Management Endpoints

### GET /api/v1/users/me

Get current user profile.

**Headers:** `Authorization: Bearer <token>`

**Response (200 OK):**
```json
{
  "id": "123e4567-e89b-12d3-a456-************",
  "email": "<EMAIL>",
  "username": "username",
  "full_name": "Full Name",
  "role": "user",
  "status": "active",
  "is_verified": true,
  "created_at": "2024-01-15T12:00:00Z"
}
```

### PUT /api/v1/users/me

Update current user profile.

**Headers:** `Authorization: Bearer <token>`

**Request Body:**
```json
{
  "full_name": "Updated Full Name",
  "username": "new_username"
}
```

**Response (200 OK):**
```json
{
  "id": "123e4567-e89b-12d3-a456-************",
  "email": "<EMAIL>",
  "username": "new_username",
  "full_name": "Updated Full Name",
  "role": "user",
  "status": "active",
  "is_verified": true,
  "updated_at": "2024-01-15T13:00:00Z"
}
```

### GET /api/v1/users/{user_id}

Get user by ID.

**Headers:** `Authorization: Bearer <token>`

**Response (200 OK):**
```json
{
  "id": "123e4567-e89b-12d3-a456-************",
  "email": "<EMAIL>",
  "username": "username",
  "full_name": "Full Name",
  "role": "user",
  "status": "active",
  "is_verified": true,
  "created_at": "2024-01-15T12:00:00Z"
}
```

### GET /api/v1/users/ (Admin Only)

List users with pagination.

**Headers:** `Authorization: Bearer <admin_token>`

**Query Parameters:**
- `skip` (int, default: 0): Number of users to skip
- `limit` (int, default: 100): Maximum number of users to return
- `active_only` (bool, default: false): Return only active users

**Response (200 OK):**
```json
[
  {
    "id": "123e4567-e89b-12d3-a456-************",
    "email": "<EMAIL>",
    "username": "user1",
    "full_name": "User One",
    "role": "user",
    "status": "active",
    "is_verified": true,
    "created_at": "2024-01-15T12:00:00Z"
  },
  {
    "id": "456e7890-e89b-12d3-a456-426614174001",
    "email": "<EMAIL>",
    "username": "user2",
    "full_name": "User Two",
    "role": "user",
    "status": "active",
    "is_verified": true,
    "created_at": "2024-01-15T12:01:00Z"
  }
]
```

## Model Context Protocol (MCP) Endpoints

### GET /api/v1/mcp/models

List available AI models.

**Headers:** `Authorization: Bearer <token>`

**Response (200 OK):**
```json
[
  {
    "id": "gpt-4",
    "name": "GPT-4",
    "type": "text_generation",
    "provider": "OpenAI",
    "version": "gpt-4-0613",
    "status": "available",
    "max_context_length": 8192,
    "capabilities": ["text_generation", "code_generation", "chat_completion"]
  },
  {
    "id": "claude-3",
    "name": "Claude 3",
    "type": "text_generation",
    "provider": "Anthropic",
    "version": "claude-3-sonnet-20240229",
    "status": "available",
    "max_context_length": 200000,
    "capabilities": ["text_generation", "code_generation", "chat_completion"]
  }
]
```

### GET /api/v1/mcp/models/{model_id}

Get specific model information.

**Headers:** `Authorization: Bearer <token>`

**Response (200 OK):**
```json
{
  "id": "gpt-4",
  "name": "GPT-4",
  "type": "text_generation",
  "provider": "OpenAI",
  "version": "gpt-4-0613",
  "status": "available",
  "max_context_length": 8192,
  "capabilities": ["text_generation", "code_generation", "chat_completion"],
  "metadata": {
    "cost_per_token": 0.00003
  }
}
```

### POST /api/v1/mcp/contexts

Create new MCP context session.

**Headers:** `Authorization: Bearer <token>`

**Request Body:**
```json
{
  "type": "conversation",
  "title": "My Chat Session",
  "description": "A conversation about AI",
  "model_id": "gpt-4",
  "max_length": 4096,
  "expires_in_hours": 24
}
```

**Response (201 Created):**
```json
{
  "id": "789e0123-e89b-12d3-a456-426614174002",
  "user_id": "123e4567-e89b-12d3-a456-************",
  "type": "conversation",
  "title": "My Chat Session",
  "description": "A conversation about AI",
  "model_id": "gpt-4",
  "max_length": 4096,
  "current_length": 0,
  "created_at": "2024-01-15T12:00:00Z",
  "expires_at": "2024-01-16T12:00:00Z",
  "is_expired": false,
  "is_full": false
}
```

### POST /api/v1/mcp/generate

Generate AI response using MCP.

**Headers:** `Authorization: Bearer <token>`

**Request Body:**
```json
{
  "context_id": "789e0123-e89b-12d3-a456-426614174002",
  "model_id": "gpt-4",
  "messages": [
    {
      "role": "user",
      "content": "Hello, how are you?"
    }
  ],
  "max_tokens": 150,
  "temperature": 0.7
}
```

**Response (200 OK):**
```json
{
  "id": "response_123",
  "context_id": "789e0123-e89b-12d3-a456-426614174002",
  "model_id": "gpt-4",
  "content": "Hello! I'm doing well, thank you for asking. How can I help you today?",
  "usage": {
    "prompt_tokens": 12,
    "completion_tokens": 18,
    "total_tokens": 30
  },
  "created_at": "2024-01-15T12:01:00Z"
}
```

## Error Responses

All endpoints may return the following error responses:

### 400 Bad Request
```json
{
  "detail": "Invalid request data"
}
```

### 401 Unauthorized
```json
{
  "detail": "Invalid or expired token"
}
```

### 403 Forbidden
```json
{
  "detail": "Insufficient permissions"
}
```

### 404 Not Found
```json
{
  "detail": "Resource not found"
}
```

### 422 Validation Error
```json
{
  "detail": [
    {
      "loc": ["body", "email"],
      "msg": "field required",
      "type": "value_error.missing"
    }
  ]
}
```

### 500 Internal Server Error
```json
{
  "detail": "Internal server error"
}
```

## Rate Limiting

API endpoints are rate limited to prevent abuse:

- **Authentication endpoints**: 5 requests per minute per IP
- **General endpoints**: 100 requests per minute per user
- **MCP generation**: 10 requests per minute per user

Rate limit headers are included in responses:
- `X-RateLimit-Limit`: Request limit per window
- `X-RateLimit-Remaining`: Remaining requests in current window
- `X-RateLimit-Reset`: Time when the rate limit resets

## Pagination

List endpoints support pagination with the following query parameters:

- `skip` (int): Number of items to skip (default: 0)
- `limit` (int): Maximum number of items to return (default: 100, max: 1000)

## Timestamps

All timestamps are in ISO 8601 format with UTC timezone:
```
2024-01-15T12:00:00Z
```
