"""
Unit tests for FolderModel.

This module contains comprehensive tests for the FolderModel
database model including domain entity conversion.
"""

import uuid
from datetime import datetime

import pytest

from src.domain.entities.folder import Folder
from src.infrastructure.database.models.folder import FolderModel


class TestFolderModel:
    """Test cases for FolderModel."""

    def test_folder_model_creation(self):
        """Test creating a FolderModel instance."""
        folder_id = uuid.uuid4()
        user_id = uuid.uuid4()
        parent_id = uuid.uuid4()
        now = datetime.now()

        folder_model = FolderModel(
            id=folder_id,
            name="Test Folder",
            parent_id=parent_id,
            user_id=user_id,
            is_archived=False,
            created_at=now,
            updated_at=now,
        )

        assert folder_model.id == folder_id
        assert folder_model.name == "Test Folder"
        assert folder_model.parent_id == parent_id
        assert folder_model.user_id == user_id
        assert folder_model.is_archived is False
        assert folder_model.created_at == now
        assert folder_model.updated_at == now

    def test_folder_model_defaults(self):
        """Test FolderModel default values."""
        user_id = uuid.uuid4()

        folder_model = FolderModel(
            name="Test Folder",
            user_id=user_id,
        )

        assert folder_model.is_archived is False
        assert folder_model.parent_id is None

    def test_to_domain_entity(self):
        """Test converting FolderModel to domain entity."""
        folder_id = uuid.uuid4()
        user_id = uuid.uuid4()
        parent_id = uuid.uuid4()
        now = datetime.now()

        folder_model = FolderModel(
            id=folder_id,
            name="Test Folder",
            parent_id=parent_id,
            user_id=user_id,
            is_archived=True,
            created_at=now,
            updated_at=now,
        )

        domain_entity = folder_model.to_domain_entity()

        assert isinstance(domain_entity, Folder)
        assert domain_entity.id == folder_id
        assert domain_entity.name == "Test Folder"
        assert domain_entity.parent_id == parent_id
        assert domain_entity.user_id == user_id
        assert domain_entity.is_archived is True
        assert domain_entity.created_at == now
        assert domain_entity.updated_at == now

    def test_from_domain_entity(self):
        """Test creating FolderModel from domain entity."""
        folder_id = uuid.uuid4()
        user_id = uuid.uuid4()
        parent_id = uuid.uuid4()
        now = datetime.now()

        domain_entity = Folder(
            id=folder_id,
            name="Test Folder",
            parent_id=parent_id,
            user_id=user_id,
            is_archived=False,
            created_at=now,
            updated_at=now,
            metadata={"key": "value"}
        )

        folder_model = FolderModel.from_domain_entity(domain_entity)

        assert folder_model.id == folder_id
        assert folder_model.name == "Test Folder"
        assert folder_model.parent_id == parent_id
        assert folder_model.user_id == user_id
        assert folder_model.is_archived is False
        assert folder_model.created_at == now
        assert folder_model.updated_at == now

    def test_round_trip_conversion(self):
        """Test converting from domain entity to model and back."""
        folder_id = uuid.uuid4()
        user_id = uuid.uuid4()
        parent_id = uuid.uuid4()
        now = datetime.now()

        original_entity = Folder(
            id=folder_id,
            name="Test Folder",
            parent_id=parent_id,
            user_id=user_id,
            is_archived=True,
            created_at=now,
            updated_at=now,
            metadata={"key": "value"}
        )

        # Convert to model and back
        folder_model = FolderModel.from_domain_entity(original_entity)
        converted_entity = folder_model.to_domain_entity()

        # Verify all fields match (except metadata which is not stored in model)
        assert converted_entity.id == original_entity.id
        assert converted_entity.name == original_entity.name
        assert converted_entity.parent_id == original_entity.parent_id
        assert converted_entity.user_id == original_entity.user_id
        assert converted_entity.is_archived == original_entity.is_archived
        assert converted_entity.created_at == original_entity.created_at
        assert converted_entity.updated_at == original_entity.updated_at

    def test_folder_model_repr(self):
        """Test FolderModel string representation."""
        folder_model = FolderModel(
            name="Test Folder",
            user_id=uuid.uuid4(),
        )

        repr_str = repr(folder_model)
        assert "FolderModel" in repr_str
        assert "Test Folder" in repr_str

    def test_none_parent_id_handling(self):
        """Test handling of None parent_id (root folder)."""
        user_id = uuid.uuid4()

        folder_model = FolderModel(
            name="Root Folder",
            user_id=user_id,
            parent_id=None,
        )

        domain_entity = folder_model.to_domain_entity()
        assert domain_entity.parent_id is None

    def test_archived_folder(self):
        """Test archived folder handling."""
        user_id = uuid.uuid4()

        folder_model = FolderModel(
            name="Archived Folder",
            user_id=user_id,
            is_archived=True,
        )

        domain_entity = folder_model.to_domain_entity()
        assert domain_entity.is_archived is True

    def test_folder_hierarchy(self):
        """Test folder hierarchy with parent-child relationship."""
        user_id = uuid.uuid4()
        parent_id = uuid.uuid4()
        child_id = uuid.uuid4()

        parent_folder = FolderModel(
            id=parent_id,
            name="Parent Folder",
            user_id=user_id,
            parent_id=None,
        )

        child_folder = FolderModel(
            id=child_id,
            name="Child Folder",
            user_id=user_id,
            parent_id=parent_id,
        )

        parent_entity = parent_folder.to_domain_entity()
        child_entity = child_folder.to_domain_entity()

        assert parent_entity.parent_id is None
        assert child_entity.parent_id == parent_id
