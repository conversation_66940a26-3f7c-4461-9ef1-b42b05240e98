# Shared Form Handling Implementation Guide

This guide provides detailed instructions for implementing a shared form handling hook to eliminate redundant form validation and submission logic across the Lonors AI Platform frontend codebase.

## Table of Contents

1. [Overview](#1-overview)
2. [Implementation Steps](#2-implementation-steps)
3. [Usage Examples](#3-usage-examples)
4. [Migration Guide](#4-migration-guide)
5. [Testing](#5-testing)
6. [Troubleshooting](#6-troubleshooting)

## 1. Overview

### Current Redundancy

Currently, similar form validation and submission logic appears across multiple features:

- `/frontend/src/features/authentication/ui/login-form.tsx`
- `/frontend/src/features/authentication/ui/register-form.tsx`
- `/frontend/src/features/agent-management/ui/agent-form.tsx`
- `/frontend/src/features/flow-builder/ui/flow-form.tsx`

### Solution

Implement a shared form handling hook that provides:

- Form state management
- Zod schema validation
- Error handling
- Submission handling
- Field change handling

## 2. Implementation Steps

### Step 1: Create the Hook

Create the shared form handling hook in the shared library:

```bash
mkdir -p frontend/src/shared/lib/hooks
touch frontend/src/shared/lib/hooks/use-form-handler.ts
```

### Step 2: Implement the Hook

Implement the `useFormHandler` hook with the following functionality:

```typescript
// /frontend/src/shared/lib/hooks/use-form-handler.ts
import { useState } from 'react';
import { ZodSchema } from 'zod';

export type FormErrors = Record<string, string>;

export interface UseFormHandlerProps<T, S extends ZodSchema<T>> {
  /**
   * Zod schema for form validation
   */
  schema: S;

  /**
   * Form submission handler
   */
  onSubmit: (values: T) => Promise<void>;

  /**
   * Initial form values
   */
  initialValues: Partial<T>;

  /**
   * Optional callback for handling validation errors
   */
  onError?: (errors: FormErrors) => void;
}

export interface UseFormHandlerResult<T> {
  /**
   * Current form values
   */
  values: Partial<T>;

  /**
   * Form validation errors
   */
  errors: FormErrors;

  /**
   * Whether the form is currently submitting
   */
  isSubmitting: boolean;

  /**
   * Whether the form has been successfully submitted
   */
  isSubmitted: boolean;

  /**
   * Handle form field change
   */
  handleChange: (field: keyof T, value: any) => void;

  /**
   * Handle form submission
   */
  handleSubmit: (e: React.FormEvent) => Promise<void>;

  /**
   * Set form values directly
   */
  setValues: React.Dispatch<React.SetStateAction<Partial<T>>>;

  /**
   * Reset form to initial values
   */
  resetForm: () => void;
}

/**
 * Custom hook for form handling with Zod validation
 */
export function useFormHandler<T, S extends ZodSchema<T>>({
  schema,
  onSubmit,
  initialValues,
  onError,
}: UseFormHandlerProps<T, S>): UseFormHandlerResult<T> {
  const [values, setValues] = useState<Partial<T>>(initialValues);
  const [errors, setErrors] = useState<FormErrors>({});
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [isSubmitted, setIsSubmitted] = useState(false);

  const handleChange = (field: keyof T, value: any) => {
    setValues((prev) => ({ ...prev, [field]: value }));
    // Clear error when field is edited
    if (errors[field as string]) {
      setErrors((prev) => {
        const newErrors = { ...prev };
        delete newErrors[field as string];
        return newErrors;
      });
    }
  };

  const resetForm = () => {
    setValues(initialValues);
    setErrors({});
    setIsSubmitted(false);
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsSubmitting(true);
    setErrors({});

    try {
      // Validate with Zod schema
      const validData = schema.parse(values);
      await onSubmit(validData);
      setIsSubmitted(true);
    } catch (error) {
      if (error instanceof Error) {
        // Handle Zod validation errors
        if (error.name === 'ZodError') {
          const zodError = error as any;
          const newErrors: FormErrors = {};

          zodError.errors.forEach((err: any) => {
            const path = err.path.join('.');
            newErrors[path] = err.message;
          });

          setErrors(newErrors);

          // Call onError callback if provided
          if (onError) {
            onError(newErrors);
          }
        } else {
          // Handle other errors
          setErrors({ form: error.message });

          if (onError) {
            onError({ form: error.message });
          }
        }
      }
    } finally {
      setIsSubmitting(false);
    }
  };

  return {
    values,
    errors,
    isSubmitting,
    isSubmitted,
    handleChange,
    handleSubmit,
    setValues,
    resetForm,
  };
}
```

### Step 3: Create a Barrel Export

Create an index file to export the hook:

```typescript
// /frontend/src/shared/lib/hooks/index.ts
export * from './use-form-handler';
// Export other hooks as needed
```

### Step 4: Add to Shared Exports

Update the shared library exports:

```typescript
// /frontend/src/shared/lib/index.ts
export * from './hooks';
// Export other utilities as needed
```

## 3. Usage Examples

### Basic Usage

```tsx
import { z } from 'zod';
import { useFormHandler } from '@/shared/lib/hooks';
import { Button, Input, FormError } from '@/shared/ui';

// Define validation schema
const loginSchema = z.object({
  email: z.string().email('Invalid email address'),
  password: z.string().min(8, 'Password must be at least 8 characters'),
});

// Define form data type
type LoginFormData = z.infer<typeof loginSchema>;

export function LoginForm() {
  const {
    values,
    errors,
    isSubmitting,
    handleChange,
    handleSubmit,
  } = useFormHandler({
    schema: loginSchema,
    initialValues: {
      email: '',
      password: '',
    },
    onSubmit: async (data) => {
      // Submit form data to API
      await loginUser(data);
      // Navigate on success
      navigate('/dashboard');
    },
  });

  return (
    <form onSubmit={handleSubmit}>
      <div className="space-y-4">
        <div>
          <label htmlFor="email">Email</label>
          <Input
            id="email"
            type="email"
            value={values.email || ''}
            onChange={(e) => handleChange('email', e.target.value)}
            error={!!errors.email}
          />
          {errors.email && <FormError>{errors.email}</FormError>}
        </div>

        <div>
          <label htmlFor="password">Password</label>
          <Input
            id="password"
            type="password"
            value={values.password || ''}
            onChange={(e) => handleChange('password', e.target.value)}
            error={!!errors.password}
          />
          {errors.password && <FormError>{errors.password}</FormError>}
        </div>

        {errors.form && (
          <div className="text-red-500">{errors.form}</div>
        )}

        <Button type="submit" disabled={isSubmitting}>
          {isSubmitting ? 'Logging in...' : 'Log in'}
        </Button>
      </div>
    </form>
  );
}
```

### Advanced Usage with Nested Fields

```tsx
import { z } from 'zod';
import { useFormHandler } from '@/shared/lib/hooks';

// Define validation schema with nested fields
const userSchema = z.object({
  name: z.object({
    first: z.string().min(2, 'First name is required'),
    last: z.string().min(2, 'Last name is required'),
  }),
  contact: z.object({
    email: z.string().email('Invalid email address'),
    phone: z.string().optional(),
  }),
});

type UserFormData = z.infer<typeof userSchema>;

export function UserForm() {
  const {
    values,
    errors,
    handleChange,
    handleSubmit,
    setValues,
  } = useFormHandler({
    schema: userSchema,
    initialValues: {
      name: { first: '', last: '' },
      contact: { email: '', phone: '' },
    },
    onSubmit: async (data) => {
      await saveUser(data);
    },
  });

  // Handle nested field changes
  const handleNestedChange = (
    parent: keyof UserFormData,
    field: string,
    value: string
  ) => {
    setValues((prev) => ({
      ...prev,
      [parent]: {
        ...prev[parent],
        [field]: value,
      },
    }));
  };

  return (
    <form onSubmit={handleSubmit}>
      {/* Form fields for nested data */}
      <input
        value={values.name?.first || ''}
        onChange={(e) => handleNestedChange('name', 'first', e.target.value)}
      />
      {errors['name.first'] && <span>{errors['name.first']}</span>}

      {/* Additional fields... */}

      <button type="submit">Submit</button>
    </form>
  );
}
```

## 4. Migration Guide

Follow these steps to migrate existing forms to use the shared hook:

### Step 1: Identify Forms to Migrate

Identify all forms that use similar validation and submission patterns.

### Step 2: Define Zod Schemas

For each form, define a Zod validation schema:

```typescript
import { z } from 'zod';

export const loginSchema = z.object({
  email: z.string().email('Invalid email address'),
  password: z.string().min(8, 'Password must be at least 8 characters'),
});

export type LoginFormData = z.infer<typeof loginSchema>;
```

### Step 3: Refactor Form Components

Refactor each form component to use the shared hook:

1. Remove local state management for form values and errors
2. Remove custom validation logic
3. Implement the `useFormHandler` hook
4. Update form fields to use the hook's methods

### Step 4: Test Thoroughly

Test each migrated form to ensure:
- Validation works correctly
- Error messages display properly
- Form submission functions as expected
- Edge cases are handled properly

## 5. Testing

### Unit Testing the Hook

Create unit tests for the `useFormHandler` hook:

```typescript
// /frontend/src/shared/lib/hooks/__tests__/use-form-handler.test.ts
import { renderHook, act } from '@testing-library/react-hooks';
import { z } from 'zod';
import { useFormHandler } from '../use-form-handler';

describe('useFormHandler', () => {
  const testSchema = z.object({
    name: z.string().min(2, 'Name is too short'),
    email: z.string().email('Invalid email'),
  });

  type TestForm = z.infer<typeof testSchema>;

  const initialValues: Partial<TestForm> = {
    name: '',
    email: '',
  };

  const onSubmitMock = jest.fn();

  beforeEach(() => {
    jest.clearAllMocks();
  });

  it('should initialize with provided values', () => {
    const { result } = renderHook(() =>
      useFormHandler({
        schema: testSchema,
        initialValues,
        onSubmit: onSubmitMock,
      })
    );

    expect(result.current.values).toEqual(initialValues);
    expect(result.current.errors).toEqual({});
    expect(result.current.isSubmitting).toBe(false);
  });

  it('should update values when handleChange is called', () => {
    const { result } = renderHook(() =>
      useFormHandler({
        schema: testSchema,
        initialValues,
        onSubmit: onSubmitMock,
      })
    );

    act(() => {
      result.current.handleChange('name', 'John');
    });

    expect(result.current.values.name).toBe('John');
  });

  it('should validate and call onSubmit with valid data', async () => {
    const { result } = renderHook(() =>
      useFormHandler({
        schema: testSchema,
        initialValues,
        onSubmit: onSubmitMock,
      })
    );

    // Set valid values
    act(() => {
      result.current.setValues({
        name: 'John',
        email: '<EMAIL>',
      });
    });

    // Submit the form
    await act(async () => {
      const mockEvent = {
        preventDefault: jest.fn(),
      } as unknown as React.FormEvent;

      await result.current.handleSubmit(mockEvent);
    });

    // Check that onSubmit was called with the correct data
    expect(onSubmitMock).toHaveBeenCalledWith({
      name: 'John',
      email: '<EMAIL>',
    });

    // Check that isSubmitted is true
    expect(result.current.isSubmitted).toBe(true);
  });

  it('should set errors for invalid data', async () => {
    const { result } = renderHook(() =>
      useFormHandler({
        schema: testSchema,
        initialValues,
        onSubmit: onSubmitMock,
      })
    );

    // Set invalid values
    act(() => {
      result.current.setValues({
        name: 'J', // Too short
        email: 'not-an-email',
      });
    });

    // Submit the form
    await act(async () => {
      const mockEvent = {
        preventDefault: jest.fn(),
      } as unknown as React.FormEvent;

      await result.current.handleSubmit(mockEvent);
    });

    // Check that onSubmit was not called
    expect(onSubmitMock).not.toHaveBeenCalled();

    // Check that errors were set
    expect(result.current.errors.name).toBeDefined();
    expect(result.current.errors.email).toBeDefined();
  });

  it('should reset the form correctly', () => {
    const { result } = renderHook(() =>
      useFormHandler({
        schema: testSchema,
        initialValues,
        onSubmit: onSubmitMock,
      })
    );

    // Change values
    act(() => {
      result.current.setValues({
        name: 'John',
        email: '<EMAIL>',
      });
    });

    // Reset the form
    act(() => {
      result.current.resetForm();
    });

    // Check that values are reset
    expect(result.current.values).toEqual(initialValues);
    expect(result.current.errors).toEqual({});
    expect(result.current.isSubmitted).toBe(false);
  });
});
```

### Integration Testing

Create integration tests for forms using the hook:

```tsx
// /frontend/src/features/authentication/ui/__tests__/login-form.test.tsx
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import { LoginForm } from '../login-form';
import { loginUser } from '../../api/auth-api';

// Mock the API function
jest.mock('../../api/auth-api', () => ({
  loginUser: jest.fn(),
}));

// Mock the navigation
const mockNavigate = jest.fn();
jest.mock('react-router-dom', () => ({
  ...jest.requireActual('react-router-dom'),
  useNavigate: () => mockNavigate,
}));

describe('LoginForm', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  it('renders the form correctly', () => {
    render(<LoginForm />);

    expect(screen.getByLabelText(/email/i)).toBeInTheDocument();
    expect(screen.getByLabelText(/password/i)).toBeInTheDocument();
    expect(screen.getByRole('button', { name: /log in/i })).toBeInTheDocument();
  });

  it('validates form inputs', async () => {
    render(<LoginForm />);

    // Submit with empty fields
    fireEvent.click(screen.getByRole('button', { name: /log in/i }));

    // Check for validation errors
    await waitFor(() => {
      expect(screen.getByText(/invalid email address/i)).toBeInTheDocument();
      expect(screen.getByText(/password must be at least 8 characters/i)).toBeInTheDocument();
    });

    // API should not be called
    expect(loginUser).not.toHaveBeenCalled();
  });

  it('submits the form with valid data', async () => {
    // Mock successful login
    (loginUser as jest.Mock).mockResolvedValue({ success: true });

    render(<LoginForm />);

    // Fill in valid data
    fireEvent.change(screen.getByLabelText(/email/i), {
      target: { value: '<EMAIL>' },
    });

    fireEvent.change(screen.getByLabelText(/password/i), {
      target: { value: 'password123' },
    });

    // Submit the form
    fireEvent.click(screen.getByRole('button', { name: /log in/i }));

    // Check that API was called with correct data
    await waitFor(() => {
      expect(loginUser).toHaveBeenCalledWith({
        email: '<EMAIL>',
        password: 'password123',
      });
    });

    // Check navigation
    expect(mockNavigate).toHaveBeenCalledWith('/dashboard');
  });

  it('handles API errors', async () => {
    // Mock API error
    (loginUser as jest.Mock).mockRejectedValue(new Error('Invalid credentials'));

    render(<LoginForm />);

    // Fill in valid data
    fireEvent.change(screen.getByLabelText(/email/i), {
      target: { value: '<EMAIL>' },
    });

    fireEvent.change(screen.getByLabelText(/password/i), {
      target: { value: 'password123' },
    });

    // Submit the form
    fireEvent.click(screen.getByRole('button', { name: /log in/i }));

    // Check for error message
    await waitFor(() => {
      expect(screen.getByText(/invalid credentials/i)).toBeInTheDocument();
    });

    // Navigation should not happen
    expect(mockNavigate).not.toHaveBeenCalled();
  });
});
```

## 6. Troubleshooting

### Common Issues and Solutions

#### Issue: Nested Field Validation Errors

**Problem**: Validation errors for nested fields are not displayed correctly.

**Solution**:
- Ensure error keys match the path format used by Zod (e.g., `name.first`)
- Use a helper function to access nested errors:

```typescript
const getNestedError = (errors: FormErrors, path: string) => {
  return errors[path] || '';
};

// Usage
{getNestedError(errors, 'name.first') && (
  <FormError>{getNestedError(errors, 'name.first')}</FormError>
)}
```

#### Issue: Form Resets on Re-render

**Problem**: Form values reset when the component re-renders.

**Solution**:
- Ensure `initialValues` doesn't change between renders
- Use `useCallback` or `useMemo` for the `onSubmit` handler

```typescript
const onSubmit = useCallback(async (data: LoginFormData) => {
  await loginUser(data);
  navigate('/dashboard');
}, [navigate]);

const {
  // ...
} = useFormHandler({
  schema: loginSchema,
  initialValues,
  onSubmit,
});
```

#### Issue: Custom Field Components

**Problem**: Integrating with custom field components is challenging.

**Solution**:
- Create adapter components that work with the form hook:

```tsx
interface FormFieldProps {
  name: string;
  label: string;
  value: string;
  onChange: (value: string) => void;
  error?: string;
  // Other props...
}

function FormField({ name, label, value, onChange, error, ...props }: FormFieldProps) {
  return (
    <div>
      <label htmlFor={name}>{label}</label>
      <Input
        id={name}
        value={value}
        onChange={(e) => onChange(e.target.value)}
        error={!!error}
        {...props}
      />
      {error && <FormError>{error}</FormError>}
    </div>
  );
}

// Usage in form
<FormField
  name="email"
  label="Email"
  value={values.email || ''}
  onChange={(value) => handleChange('email', value)}
  error={errors.email}
  type="email"
/>
```

---

By implementing this shared form handling hook, you'll eliminate redundant form logic across the Lonors AI Platform frontend codebase, improving maintainability and consistency.

*Last Updated: 2024-12-30*# Shared Form Handling Implementation Guide

This guide provides detailed instructions for implementing a shared form handling hook to eliminate redundant form validation and submission logic across the Lonors AI Platform frontend codebase.

## Table of Contents

1. [Overview](#1-overview)
2. [Implementation Steps](#2-implementation-steps)
3. [Usage Examples](#3-usage-examples)
4. [Migration Guide](#4-migration-guide)
5. [Testing](#5-testing)
6. [Troubleshooting](#6-troubleshooting)

## 1. Overview

### Current Redundancy

Currently, similar form validation and submission logic appears across multiple features:

- `/frontend/src/features/authentication/ui/login-form.tsx`
- `/frontend/src/features/authentication/ui/register-form.tsx`
- `/frontend/src/features/agent-management/ui/agent-form.tsx`
- `/frontend/src/features/flow-builder/ui/flow-form.tsx`

### Solution

Implement a shared form handling hook that provides:

- Form state management
- Zod schema validation
- Error handling
- Submission handling
- Field change handling

## 2. Implementation Steps

### Step 1: Create the Hook

Create the shared form handling hook in the shared library:

```bash
mkdir -p frontend/src/shared/lib/hooks
touch frontend/src/shared/lib/hooks/use-form-handler.ts
```

### Step 2: Implement the Hook

Implement the `useFormHandler` hook with the following functionality:

```typescript
// /frontend/src/shared/lib/hooks/use-form-handler.ts
import { useState } from 'react';
import { ZodSchema } from 'zod';

export type FormErrors = Record<string, string>;

export interface UseFormHandlerProps<T, S extends ZodSchema<T>> {
  /**
   * Zod schema for form validation
   */
  schema: S;

  /**
   * Form submission handler
   */
  onSubmit: (values: T) => Promise<void>;

  /**
   * Initial form values
   */
  initialValues: Partial<T>;

  /**
   * Optional callback for handling validation errors
   */
  onError?: (errors: FormErrors) => void;
}

export interface UseFormHandlerResult<T> {
  /**
   * Current form values
   */
  values: Partial<T>;

  /**
   * Form validation errors
   */
  errors: FormErrors;

  /**
   * Whether the form is currently submitting
   */
  isSubmitting: boolean;

  /**
   * Whether the form has been successfully submitted
   */
  isSubmitted: boolean;

  /**
   * Handle form field change
   */
  handleChange: (field: keyof T, value: any) => void;

  /**
   * Handle form submission
   */
  handleSubmit: (e: React.FormEvent) => Promise<void>;

  /**
   * Set form values directly
   */
  setValues: React.Dispatch<React.SetStateAction<Partial<T>>>;

  /**
   * Reset form to initial values
   */
  resetForm: () => void;
}

/**
 * Custom hook for form handling with Zod validation
 */
export function useFormHandler<T, S extends ZodSchema<T>>({
  schema,
  onSubmit,
  initialValues,
  onError,
}: UseFormHandlerProps<T, S>): UseFormHandlerResult<T> {
  const [values, setValues] = useState<Partial<T>>(initialValues);
  const [errors, setErrors] = useState<FormErrors>({});
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [isSubmitted, setIsSubmitted] = useState(false);

  const handleChange = (field: keyof T, value: any) => {
    setValues((prev) => ({ ...prev, [field]: value }));
    // Clear error when field is edited
    if (errors[field as string]) {
      setErrors((prev) => {
        const newErrors = { ...prev };
        delete newErrors[field as string];
        return newErrors;
      });
    }
  };

  const resetForm = () => {
    setValues(initialValues);
    setErrors({});
    setIsSubmitted(false);
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsSubmitting(true);
    setErrors({});

    try {
      // Validate with Zod schema
      const validData = schema.parse(values);
      await onSubmit(validData);
      setIsSubmitted(true);
    } catch (error) {
      if (error instanceof Error) {
        // Handle Zod validation errors
        if (error.name === 'ZodError') {
          const zodError = error as any;
          const newErrors: FormErrors = {};

          zodError.errors.forEach((err: any) => {
            const path = err.path.join('.');
            newErrors[path] = err.message;
          });

          setErrors(newErrors);

          // Call onError callback if provided
          if (onError) {
            onError(newErrors);
          }
        } else {
          // Handle other errors
          setErrors({ form: error.message });

          if (onError) {
            onError({ form: error.message });
          }
        }
      }
    } finally {
      setIsSubmitting(false);
    }
  };

  return {
    values,
    errors,
    isSubmitting,
    isSubmitted,
    handleChange,
    handleSubmit,
    setValues,
    resetForm,
  };
}
```

### Step 3: Create a Barrel Export

Create an index file to export the hook:

```typescript
// /frontend/src/shared/lib/hooks/index.ts
export * from './use-form-handler';
// Export other hooks as needed
```

### Step 4: Add to Shared Exports

Update the shared library exports:

```typescript
// /frontend/src/shared/lib/index.ts
export * from './hooks';
// Export other utilities as needed
```

## 3. Usage Examples

### Basic Usage

```tsx
import { z } from 'zod';
import { useFormHandler } from '@/shared/lib/hooks';
import { Button, Input, FormError } from '@/shared/ui';

// Define validation schema
const loginSchema = z.object({
  email: z.string().email('Invalid email address'),
  password: z.string().min(8, 'Password must be at least 8 characters'),
});

// Define form data type
type LoginFormData = z.infer<typeof loginSchema>;

export function LoginForm() {
  const {
    values,
    errors,
    isSubmitting,
    handleChange,
    handleSubmit,
  } = useFormHandler({
    schema: loginSchema,
    initialValues: {
      email: '',
      password: '',
    },
    onSubmit: async (data) => {
      // Submit form data to API
      await loginUser(data);
      // Navigate on success
      navigate('/dashboard');
    },
  });

  return (
    <form onSubmit={handleSubmit}>
      <div className="space-y-4">
        <div>
          <label htmlFor="email">Email</label>
          <Input
            id="email"
            type="email"
            value={values.email || ''}
            onChange={(e) => handleChange('email', e.target.value)}
            error={!!errors.email}
          />
          {errors.email && <FormError>{errors.email}</FormError>}
        </div>

        <div>
          <label htmlFor="password">Password</label>
          <Input
            id="password"
            type="password"
            value={values.password || ''}
            onChange={(e) => handleChange('password', e.target.value)}
            error={!!errors.password}
          />
          {errors.password && <FormError>{errors.password}</FormError>}
        </div>

        {errors.form && (
          <div className="text-red-500">{errors.form}</div>
        )}

        <Button type="submit" disabled={isSubmitting}>
          {isSubmitting ? 'Logging in...' : 'Log in'}
        </Button>
      </div>
    </form>
  );
}
```

### Advanced Usage with Nested Fields

```tsx
import { z } from 'zod';
import { useFormHandler } from '@/shared/lib/hooks';

// Define validation schema with nested fields
const userSchema = z.object({
  name: z.object({
    first: z.string().min(2, 'First name is required'),
    last: z.string().min(2, 'Last name is required'),
  }),
  contact: z.object({
    email: z.string().email('Invalid email address'),
    phone: z.string().optional(),
  }),
});

type UserFormData = z.infer<typeof userSchema>;

export function UserForm() {
  const {
    values,
    errors,
    handleChange,
    handleSubmit,
    setValues,
  } = useFormHandler({
    schema: userSchema,
    initialValues: {
      name: { first: '', last: '' },
      contact: { email: '', phone: '' },
    },
    onSubmit: async (data) => {
      await saveUser(data);
    },
  });

  // Handle nested field changes
  const handleNestedChange = (
    parent: keyof UserFormData,
    field: string,
    value: string
  ) => {
    setValues((prev) => ({
      ...prev,
      [parent]: {
        ...prev[parent],
        [field]: value,
      },
    }));
  };

  return (
    <form onSubmit={handleSubmit}>
      {/* Form fields for nested data */}
      <input
        value={values.name?.first || ''}
        onChange={(e) => handleNestedChange('name', 'first', e.target.value)}
      />
      {errors['name.first'] && <span>{errors['name.first']}</span>}

      {/* Additional fields... */}

      <button type="submit">Submit</button>
    </form>
  );
}
```

## 4. Migration Guide

Follow these steps to migrate existing forms to use the shared hook:

### Step 1: Identify Forms to Migrate

Identify all forms that use similar validation and submission patterns.

### Step 2: Define Zod Schemas

For each form, define a Zod validation schema:

```typescript
import { z } from 'zod';

export const loginSchema = z.object({
  email: z.string().email('Invalid email address'),
  password: z.string().min(8, 'Password must be at least 8 characters'),
});

export type LoginFormData = z.infer<typeof loginSchema>;
```

### Step 3: Refactor Form Components

Refactor each form component to use the shared hook:

1. Remove local state management for form values and errors
2. Remove custom validation logic
3. Implement the `useFormHandler` hook
4. Update form fields to use the hook's methods

### Step 4: Test Thoroughly

Test each migrated form to ensure:
- Validation works correctly
- Error messages display properly
- Form submission functions as expected
- Edge cases are handled properly

## 5. Testing

### Unit Testing the Hook

Create unit tests for the `useFormHandler` hook:

```typescript
// /frontend/src/shared/lib/hooks/__tests__/use-form-handler.test.ts
import { renderHook, act } from '@testing-library/react-hooks';
import { z } from 'zod';
import { useFormHandler } from '../use-form-handler';

describe('useFormHandler', () => {
  const testSchema = z.object({
    name: z.string().min(2, 'Name is too short'),
    email: z.string().email('Invalid email'),
  });

  type TestForm = z.infer<typeof testSchema>;

  const initialValues: Partial<TestForm> = {
    name: '',
    email: '',
  };

  const onSubmitMock = jest.fn();

  beforeEach(() => {
    jest.clearAllMocks();
  });

  it('should initialize with provided values', () => {
    const { result } = renderHook(() =>
      useFormHandler({
        schema: testSchema,
        initialValues,
        onSubmit: onSubmitMock,
      })
    );

    expect(result.current.values).toEqual(initialValues);
    expect(result.current.errors).toEqual({});
    expect(result.current.isSubmitting).toBe(false);
  });

  it('should update values when handleChange is called', () => {
    const { result } = renderHook(() =>
      useFormHandler({
        schema: testSchema,
        initialValues,
        onSubmit: onSubmitMock,
      })
    );

    act(() => {
      result.current.handleChange('name', 'John');
    });

    expect(result.current.values.name).toBe('John');
  });

  it('should validate and call onSubmit with valid data', async () => {
    const { result } = renderHook(() =>
      useFormHandler({
        schema: testSchema,
        initialValues,
        onSubmit: onSubmitMock,
      })
    );

    // Set valid values
    act(() => {
      result.current.setValues({
        name: 'John',
        email: '<EMAIL>',
      });
    });

    // Submit the form
    await act(async () => {
      const mockEvent = {
        preventDefault: jest.fn(),
      } as unknown as React.FormEvent;

      await result.current.handleSubmit(mockEvent);
    });

    // Check that onSubmit was called with the correct data
    expect(onSubmitMock).toHaveBeenCalledWith({
      name: 'John',
      email: '<EMAIL>',
    });

    // Check that isSubmitted is true
    expect(result.current.isSubmitted).toBe(true);
  });

  it('should set errors for invalid data', async () => {
    const { result } = renderHook(() =>
      useFormHandler({
        schema: testSchema,
        initialValues,
        onSubmit: onSubmitMock,
      })
    );

    // Set invalid values
    act(() => {
      result.current.setValues({
        name: 'J', // Too short
        email: 'not-an-email',
      });
    });

    // Submit the form
    await act(async () => {
      const mockEvent = {
        preventDefault: jest.fn(),
      } as unknown as React.FormEvent;

      await result.current.handleSubmit(mockEvent);
    });

    // Check that onSubmit was not called
    expect(onSubmitMock).not.toHaveBeenCalled();

    // Check that errors were set
    expect(result.current.errors.name).toBeDefined();
    expect(result.current.errors.email).toBeDefined();
  });

  it('should reset the form correctly', () => {
    const { result } = renderHook(() =>
      useFormHandler({
        schema: testSchema,
        initialValues,
        onSubmit: onSubmitMock,
      })
    );

    // Change values
    act(() => {
      result.current.setValues({
        name: 'John',
        email: '<EMAIL>',
      });
    });

    // Reset the form
    act(() => {
      result.current.resetForm();
    });

    // Check that values are reset
    expect(result.current.values).toEqual(initialValues);
    expect(result.current.errors).toEqual({});
    expect(result.current.isSubmitted).toBe(false);
  });
});
```

### Integration Testing

Create integration tests for forms using the hook:

```tsx
// /frontend/src/features/authentication/ui/__tests__/login-form.test.tsx
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import { LoginForm } from '../login-form';
import { loginUser } from '../../api/auth-api';

// Mock the API function
jest.mock('../../api/auth-api', () => ({
  loginUser: jest.fn(),
}));

// Mock the navigation
const mockNavigate = jest.fn();
jest.mock('react-router-dom', () => ({
  ...jest.requireActual('react-router-dom'),
  useNavigate: () => mockNavigate,
}));

describe('LoginForm', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  it('renders the form correctly', () => {
    render(<LoginForm />);

    expect(screen.getByLabelText(/email/i)).toBeInTheDocument();
    expect(screen.getByLabelText(/password/i)).toBeInTheDocument();
    expect(screen.getByRole('button', { name: /log in/i })).toBeInTheDocument();
  });

  it('validates form inputs', async () => {
    render(<LoginForm />);

    // Submit with empty fields
    fireEvent.click(screen.getByRole('button', { name: /log in/i }));

    // Check for validation errors
    await waitFor(() => {
      expect(screen.getByText(/invalid email address/i)).toBeInTheDocument();
      expect(screen.getByText(/password must be at least 8 characters/i)).toBeInTheDocument();
    });

    // API should not be called
    expect(loginUser).not.toHaveBeenCalled();
  });

  it('submits the form with valid data', async () => {
    // Mock successful login
    (loginUser as jest.Mock).mockResolvedValue({ success: true });

    render(<LoginForm />);

    // Fill in valid data
    fireEvent.change(screen.getByLabelText(/email/i), {
      target: { value: '<EMAIL>' },
    });

    fireEvent.change(screen.getByLabelText(/password/i), {
      target: { value: 'password123' },
    });

    // Submit the form
    fireEvent.click(screen.getByRole('button', { name: /log in/i }));

    // Check that API was called with correct data
    await waitFor(() => {
      expect(loginUser).toHaveBeenCalledWith({
        email: '<EMAIL>',
        password: 'password123',
      });
    });

    // Check navigation
    expect(mockNavigate).toHaveBeenCalledWith('/dashboard');
  });

  it('handles API errors', async () => {
    // Mock API error
    (loginUser as jest.Mock).mockRejectedValue(new Error('Invalid credentials'));

    render(<LoginForm />);

    // Fill in valid data
    fireEvent.change(screen.getByLabelText(/email/i), {
      target: { value: '<EMAIL>' },
    });

    fireEvent.change(screen.getByLabelText(/password/i), {
      target: { value: 'password123' },
    });

    // Submit the form
    fireEvent.click(screen.getByRole('button', { name: /log in/i }));

    // Check for error message
    await waitFor(() => {
      expect(screen.getByText(/invalid credentials/i)).toBeInTheDocument();
    });

    // Navigation should not happen
    expect(mockNavigate).not.toHaveBeenCalled();
  });
});
```

## 6. Troubleshooting

### Common Issues and Solutions

#### Issue: Nested Field Validation Errors

**Problem**: Validation errors for nested fields are not displayed correctly.

**Solution**:
- Ensure error keys match the path format used by Zod (e.g., `name.first`)
- Use a helper function to access nested errors:

```typescript
const getNestedError = (errors: FormErrors, path: string) => {
  return errors[path] || '';
};

// Usage
{getNestedError(errors, 'name.first') && (
  <FormError>{getNestedError(errors, 'name.first')}</FormError>
)}
```

#### Issue: Form Resets on Re-render

**Problem**: Form values reset when the component re-renders.

**Solution**:
- Ensure `initialValues` doesn't change between renders
- Use `useCallback` or `useMemo` for the `onSubmit` handler

```typescript
const onSubmit = useCallback(async (data: LoginFormData) => {
  await loginUser(data);
  navigate('/dashboard');
}, [navigate]);

const {
  // ...
} = useFormHandler({
  schema: loginSchema,
  initialValues,
  onSubmit,
});
```

#### Issue: Custom Field Components

**Problem**: Integrating with custom field components is challenging.

**Solution**:
- Create adapter components that work with the form hook:

```tsx
interface FormFieldProps {
  name: string;
  label: string;
  value: string;
  onChange: (value: string) => void;
  error?: string;
  // Other props...
}

function FormField({ name, label, value, onChange, error, ...props }: FormFieldProps) {
  return (
    <div>
      <label htmlFor={name}>{label}</label>
      <Input
        id={name}
        value={value}
        onChange={(e) => onChange(e.target.value)}
        error={!!error}
        {...props}
      />
      {error && <FormError>{error}</FormError>}
    </div>
  );
}

// Usage in form
<FormField
  name="email"
  label="Email"
  value={values.email || ''}
  onChange={(value) => handleChange('email', value)}
  error={errors.email}
  type="email"
/>
```

---

By implementing this shared form handling hook, you'll eliminate redundant form logic across the Lonors AI Platform frontend codebase, improving maintainability and consistency.

*Last Updated: 2024-12-30*
