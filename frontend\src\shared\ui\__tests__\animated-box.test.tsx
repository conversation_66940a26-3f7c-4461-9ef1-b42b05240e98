import { render, screen, waitFor } from '@testing-library/react';
import { afterEach, beforeEach, describe, expect, it, vi } from 'vitest';

// Mock the useAnime hook using factory function
vi.mock('@/shared/lib/hooks/useAnime', () => ({
  useAnime: vi.fn(),
}));

// Mock the animations module
vi.mock('@/shared/lib/animations', () => ({
  ANIMATION_PRESETS: {
    fadeIn: { opacity: [0, 1], duration: 300 },
    slideUp: { translateY: [20, 0], opacity: [0, 1], duration: 400 },
    slideDown: { translateY: [-20, 0], opacity: [0, 1], duration: 400 },
    slideLeft: { translateX: [20, 0], opacity: [0, 1], duration: 400 },
    slideRight: { translateX: [-20, 0], opacity: [0, 1], duration: 400 },
    scaleIn: { scale: [0.8, 1], opacity: [0, 1], duration: 300 },
    bounceIn: { scale: [0.3, 1.05, 0.9, 1], opacity: [0, 1], duration: 600 },
  },
}));

import { AnimatedBox } from '../animated-box';

describe('AnimatedBox', () => {
  const mockRef = { current: null };
  let mockUseAnime: any;

  beforeEach(async () => {
    // Get the mocked useAnime function
    const useAnimeModule = await import('@/shared/lib/hooks/useAnime');
    mockUseAnime = useAnimeModule.useAnime;

    mockUseAnime.mockReturnValue(mockRef);
  });

  afterEach(() => {
    vi.clearAllMocks();
  });

  describe('Basic Rendering', () => {
    it('renders children correctly', () => {
      render(
        <AnimatedBox>
          <div data-testid="child">Test Content</div>
        </AnimatedBox>
      );

      expect(screen.getByTestId('child')).toBeInTheDocument();
      expect(screen.getByText('Test Content')).toBeInTheDocument();
    });

    it('applies default opacity-0 class', () => {
      render(
        <AnimatedBox data-testid="animated-box">
          <div>Content</div>
        </AnimatedBox>
      );

      const box = screen.getByTestId('animated-box');
      expect(box).toHaveClass('opacity-0');
    });

    it('merges custom className with default classes', () => {
      render(
        <AnimatedBox className="custom-class" data-testid="animated-box">
          <div>Content</div>
        </AnimatedBox>
      );

      const box = screen.getByTestId('animated-box');
      expect(box).toHaveClass('opacity-0', 'custom-class');
    });

    it('passes through HTML attributes', () => {
      render(
        <AnimatedBox
          data-testid="animated-box"
          id="test-id"
          role="banner"
          aria-label="Test animation"
        >
          <div>Content</div>
        </AnimatedBox>
      );

      const box = screen.getByTestId('animated-box');
      expect(box).toHaveAttribute('id', 'test-id');
      expect(box).toHaveAttribute('role', 'banner');
      expect(box).toHaveAttribute('aria-label', 'Test animation');
    });
  });

  describe('Animation Configuration', () => {
    it('uses default animation config when none provided', () => {
      render(
        <AnimatedBox>
          <div>Content</div>
        </AnimatedBox>
      );

      expect(mockUseAnime).toHaveBeenCalledWith(
        { opacity: [0, 1], translateY: [20, 0] },
        []
      );
    });

    it('uses custom animation config when provided', () => {
      const customConfig = {
        opacity: [0, 1],
        scale: [0.8, 1],
        duration: 500,
      };

      render(
        <AnimatedBox animationConfig={customConfig}>
          <div>Content</div>
        </AnimatedBox>
      );

      expect(mockUseAnime).toHaveBeenCalledWith(customConfig, []);
    });

    it('uses animation preset when provided', () => {
      render(
        <AnimatedBox animationPreset="fadeIn">
          <div>Content</div>
        </AnimatedBox>
      );

      expect(mockUseAnime).toHaveBeenCalledWith(
        { preset: 'fadeIn' },
        []
      );
    });

    it('merges preset with custom config', () => {
      const customConfig = { duration: 800 };

      render(
        <AnimatedBox
          animationPreset="slideUp"
          animationConfig={customConfig}
        >
          <div>Content</div>
        </AnimatedBox>
      );

      expect(mockUseAnime).toHaveBeenCalledWith(
        { preset: 'slideUp', duration: 800 },
        []
      );
    });

    it('uses custom animation dependencies', () => {
      const deps = ['dep1', 'dep2'];

      render(
        <AnimatedBox animationDeps={deps}>
          <div>Content</div>
        </AnimatedBox>
      );

      expect(mockUseAnime).toHaveBeenCalledWith(
        { opacity: [0, 1], translateY: [20, 0] },
        deps
      );
    });
  });

  describe('Memoization', () => {
    it('memoizes animation config to prevent unnecessary re-renders', () => {
      const { rerender } = render(
        <AnimatedBox animationPreset="fadeIn">
          <div>Content</div>
        </AnimatedBox>
      );

      // Clear the mock to check subsequent calls
      mockUseAnime.mockClear();

      // Re-render with same props
      rerender(
        <AnimatedBox animationPreset="fadeIn">
          <div>Content</div>
        </AnimatedBox>
      );

      // Should use memoized config
      expect(mockUseAnime).toHaveBeenCalledTimes(1);
    });

    it('updates memoized config when preset changes', () => {
      const { rerender } = render(
        <AnimatedBox animationPreset="fadeIn">
          <div>Content</div>
        </AnimatedBox>
      );

      rerender(
        <AnimatedBox animationPreset="slideUp">
          <div>Content</div>
        </AnimatedBox>
      );

      expect(mockUseAnime).toHaveBeenLastCalledWith(
        { preset: 'slideUp' },
        []
      );
    });
  });

  describe('Accessibility', () => {
    it('maintains accessibility attributes', () => {
      render(
        <AnimatedBox
          role="region"
          aria-label="Animated content"
          data-testid="animated-box"
        >
          <div>Accessible content</div>
        </AnimatedBox>
      );

      const box = screen.getByTestId('animated-box');
      expect(box).toHaveAttribute('role', 'region');
      expect(box).toHaveAttribute('aria-label', 'Animated content');
    });

    it('does not interfere with child accessibility', () => {
      render(
        <AnimatedBox>
          <button aria-label="Action button">Click me</button>
        </AnimatedBox>
      );

      const button = screen.getByRole('button');
      expect(button).toHaveAttribute('aria-label', 'Action button');
    });
  });

  describe('Performance', () => {
    it('handles rapid re-renders without errors', async () => {
      const { rerender } = render(
        <AnimatedBox animationPreset="fadeIn">
          <div>Content 1</div>
        </AnimatedBox>
      );

      // Simulate rapid re-renders
      for (let i = 0; i < 10; i++) {
        rerender(
          <AnimatedBox animationPreset="fadeIn">
            <div>Content {i}</div>
          </AnimatedBox>
        );
      }

      await waitFor(() => {
        expect(screen.getByText('Content 9')).toBeInTheDocument();
      });
    });
  });

  describe('Error Handling', () => {
    it('handles missing animation preset gracefully', () => {
      render(
        <AnimatedBox animationPreset={'nonexistent' as any}>
          <div>Content</div>
        </AnimatedBox>
      );

      expect(mockUseAnime).toHaveBeenCalledWith(
        { preset: 'nonexistent' },
        []
      );
    });

    it('handles undefined animation config', () => {
      render(
        <AnimatedBox animationConfig={undefined}>
          <div>Content</div>
        </AnimatedBox>
      );

      expect(mockUseAnime).toHaveBeenCalledWith(
        { opacity: [0, 1], translateY: [20, 0] },
        []
      );
    });
  });

  describe('Component Display Name', () => {
    it('has correct display name for debugging', () => {
      expect(AnimatedBox.displayName).toBe('AnimatedBox');
    });
  });
});
