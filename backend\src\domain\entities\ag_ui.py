"""
AG-UI Protocol domain entities.

This module defines the domain entities for AG-UI protocol
implementation including dynamic UI components and state management.
"""

import uuid
from datetime import datetime, timezone
from enum import Enum
from typing import Any, Dict, List, Optional

from pydantic import BaseModel, Field


class ComponentType(str, Enum):
    """UI component types."""
    BUTTON = "button"
    INPUT = "input"
    SELECT = "select"
    TEXTAREA = "textarea"
    CHECKBOX = "checkbox"
    RADIO = "radio"
    SLIDER = "slider"
    TOGGLE = "toggle"
    CARD = "card"
    MODAL = "modal"
    FORM = "form"
    TABLE = "table"
    CHART = "chart"
    CUSTOM = "custom"


class EventType(str, Enum):
    """UI event types."""
    CLICK = "click"
    CHANGE = "change"
    INPUT = "input"
    SUBMIT = "submit"
    FOCUS = "focus"
    BLUR = "blur"
    HOVER = "hover"
    SCROLL = "scroll"
    RESIZE = "resize"
    CUSTOM = "custom"


class ComponentState(str, Enum):
    """Component state enumeration."""
    ACTIVE = "active"
    INACTIVE = "inactive"
    DISABLED = "disabled"
    LOADING = "loading"
    ERROR = "error"


class AGUIComponent(BaseModel):
    """
    AG-UI Component entity.
    
    Represents a dynamic UI component in the AG-UI protocol.
    """
    
    id: str = Field(..., description="Unique component identifier")
    type: ComponentType = Field(..., description="Component type")
    parent_id: Optional[str] = Field(None, description="Parent component ID")
    name: str = Field(..., description="Component name")
    label: Optional[str] = Field(None, description="Component label")
    value: Any = Field(None, description="Component value")
    state: ComponentState = Field(default=ComponentState.ACTIVE, description="Component state")
    properties: Dict[str, Any] = Field(default_factory=dict, description="Component properties")
    styles: Dict[str, Any] = Field(default_factory=dict, description="Component styles")
    events: List[EventType] = Field(default_factory=list, description="Supported events")
    validation: Dict[str, Any] = Field(default_factory=dict, description="Validation rules")
    metadata: Dict[str, Any] = Field(default_factory=dict, description="Component metadata")
    created_at: datetime = Field(default_factory=lambda: datetime.now(timezone.utc))
    updated_at: datetime = Field(default_factory=lambda: datetime.now(timezone.utc))
    
    class Config:
        """Pydantic configuration."""
        use_enum_values = True
        json_encoders = {
            datetime: lambda v: v.isoformat(),
        }
    
    def update_value(self, value: Any) -> None:
        """Update component value."""
        self.value = value
        self.updated_at = datetime.now(timezone.utc)
    
    def update_state(self, state: ComponentState) -> None:
        """Update component state."""
        self.state = state
        self.updated_at = datetime.now(timezone.utc)
    
    def update_properties(self, properties: Dict[str, Any]) -> None:
        """Update component properties."""
        self.properties.update(properties)
        self.updated_at = datetime.now(timezone.utc)


class AGUILayout(BaseModel):
    """
    AG-UI Layout entity.
    
    Represents a layout containing multiple components.
    """
    
    id: uuid.UUID = Field(default_factory=uuid.uuid4, description="Unique layout identifier")
    user_id: uuid.UUID = Field(..., description="User who owns this layout")
    name: str = Field(..., description="Layout name")
    description: Optional[str] = Field(None, description="Layout description")
    components: List[AGUIComponent] = Field(default_factory=list, description="Layout components")
    layout_config: Dict[str, Any] = Field(default_factory=dict, description="Layout configuration")
    metadata: Dict[str, Any] = Field(default_factory=dict, description="Layout metadata")
    created_at: datetime = Field(default_factory=lambda: datetime.now(timezone.utc))
    updated_at: datetime = Field(default_factory=lambda: datetime.now(timezone.utc))
    
    class Config:
        """Pydantic configuration."""
        json_encoders = {
            datetime: lambda v: v.isoformat(),
            uuid.UUID: lambda v: str(v),
        }
    
    def add_component(self, component: AGUIComponent) -> None:
        """Add component to layout."""
        self.components.append(component)
        self.updated_at = datetime.now(timezone.utc)
    
    def remove_component(self, component_id: str) -> bool:
        """Remove component from layout."""
        for i, component in enumerate(self.components):
            if component.id == component_id:
                del self.components[i]
                self.updated_at = datetime.now(timezone.utc)
                return True
        return False
    
    def get_component(self, component_id: str) -> Optional[AGUIComponent]:
        """Get component by ID."""
        for component in self.components:
            if component.id == component_id:
                return component
        return None
    
    def update_component(self, component_id: str, updates: Dict[str, Any]) -> bool:
        """Update component in layout."""
        component = self.get_component(component_id)
        if component:
            if "value" in updates:
                component.update_value(updates["value"])
            if "state" in updates:
                component.update_state(ComponentState(updates["state"]))
            if "properties" in updates:
                component.update_properties(updates["properties"])
            self.updated_at = datetime.now(timezone.utc)
            return True
        return False


class AGUIEvent(BaseModel):
    """
    AG-UI Event entity.
    
    Represents an event triggered by a UI component.
    """
    
    id: uuid.UUID = Field(default_factory=uuid.uuid4, description="Unique event identifier")
    session_id: uuid.UUID = Field(..., description="Session ID")
    component_id: str = Field(..., description="Component that triggered the event")
    event_type: EventType = Field(..., description="Event type")
    data: Dict[str, Any] = Field(default_factory=dict, description="Event data")
    timestamp: datetime = Field(default_factory=lambda: datetime.now(timezone.utc))
    
    class Config:
        """Pydantic configuration."""
        use_enum_values = True
        json_encoders = {
            datetime: lambda v: v.isoformat(),
            uuid.UUID: lambda v: str(v),
        }


class AGUISession(BaseModel):
    """
    AG-UI Session entity.
    
    Represents an active AG-UI session with state synchronization.
    """
    
    id: uuid.UUID = Field(default_factory=uuid.uuid4, description="Unique session identifier")
    user_id: uuid.UUID = Field(..., description="User ID")
    layout_id: Optional[uuid.UUID] = Field(None, description="Current layout ID")
    connection_id: str = Field(..., description="WebSocket connection ID")
    state: Dict[str, Any] = Field(default_factory=dict, description="Session state")
    metadata: Dict[str, Any] = Field(default_factory=dict, description="Session metadata")
    created_at: datetime = Field(default_factory=lambda: datetime.now(timezone.utc))
    last_activity: datetime = Field(default_factory=lambda: datetime.now(timezone.utc))
    
    class Config:
        """Pydantic configuration."""
        json_encoders = {
            datetime: lambda v: v.isoformat(),
            uuid.UUID: lambda v: str(v),
        }
    
    def update_activity(self) -> None:
        """Update last activity timestamp."""
        self.last_activity = datetime.now(timezone.utc)
    
    def update_state(self, state_updates: Dict[str, Any]) -> None:
        """Update session state."""
        self.state.update(state_updates)
        self.update_activity()


# Request/Response schemas
class AGUIComponentCreate(BaseModel):
    """Schema for creating AG-UI component."""
    
    type: ComponentType = Field(..., description="Component type")
    parent_id: Optional[str] = Field(None, description="Parent component ID")
    name: str = Field(..., description="Component name")
    label: Optional[str] = Field(None, description="Component label")
    value: Any = Field(None, description="Initial component value")
    properties: Dict[str, Any] = Field(default_factory=dict, description="Component properties")
    styles: Dict[str, Any] = Field(default_factory=dict, description="Component styles")
    events: List[EventType] = Field(default_factory=list, description="Supported events")
    validation: Dict[str, Any] = Field(default_factory=dict, description="Validation rules")
    metadata: Dict[str, Any] = Field(default_factory=dict, description="Component metadata")


class AGUIComponentUpdate(BaseModel):
    """Schema for updating AG-UI component."""
    
    value: Optional[Any] = Field(None, description="Component value")
    state: Optional[ComponentState] = Field(None, description="Component state")
    properties: Optional[Dict[str, Any]] = Field(None, description="Component properties")
    styles: Optional[Dict[str, Any]] = Field(None, description="Component styles")
    metadata: Optional[Dict[str, Any]] = Field(None, description="Component metadata")


class AGUILayoutCreate(BaseModel):
    """Schema for creating AG-UI layout."""
    
    name: str = Field(..., description="Layout name")
    description: Optional[str] = Field(None, description="Layout description")
    components: List[AGUIComponentCreate] = Field(default_factory=list, description="Initial components")
    layout_config: Dict[str, Any] = Field(default_factory=dict, description="Layout configuration")
    metadata: Dict[str, Any] = Field(default_factory=dict, description="Layout metadata")


class AGUILayoutUpdate(BaseModel):
    """Schema for updating AG-UI layout."""
    
    name: Optional[str] = Field(None, description="Layout name")
    description: Optional[str] = Field(None, description="Layout description")
    layout_config: Optional[Dict[str, Any]] = Field(None, description="Layout configuration")
    metadata: Optional[Dict[str, Any]] = Field(None, description="Layout metadata")


class AGUIEventRequest(BaseModel):
    """Schema for AG-UI event request."""
    
    component_id: str = Field(..., description="Component ID")
    event_type: EventType = Field(..., description="Event type")
    data: Dict[str, Any] = Field(default_factory=dict, description="Event data")


class AGUIStateUpdate(BaseModel):
    """Schema for AG-UI state update."""
    
    component_updates: Dict[str, AGUIComponentUpdate] = Field(
        default_factory=dict, description="Component updates"
    )
    layout_updates: Optional[AGUILayoutUpdate] = Field(None, description="Layout updates")
    session_state: Dict[str, Any] = Field(default_factory=dict, description="Session state updates")


# Response schemas
class AGUIComponentResponse(BaseModel):
    """Schema for AG-UI component response."""
    
    id: str
    type: ComponentType
    parent_id: Optional[str]
    name: str
    label: Optional[str]
    value: Any
    state: ComponentState
    properties: Dict[str, Any]
    styles: Dict[str, Any]
    events: List[EventType]
    validation: Dict[str, Any]
    metadata: Dict[str, Any]
    created_at: datetime
    updated_at: datetime
    
    class Config:
        """Pydantic configuration."""
        from_attributes = True
        use_enum_values = True
        json_encoders = {
            datetime: lambda v: v.isoformat(),
        }


class AGUILayoutResponse(BaseModel):
    """Schema for AG-UI layout response."""
    
    id: uuid.UUID
    user_id: uuid.UUID
    name: str
    description: Optional[str]
    components: List[AGUIComponentResponse]
    layout_config: Dict[str, Any]
    metadata: Dict[str, Any]
    created_at: datetime
    updated_at: datetime
    
    class Config:
        """Pydantic configuration."""
        from_attributes = True
        json_encoders = {
            datetime: lambda v: v.isoformat(),
            uuid.UUID: lambda v: str(v),
        }


class AGUISessionResponse(BaseModel):
    """Schema for AG-UI session response."""
    
    id: uuid.UUID
    user_id: uuid.UUID
    layout_id: Optional[uuid.UUID]
    connection_id: str
    state: Dict[str, Any]
    metadata: Dict[str, Any]
    created_at: datetime
    last_activity: datetime
    
    class Config:
        """Pydantic configuration."""
        from_attributes = True
        json_encoders = {
            datetime: lambda v: v.isoformat(),
            uuid.UUID: lambda v: str(v),
        }
