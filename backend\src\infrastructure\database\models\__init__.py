"""
Database models package.

This package contains SQLAlchemy models for all database entities.
"""

from src.infrastructure.database.models.base import Base
from src.infrastructure.database.models.folder import FolderModel
from src.infrastructure.database.models.note import NoteModel
from src.infrastructure.database.models.tag import TagModel
from src.infrastructure.database.models.user import UserModel

# Import all models here to ensure they are registered with SQLAlchemy
__all__ = ["Base", "FolderModel", "NoteModel", "TagModel", "UserModel"]
