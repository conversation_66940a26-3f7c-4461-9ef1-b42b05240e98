"""
TDD Tests for User DTOs - RED PHASE

This module contains comprehensive tests for User Data Transfer Objects
following TDD methodology. These tests will initially fail and drive
the implementation of the application DTOs.
"""

import pytest
from datetime import datetime, UTC
from uuid import uuid4
from typing import Optional

# These imports will fail initially - this is the RED phase
try:
    from src.application.dto.user_dto import (
        CreateUserDTO,
        UpdateUserDTO,
        UserResponseDTO,
        LoginDTO,
        LoginResponseDTO,
        ChangePasswordDTO,
        PaginatedUsersDTO,
    )
    from src.domain.exceptions import DomainValidationError
except ImportError:
    # Expected during RED phase
    pass


class TestCreateUserDTO:
    """Test suite for CreateUserDTO."""

    @pytest.mark.unit
    def test_create_user_dto_valid_data(self):
        """Test creating CreateUserDTO with valid data."""
        # RED PHASE: This test will fail because CreateUserDTO doesn't exist yet
        dto = CreateUserDTO(
            email="<EMAIL>",
            username="testuser",
            full_name="Test User",
            password="StrongP@ssw0rd!",
        )

        assert dto.email == "<EMAIL>"
        assert dto.username == "testuser"
        assert dto.full_name == "Test User"
        assert dto.password == "StrongP@ssw0rd!"

    @pytest.mark.unit
    def test_create_user_dto_invalid_email(self):
        """Test CreateUserDTO validation fails with invalid email."""
        # RED PHASE: This test will fail because validation doesn't exist yet
        with pytest.raises(DomainValidationError, match="Invalid email"):
            CreateUserDTO(
                email="invalid-email",
                username="testuser",
                full_name="Test User",
                password="StrongP@ssw0rd!",
            )

    @pytest.mark.unit
    def test_create_user_dto_invalid_username(self):
        """Test CreateUserDTO validation fails with invalid username."""
        # RED PHASE: This test will fail because validation doesn't exist yet
        with pytest.raises(DomainValidationError, match="Invalid username"):
            CreateUserDTO(
                email="<EMAIL>",
                username="ab",  # Too short
                full_name="Test User",
                password="StrongP@ssw0rd!",
            )

    @pytest.mark.unit
    def test_create_user_dto_invalid_password(self):
        """Test CreateUserDTO validation fails with weak password."""
        # RED PHASE: This test will fail because validation doesn't exist yet
        with pytest.raises(DomainValidationError, match="Password does not meet"):
            CreateUserDTO(
                email="<EMAIL>",
                username="testuser",
                full_name="Test User",
                password="weak",  # Too weak
            )

    @pytest.mark.unit
    def test_create_user_dto_empty_full_name(self):
        """Test CreateUserDTO validation fails with empty full name."""
        # RED PHASE: This test will fail because validation doesn't exist yet
        with pytest.raises(DomainValidationError, match="Full name cannot be empty"):
            CreateUserDTO(
                email="<EMAIL>",
                username="testuser",
                full_name="",  # Empty
                password="StrongP@ssw0rd!",
            )


class TestUpdateUserDTO:
    """Test suite for UpdateUserDTO."""

    @pytest.mark.unit
    def test_update_user_dto_valid_data(self):
        """Test creating UpdateUserDTO with valid data."""
        # RED PHASE: This test will fail because UpdateUserDTO doesn't exist yet
        dto = UpdateUserDTO(full_name="Updated Name")

        assert dto.full_name == "Updated Name"

    @pytest.mark.unit
    def test_update_user_dto_optional_fields(self):
        """Test UpdateUserDTO with optional fields."""
        # RED PHASE: This test will fail because optional handling doesn't exist yet
        dto = UpdateUserDTO()

        assert dto.full_name is None

    @pytest.mark.unit
    def test_update_user_dto_invalid_full_name(self):
        """Test UpdateUserDTO validation fails with invalid full name."""
        # RED PHASE: This test will fail because validation doesn't exist yet
        with pytest.raises(DomainValidationError, match="Full name cannot be empty"):
            UpdateUserDTO(full_name="")


class TestUserResponseDTO:
    """Test suite for UserResponseDTO."""

    @pytest.mark.unit
    def test_user_response_dto_creation(self):
        """Test creating UserResponseDTO."""
        # RED PHASE: This test will fail because UserResponseDTO doesn't exist yet
        user_id = str(uuid4())
        created_at = datetime.now(UTC)
        updated_at = datetime.now(UTC)

        dto = UserResponseDTO(
            id=user_id,
            email="<EMAIL>",
            username="testuser",
            full_name="Test User",
            is_active=True,
            is_verified=True,
            status="active",
            created_at=created_at,
            updated_at=updated_at,
        )

        assert dto.id == user_id
        assert dto.email == "<EMAIL>"
        assert dto.username == "testuser"
        assert dto.full_name == "Test User"
        assert dto.is_active is True
        assert dto.is_verified is True
        assert dto.status == "active"
        assert dto.created_at == created_at
        assert dto.updated_at == updated_at

    @pytest.mark.unit
    def test_user_response_dto_from_entity(self):
        """Test creating UserResponseDTO from domain entity."""
        # RED PHASE: This test will fail because from_entity method doesn't exist yet
        from src.domain.entities.user import User
        from src.domain.value_objects.email import Email
        from src.domain.value_objects.username import Username

        user = User(
            id=str(uuid4()),
            email=Email("<EMAIL>"),
            username=Username("testuser"),
            full_name="Test User",
            hashed_password="hashed_password",
            is_active=True,
            is_verified=True,
        )

        dto = UserResponseDTO.from_entity(user)

        assert dto.id == user.id
        assert dto.email == user.email.value
        assert dto.username == user.username.value
        assert dto.full_name == user.full_name
        assert dto.is_active == user.is_active
        assert dto.is_verified == user.is_verified


class TestLoginDTO:
    """Test suite for LoginDTO."""

    @pytest.mark.unit
    def test_login_dto_valid_data(self):
        """Test creating LoginDTO with valid data."""
        # RED PHASE: This test will fail because LoginDTO doesn't exist yet
        dto = LoginDTO(
            email="<EMAIL>",
            password="StrongP@ssw0rd!",
        )

        assert dto.email == "<EMAIL>"
        assert dto.password == "StrongP@ssw0rd!"

    @pytest.mark.unit
    def test_login_dto_invalid_email(self):
        """Test LoginDTO validation fails with invalid email."""
        # RED PHASE: This test will fail because validation doesn't exist yet
        with pytest.raises(DomainValidationError, match="Invalid email"):
            LoginDTO(
                email="invalid-email",
                password="StrongP@ssw0rd!",
            )


class TestLoginResponseDTO:
    """Test suite for LoginResponseDTO."""

    @pytest.mark.unit
    def test_login_response_dto_creation(self):
        """Test creating LoginResponseDTO."""
        # RED PHASE: This test will fail because LoginResponseDTO doesn't exist yet
        user_dto = UserResponseDTO(
            id=str(uuid4()),
            email="<EMAIL>",
            username="testuser",
            full_name="Test User",
            is_active=True,
            is_verified=True,
            status="active",
            created_at=datetime.now(UTC),
            updated_at=datetime.now(UTC),
        )

        dto = LoginResponseDTO(
            access_token="access_token",
            refresh_token="refresh_token",
            token_type="bearer",
            expires_in=3600,
            user=user_dto,
        )

        assert dto.access_token == "access_token"
        assert dto.refresh_token == "refresh_token"
        assert dto.token_type == "bearer"
        assert dto.expires_in == 3600
        assert dto.user == user_dto


class TestChangePasswordDTO:
    """Test suite for ChangePasswordDTO."""

    @pytest.mark.unit
    def test_change_password_dto_valid_data(self):
        """Test creating ChangePasswordDTO with valid data."""
        # RED PHASE: This test will fail because ChangePasswordDTO doesn't exist yet
        dto = ChangePasswordDTO(
            current_password="OldP@ssw0rd!",
            new_password="NewP@ssw0rd!",
        )

        assert dto.current_password == "OldP@ssw0rd!"
        assert dto.new_password == "NewP@ssw0rd!"

    @pytest.mark.unit
    def test_change_password_dto_invalid_new_password(self):
        """Test ChangePasswordDTO validation fails with weak new password."""
        # RED PHASE: This test will fail because validation doesn't exist yet
        with pytest.raises(DomainValidationError, match="Password does not meet"):
            ChangePasswordDTO(
                current_password="OldP@ssw0rd!",
                new_password="weak",  # Too weak
            )


class TestPaginatedUsersDTO:
    """Test suite for PaginatedUsersDTO."""

    @pytest.mark.unit
    def test_paginated_users_dto_creation(self):
        """Test creating PaginatedUsersDTO."""
        # RED PHASE: This test will fail because PaginatedUsersDTO doesn't exist yet
        user_dto = UserResponseDTO(
            id=str(uuid4()),
            email="<EMAIL>",
            username="testuser",
            full_name="Test User",
            is_active=True,
            is_verified=True,
            status="active",
            created_at=datetime.now(UTC),
            updated_at=datetime.now(UTC),
        )

        dto = PaginatedUsersDTO(
            items=[user_dto],
            total=1,
            page=1,
            size=10,
            pages=1,
        )

        assert len(dto.items) == 1
        assert dto.total == 1
        assert dto.page == 1
        assert dto.size == 10
        assert dto.pages == 1
        assert dto.items[0] == user_dto

    @pytest.mark.unit
    def test_paginated_users_dto_empty_list(self):
        """Test creating PaginatedUsersDTO with empty list."""
        # RED PHASE: This test will fail because empty handling doesn't exist yet
        dto = PaginatedUsersDTO(
            items=[],
            total=0,
            page=1,
            size=10,
            pages=0,
        )

        assert len(dto.items) == 0
        assert dto.total == 0
        assert dto.pages == 0

    @pytest.mark.unit
    def test_paginated_users_dto_pagination_calculation(self):
        """Test PaginatedUsersDTO pagination calculations."""
        # RED PHASE: This test will fail because calculation methods don't exist yet
        dto = PaginatedUsersDTO.create(
            items=[],
            total=25,
            page=2,
            size=10,
        )

        assert dto.total == 25
        assert dto.page == 2
        assert dto.size == 10
        assert dto.pages == 3  # ceil(25/10)
        assert dto.has_next is True
        assert dto.has_previous is True
