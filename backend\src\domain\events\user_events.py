"""
User domain events for the Lonors application.

This module contains all user-related domain events that are raised
when important user actions occur in the system.
"""

from .base import DomainEvent


class UserActivatedEvent(DomainEvent):
    """Event raised when a user is activated."""

    def __init__(self, user_id: str) -> None:
        """
        Initialize user activated event.

        Args:
            user_id: ID of the activated user
        """
        super().__init__(user_id=user_id)

    @property
    def user_id(self) -> str:
        """Get the user ID."""
        return self.data["user_id"]


class UserEmailVerifiedEvent(DomainEvent):
    """Event raised when a user's email is verified."""

    def __init__(self, user_id: str, email: str) -> None:
        """
        Initialize user email verified event.

        Args:
            user_id: ID of the user whose email was verified
            email: Email address that was verified
        """
        super().__init__(user_id=user_id, email=email)

    @property
    def user_id(self) -> str:
        """Get the user ID."""
        return self.data["user_id"]

    @property
    def email(self) -> str:
        """Get the verified email."""
        return self.data["email"]


class UserDeactivatedEvent(DomainEvent):
    """Event raised when a user is deactivated."""

    def __init__(self, user_id: str) -> None:
        """
        Initialize user deactivated event.

        Args:
            user_id: ID of the deactivated user
        """
        super().__init__(user_id=user_id)

    @property
    def user_id(self) -> str:
        """Get the user ID."""
        return self.data["user_id"]


class UserProfileUpdatedEvent(DomainEvent):
    """Event raised when a user's profile is updated."""

    def __init__(self, user_id: str, updated_fields: dict) -> None:
        """
        Initialize user profile updated event.

        Args:
            user_id: ID of the user whose profile was updated
            updated_fields: Dictionary of fields that were updated
        """
        super().__init__(user_id=user_id, updated_fields=updated_fields)

    @property
    def user_id(self) -> str:
        """Get the user ID."""
        return self.data["user_id"]

    @property
    def updated_fields(self) -> dict:
        """Get the updated fields."""
        return self.data["updated_fields"]


class UserPasswordChangedEvent(DomainEvent):
    """Event raised when a user's password is changed."""

    def __init__(self, user_id: str) -> None:
        """
        Initialize user password changed event.

        Args:
            user_id: ID of the user whose password was changed
        """
        super().__init__(user_id=user_id)

    @property
    def user_id(self) -> str:
        """Get the user ID."""
        return self.data["user_id"]
