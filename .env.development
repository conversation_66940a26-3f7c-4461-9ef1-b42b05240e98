# Lonors Development Environment Configuration
# Docker Compose environment variables for development

# =============================================================================
# PROJECT CONFIGURATION
# =============================================================================
COMPOSE_PROJECT_NAME=lonors-dev
COMPOSE_FILE=docker-compose.yml
DOCKER_BUILDKIT=1
COMPOSE_DOCKER_CLI_BUILD=1

# =============================================================================
# DATABASE CONFIGURATION (PostgreSQL)
# =============================================================================
POSTGRES_DB=lonors_db
POSTGRES_USER=lonors_user
POSTGRES_PASSWORD=lonors_dev_password_2024
POSTGRES_HOST=postgres
POSTGRES_PORT=5432

# Database connection URL
DATABASE_URL=postgresql+asyncpg://lonors_user:lonors_dev_password_2024@postgres:5432/lonors_db

# =============================================================================
# CACHE CONFIGURATION (DragonflyDB)
# =============================================================================
REDIS_HOST=dragonflydb
REDIS_PORT=6379
REDIS_PASSWORD=dragonfly_dev_password_2024
REDIS_URL=redis://:dragonfly_dev_password_2024@dragonflydb:6379

# =============================================================================
# BACKEND CONFIGURATION (Python FastAPI)
# =============================================================================
ENVIRONMENT=development
DEBUG=true
LOG_LEVEL=debug

# Security
JWT_SECRET=dev_jwt_secret_key_change_in_production_2024
JWT_ALGORITHM=HS256
JWT_EXPIRES_IN=7d

# Server configuration
BACKEND_HOST=0.0.0.0
BACKEND_PORT=3001
BACKEND_WORKERS=1

# CORS configuration
CORS_ORIGINS=http://localhost:5500,http://127.0.0.1:5500,http://frontend:5500

# API configuration
API_V1_PREFIX=/api/v1
API_TITLE=Lonors Backend API
API_VERSION=1.0.0
API_DESCRIPTION=Lonors AI Agent Platform Backend

# =============================================================================
# FRONTEND CONFIGURATION (React + Vite)
# =============================================================================
NODE_ENV=development

# Vite configuration
VITE_API_URL=http://localhost:3001
VITE_API_BASE_URL=http://localhost:3001/api/v1
VITE_APP_NAME=Lonors
VITE_APP_VERSION=1.0.0
VITE_ENVIRONMENT=development

# Development server configuration
VITE_DEV_SERVER_HOST=0.0.0.0
VITE_DEV_SERVER_PORT=5500
VITE_DEV_SERVER_OPEN=false

# Hot reload optimization
CHOKIDAR_USEPOLLING=false
WATCHPACK_POLLING=false

# =============================================================================
# MONITORING CONFIGURATION
# =============================================================================
PROMETHEUS_PORT=9090
GRAFANA_PORT=3000

# =============================================================================
# DOCKER RESOURCE LIMITS (Development)
# =============================================================================
# PostgreSQL
POSTGRES_CPU_LIMIT=1
POSTGRES_MEMORY_LIMIT=1g
POSTGRES_CPU_RESERVATION=0.25
POSTGRES_MEMORY_RESERVATION=256m

# DragonflyDB
DRAGONFLY_CPU_LIMIT=0.5
DRAGONFLY_MEMORY_LIMIT=512m
DRAGONFLY_CPU_RESERVATION=0.1
DRAGONFLY_MEMORY_RESERVATION=128m

# Backend
BACKEND_CPU_LIMIT=1
BACKEND_MEMORY_LIMIT=1g
BACKEND_CPU_RESERVATION=0.25
BACKEND_MEMORY_RESERVATION=256m

# Frontend
FRONTEND_CPU_LIMIT=1
FRONTEND_MEMORY_LIMIT=1g
FRONTEND_CPU_RESERVATION=0.25
FRONTEND_MEMORY_RESERVATION=256m

# =============================================================================
# DEVELOPMENT TOOLS CONFIGURATION
# =============================================================================
# Enable development profiles
COMPOSE_PROFILES=monitoring

# Volume mount optimization
VOLUME_MOUNT_TYPE=cached

# =============================================================================
# NETWORK CONFIGURATION
# =============================================================================
NETWORK_SUBNET=172.20.0.0/16
NETWORK_GATEWAY=172.20.0.1

# =============================================================================
# HEALTH CHECK CONFIGURATION
# =============================================================================
HEALTH_CHECK_INTERVAL=30s
HEALTH_CHECK_TIMEOUT=10s
HEALTH_CHECK_RETRIES=3
HEALTH_CHECK_START_PERIOD=60s

# =============================================================================
# LOGGING CONFIGURATION
# =============================================================================
LOG_FORMAT=json
LOG_ROTATION=daily
LOG_MAX_SIZE=100m
LOG_MAX_FILES=7

# =============================================================================
# DEVELOPMENT FLAGS
# =============================================================================
# Enable hot reload
HOT_RELOAD=true

# Enable debug mode
DEBUG_MODE=true

# Enable verbose logging
VERBOSE_LOGGING=true

# Enable development tools
DEV_TOOLS=true

# =============================================================================
# TESTING CONFIGURATION
# =============================================================================
# Test database
TEST_DATABASE_URL=postgresql+asyncpg://lonors_user:lonors_dev_password_2024@postgres:5432/lonors_test_db

# Test Redis
TEST_REDIS_URL=redis://:dragonfly_dev_password_2024@dragonflydb:6379/1

# Test configuration
PYTEST_WORKERS=auto
COVERAGE_THRESHOLD=90

# =============================================================================
# AI/ML CONFIGURATION (Future)
# =============================================================================
# Placeholder for AI model configuration
OLLAMA_HOST=localhost
OLLAMA_PORT=11434
HUGGINGFACE_CACHE_DIR=/app/.cache/huggingface

# =============================================================================
# PROTOCOL CONFIGURATION (Future)
# =============================================================================
# MCP Protocol
MCP_WEBSOCKET_URL=ws://localhost:3001/ws/mcp

# A2A Protocol
A2A_WEBSOCKET_URL=ws://localhost:3001/ws/a2a

# AG-UI Protocol
AG_UI_WEBSOCKET_URL=ws://localhost:3001/ws/ag-ui
