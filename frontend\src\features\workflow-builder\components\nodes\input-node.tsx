'use client';

import React, { memo, useCallback } from 'react';
import { <PERSON><PERSON>, Position, NodeProps } from 'reactflow';
import { cn } from '@/shared/lib/utils';
import { Badge } from '@/shared/ui/badge';
import { Button } from '@/shared/ui/button';
import { NodeData, PortType } from '../../types';
import {
  ArrowRight,
  Settings,
  Type,
  Hash,
  ToggleLeft,
  FileText,
  Image,
  Music,
  Video,
  Database,
  User,
} from 'lucide-react';

interface InputNodeData extends NodeData {
  inputType: PortType;
  inputName: string;
  defaultValue?: any;
  required?: boolean;
  placeholder?: string;
  validation?: {
    min?: number;
    max?: number;
    pattern?: string;
  };
  currentValue?: any;
}

interface InputNodeProps extends NodeProps {
  data: InputNodeData;
}

// Input type icon mapping
const getInputTypeIcon = (type: PortType) => {
  switch (type) {
    case PortType.STRING:
      return Type;
    case PortType.NUMBER:
      return Hash;
    case PortType.BOOLEAN:
      return ToggleLeft;
    case PortType.FILE:
      return FileText;
    case PortType.IMAGE:
      return Image;
    case PortType.AUDIO:
      return Music;
    case PortType.VIDEO:
      return Video;
    case PortType.OBJECT:
    case PortType.ARRAY:
      return Database;
    case PortType.AGENT:
      return User;
    default:
      return Type;
  }
};

// Input type color mapping
const getInputTypeColor = (type: PortType) => {
  switch (type) {
    case PortType.STRING:
      return 'text-blue-600 bg-blue-50 border-blue-200';
    case PortType.NUMBER:
      return 'text-green-600 bg-green-50 border-green-200';
    case PortType.BOOLEAN:
      return 'text-purple-600 bg-purple-50 border-purple-200';
    case PortType.FILE:
      return 'text-orange-600 bg-orange-50 border-orange-200';
    case PortType.IMAGE:
      return 'text-pink-600 bg-pink-50 border-pink-200';
    case PortType.AUDIO:
      return 'text-indigo-600 bg-indigo-50 border-indigo-200';
    case PortType.VIDEO:
      return 'text-red-600 bg-red-50 border-red-200';
    case PortType.OBJECT:
    case PortType.ARRAY:
      return 'text-gray-600 bg-gray-50 border-gray-200';
    case PortType.AGENT:
      return 'text-cyan-600 bg-cyan-50 border-cyan-200';
    default:
      return 'text-slate-600 bg-slate-50 border-slate-200';
  }
};

// Format value for display
const formatValue = (value: any, type: PortType): string => {
  if (value === null || value === undefined) return 'No value';
  
  switch (type) {
    case PortType.STRING:
      return `"${value}"`;
    case PortType.NUMBER:
      return value.toString();
    case PortType.BOOLEAN:
      return value ? 'true' : 'false';
    case PortType.OBJECT:
      return JSON.stringify(value, null, 2).substring(0, 50) + '...';
    case PortType.ARRAY:
      return `Array(${value.length})`;
    default:
      return value.toString();
  }
};

export const InputNode = memo<InputNodeProps>(({ 
  data, 
  selected, 
  dragging,
  id 
}) => {
  const { 
    inputType,
    inputName,
    defaultValue,
    required = false,
    placeholder,
    validation,
    currentValue
  } = data;

  const InputIcon = getInputTypeIcon(inputType);
  const inputColorClass = getInputTypeColor(inputType);
  const hasValue = currentValue !== undefined && currentValue !== null;

  const handleConfigure = useCallback((e: React.MouseEvent) => {
    e.stopPropagation();
    console.log('Configure input:', inputName);
  }, [inputName]);

  return (
    <div
      className={cn(
        'relative bg-background border-2 rounded-lg shadow-sm transition-all duration-200',
        'min-w-[200px] max-w-[260px]',
        selected && 'border-primary ring-2 ring-primary/20',
        dragging && 'shadow-lg scale-105',
        hasValue && 'border-blue-400 shadow-blue-100'
      )}
    >
      {/* Output Handle */}
      <Handle
        type="source"
        position={Position.Right}
        className="w-3 h-3 border-2 border-background bg-muted-foreground hover:bg-primary transition-colors"
        style={{ right: -6 }}
      />

      {/* Status indicator overlay */}
      <div className={cn(
        'absolute top-0 left-0 w-full h-1 rounded-t-lg transition-all duration-300',
        hasValue && 'bg-gradient-to-r from-blue-400 to-blue-600',
        !hasValue && 'bg-gradient-to-r from-slate-300 to-slate-400'
      )} />

      {/* Header */}
      <div className="p-3">
        <div className="flex items-start justify-between mb-2">
          <div className="flex items-center space-x-2 min-w-0 flex-1">
            <div className={cn(
              'p-1.5 rounded-md border',
              inputColorClass
            )}>
              <InputIcon className="h-4 w-4" />
            </div>
            <div className="min-w-0 flex-1">
              <h3 className="font-semibold text-sm truncate">
                {inputName}
              </h3>
              <div className="flex items-center gap-1 mt-1">
                <Badge variant="outline" className="text-xs">
                  {inputType}
                </Badge>
                {required && (
                  <Badge variant="destructive" className="text-xs">
                    Required
                  </Badge>
                )}
              </div>
            </div>
          </div>
          
          <ArrowRight className="h-4 w-4 text-muted-foreground" />
        </div>

        {data.description && (
          <p className="text-xs text-muted-foreground line-clamp-2 mb-2">
            {data.description}
          </p>
        )}

        {/* Current Value Display */}
        <div className="mb-3">
          <div className="text-xs text-muted-foreground mb-1">Current Value:</div>
          <div className={cn(
            'text-sm font-mono bg-muted p-2 rounded border',
            hasValue ? 'border-blue-200 bg-blue-50' : 'border-dashed'
          )}>
            {hasValue ? (
              <span className="text-blue-800">
                {formatValue(currentValue, inputType)}
              </span>
            ) : (
              <span className="text-muted-foreground italic">
                {placeholder || `Enter ${inputType} value`}
              </span>
            )}
          </div>
        </div>

        {/* Default Value */}
        {defaultValue !== undefined && (
          <div className="mb-3">
            <div className="text-xs text-muted-foreground mb-1">Default:</div>
            <div className="text-sm font-mono bg-muted p-1 rounded text-muted-foreground">
              {formatValue(defaultValue, inputType)}
            </div>
          </div>
        )}

        {/* Validation Rules */}
        {validation && (
          <div className="mb-3">
            <div className="text-xs text-muted-foreground mb-1">Validation:</div>
            <div className="flex flex-wrap gap-1">
              {validation.min !== undefined && (
                <Badge variant="outline" className="text-xs">
                  Min: {validation.min}
                </Badge>
              )}
              {validation.max !== undefined && (
                <Badge variant="outline" className="text-xs">
                  Max: {validation.max}
                </Badge>
              )}
              {validation.pattern && (
                <Badge variant="outline" className="text-xs">
                  Pattern
                </Badge>
              )}
            </div>
          </div>
        )}

        {/* Controls */}
        <div className="flex gap-1">
          <Button size="sm" variant="outline" onClick={handleConfigure} className="flex-1">
            <Settings className="h-3 w-3 mr-1" />
            Configure
          </Button>
        </div>
      </div>

      {/* Value indicator overlay */}
      {hasValue && (
        <div className="absolute inset-0 bg-gradient-to-br from-blue-500/5 to-blue-600/10 rounded-lg pointer-events-none" />
      )}

      {/* Required indicator */}
      {required && !hasValue && (
        <div className="absolute -top-2 -right-2 bg-red-500 text-white text-xs px-2 py-1 rounded-full">
          Required
        </div>
      )}
    </div>
  );
});

InputNode.displayName = 'InputNode';
