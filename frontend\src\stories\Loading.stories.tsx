import type { <PERSON><PERSON>, StoryObj } from '@storybook/nextjs';
import { 
  LoadingSpinner, 
  LoadingOverlay, 
  LoadingSkeleton, 
  LoadingCard, 
  LoadingPage 
} from '@/shared/ui/loading';
import { Button } from '@/shared/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/shared/ui/card';

const meta: Meta<typeof LoadingSpinner> = {
  title: 'UI/Loading',
  component: LoadingSpinner,
  parameters: {
    layout: 'centered',
  },
  tags: ['autodocs'],
};

export default meta;
type Story = StoryObj<typeof meta>;

export const Spinner: Story = {
  render: () => (
    <div className="space-y-4">
      <div className="flex items-center space-x-4">
        <LoadingSpinner size="sm" />
        <span>Small</span>
      </div>
      <div className="flex items-center space-x-4">
        <LoadingSpinner size="md" />
        <span>Medium</span>
      </div>
      <div className="flex items-center space-x-4">
        <LoadingSpinner size="lg" />
        <span>Large</span>
      </div>
    </div>
  ),
};

export const Overlay: Story = {
  render: () => (
    <LoadingOverlay isLoading={true} message="Loading data...">
      <Card className="w-80">
        <CardHeader>
          <CardTitle>Sample Content</CardTitle>
        </CardHeader>
        <CardContent>
          <p>This content is behind a loading overlay.</p>
          <Button className="mt-4">Click me</Button>
        </CardContent>
      </Card>
    </LoadingOverlay>
  ),
};

export const Skeleton: Story = {
  render: () => (
    <div className="space-y-6">
      <LoadingSkeleton lines={3} />
      <LoadingSkeleton lines={2} avatar={true} />
      <LoadingSkeleton lines={4} />
    </div>
  ),
};

export const LoadingCards: Story = {
  render: () => (
    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
      <LoadingCard />
      <LoadingCard showAvatar={true} />
      <LoadingCard showActions={true} />
      <LoadingCard showAvatar={true} showActions={true} />
    </div>
  ),
};

export const Page: Story = {
  render: () => <LoadingPage message="Loading application..." />,
};
