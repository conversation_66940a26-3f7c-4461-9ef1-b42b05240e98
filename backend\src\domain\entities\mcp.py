"""
Model Context Protocol (MCP) domain entities.

This module defines the domain entities for MCP protocol
implementation including context management and model operations.
"""

import uuid
from datetime import datetime, timezone
from enum import Enum
from typing import Any, Dict, List, Optional

from pydantic import BaseModel, Field


class ModelType(str, Enum):
    """Supported model types."""
    TEXT_GENERATION = "text_generation"
    CODE_GENERATION = "code_generation"
    CHAT_COMPLETION = "chat_completion"
    EMBEDDING = "embedding"
    IMAGE_GENERATION = "image_generation"


class ModelStatus(str, Enum):
    """Model status enumeration."""
    AVAILABLE = "available"
    BUSY = "busy"
    OFFLINE = "offline"
    ERROR = "error"


class ContextType(str, Enum):
    """Context type enumeration."""
    CONVERSATION = "conversation"
    CODE_SESSION = "code_session"
    DOCUMENT = "document"
    TASK = "task"


class MCPModel(BaseModel):
    """
    MCP Model entity.
    
    Represents an AI model available through the MCP protocol.
    """
    
    id: str = Field(..., description="Unique model identifier")
    name: str = Field(..., description="Human-readable model name")
    type: ModelType = Field(..., description="Model type")
    provider: str = Field(..., description="Model provider (e.g., OpenAI, Anthropic)")
    version: str = Field(..., description="Model version")
    status: ModelStatus = Field(default=ModelStatus.AVAILABLE, description="Current model status")
    max_context_length: int = Field(default=4096, description="Maximum context length")
    capabilities: List[str] = Field(default_factory=list, description="Model capabilities")
    metadata: Dict[str, Any] = Field(default_factory=dict, description="Additional model metadata")
    
    class Config:
        """Pydantic configuration."""
        use_enum_values = True


class MCPContext(BaseModel):
    """
    MCP Context entity.
    
    Represents a context session for model interactions.
    """
    
    id: uuid.UUID = Field(default_factory=uuid.uuid4, description="Unique context identifier")
    user_id: uuid.UUID = Field(..., description="User who owns this context")
    type: ContextType = Field(..., description="Context type")
    title: str = Field(..., description="Context title")
    description: Optional[str] = Field(None, description="Context description")
    model_id: str = Field(..., description="Associated model ID")
    max_length: int = Field(default=4096, description="Maximum context length")
    current_length: int = Field(default=0, description="Current context length")
    metadata: Dict[str, Any] = Field(default_factory=dict, description="Context metadata")
    created_at: datetime = Field(default_factory=lambda: datetime.now(timezone.utc))
    updated_at: datetime = Field(default_factory=lambda: datetime.now(timezone.utc))
    expires_at: Optional[datetime] = Field(None, description="Context expiration time")
    
    class Config:
        """Pydantic configuration."""
        use_enum_values = True
        json_encoders = {
            datetime: lambda v: v.isoformat(),
            uuid.UUID: lambda v: str(v),
        }
    
    def is_expired(self) -> bool:
        """Check if context is expired."""
        if self.expires_at is None:
            return False
        return datetime.now(timezone.utc) > self.expires_at
    
    def is_full(self) -> bool:
        """Check if context is at maximum capacity."""
        return self.current_length >= self.max_length
    
    def add_content_length(self, length: int) -> None:
        """Add to current content length."""
        self.current_length += length
        self.updated_at = datetime.now(timezone.utc)


class MCPMessage(BaseModel):
    """
    MCP Message entity.
    
    Represents a message within an MCP context.
    """
    
    id: uuid.UUID = Field(default_factory=uuid.uuid4, description="Unique message identifier")
    context_id: uuid.UUID = Field(..., description="Associated context ID")
    role: str = Field(..., description="Message role (user, assistant, system)")
    content: str = Field(..., description="Message content")
    content_length: int = Field(..., description="Content length in tokens")
    metadata: Dict[str, Any] = Field(default_factory=dict, description="Message metadata")
    created_at: datetime = Field(default_factory=lambda: datetime.now(timezone.utc))
    
    class Config:
        """Pydantic configuration."""
        json_encoders = {
            datetime: lambda v: v.isoformat(),
            uuid.UUID: lambda v: str(v),
        }


class MCPRequest(BaseModel):
    """Schema for MCP API requests."""
    
    context_id: Optional[uuid.UUID] = Field(None, description="Context ID for continuation")
    model_id: str = Field(..., description="Model to use")
    messages: List[Dict[str, str]] = Field(..., description="Messages to process")
    max_tokens: Optional[int] = Field(None, description="Maximum tokens to generate")
    temperature: Optional[float] = Field(None, ge=0.0, le=2.0, description="Sampling temperature")
    stream: bool = Field(default=False, description="Whether to stream response")
    metadata: Dict[str, Any] = Field(default_factory=dict, description="Request metadata")


class MCPResponse(BaseModel):
    """Schema for MCP API responses."""
    
    id: str = Field(..., description="Response ID")
    context_id: uuid.UUID = Field(..., description="Context ID")
    model_id: str = Field(..., description="Model used")
    content: str = Field(..., description="Generated content")
    usage: Dict[str, int] = Field(..., description="Token usage statistics")
    metadata: Dict[str, Any] = Field(default_factory=dict, description="Response metadata")
    created_at: datetime = Field(default_factory=lambda: datetime.now(timezone.utc))
    
    class Config:
        """Pydantic configuration."""
        json_encoders = {
            datetime: lambda v: v.isoformat(),
            uuid.UUID: lambda v: str(v),
        }


class MCPContextCreate(BaseModel):
    """Schema for creating MCP context."""
    
    type: ContextType = Field(..., description="Context type")
    title: str = Field(..., description="Context title")
    description: Optional[str] = Field(None, description="Context description")
    model_id: str = Field(..., description="Associated model ID")
    max_length: Optional[int] = Field(None, description="Maximum context length")
    expires_in_hours: Optional[int] = Field(None, description="Context expiration in hours")
    metadata: Dict[str, Any] = Field(default_factory=dict, description="Context metadata")


class MCPContextUpdate(BaseModel):
    """Schema for updating MCP context."""
    
    title: Optional[str] = Field(None, description="Context title")
    description: Optional[str] = Field(None, description="Context description")
    metadata: Dict[str, Any] = Field(default_factory=dict, description="Context metadata")


class MCPContextResponse(BaseModel):
    """Schema for MCP context response."""
    
    id: uuid.UUID
    user_id: uuid.UUID
    type: ContextType
    title: str
    description: Optional[str]
    model_id: str
    max_length: int
    current_length: int
    metadata: Dict[str, Any]
    created_at: datetime
    updated_at: datetime
    expires_at: Optional[datetime]
    is_expired: bool
    is_full: bool
    
    class Config:
        """Pydantic configuration."""
        from_attributes = True
        use_enum_values = True
        json_encoders = {
            datetime: lambda v: v.isoformat(),
            uuid.UUID: lambda v: str(v),
        }


class MCPModelResponse(BaseModel):
    """Schema for MCP model response."""
    
    id: str
    name: str
    type: ModelType
    provider: str
    version: str
    status: ModelStatus
    max_context_length: int
    capabilities: List[str]
    metadata: Dict[str, Any]
    
    class Config:
        """Pydantic configuration."""
        from_attributes = True
        use_enum_values = True
