import { describe, expect, it } from 'vitest';
import {
    cn,
    debounce,
    formatDate,
    formatFileSize,
    generateId,
    throttle
} from '../utils';

describe('utils', () => {
  describe('cn (className utility)', () => {
    it('merges class names correctly', () => {
      expect(cn('class1', 'class2')).toBe('class1 class2');
    });

    it('handles conditional classes', () => {
      expect(cn('base', true && 'conditional', false && 'hidden')).toBe('base conditional');
    });

    it('handles undefined and null values', () => {
      expect(cn('base', undefined, null, 'valid')).toBe('base valid');
    });

    it('handles empty strings', () => {
      expect(cn('base', '', 'valid')).toBe('base valid');
    });

    it('handles arrays of classes', () => {
      expect(cn(['class1', 'class2'], 'class3')).toBe('class1 class2 class3');
    });

    it('handles objects with boolean values', () => {
      expect(cn({
        'active': true,
        'disabled': false,
        'visible': true
      })).toBe('active visible');
    });

    it('returns empty string for no arguments', () => {
      expect(cn()).toBe('');
    });
  });

  describe('formatDate', () => {
    it('formats date correctly', () => {
      const date = new Date('2024-01-15T10:30:00Z');
      const formatted = formatDate(date);
      expect(formatted).toMatch(/January 15, 2024/);
    });

    it('handles string dates', () => {
      const formatted = formatDate('2024-01-15');
      expect(formatted).toMatch(/January 15, 2024/);
    });

    it('handles invalid dates', () => {
      expect(() => formatDate('invalid-date')).not.toThrow();
    });

    it('returns consistent format', () => {
      const date = new Date('2024-01-15T10:30:00Z');
      const formatted = formatDate(date);
      expect(typeof formatted).toBe('string');
      expect(formatted.length).toBeGreaterThan(0);
    });
  });

  describe('formatFileSize', () => {
    it('formats bytes correctly', () => {
      expect(formatFileSize(1024)).toBe('1 KB');
      expect(formatFileSize(1048576)).toBe('1 MB');
      expect(formatFileSize(1073741824)).toBe('1 GB');
    });

    it('handles zero bytes', () => {
      expect(formatFileSize(0)).toBe('0 Bytes');
    });

    it('handles small byte values', () => {
      expect(formatFileSize(512)).toBe('512 Bytes');
    });

    it('handles decimal precision', () => {
      expect(formatFileSize(1536)).toBe('1.5 KB');
    });

    it('handles very large files', () => {
      expect(formatFileSize(1099511627776)).toBe('1 TB');
    });
  });

  describe('debounce', () => {
    it('delays function execution', async () => {
      let callCount = 0;
      const debouncedFn = debounce(() => callCount++, 100);

      debouncedFn();
      debouncedFn();
      debouncedFn();

      expect(callCount).toBe(0);

      await new Promise(resolve => setTimeout(resolve, 150));
      expect(callCount).toBe(1);
    });

    it('cancels previous calls', async () => {
      let callCount = 0;
      const debouncedFn = debounce(() => callCount++, 100);

      debouncedFn();
      setTimeout(() => debouncedFn(), 50);
      setTimeout(() => debouncedFn(), 75);

      // Wait longer to ensure the debounced function executes
      await new Promise(resolve => setTimeout(resolve, 300));
      expect(callCount).toBe(1);
    });

    it('passes arguments correctly', async () => {
      let receivedArgs: any[] = [];
      const debouncedFn = debounce((...args: any[]) => {
        receivedArgs = args;
      }, 50);

      debouncedFn('test', 123, { key: 'value' });

      await new Promise(resolve => setTimeout(resolve, 100));
      expect(receivedArgs).toEqual(['test', 123, { key: 'value' }]);
    });
  });

  describe('throttle', () => {
    it('limits function calls', async () => {
      let callCount = 0;
      const throttledFn = throttle(() => callCount++, 100);

      throttledFn();
      throttledFn();
      throttledFn();

      expect(callCount).toBe(1);

      await new Promise(resolve => setTimeout(resolve, 150));
      throttledFn();
      expect(callCount).toBe(2);
    });

    it('executes immediately on first call', () => {
      let callCount = 0;
      const throttledFn = throttle(() => callCount++, 100);

      throttledFn();
      expect(callCount).toBe(1);
    });
  });

  describe('generateId', () => {
    it('generates unique IDs', () => {
      const id1 = generateId();
      const id2 = generateId();

      expect(id1).not.toBe(id2);
      expect(typeof id1).toBe('string');
      expect(typeof id2).toBe('string');
    });

    it('generates IDs of consistent length', () => {
      const id1 = generateId();
      const id2 = generateId();

      expect(id1.length).toBe(id2.length);
      expect(id1.length).toBeGreaterThan(0);
    });

    it('generates alphanumeric IDs', () => {
      const id = generateId();
      expect(id).toMatch(/^[a-zA-Z0-9-]+$/);
    });

    it('handles multiple calls efficiently', () => {
      const ids = Array.from({ length: 100 }, () => generateId());
      const uniqueIds = new Set(ids);

      expect(uniqueIds.size).toBe(100); // All IDs should be unique
    });

    it('accepts custom length parameter', () => {
      const shortId = generateId(4);
      const longId = generateId(16);

      expect(shortId.length).toBe(4);
      expect(longId.length).toBe(16);
    });
  });
});
